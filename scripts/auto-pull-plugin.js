/**
 * 自动 pull 所有的plugin ，用于项目初始化 
 */

const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const mergePackage = require('./mergePackage');
const log = require('./log');

// 定义要更新的代码仓库目录列表
const repositories = require(path.join(process.cwd(), './scripts/plugins.js'));

// 定义要执行的命令列表
const getCommands = (repo, branch = 'master') => [
  `git remote remove ${repo}`,
  `git remote add ${repo} ********************:torpedo/plugin-web-${repo}.git`,
  `git rm -r src/${repo}`,
  `git subtree add --prefix=src/${repo} ${repo} ${branch}`,
];

function checkFolderExists(folderPath) {
  try {
    // 使用 fs.statSync() 方法检查文件夹状态
    const stats = fs.statSync(folderPath);
    return stats.isDirectory(); // 判断是否为文件夹
  } catch (error) {
    if (error.code === 'ENOENT') {
      return false; // 文件夹不存在
    } else {
      throw error; // 其他错误，抛出异常
    }
  }
}

// 执行 Git 命令的函数
function executeGitCommand(command) {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        reject(error);
      } else {
        log.success(`Git 执行成功:`,` ${command}`);
        resolve();
      }
    });
  });
}

// 批量执行 Git 命令
async function batchExecuteGitCommands(repo, branch) {
  const commands = getCommands(repo, branch);
  for (const command of commands) {
    try {
      await executeGitCommand(command);
    } catch (error) {
      // 处理执行 Git 命令失败的情况
      log.warn(`Git 执行失败:`,` ${command}`);
      log.error(error.message);
    }
  }
}

// 批量添加 plugin
async function batchGitAddPlugin() {
  const needMergeRepo = []
  for (const repo of Object.keys(repositories)) {
    // 文件夹存在，就说明已经初始化过了，不需要在执行了
    needMergeRepo.push(repo)
    if (checkFolderExists(path.join(process.cwd(), `./src/${repo}`))) {
      log.warn(`npm run plugin:`,` --------- ${repo} 已经初始化过了！---------`);
    } else {
      console.log('')
      console.log(`--------- ${repo} 开始初始化！-----------`)
      await batchExecuteGitCommands(repo, repositories[repo]);
      console.log(`--------- ${repo} 结束初始化！-----------`)
    }
  }
  // Working tree has modifications.  Cannot add. add 完后，单独处理
  needMergeRepo.forEach(repo => {
    mergePackage(repo);
  });
}

// 执行批量添加 plugin
batchGitAddPlugin();
