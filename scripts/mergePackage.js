/**
 * 自动合并各个plugin的 package.json 的依赖，生成该项目的依赖
 */

const fs = require('fs');
const path = require('path');
const log = require('./log');

// 辅助函数：合并两个依赖对象
function mergeDependencies(dependenciesA, dependenciesB) {
  const mergedDependencies = { ...dependenciesA };

  for (const dependency in dependenciesB) {
    if (dependency in mergedDependencies) {
      const versionA = mergedDependencies[dependency];
      const versionB = dependenciesB[dependency];

      if (compareVersions(versionA, versionB) < 0) {
        mergedDependencies[dependency] = versionB;
      }
    } else {
      mergedDependencies[dependency] = dependenciesB[dependency];
    }
  }

  return mergedDependencies;
}

// 辅助函数：比较版本号（包括额外后缀的情况）
function compareVersions(versionA, versionB) {
  const parsedVersionA = parseVersion(versionA);
  const parsedVersionB = parseVersion(versionB);

  if (parsedVersionA.major !== parsedVersionB.major) {
    return parsedVersionA.major > parsedVersionB.major ? 1 : -1;
  }

  if (parsedVersionA.minor !== parsedVersionB.minor) {
    return parsedVersionA.minor > parsedVersionB.minor ? 1 : -1;
  }

  if (parsedVersionA.patch !== parsedVersionB.patch) {
    return parsedVersionA.patch > parsedVersionB.patch ? 1 : -1;
  }

  if (parsedVersionA.suffix && parsedVersionB.suffix) {
    return parsedVersionA.suffix > parsedVersionB.suffix ? 1 : -1;
  }

  if (parsedVersionA.suffix && !parsedVersionB.suffix) {
    return -1;
  }

  if (!parsedVersionA.suffix && parsedVersionB.suffix) {
    return 1;
  }

  return 0;
}

// 辅助函数：解析版本号
function parseVersion(version) {
  const parts = version.split('-');
  const versionParts = parts[0].split('.');
  const major = parseInt(versionParts[0].replace(/^[\^~><=]+/, '')) || 0;
  const minor = parseInt(versionParts[1]) || 0;
  const patch = parseInt(versionParts[2]) || 0;
  const suffix = parts[1] || null;

  return {
    major,
    minor,
    patch,
    suffix,
  };
}


module.exports = (repo) => {
  // 获取 package.json 文件的路径,读取 package.json 文件内容并解析为对象
  const packagePath = path.join(process.cwd(), 'package.json');
  const packageObj = JSON.parse(fs.readFileSync(packagePath));

  try {
    const curPluginPath = path.join(process.cwd(), 'src', repo, 'package.json');
    const curPackageObj = JSON.parse(fs.readFileSync(curPluginPath));
    // 合并 dependencies 和 devDependencies
    const mergedDependencies = mergeDependencies(packageObj.dependencies, curPackageObj.dependencies);
    const mergedDevDependencies = mergeDependencies(
      packageObj.devDependencies,
      curPackageObj.devDependencies,
    );
    // 更新 packageObj 的 dependencies 和 devDependencies
    packageObj.dependencies = mergedDependencies;
    packageObj.devDependencies = mergedDevDependencies;
  } catch (error) {
    // 处理执行 Git 命令失败的情况
    log.notice(`MergePackage：`,` ${repo} 的 package.json 不存在！`);
    log.error(error.message);
    return;
  }

  // 将更新后的 package 对象转换为 JSON 字符串
  const updatedPackage = JSON.stringify(packageObj, null, 2);

  // 将更新后的 package.json 文件写入磁盘
  fs.writeFileSync(packagePath, updatedPackage);

  log.success(`更新package.json：`,`${repo} package.json 更新完成！`);
};
