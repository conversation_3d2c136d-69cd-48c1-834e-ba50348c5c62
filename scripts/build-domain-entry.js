/**
 * 创建项目启动入口 index.ts
 */

const fs = require('fs');
const path = require('path');

// 设置子项目目录
const srcDirectory = path.join(process.cwd(), './src');
const convertedStr = (str) => str.replace(/-(\w)/g, (_, char) => char.toUpperCase());

// 获取 src 目录下的所有文件夹
const directories = fs
  .readdirSync(srcDirectory, { withFileTypes: true })
  .filter((dirent) => dirent.isDirectory())
  .map((dirent) => dirent.name);

// 构建引入语句
const importStatements = directories
  .map((directory) => {
    return `import ${convertedStr(directory)} from './${directory}/src';`;
  })
  .join('\n');

// 构建完整的 index.ts 文件内容
const indexContent = `
${importStatements}

const pluginObject = {
${directories.map((item) => convertedStr(item)).join(',\n')}
}

let plugins = []
for(let item of Object.keys(pluginObject)){
  plugins = plugins.concat(pluginObject[item]
  
)
}
  

  
export default plugins;
`;

// 写入 index.js 文件
fs.writeFileSync(path.join(srcDirectory, 'index.js'), indexContent);

console.log('创建入口：','index.ts 入口文件已创建成功！');
