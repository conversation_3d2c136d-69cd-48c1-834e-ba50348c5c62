{"name": "@ekuaibao/plugin-web-pay-domain", "version": "1.28.5-release-qa.36", "author": "", "scripts": {"plugin": "node ./scripts/auto-pull-plugin.js", "fix-jszip-issue": "node ./scripts/fix-jszip-issue.js", "lint": "eslint --ext .tsx,.ts --fix ./src", "build:pre": "node ./scripts/build-domain-entry.js", "start": "npm run build:pre && npm run dev", "dev": "cross-env NODE_ENV=development webpack-dev-server --progress --color --config webpack.config.dev.js", "clean": "<PERSON><PERSON><PERSON> build", "build": "run-s clean build:pre build:src", "upload_plugin_to_cdn": "upload_plugin_to_cdn build", "build:src": "NODE_ENV=production webpack --progress --color -p --config webpack.config.pro.js", "test": "jest", "push_plugin": "node ./scripts/push-back-plugin.js"}, "devDependencies": {"@babel/runtime": "7.18.3", "@ekuaibao/webpack-retry-chunk-load-plugin": "^1.5.4", "@types/file-saver": "^2.0.5", "@types/jest": "^24.0.17", "@types/node": "^12.7.1", "@types/react": "^16.9.1", "@typescript-eslint/eslint-plugin": "^3.7.1", "@typescript-eslint/parser": "^3.7.1", "babel-eslint": "^10.1.0", "cross-env": "^7.0.3", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-jsx-control-statements": "^2.2.1", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.20.5", "husky": "^4.2.5", "jest": "^24.8.0", "lint-staged": "^10.2.11", "npm-run-all": "^4.1.5", "npmlog": "^7.0.1", "prettier": "^2.0.5", "rimraf": "^3.0.2", "ts-jest": "^24.0.2", "upload_to_cdn": "^1.2.8", "whispered-build": "3.3.13-beta.3", "@types/big.js": "6.2.2", "@hose/eui-icons": "^3.0.41"}, "dependencies": {"@ekuaibao/collection-definition": "1.0.1-release.1", "@ekuaibao/datagrid": "1.9.0", "@ekuaibao/ekuaibao_types": "^1.0.62-beta.0", "@ekuaibao/enhance-layer-manager": "5.6.6", "@ekuaibao/enhance-stacker-manager": "^3.1.3", "@ekuaibao/eui-styles": "^2.1.0", "@ekuaibao/keel": "^1.1.1", "@ekuaibao/lib": "2.15.12-qa.2", "@ekuaibao/loading": "^4.0.1", "@ekuaibao/money-math": "^4.1.5", "@ekuaibao/react-ioc": "^1.0.0", "@ekuaibao/sdk-bridge": "^1.10.11", "@ekuaibao/template": "^5.0.5", "@ekuaibao/uploader": "^3.3.2", "@ekuaibao/vendor-antd": "3.9.9-beta.3", "@ekuaibao/vendor-common": "^1.1.0", "@ekuaibao/vendor-lodash": "4.17.13", "@ekuaibao/vendor-whispered": "^2.2.2", "@ekuaibao/web-theme-variables": "^1.1.1", "@hose/eui": "3.9.3", "@hose/eui-theme": "^3.0.6", "@hose/pro-table": "2.81.0", "@hose/eui-table-plugin": "^0.1.11", "array-move": "^4.0.0", "big.js": "5.2.2", "colors": "1.4.0", "ekbc-datagrid": "^5.2.0", "ekbc-query-builder": "^2.0.2", "file-saver": "^2.0.5", "mobx": "^5.15.6", "mobx-react": "^6.3.0", "react": "^16.12.0", "react-copy-to-clipboard": "^5.0.2", "react-dom": "^16.12.0", "react-sortable-hoc": "^0.8.4", "tslib": "^1"}, "publishConfig": {"registry": "https://npm.ekuaibao.com/"}, "license": "UNLICENSED", "xhusky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"linters": {"*.{js,jsx,ts,tsx,json,css,less,scss,md}": ["prettier --write", "git add"]}, "ignore": ["**/assets/**/*"]}, "jest": {"moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node", "mjs"], "collectCoverage": true, "collectCoverageFrom": ["<rootDir>/src/**/*.{ts,tsx}", "!**/*.d.ts"], "coverageDirectory": "temp/coverage", "testMatch": ["<rootDir>/src/**/*.spec.{ts,tsx}"], "transform": {"^.+\\.tsx?$": "ts-jest"}}, "description": "支付业务域"}