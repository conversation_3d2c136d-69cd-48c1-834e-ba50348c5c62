/***************************************************
 * Created by nanyuantingfeng on 2019-07-04 16:52. *
 ***************************************************/
const config = require("whispered-build/webpack.plugin.dev.config");
const fs = require("fs");

require("@ekuaibao/vendor-lodash/patch")(config);
require("@ekuaibao/vendor-common/patch")(config);
require("@ekuaibao/vendor-whispered/patch")(config);
require("@ekuaibao/vendor-antd/patch")(config);
if (fs.existsSync(__dirname + '/webpack.config.local.js')) {
    require('./webpack.config.local')(config)
}

const devConfig = config.toConfig();
devConfig.devtool = 'cheap-module-eval-source-map'

module.exports = devConfig;
