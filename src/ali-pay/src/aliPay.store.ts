/**************************************
 * Created By LinK On 2019/11/25 18:55.
 **************************************/
import { observable } from 'mobx'
import { action } from '@ekuaibao/mobx-store'
import { Resource } from '@ekuaibao/fetch'

const aliPayAuthorized = new Resource('/api/pay/v1/channels/$ALIPAY/authorized')

interface AliPayAuthorConfig {
  result: boolean
  address: string
}

export default class AliPayStore {
  @observable //记录是否支付宝授权状态
  aliPayAuthorConfig: AliPayAuthorConfig

  @action
  async checkAliPayAuthorized() {
    const res = await aliPayAuthorized.GET()
    this.aliPayAuthorConfig = res.value
  }
}
