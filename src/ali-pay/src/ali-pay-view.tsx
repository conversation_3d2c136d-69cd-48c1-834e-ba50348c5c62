/**************************************
 * Created By LinK On 2019/11/25 16:33.
 **************************************/

import React, { PureComponent } from 'react'
import styles from './Detail.module.less'
import { app as api } from '@ekuaibao/whispered'
import { connect } from '@ekuaibao/mobx-store'
import { T } from '@ekuaibao/i18n'

interface Props {
  aliPayAuthorConfig: {
    result: boolean
    address: string
  }
}

//@ts-ignore
@connect(store => ({
  aliPayAuthorConfig: store.states['@aliPay'].aliPayAuthorConfig
}))
export default class AliPayView extends PureComponent<Props> {

  componentDidMount() {
    api.store.dispatch('@aliPay/checkAliPayAuthorized')()
  }

  render() {
    const { aliPayAuthorConfig } = this.props
    const authorzied = aliPayAuthorConfig && aliPayAuthorConfig.result
    const btnText = authorzied ? i18n.get('已授权') : i18n.get('立即授权支付宝（办公）')
    const cls = authorzied ? 'verify-code disabled' : 'verify-code'
    const handleClick = aliPayAuthorConfig && !aliPayAuthorConfig.result
      ? () => api.emit('@vendor:open:link', aliPayAuthorConfig.address)
      : () => void 0
    const label = i18n.get('已开通')
    const src = 'https://pic.ekuaibao.com/extensionServiceInfo_inner_description_new.png'
    return (
      <div className={styles['detail-content']}>
        <div className="content-left">
          <img className="detail-image" src={src} alt=""/>
        </div>
        <div className="content-right">
          <div className="label">{i18n.get(`功能{__k0}`, { __k0: label })}</div>
          <div className="description"><T name='点击授权支付宝（办公）后可使用该渠道进行下单支付'/></div>
          {aliPayAuthorConfig && (<div className="action">
            <div className={cls} onClick={handleClick} data-testid="ali-pay-authorize-button">{btnText}</div>
          </div>)}
        </div>
      </div>
    )
  }

}
