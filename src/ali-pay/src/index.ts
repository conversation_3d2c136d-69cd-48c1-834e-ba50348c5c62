/**************************************
 * Created By LinK On 2019/11/25 16:32.
 **************************************/
import key from './key'

export default [
  {
    point: '@@components',
    namespace: key.ID,
    onfirst: async app => {
      await app.store.dynamic(key.ID, () => import('./aliPay.store')).load(key.ID)
    },
    onload: () => [{ key: 'AliPayView', component: () => import('./ali-pay-view') }]
  }
]
