/***************************************************
 * Created by nanyuantingfeng on 2019-07-04 16:52. *
 ***************************************************/
const config = require("whispered-build/webpack.plugin.pro.config");
const { RetryChunkLoadPlugin } = require('@ekuaibao/webpack-retry-chunk-load-plugin');

require("@ekuaibao/vendor-lodash/patch")(config);
require("@ekuaibao/vendor-common/patch")(config);
require("@ekuaibao/vendor-whispered/patch")(config);
require("@ekuaibao/vendor-antd/patch")(config);

if (process.env.NODE_ENV === 'production') {
  config.plugin('RetryChunkLoadPlugin').use(RetryChunkLoadPlugin);
}

module.exports = config.toConfig();
