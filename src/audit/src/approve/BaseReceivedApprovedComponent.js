import React, { PureComponent } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'

import { MessageCenter } from '@ekuaibao/messagecenter'
import { externalStaffActionBlackList } from '../view-util'
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

export class BaseReceivedApprovedComponent extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
    this.state = {
      scenes: [],
    }
  }

  fetchPending = (params = {}, dimensionItems = {}, otherQuery = {}) => {
    params.status = this.__status
    const { scenes } = this.state
    const { scene } = params
    const findScene = scenes.find(s => s.sceneIndex === scene)
    if (findScene) {
      params.scene = findScene.scene !== 'waitInvoice' ? findScene.scene : ''
    }
    scene && this.setState({ scene, fetchParams: params, dimensionItems, otherQuery })
    return api.invokeService('@audit:invoke:fetchApproved', params, findScene, dimensionItems, otherQuery)
  }

  handleSelectAllBtnClick = (params = {}) => {
    params.status = this.__status
    const { scenes, scene, fetchParams, dimensionItems } = this.state
    const findScene = scenes.find(s => s.sceneIndex === scene)
    findScene && (params.scene = findScene.scene || '')

    if (findScene) {
      params.scene = findScene.scene !== 'waitInvoice' ? findScene.scene : ''
    }
    if (fetchParams) {
      if (fetchParams.filters) {
        params.filters = fetchParams.filters
      }
      if (fetchParams.searchText) {
        params.searchText = fetchParams.searchText
      }
    }

    const { legalEntityCurrencyPower, showPrintBtn } = this.props
    return api
      .invokeService(
        '@audit:invoke:searchApprovedFlowsCalcNoPage',
        params,
        findScene,
        dimensionItems,
        legalEntityCurrencyPower,
      )
      .then(resp => {
        let data = {}
        resp &&
          resp.value &&
          resp.value.flows.length > 0 &&
          resp.value.flows.forEach(flow => {
            data[flow.id] = flow
          })
        let sum = (resp && resp.value && resp.value.formMoney) || 0
        let keys = Object.keys(data)
        let buttons = [
          {
            name: i18n.get('导出'),
            type: 'normal',
            key: 'export_all',
          },
          {
            name: i18n.get('打印单据'),
            type: 'normal',
            key: 'print',
          },
        ]

        if (showPrintBtn)
          buttons.push({
            name: i18n.get('打印单据和发票'),
            type: 'normal',
            key: 'printInvoice',
          })

        api.open('@layout:DataGridSelectAllModal', { keys, buttons, sum }).then(name => {
          this.handleButtonsClick({ name, data, keys }, params)
        })
      })
  }
  __billInfoPopup = flow => {
    const title = `${i18n.get(billTypeMap()[flow.formType])}${i18n.get('详情')}`
    // 新增审批状态 从已审批进入
    api.open(
      '@bills:BillInfoPopup',
      {
        title,
        backlog: { id: -1, flowId: flow },
        invokeService: '@bills:get:flow-info',
        scene: 'APPROVER',
        params: {
          id: flow.id,
          __status: this.__status ? this.__status['approvedFlow.action'] : '',
        },
        reload: this.bus.reload,
        mask: false
      },
      true,
    )
  }

  handleTableRowClick = backlog => {
    startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()

    if (api.require('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
      api.open('@bills:BillInfoDrawerV2', {
        currentId: backlog.id,
        flows: this._currentDataSource,
        bus: this.bus,
      })
      return
    }

    api.invokeService('@bills:get:flow-info', { id: backlog.id }).then(resp => {
      this.__billInfoPopup(resp.value)
    })
  }

  getOptionButtons = () => {
    const btns = [
      { text: i18n.get('导出选中'), name: 'export_selected' },
      { text: i18n.get('打印单据'), name: 'print' },
      { text: i18n.get('打印单据和发票'), name: 'printInvoice' },
    ]
    const { showPrintBtn, userInfo } = this.props
    let showButtons = showPrintBtn ? btns : btns.filter(v => v.name !== 'printInvoice')
    if (userInfo?.staff?.external) {
      showButtons = showButtons.filter(v => !externalStaffActionBlackList.includes(v.name))
    }
    return showButtons
  }

  buttons = this.getOptionButtons()

  selectAllBtnStyles = { color: 'var(--brand-base)' }
}
