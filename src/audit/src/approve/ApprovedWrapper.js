import MessageCenter from '@ekuaibao/messagecenter'
import { app as api } from '@ekuaibao/whispered'
import { handlePrint, handleActionImplementation } from '../service'
const { exportExcel } = api.require('@lib/export-excel-service')
import { Resource } from '@ekuaibao/fetch'
import { EnhanceConnect } from '@ekuaibao/store'
import { fnFilterMapping, getInitScenes } from '../util/Utils'
const LoaderWithLegacyData = api.require('@elements/data-grid-v2/LoaderWithLegacyData')
const { globalSearchOptions, searchOptions } = api.require('@lib/data-grid-v2/CustomSearchUtil')
const { createNodeNameColumn, createNodeStaffColumn } = api.require('@elements/data-grid-v2/CreateColumn')
import { mapping } from '../util/mapping4Approved'
import { createActionColumn4Approved, createRiskWarningColumn } from '../util/columnsAndSwitcherUtil'
import { BaseReceivedApprovedComponent } from './BaseReceivedApprovedComponent'
import { cloneDeep } from 'lodash'
import FilterBar from '../record-table/filter-bar'
import React, { useState } from 'react'
import moment from 'moment'
const getStaffShowByConfig = api.require('@elements/staffs/staffShowFn')
const StaffFilter = api.require('@elements/data-grid-v2/StaffFilter')

const approved = new Resource('/api/flow/v2/filter')

const scenesType = 'APPROVED'
const prefixColumns = { state: '$', '*': 'form' }

const tableColumns = () => {
  return [
    {
      title: i18n.get('审批人'),
      dataIndex: 'flowApprovedId',
      width: window.isNewHome ? void 0 : 210,
      render: function (text, line) {
        const str = getStaffShowByConfig(line.flowApprovedId)
        return <span>{str}</span>
      },
      hiddenFilterAction: true,
      filterType: 'custom',
      filterStyles: {
        wrapperStyle: {
          border: 'none',
          backgroundColor: 'transparent'
        },
        bodyStyle: {
          padding: '0'
        }
      },
      renderFilter: (props) => <StaffFilter {...props} />,
    },
  ]
}

@EnhanceConnect(state => ({
  specifications: state['@custom-specification'].specificationGroupsList,
  invoiceReviewPower: state['@common'].powers.invoiceReview,
  legalEntityCurrencyPower: state['@common'].powers.legalEntityCurrency,
  // 涉及需求 https://hose2019.feishu.cn/wiki/wikcncXiMtXZQwNcS64OMEwSsJd#fTSuk8
  KA_GLOBAL_SEARCH_2: state['@common'].powers.KA_GLOBAL_SEARCH_2,
  showPrintBtn: state['@common'].showPrintBtn,
}))
export default class ApprovedWrapper extends BaseReceivedApprovedComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
    this.state = {
      scenes: [],
      total: 0,
      queryDateRange: { ...getDefultDateRange() },
    }
  }

  componentWillUnmount() {
    this.bus.un('buttons:click', this.handleButtonsClick)
    this.bus.un('table:row:click', this.handleTableRowClick)
    this.bus.un('table:row:action', this.handleActions)
    this.bus.un('selectAllBtn:click', this.handleSelectAllBtnClick)
    this.bus.un('initScenes:action', this.initScenes)
  }

  fnGetScene = () => {
    const { scenes, scene } = this.state
    let cloneScenes = cloneDeep(scenes)
    let findScenes = cloneScenes.find(s => s.sceneIndex === scene)
    if (findScenes) {
      findScenes.scene = findScenes.scene !== 'waitInvoice' ? findScenes.scene : ''
    }
    if (findScenes && findScenes.scene) return scene
    return findScenes
  }
  _handlePrintList = (keys, data, fn, printInvoice) => {
    handlePrint.call(this, keys, data, fn, false, printInvoice)
  }

  componentDidMount() {
    this.bus.on('buttons:click', this.handleButtonsClick)
    this.bus.on('table:row:click', this.handleTableRowClick)
    this.bus.on('table:row:action', this.handleActions)
    this.bus.on('selectAllBtn:click', this.handleSelectAllBtnClick)
    this.bus.on('initScenes:action', this.initScenes)

    // 获取场景列表
    approved.GET('/$type', { type: scenesType }, null, { hiddenLoading: true }).then(async res => {
      const { value } = res
      const { specifications = [] } = this.props
      if (specifications.length === 0) {
        await api.invokeService(
          '@custom-specification:get:specificationGroups:withSpecificationVersioned',
        )
      }
      this.initScenes(value)
    })
  }

  initScenes = data => {
    const { specifications, userInfo} = this.props
    const scenesData = getInitScenes({data,prefix:'',specifications,userInfo})
    this.setState({ scenes:scenesData.scenes })
  }

  __status = { 'approvedFlow.action': 'APPROVED' }
  __handleInsertAssist = title => {
    !!title &&
      api.invokeService('@common:insert:assist:record', {
        title,
      })
  }
  handleButtonsClick = ({ name, data, keys }, fetchParam) => {
    keys = keys ? keys : this.bus.getSelectedRowKeys()
    data = data ? data : this.bus.getSelectedRowData()
    this._actionTitle =
      Object.values(data)
        .map(item => item?.form?.title)
        .join(',') || ''
    switch (name) {
      case 'print':
        return this._handlePrintList(
          keys,
          data,
          () => {
            this.bus.clearSelectedRowKeys()
            this.__handleInsertAssist(this._actionTitle ? `打印${this._actionTitle}单据` : '') // @i18n-ignore
            this.bus.reload()
          },
          '0',
        )
      case 'printInvoice':
        return this._handlePrintList(
          keys,
          data,
          () => {
            this.bus.clearSelectedRowKeys()
            this.__handleInsertAssist(this._actionTitle ? `打印${this_actionTitle}单据` : '') // @i18n-ignore
            this.bus.reload()
          },
          '1',
        )
      case 'export_selected':
        this.__handleInsertAssist(this._actionTitle ? `导出${this._actionTitle}单据` : '') // @i18n-ignore
        return exportExcel({ exportType: 'export_selected', funcType: 'approve', data }, this.bus)
      case 'export_all': {
        let params = fetchParam || this.bus.getFilterParam()
        let { userInfo } = this.props
        params.status = { ...this.__status, 'approvedFlow.action': ['APPROVED'] }
        const scene = this.fnGetScene()
        params.scene = scene
        const { queryDateRange } = this.state
        let exportParam = {
          exportType: 'export_all',
          funcType: 'approve',
          data: params,
          others: {
            filter: `(formType != "permit") && (approvedFlow.updateTime > ${moment(
              queryDateRange?.startDate,
            ).valueOf()}) && (approvedFlow.updateTime < ${moment(
              queryDateRange.endDate,
            ).valueOf()}) && (approvedFlow.approverId.in("${userInfo.staff.id}"))`,
          },
          needAsyncExport: true,
        }
        if (scene && scene.sceneIndex === 'waitInvoice') {
          exportParam.others.queryString = 'waitInvoice=true'
        }
        return exportExcel(exportParam, this.bus, true)
      }
    }
  }

  handleActions = (type, line) => {
    handleActionImplementation.call(this, {
      type,
      backlog: line,
      fn: () => {
        this.bus.reload().then(this.bus.clearSelectedRowKeys)
      },
    })
  }

  handleSelectAllBtnClick = (params = {}) => {
    params.status = this.__status
    const { scenes, scene, fetchParams } = this.state
    const findScene = scenes.find(s => s.sceneIndex === scene)
    findScene && (params.scene = findScene.scene || '')

    if (findScene) {
      params.scene = findScene.scene !== 'waitInvoice' ? findScene.scene : ''
    }
    if (fetchParams) {
      if (fetchParams.filters) {
        params.filters = fetchParams.filters
      }
      if (fetchParams.searchText) {
        params.searchText = fetchParams.searchText
      }
    }

    const { total } = this.state
    let buttons = [
      {
        name: i18n.get('导出'),
        type: 'normal',
        key: 'export_all',
      },
    ]

    api
      .open('@layout:DataGridSelectAllModal', {
        totalCount: total,
        buttons,
        isBillsTotalMoney: true,
      })
      .then(name => {
        this.handleButtonsClick({ name, data: {}, keys: [] }, params)
      })
  }

  handleFilterBarChange = (filterParams = {}) => {
    const { startDate, endDate } = filterParams
    if (startDate && endDate) {
      this.setState({ queryDateRange: { startDate, endDate } }, () => {
        this.bus.reload()
      })
    }
  }

  fetch = (params = {}, dimensionItems = {}) => {
    const { queryDateRange } = this.state
    if (queryDateRange?.startDate && queryDateRange?.endDate) {
      params.otherFilters = [
        `(approvedFlow.updateTime > ${moment(
          queryDateRange?.startDate,
        ).valueOf()}) && (approvedFlow.updateTime <  ${moment(queryDateRange.endDate).valueOf()})`,
      ]
    }
    const flowApprovedIds = params.filters?.flowApprovedId?.flowApprovedId // 数据产生两层结构，原因待确认
    if (flowApprovedIds?.length > 0) {
      delete params.filters.flowApprovedId
    }
    const otherQuery = { flowApprovedId: flowApprovedIds }
    return this.fetchPending({ ...params, needOrderByCode: false }, dimensionItems, otherQuery).then(res => {
      this._currentDataSource = res.dataSource
      this.setState({ total: res?.total })
      return res
    })
  }

  render() {
    const {
      baseDataProperties,
      invoiceReviewPower,
      KA_GLOBAL_SEARCH_2
    } = this.props
    const { scenes } = this.state

    if (!scenes.length) {
      return null
    }


    return (
      <LoaderWithLegacyData
        newSearch={true}
        searchOptions={KA_GLOBAL_SEARCH_2 ? globalSearchOptions('form') : searchOptions('form')}
        enableGlobalSearch={KA_GLOBAL_SEARCH_2}
        scenes={scenes}
        fetch={this.fetch}
        otherColumns={tableColumns}
        scenesType={scenesType}
        buttons={this.buttons}
        onButtonClick={this.handleButtonsClick}
        onSelectedAll={this.handleSelectAllBtnClick}
        prefixColumns={prefixColumns}
        bus={this.bus}
        resource={approved}
        baseDataProperties={baseDataProperties}
        saveSceneWithGroupIndex={true}
        createAction={createActionColumn4Approved}
        mapping={fnFilterMapping(mapping, invoiceReviewPower)}
        createRiskWarningColumn={createRiskWarningColumn}
        createNodeStaffColumn={createNodeStaffColumn}
        createNodeNameColumn={createNodeNameColumn}
        headerTopRender={() => <FilterBarView onChange={this.handleFilterBarChange} />}
        useNewFieldSet
      />
    )
  }
}

export const FilterBarView = props => {
  const { onChange, showBillType = false, billType } = props
  const [dateRange, setDateRange] = useState(getDefultDateRange())
  const [billsType, setBillType] = useState(billType)

  const handleFilterBarChange = (filterParams = {},type) => {
    const { sdate, edate } = filterParams
    if (sdate && edate) {
      const dataRange = { startDate: sdate, endDate: edate }
      setDateRange(dataRange)
      setBillType(type)
      onChange(dataRange,type)
    }
  }


  return (
    <FilterBar
      style={{ marginBottom: '10px' }}
      sdate={dateRange?.startDate}
      edate={dateRange?.endDate}
      queryType={'operation'}
      showSearch={false}
      billType = {billsType}
      showBillType={showBillType}
      onChange={handleFilterBarChange}
    />
  )
}

function getDefultDateRange() {
  return {
    startDate: moment().subtract(6, 'months').format('YYYY-MM-DD') + ' 00:00:00',
    endDate: moment().format('YYYY-MM-DD') + ' 23:59:59',
  }
}
