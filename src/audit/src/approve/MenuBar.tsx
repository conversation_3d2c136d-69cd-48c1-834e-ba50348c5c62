import React, { useEffect, useState } from 'react'
import { Button, Switch, Tooltip } from '@hose/eui'
import { app as api } from '@ekuaibao/whispered'
import { OutlinedTipsMaybe } from '@hose/eui-icons'
import { getFlowApprovalConfig, setApprovalConfig } from '../audit-action'

export const MenuBar: React.FunctionComponent<any> = props => {
  const { setContinuousApproval, setListMode, INTELLIGENT_APPROVAL } = props
  const [checked, setChecked] = useState(false)
  useEffect(() => {
    api.dispatch(getFlowApprovalConfig()).then((res: any) => {
      setChecked(res?.value?.continuousApproval)
      setContinuousApproval(res?.value?.continuousApproval)
    })
  }, [])
  const handleSwitchChange = (check: boolean) => {
    api.dispatch(setApprovalConfig(check)).then((res: any) => {
      const continuousApproval = res?.value?.continuousApproval
      setChecked(continuousApproval)
      setContinuousApproval(continuousApproval)
    })
  }
  return (
    <div>
      <Switch checked={checked} onChange={handleSwitchChange} />
      <span style={{ fontSize: '14px', marginLeft: '6px' }}>{i18n.get('连续审批')}</span>
      <Tooltip
        arrowPointAtCenter
        placement="topRight"
        title={i18n.get(
          '开启连续审批，在移动端和PC端单据审批完成后，不返回单据列表，自动显示下一个待审批的单据详情。',
        )}>
        <OutlinedTipsMaybe fontSize={14} style={{ color: '#999', marginLeft: '6px' }}/>
      </Tooltip>
      {INTELLIGENT_APPROVAL && (<Button category="secondary" className='ml-8' onClick={setListMode}>
        {i18n.get('明细维度')}
      </Button>)}
    </div>
  )
}
