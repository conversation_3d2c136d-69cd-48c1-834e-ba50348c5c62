/**************************************************
 * Created by nany<PERSON>ingfeng on 20/09/2017 12:19.
 **************************************************/
import React from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import moment from 'moment'
import { app as api } from '@ekuaibao/whispered'
import * as actions from '../audit-action'
import key from '../key'
import { BaseHistoryComponent } from '../BaseHistoryComponent'
import './HistoryWrapper.less'
import { connect } from '@ekuaibao/mobx-store'
import MessageCenter from '@ekuaibao/messagecenter'
import { Fetch, Resource } from '@ekuaibao/fetch'
import { QuerySelect } from 'ekbc-query-builder'
import { getLinkNodeElement, triggerClick } from '@ekuaibao/sdk-bridge/sdk/utils'
import { showMessage } from '@ekuaibao/show-util'
const prefixColumns = { state: 'flowId', '*': 'flowId.form' }
import * as viewUtil from '../view-util'
import { billTypeToMoneyKey } from '@ekuaibao/lib/lib/enums'
const Money = api.require('@elements/puppet/Money')
const LoaderWithLegacyData = api.require('@elements/data-grid-v2/LoaderWithLegacyData')
import { getInitScenes } from '../util/Utils'
import { historyMapping } from '../util/mapping4Approve'
import { FilterBarView } from './ApprovedWrapper'
import { fetchBackLogs } from '../util/fetchUtil'
const { searchOptions, globalSearchOptions } = api.require('@lib/data-grid-v2/CustomSearchUtil')
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

// 默认的导出config
const defaultConfig = {
  exportAll: false, // 是否导出全部
  includeDetails: false,
  exportRefCode: false,
  includeTrips: false,
  exportTime: false,
  detailsType: 'part',
}

const scenesType = 'APPROVEDHISTORY'
const approved = new Resource('/api/flow/v2/filter')

const tableColumns = () => {
  return [
    {
      title: i18n.get('审批时间'),
      dataIndex: 'flowId.form.updateTime',
      width: window.isNewHome ? void 0 : 150,
      sorter: false,
      render: function (text, line) {
        return viewUtil.tdActiconTime(text, line)
      },
    },
    {
      title: i18n.get('操作类型'),
      dataIndex: 'flowId.form.status',
      width: window.isNewHome ? void 0 : 140,
      sorter: false,
      render: function (text, line) {
        return viewUtil.tdStatus(line)
      },
      allowReordering: false,
      allowGrouping: false,
      allowHiding: false,
    },
    {
      title: i18n.get('单据金额'),
      sorter: false,
      dataIndex: 'flowId.form.specificationMoney',
      width: window.isNewHome ? void 0 : 120,
      render: (value, record) => {
        record = value || record
        if (record && record.flowId) {
          let type = record.flowId.formType
          let form = record.flowId.form
          const moneyKey = billTypeToMoneyKey[type]
          const money = form[moneyKey]
          return money === undefined ? '-' : <Money currencySize={10} valueSize={12} color="#333333" value={money} />
        }
      },
    },
  ]
}

@connect(store => ({ size: store.states['@layout'].size }))
@EnhanceConnect(state => ({
  paymentRecords: state[key.ID].payment_record_list,
  dataRecords: state[key.ID].operation_records,
  dataBills: state[key.ID].operation_bills,
  specifications: state['@custom-specification'].specificationGroupsList,
  KA_GLOBAL_SEARCH_2: state['@common'].powers.KA_GLOBAL_SEARCH_2,
}))
export default class OperationView extends BaseHistoryComponent {
  bus = new MessageCenter()
  _currentDataSource: any[] = []
  constructor() {
    super() // BaseHistoryComponent 待确认要不要继承
    const sdate = moment().subtract(6, 'months').format('YYYY-MM-DD') + ' 00:00:00'
    const edate = moment().format('YYYY-MM-DD') + ' 23:59:59'
    this.state = {
      sdate: sdate,
      edate: edate,
      billType: 'all',
      searchText: '',
      scene: undefined,
      scenes: [],
      total: 0,
      queryParams: {},
    }
  }

  buttons = [{ text: i18n.get('导出选中'), name: 'export_selected' }]

  componentDidMount() {
    this.bus.on('table:row:click', this.handleRowClick)
    this.bus.on('initScenes:action', this.initScenes)
    // 获取场景列表
    approved.GET('/$type', { type: scenesType }, undefined, { hiddenLoading: true }).then(async res => {
      const { value } = res
      const { specifications = [] } = this.props
      if (specifications.length === 0) {
        await api.invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned')
      }
      this.initScenes(value)
    })
  }

  componentWillUnmount() {
    this.bus.un('table:row:click', this.handleRowClick)
    this.bus.un('initScenes:action', this.initScenes)
  }

  fetchPending = (params: any = {}, dimensionItems = {}) => {
    const { sdate, edate, scenes } = this.state
    params.status = { state: ['PROCESSED'] }
    if (sdate && edate) {
      params.otherFilters = [this.getOtherFilters()]
    }
    const findScene = scenes.find((s: any) => s.sceneIndex === params.scene)
    if (findScene) {
      params.scene = findScene.scene !== 'waitInvoice' ? findScene.scene : ''
    }

    this.setState({ fetchParams: params, dimensionItems })
    if (this.props?.Credit) {
      params.creditSearch = true
    }
    params.updateTimeDesc = true
    params.urgentDesc = false
    return fetchBackLogs(params, findScene, dimensionItems, true).then((res: any) => {
      this._currentDataSource = res.dataSource
      this.setState({ total: res?.total, queryParams: params })
      return res
    })
  }
  /**
   * 获取其他字段
   */
  getOtherFilters = () => {
    const { sdate, edate, billType } = this.state
    let filter = `(updateTime > ${moment(sdate).valueOf()}) && (updateTime <  ${moment(edate).valueOf()})`
    if (billType !== 'all') {
      return filter + ` && type == "${billType}"`
    }
    return filter
  }

  handleRowClick = async flow => {
    startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()

    if (api.require<any>('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
      api.open('@bills:BillInfoDrawerV2', {
        currentId: flow.flowId.id,
        flows: this._currentDataSource?.map((item) => item.flowId) || [],
        bus: this.bus,
      })
      return
    }

    const action = await actions.getFlowInfo(flow.flowId.id)
    api.dispatch(action).then(flow => {
      api.open(
        '@bills:BillInfoPopup',
        {
          title: i18n.get('审批记录'),
          backlog: { id: -1, flowId: flow },
          onFooterButtonsClick: this.handlePrint,
          invokeService: '@audit:get:history-flow:info',
          params: flow.id,
          scene: 'APPROVER',
          mask: false
        },
        true,
      )
    })
  }

  /** 按钮点击 */
  handleButtonsClick = async ({ name, data }) => {
    const config = { ...defaultConfig }
    data = data ? data : this.bus.getSelectedRowData() // 选中数据

    const actionTitle =
      Object.values(data)
        .map((item: any) => item.flowId?.form?.title)
        .join(',') || ''

    switch (name) {
      case 'export_selected':
        this.__handleInsertAssist(actionTitle ? `导出${actionTitle}单据` : '') // @i18n-ignore
        return this.exportSelectedRecords({ data, config })
      case 'export_all': {
        const params = {
          data: [],
          config: { ...config, exportAll: false },
          filterStr: this.getFilterStr(this.state.queryParams),
        }
        this.__handleInsertAssist(actionTitle ? `导出所有审批记录` : '') // @i18n-ignore
        return this.asyncExport(params)
      }
    }
  }

  filterRowIds = data => {
    return Object.keys(data).map(key => {
      const v = data[key]
      return typeof v === 'string' ? v : v.id
    })
  }
  // 因后端接口不变，所以从 HistoryWrapper挪过来的方法
  exportSelectedRecords = (param: any) => {
    let { data, config } = param
    if (data.keys && !data.length) {
      data = data.keys
    }
    const params = {
      ids: this.filterRowIds(data),
      format: 'xlsx',
    }
    if (config) {
      params.config = this.getConfigString(config, false)
    }
    const el = getLinkNodeElement()
    const url = `${Fetch.fixOrigin(location.origin)}/api/flow/v2/export/batchApproverecord?corpId=${
      Fetch.ekbCorpId
    }&accessToken=${Fetch.accessToken}`
    fetch(url, {
      method: 'POST',
      body: JSON.stringify(params),
      headers: new Headers({
        Accept: 'application/octet-stream',
        'Content-Type': 'application/json',
      }),
    })
      .then(res =>
        res.blob().then(blob => {
          const url = window.URL.createObjectURL(blob)
          const contentDisposition = res.headers.get('Content-Disposition') || ''
          const filename = decodeURI(
            contentDisposition.substring(contentDisposition.indexOf('filename=') + 10, contentDisposition.length - 1),
          )
          const name = decodeURIComponent(filename)
          el.setAttribute('href', url)
          el.setAttribute('download', name)
          triggerClick(el)
        }),
      )
      .catch(err => showMessage.error(i18n.get(err.msg || err.errorMessage)))
  }

  /** 选择全部 */
  handleSelectAllBtnClick = (params = {}) => {
    const buttons = [
      {
        name: i18n.get('导出'),
        type: 'normal',
        key: 'export_all',
      },
    ]
    api
      .open('@layout:DataGridSelectAllModal', {
        totalCount: ` ${this.state.total} `,
        buttons,
        isBillsTotalMoney: true,
      })
      .then(name => {
        this.handleButtonsClick({ name, data: {} }, params)
      })
  }

  __handleInsertAssist = (title: string) => {
    !!title &&
      api.invokeService('@common:insert:assist:record', {
        title,
      })
  }

  asyncExport = async params => {
    let { config, filterStr } = params
    if (config) {
      config = this.getConfigString(config, false)
    }
    const val = await api.open('@layout:AsyncExportModal')
    const { taskName } = val
    const path = '/api/flow/v2/export/approverecord/$xlsx/async'
    Fetch.GET(path, {
      filter: decodeURIComponent(filterStr),
      taskName,
      config,
    })
  }

  getConfigString = (config: any, needEncode = true) => {
    const { includeDetails, includeTrips, exportRefCode, exportAll, exportTime, detailsType, fields = [] } = config
    let str = JSON.stringify({
      includeDetails,
      includeTrips,
      fields,
      exportRefCode,
      exportAll,
      exportTime,
      detailsType,
    })
    if (needEncode) {
      str = encodeURIComponent(str)
    }
    return str
  }

  getFilterStr = params => {
    const query = new QuerySelect()
      .filterBy(`type!="permit"`)
      .filterBy(`state.in("PROCESSED")`)
      .filterBy(this.getOtherFilters())
      .filterBy(
        `flowId.form.title.contains("${params.searchText || ''}")` +
          `||flowId.form.code.contains("${params.searchText || ''}")` +
          `||flowId.form.submitterId.name.contains("${params.searchText || ''}")`,
      )
    return encodeURIComponent(query.value().filterBy)
  }

  handleFilterBarChange = (filterParams, billType) => {
    const { startDate, endDate } = filterParams
    this.onConditionsChange()
    this.setState({ sdate: startDate, edate: endDate, billType }, () => {
      this.bus.reload()
    })
  }

  onConditionsChange = () => {
    typeof this.bus.clearSelectedData === 'function' && this.bus.clearSelectedData()
  }

  initScenes = data => {
    const { specifications } = this.props
    const scenesData = getInitScenes({ data, prefix: 'flowId', specifications, isHasWaitInvoice: false })
    this.setState({ scenes: scenesData.scenes, activeSceneIndex: scenesData.activeSceneIndex })
  }

  render() {
    const { KA_GLOBAL_SEARCH_2, baseDataProperties } = this.props
    const { billType } = this.state
    if (!this.state.scenes.length) {
      return null
    }

    return (
      <div className="approve-history-wrapper">
        <LoaderWithLegacyData
          newSearch={true}
          searchOptions={KA_GLOBAL_SEARCH_2 ? globalSearchOptions() : searchOptions()}
          scenes={this.state.scenes}
          otherColumns={tableColumns}
          enableGlobalSearch={KA_GLOBAL_SEARCH_2}
          fetch={this.fetchPending}
          scenesType={scenesType}
          buttons={this.buttons}
          onButtonClick={this.handleButtonsClick}
          onSelectedAll={this.handleSelectAllBtnClick}
          prefixColumns={prefixColumns}
          bus={this.bus}
          mapping={historyMapping}
          resource={approved}
          baseDataProperties={baseDataProperties}
          saveSceneWithGroupIndex={true}
          headerTopRender={() => (
            <FilterBarView billType={billType} showBillType={true} onChange={this.handleFilterBarChange} />
          )}
          useNewFieldSet
        />
      </div>
    )
  }
}
