import React from 'react'
import { render as reactDomRender } from 'react-dom'
import { app as api } from '@ekuaibao/whispered'
const LoaderWithLegacyData = api.require('@elements/data-grid-v2/LoaderWithLegacyData')
const { createNodeNameColumn } = api.require('@elements/data-grid-v2/CreateColumn')
const { searchOptions, globalSearchOptions } = api.require('@lib/data-grid-v2/CustomSearchUtil')
import { fetchBackLogs, getScenes, scenesFilter } from '../util/fetchUtil'
import MessageCenter from '@ekuaibao/messagecenter'
import { mapping } from '../util/mapping4Approve'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import { checkApproveProgress, searchBacklogsSimple, searchBackLogsCalcNoPage } from '../audit-action'
import { fnFilterMapping, getInitScenes } from '../util/Utils'
import {
  handleActionImplementation,
  handleAgreeBills,
  handleBatchResult,
  handlePrintRemind,
  handleReceivePrint,
  handleRejectBills,
  handleTransfer,
  handleHangUp,
} from '../service'
import { newTrack } from '../util/trackAudit'
import qs from 'qs'
const urlParams = qs.parse(location.search.slice(1))
const { isEmail } = urlParams || {}
const { exportExcel } = api.require('@lib/export-excel-service')
import { MenuBar } from './MenuBar'
import {
  createActionColumn4Approve,
  createRiskWarningColumn,
  createHangUpColumn,
  createCreditColumn,
} from '../util/columnsAndSwitcherUtil'
import { getCreditModel, checkBatchApprove } from '../audit-action'
import { EnhanceConnect } from '@ekuaibao/store'
import { getApprovalState } from '../services/ApproveService'
import { BaseAuditApprovedComponent } from './BaseAuditApprovedComponent'
import { get, cloneDeep } from 'lodash'
import { Tooltip } from 'antd'
import { Modal as EUIModal } from '@hose/eui'
import { OutlinedTipsMaybe } from '@hose/eui-icons'
import ApproveFeeTypeWrapper from './ApproveFeeTypeWrapper'
import { isFunction } from '@ekuaibao/helpers'
import { externalStaffActionBlackList } from '../view-util'
import { showMessage } from '@ekuaibao/show-util'
const { getBoolVariation } = api.require('@lib/featbit')

const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')
const { supportBillDetailsSwitchingInDrawer } = api.require('@lib/featbit')

const scenesType = 'APPROVE'
const prefixColumns = { state: 'flowId', '*': 'flowId.form' }

let _currentBills = []

// @i18n-ignore
const ACTIONS = {
  3: '同意',
  6: '支付',
  14: '添加寄送信息',
  15: '跳过寄送',
  12: '确认收单',
  1: '驳回',
  5: '修改',
  0: '打印提醒',
  16: '收到打印',
  11: '转交审批',
  17: '加签审批',
}

@EnhanceConnect(state => ({
  specifications: state['@custom-specification'].specificationGroupsList,
  invoiceReviewPower: state['@common'].powers.invoiceReview,
  legalEntityCurrencyPower: state['@common'].powers.legalEntityCurrency,
  Credit: state['@common'].powers.Credit,
  // 涉及需求 https://hose2019.feishu.cn/wiki/wikcncXiMtXZQwNcS64OMEwSsJd#fTSuk8
  KA_GLOBAL_SEARCH_2: state['@common'].powers.KA_GLOBAL_SEARCH_2,
  showPrintBtn: state['@common'].showPrintBtn,
  INTELLIGENT_APPROVAL: state['@common'].powers.INTELLIGENT_APPROVAL,
}))
export default class ApproveWrapper extends BaseAuditApprovedComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
    this.datagridInstance = null
    this.feeTypeModeStorageKey = 'approveTableFeeTypeMode'
    const feeTypeMode = props.INTELLIGENT_APPROVAL && localStorage.getItem(this.feeTypeModeStorageKey) === 'true'
    this.state = {
      scenes: [],
      activeSceneIndex: '',
      level: [],
      detailReadable: false,
      isLightingMode: this.props.isLightingMode,
      continuousApproval: false,
      notAllowBatchApproveIds: {},
      allowBatchApproveIds: {},
      feeTypeMode,
    }
    window.__AUDIT_FIRST_TIME = Date.now()
  }
  fnfilterMCButs = () => {
    if (window.__PLANTFORM__ === 'MC') {
      this.buttons = this.buttons.filter(
        line =>
          line.name !== 'approve_transfer' && line.name !== 'approve_sign_transfer' && line.name !== 'print_remind',
      )
    }
  }
  componentWillReceiveProps(nextProps, nextContext) {
    if (nextProps) {
      this.setState({ isLightingMode: nextProps.isLightingMode })
    }
  }
  async componentWillMount() {
    this.bus.on('buttons:click', this.handleButtonsClickPre)
    this.bus.on('table:row:click', this.handleTableRowClick)
    this.bus.on('table:row:action', this.handleActions)
    this.bus.on('selectAllBtn:click', this.handleSelectAllBtnClick)
    this.bus.on('initScenes:action', this.initScenes)
    if (this.props?.Credit) {
      const res = await getCreditModel()
      const level = get(res, 'value.levels', [])
      const detailReadable = get(res, 'value.detailReadable', false)
      this.setState({ level, detailReadable })
    }
    // 获取场景列表
    getScenes(scenesType).then(async res => {
      const { value } = res
      const { specifications = [] } = this.props
      if (specifications.length === 0) {
        await api.invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned')
      }
      this.initScenes(value)
    })
    this.getApprovalResult()
  }

  preRenderCellHandler = (element, info, column, columns) => {
    const { notAllowBatchApproveIds = {} } = this.state
    const notAllowBatchApproveIdsArr = Object.keys(notAllowBatchApproveIds)
    const isInNotAllowBatchApprove = !!~notAllowBatchApproveIdsArr.indexOf(info?.data?.id)
    let notAllowBatchApproveReason = ''
    if (isInNotAllowBatchApprove) {
      notAllowBatchApproveReason = notAllowBatchApproveIds[info?.data?.id]?.batchApproveReason
    }
    const lineFirstCellIsGroupSpece = !!~(element.parentElement?.firstChild?.className || '').indexOf(
      'dx-datagrid-group-space',
    )
    if (
      (lineFirstCellIsGroupSpece ? info.columnIndex === 2 : info.columnIndex === 1) &&
      !!~notAllowBatchApproveIdsArr.indexOf(info?.data?.id)
    ) {
      const tooltipEle = document.createElement('div')
      reactDomRender(
        <div style={{ marginTop: 4 }}>
          <span
            style={{
              color: 'var(--eui-illustration-orange)',
              marginRight: 4,
            }}
          >
            {i18n.get('该单据不可批量审批')}
          </span>
          <Tooltip
            placement="top"
            title={i18n.get(
              {
                OVER_STANDARD: '该单据超标，不可批量审批',
                LIMIT: '该单据你没有批量审批的权限，如有疑问，请和管理员联系',
              }[notAllowBatchApproveReason] || '',
            )}
          >
            <OutlinedTipsMaybe fontSize={16} />
          </Tooltip>
        </div>,
        tooltipEle,
      )
      if (element.lastChild?.parentNode?.appendChild) {
        element.lastChild.parentNode?.appendChild(tooltipEle)
      }
    }
  }

  updateBatchApproveCheckData = async ({ backlogIds = [], rowsData = {} } = {}) => {
    const { items: result } = (await checkBatchApprove({ backlogIds })) || {}
    if (!result || !result.length) {
      return true
    }
    // 过滤出可审批与不可审批的数据
    const isAllowBatchApproveArr = result.filter(item => item.isAllowBatchApprove)
    const isNotAllowBatchApproveArr = result.filter(item => !item.isAllowBatchApprove)
    const allowBatchApproveIdsNew = {}
    isAllowBatchApproveArr.forEach(item => {
      allowBatchApproveIdsNew[item.backlogId] = item
    })
    const notAllowBatchApproveIdsNew = {}
    isNotAllowBatchApproveArr.forEach(item => {
      notAllowBatchApproveIdsNew[item.backlogId] = item
    })
    const { allowBatchApproveIds, notAllowBatchApproveIds } = this.getNewArrowBatchApproveData(
      allowBatchApproveIdsNew,
      notAllowBatchApproveIdsNew,
    )
    const selectableRowKeys = backlogIds.filter(id => !notAllowBatchApproveIds[id])
    this.setState(
      {
        allowBatchApproveIds,
        notAllowBatchApproveIds,
      },
      () => {
        const newRowData = {}
        selectableRowKeys.forEach(key => {
          newRowData[key] = rowsData[key]
        })
        this.bus.setSelectedRowKeys(selectableRowKeys)
        this.bus.setSelectedRowData(newRowData)
        // this.datagridInstance?.repaint && this.datagridInstance.repaint()
      },
    )
    if (isNotAllowBatchApproveArr.length && !isAllowBatchApproveArr.length) {
      // 全部不可批量审批
      EUIModal.warning({
        title: i18n.get('全部为禁止批量审批的单据'),
        content: i18n.get('所选单据中全部为“禁止批量审批的单据”，需单独审批。请返回列表重新选择'),
      })
      return false
    } else if (isNotAllowBatchApproveArr.length) {
      // 存在不可批量审批，也存在可批量审批
      return await this.confirmPromise()
    } else {
      return true
    }
  }

  confirmPromise = () => {
    return new Promise((resolve, reject) => {
      try {
        EUIModal.confirm({
          title: i18n.get('存在禁止批量审批的单据'),
          content: i18n.get(
            '所选单据中包含“禁止批量审批的单据”，需单独审批。如继续，则默认去除需单独审批的单据，如需要重新选择或者查看详情，请点击【取消】',
          ),
          okText: i18n.get('继续'),
          cancelText: i18n.get('取消'),
          onOk: () => {
            resolve(true)
          },
          onCancel: () => {
            resolve(false)
          },
        })
      } catch (e) {
        reject(e)
      }
    })
  }

  getNewArrowBatchApproveData = (allowBatchApproveIdsNew, notAllowBatchApproveIdsNew) => {
    const { allowBatchApproveIds: allowBatchApproveIdsOld, notAllowBatchApproveIds: notAllowBatchApproveIdsOld } =
      this.state
    const allowBatchApproveIdsTemp = cloneDeep(allowBatchApproveIdsOld)
    const notAllowBatchApproveIdsTemp = cloneDeep(notAllowBatchApproveIdsOld)
    Object.keys(allowBatchApproveIdsNew).forEach(backlogId => {
      // 接口返回可批量审批
      if (!allowBatchApproveIdsOld[backlogId]) {
        // 当前可批量审批中不存在 => 添加到可批量
        allowBatchApproveIdsTemp[backlogId] = allowBatchApproveIdsNew[backlogId]
      }
      if (notAllowBatchApproveIdsOld[backlogId]) {
        // 当前不可批量审批中存在 => 从不可批量中删除
        delete notAllowBatchApproveIdsTemp[backlogId]
      }
    })
    Object.keys(notAllowBatchApproveIdsNew).forEach(backlogId => {
      // 接口返回不可批量审批
      if (!notAllowBatchApproveIdsOld[backlogId]) {
        // 当前不可批量审批中不存在 => 添加到不可批量审批
        notAllowBatchApproveIdsTemp[backlogId] = notAllowBatchApproveIdsNew[backlogId]
      }
      if (allowBatchApproveIdsOld[backlogId]) {
        // 当前可批量审批中存在 => 从可批量审批中删除
        delete allowBatchApproveIdsTemp[backlogId]
      }
    })
    return {
      allowBatchApproveIds: allowBatchApproveIdsTemp,
      notAllowBatchApproveIds: notAllowBatchApproveIdsTemp,
    }
  }

  initScenes = data => {
    const { specifications, userInfo } = this.props
    const scenesData = getInitScenes({ data, prefix: 'flowId', specifications, userInfo })
    this.setState({ scenes: scenesData.scenes, activeSceneIndex: scenesData.activeSceneIndex })
  }

  fetchPending = async (params = {}, dimensionItems = {}) => {
    const { scenes } = this.state
    params.status = { state: ['APPROVING'] }
    const { scene } = params
    const findScene = scenes.find(s => s.sceneIndex === scene)
    if (findScene) {
      params.scene = findScene.scene !== 'waitInvoice' ? findScene.scene : ''
    }
    scene && this.setState({ scene, fetchParams: params, dimensionItems })
    if (this.props?.Credit) {
      params.creditSearch = true
    }
    const res = await fetchBackLogs(params, findScene, dimensionItems, true)
    this._currentBills = res.dataSource
    return res
  }

  getApprovalResult = () => {
    api.dispatch(checkApproveProgress()).then(res => {
      const { value } = res
      if (value && value.residue > 0) {
        return getApprovalState()
      }
      if (value && value.residue === 0) {
        handleBatchResult(value, 'batchAgree', false)
      }
    })
  }

  handleSelectAllBtnClick = (params = {}) => {
    const { legalEntityCurrencyPower } = this.props
    const { scenes, scene, fetchParams, dimensionItems } = this.state
    const findScene = scenes.find(s => s.sceneIndex === scene)
    if (findScene) {
      params.scene = findScene.scene !== 'waitInvoice' ? findScene.scene : ''
    }
    if (this.state.isLightingMode) {
      params = { ...params, filters: this.fnGetLightingModeParam() }
    } else {
      params.status = { state: ['APPROVING'] }
      if (fetchParams) {
        if (fetchParams.filters) params.filters = fetchParams.filters
        if (fetchParams.searchText) params.searchText = fetchParams.searchText
      }
    }

    if (window.IS_SZJL) {
      return searchBackLogsCalcNoPage(params, findScene, dimensionItems, legalEntityCurrencyPower).then(resp => {
        this.fnSelectAll(resp, params)
      })
    } else {
      return searchBacklogsSimple(params, findScene, dimensionItems, legalEntityCurrencyPower).then(resp => {
        this.fnSelectAll(resp, params)
      })
    }
  }

  fnGetLightingModeParam = () => {
    const data = this.bus.getDataSource && this.bus.getDataSource()
    const codes = data.map(line => line.flowId.form.code)
    return { 'flowId.form.code': codes, state: 'APPROVING' }
  }

  fnSelectAll = (resp, params) => {
    const { showPrintBtn, userInfo } = this.props
    const data = {}
    let sum = 0
    if (window.IS_SZJL) {
      resp?.value?.flows?.forEach?.(flow => {
        data[flow.id] = flow
      })
      sum = resp?.value?.formMoney || 0
    } else {
      resp?.items?.forEach?.(flow => {
        data[flow.id] = { flowId: { id: flow.flowId }, id: flow.id }
      })
    }
    const keys = Object.keys(data)
    let buttons = [
      {
        name: i18n.get('同意'),
        type: 'primary',
        key: 'agree',
      },
      {
        name: i18n.get('驳回'),
        type: 'normal',
        key: 'reject',
        dangerous: true,
      },
      {
        name: i18n.get('导出'),
        type: 'normal',
        key: 'export_all',
      },
      {
        name: i18n.get('打印单据'),
        type: 'normal',
        key: 'print',
      },
      {
        name: i18n.get('打印提醒'),
        type: 'normal',
        key: 'print_remind',
      },
      {
        name: i18n.get('转交审批'),
        type: 'normal',
        key: 'approve_transfer',
      },
      {
        name: i18n.get('加签审批'),
        type: 'normal',
        key: 'approve_sign_transfer',
      },
      {
        name: i18n.get('收到打印'),
        type: 'normal',
        key: 'recive_print',
      },
    ]
    if (showPrintBtn) {
      buttons = buttons
        .slice(0, 4)
        .concat([
          {
            name: i18n.get('打印单据和发票'),
            type: 'normal',
            key: 'printInvoice',
          },
        ])
        .concat(buttons.slice(4, buttons.length))
    }
    if (window.__PLANTFORM__ === 'MC') {
      buttons = buttons.filter(
        line => line.key !== 'approve_sign_transfer' && line.key !== 'approve_transfer' && line.key !== 'print_remind',
      )
    }

    if (userInfo?.staff?.external) {
      buttons = buttons.filter(item => !externalStaffActionBlackList.includes(item.key))
    }

    if (window.IS_SZJL) {
      api.open('@layout:DataGridSelectAllModal', { keys, buttons, sum }).then(name => {
        typeof name === 'object'
          ? this.handleButtonsClickPre({ ...name, data, keys }, params)
          : this.handleButtonsClickPre({ name, data, keys, isSelectAll: true }, params)
      })
    } else {
      api.open('@layout:DataGridSelectAllModal', { keys, buttons, isBillsTotalMoney: true }).then(name => {
        typeof name === 'object'
          ? this.handleButtonsClickPre({ ...name, data, keys }, params)
          : this.handleButtonsClickPre({ name, data, keys, isSelectAll: true }, params)
      })
    }
  }

  handleButtonsClickPre = ({ name, data, keys, isSelectAll }, fetchParam) => {
    keys = keys ? keys : this.bus.getSelectedRowKeys()
    if ((name === 'agree' || name === 'reject') && keys.length > 1) {
      this.updateBatchApproveCheckData({
        backlogIds: keys,
        rowsData: data,
      }).then(isContinueSubmit => {
        let newKeys = keys
        const newData = cloneDeep(data)
        if (isContinueSubmit) {
          const { notAllowBatchApproveIds = {} } = this.state
          const notArrowBatchApproveKeys = Object.keys(notAllowBatchApproveIds)
          if (notArrowBatchApproveKeys.length) {
            // 去除不可批量审批的数据
            newKeys = keys.filter(key => !~notArrowBatchApproveKeys.indexOf(key))
            notArrowBatchApproveKeys.forEach(key => {
              delete newData[key]
            })
          }
          this.handleButtonsClick({ name, data: newData, keys: newKeys, isSelectAll }, fetchParam)
        }
      })
    } else {
      this.handleButtonsClick({ name, data, keys, isSelectAll }, fetchParam)
    }
  }

  handleButtonsClick = ({ name, data, keys, isSelectAll }, fetchParam) => {
    let Bill_approval_result = ''
    keys = keys ? keys : this.bus.getSelectedRowKeys()
    data = data ? data : this.bus.getSelectedRowData()
    this._actionTitle =
      Object.values(data)
        .map(item => item.flowId?.form?.title)
        .join(',') || ''

    switch (name) {
      case 'agree': //批量同意
        Bill_approval_result = '同意'
        this.trackMore(data, Bill_approval_result)
        this.bus.emit('comment:action', 'openPanel')
        return this._handleAgreeBills(keys, data, isSelectAll)
      case 'reject': //批量驳回
        Bill_approval_result = '驳回'
        this.trackMore(data, Bill_approval_result)
        this.bus.emit('comment:action', 'openPanel')
        return this._handleRejectBills(keys, data)
      case 'print': //批量打印
        return this._handlePrintList(
          keys,
          data,
          () => {
            this.reloadAndClearSelecteds()
            this.__handleInsertAssist(this._actionTitle ? `打印${this._actionTitle}单据` : '') // @i18n-ignore
          },
          '0',
        )
      case 'printInvoice': //批量打印
        return this._handlePrintList(
          keys,
          data,
          () => {
            this.reloadAndClearSelecteds()
            this.__handleInsertAssist(this._actionTitle ? `打印${this._actionTitle}单据` : '') // @i18n-ignore
          },
          '1',
        )
      case 'export_selected': //批量导出选中
        this.__handleInsertAssist(this._actionTitle ? `导出${this._actionTitle}单据` : '') // @i18n-ignore
        return exportExcel(
          { exportType: 'export_selected', funcType: 'approve', data, fromPage: 'approvePage' },
          this.bus,
        )
      case 'export_all': {
        //导出全部
        const params = fetchParam || this.bus.getFilterParam()
        const { isLightingMode } = this.state
        params.status = { ...params.status, state: ['APPROVING'] }
        const scene = this.fnGetScene()
        params.scene = scene
        // fix 导出全部时，是否需要异步导出
        const exportParam = {
          exportType: 'export_all',
          funcType: 'backlog',
          data: params,
          fromPage: 'approvePage',
          onlyAsyncExport: true,
        }
        if (scene && scene.sceneIndex === 'waitInvoice') {
          exportParam.others = { queryString: 'waitInvoice=true' }
        }
        if (isLightingMode) {
          params.filters = this.fnGetLightingModeParam()
        }
        this.__handleInsertAssist(this._actionTitle ? `导出所有单据` : '') // @i18n-ignore
        return exportExcel(exportParam, this.bus)
      }
      case 'print_remind': //打印提醒
        this._handlePrintRemindList(keys, data)
        break
      case 'approve_transfer': //转交审批
        this._handleTransfer(keys, data, isSelectAll)
        this.bus.emit('comment:action', 'openPanel')
        break
      case 'approve_sign_transfer': //加签审批
        this._handleTransfer(keys, data, isSelectAll, true)
        this.bus.emit('comment:action', 'openPanel')
        break
      case 'recive_print': //收到打印
        const flowIds = Object.values(data).map(item => item.flowId.id)
        this.handleReceivePrint(flowIds, () => {
          this.reloadAndClearSelecteds()
          this.__handleInsertAssist(this._actionTitle ? `收到打印${this._actionTitle}单据` : '') // @i18n-ignore
        })
        break
      case 'hangUp':
        const ids = Object.values(data).map(item => item.id)
        this.handleHangUp(ids, () => {
          this.reloadAndClearSelecteds()
          this.__handleInsertAssist(this._actionTitle ? `暂挂${this._actionTitle}单据` : '') // @i18n-ignore
        })
        break
      default:
        break
    }
  }
  trackMore(data, Bill_approval_result) {
    //批量埋点
    if (!Bill_approval_result) return
    for (const key in data) {
      newTrack('Bill_approval', {
        Bill_approval_result,
        navWeight: i18n.get('单据处理'),
        flowID: data[key].flowId.id,
        Approval_page_display_source: isEmail ? '邮件' : '非邮件',
      })
    }
  }
  reloadAndClearSelecteds = (continuousApproval, actionEvent) => {
    this.bus.clearSelectedRowKeys && this.bus.clearSelectedRowKeys()
    if (!this.bus.reload) {
      return
    }
    if (continuousApproval) {
      const { fetchParams } = this.state
      const newParams = {
        ...fetchParams,
        page: { currentPage: 1, pageSize: fetchParams.page.pageSize ?? 20 },
      }
      this.bus.reload(newParams).then(() => {
        if (actionEvent) {
          setTimeout(() => {
            this.bus.emit('table:select:current:row')
          }, 500)
        }
        const list = this.bus.getDataSource()
        if (list?.length) {
          this.handleTableRowClick(list[0])
        }
      })
    } else {
      this.bus.reload().then(() => {
        if (actionEvent) {
          setTimeout(() => {
            this.bus.emit('table:select:current:row')
          }, 500)
        }
      })
    }
  }

  __actionDone = (title, continuousApproval = false, actionEvent) => {
    api.invokeService('@layout5:refresh:menu:data')
    this.reloadAndClearSelecteds(continuousApproval, actionEvent)
    this.__handleInsertAssist(title)
  }

  __handleInsertAssist = title => {
    !!title &&
      api.invokeService('@common:insert:assist:record', {
        title,
      })
  }

  _handlePrintRemindList = (keys, data) => {
    const backLogs = Object.values(data)
    const flowIds = backLogs.map(element => element.flowId.id)
    handlePrintRemind.call(this, flowIds, () =>
      this.__actionDone(this._actionTitle ? `打印提醒${this._actionTitle}单据` : ''),
    ) // @i18n-ignore
  }

  _handleTransfer(keys, data, isSelectAll, isSignNode) {
    let { fetchParams, isLightingMode } = this.state
    const type = isSignNode ? '转交审批' : '加签审批' // @i18n-ignore
    const title = this._actionTitle ? `${type}${this._actionTitle}单据` : '' // @i18n-ignore
    if (!fetchParams) {
      fetchParams = { status: { state: ['APPROVING'] } }
    }
    if (isSelectAll) {
      let scene = this.fnGetScene()
      if (scene && scene.sceneIndex === 'waitInvoice') {
        scene = { scene: 'waitInvoice' }
      }
      fetchParams.scene = scene
      if (isLightingMode) {
        fetchParams.filters = this.fnGetLightingModeParam()
      }
    }
    handleTransfer.call(this, {
      ids: keys,
      data,
      showAfter: true,
      fn: () => this.__actionDone(title),
      isSelectAll,
      fetchParams,
      isSignNode,
    })
  }

  _handleAgreeBills(keys, data, isSelectAll) {
    let { fetchParams, isLightingMode } = this.state
    const title = this._actionTitle ? `同意${this._actionTitle}单据审批` : '' // @i18n-ignore
    if (!fetchParams) {
      fetchParams = { status: { state: ['APPROVING'] } }
    }
    if (isSelectAll) {
      let scene = this.fnGetScene()
      if (scene && scene.sceneIndex === 'waitInvoice') {
        scene = { scene: 'waitInvoice' }
      }
      fetchParams.scene = scene
      if (isLightingMode) {
        fetchParams.filters = this.fnGetLightingModeParam()
      }
    }
    handleAgreeBills.call(this, keys, data, () => this.__actionDone(title), isSelectAll, fetchParams)
  }

  _handleRejectBills(keys, data) {
    const title = this._actionTitle ? `驳回${this._actionTitle}单据` : '' // @i18n-ignore
    handleRejectBills.call(this, keys, data, () => this.__actionDone(title))
  }

  __billInfoPopup = backlog => {
    const title = `${i18n.get(billTypeMap()[backlog.type])}${i18n.get('详情')}`
    this._actionTitle = backlog.flowId?.form?.title
    api.open(
      '@bills:BillInfoPopup',
      {
        bus: this.bus,
        title,
        backlog,
        riskTip: {},
        isEditConfig: true,
        isShowCondition: true,
        invokeService: '@audit:get:backlog-info',
        params: {
          id: backlog.id,
          type: backlog.type,
        },
        source: 'approving',
        scene: 'APPROVER',
        needConfigButton: true,
        reload: this.bus.reload,
        mask: false,
        onOpenOwnerLoanList: line => {
          this.__handleLine(9, line)
        },
        onFooterButtonsClick: (type, line, riskData) => {
          if (![3, 5, 6].includes(type)) {
            api.close()
          }
          setTimeout(() => {
            this.__handleLine(type, line, riskData, 'single')
          }, 0)
        },
      },
      true,
    )
  }

  /**
   * 处理行操作，包含防重复点击机制
   * @param {number} type - 操作类型
   * @param {Object} line - 行数据
   * @param {Object} riskData - 风险数据
   * @param {string} actionType - 操作类型
   */
  __handleLine = (type, line, riskData, actionType) => {
    // 防重复点击：检查是否正在处理相同的flowId
    const flowId = line.flowId.id
    if (this.isProcessing) {
      showMessage.warning(i18n.get('操作正在进行中，请稍后重试'))
      return
    }

    let { fetchParams } = this.state
    if (!fetchParams) {
      fetchParams = { status: { state: ['APPROVING'] } }
    }
    let actionName = ACTIONS[type]
    let title = this._actionTitle && actionName ? `${actionName}${this._actionTitle}单据` : '' // @i18n-ignore
    handleActionImplementation.call(this, {
      type,
      backlog: line,
      fn: () => {
        if ([3, 5, 6].includes(type)) {
          // 标记正在处理
          if (getBoolVariation('cyxq-77175')) {
            this.isProcessing = true
          }
          api
            .invokeService('@bills:get:current:backLog', flowId)
            .then(res => {
              if (!res?.id?.length) {
                // 审批中修改单据时，如果用户主动关闭有可能待办还没有重新生成
                api.close()
                return
              }
              this.bus.invoke('bills:update:flow', {
                id: res.id,
              })
            })
            .catch(error => {
              console.error('获取当前待办失败:', error)
              this.isProcessing = false
              // 可以添加错误提示
            })
            .finally(() => {
              // 清除处理标记
              this.isProcessing = false
            })
        }
        const actionEvent = [3, 1, 11, 17].includes(type)
        this.__actionDone(title, actionEvent && this.state.continuousApproval, actionEvent)
      },
      riskData,
      actionType,
      fetchParams,
    })
  }

  handleReceivePrint = (key, fn) => {
    handleReceivePrint.call(this, key, fn)
  }

  handleHangUp = (key, fn) => {
    handleHangUp.call(this, key, fn)
  }

  handleTableRowClick = backlog => {
    startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()
    if (supportBillDetailsSwitchingInDrawer()) {
      api.open('@bills:BillInfoDrawerV2', {
        flows: this._currentBills?.map(item => item.flowId) || [],
        currentId: backlog.flowId.id,
        bus: this.bus,
        backlogs: this._currentBills,
        billDetailsProps: {
          source: 'approving',
          isEditConfig: true,
        },
        scene: 'APPROVER',
        reload: this.bus.reload,
        onOpenOwnerLoanList: line => {
          this.__handleLine(9, line)
        },
        onFooterButtonsClick: (type, line, riskData) => {
          if (![3, 5, 6].includes(type)) {
            api.close()
          }
          setTimeout(() => {
            this.__handleLine(type, line, riskData, 'single')
          }, 0)
        },
      })
      return
    }

    api
      .invokeService('@audit:get:backlog-info', {
        id: backlog.id,
        type: backlog.type,
      })
      .then(backlog => {
        this.__billInfoPopup(backlog)
      })
  }
  handleSwitchChange = check => {
    this.setState({ continuousApproval: check })
  }
  handleListModeChange = _ => {
    const { hideLightningApproveEnter, INTELLIGENT_APPROVAL } = this.props
    if (isFunction(hideLightningApproveEnter)) hideLightningApproveEnter(true)
    localStorage.setItem(this.feeTypeModeStorageKey, !this.state.feeTypeMode)
    this.setState({ feeTypeMode: !this.state.feeTypeMode })
    api?.logger?.info('切换待审批展示维度', {
      [this.feeTypeModeStorageKey]: !this.state.feeTypeMode,
      currentPage: !this.state.feeTypeMode ? '明细维度' : '单据维度',
    })
  }
  renderMenuBar = () => {
    return (
      <MenuBar
        setContinuousApproval={this.handleSwitchChange}
        setListMode={this.handleListModeChange}
        INTELLIGENT_APPROVAL={this.props.INTELLIGENT_APPROVAL}
      />
    )
  }

  handleActions = (type, line) => {
    this.__handleLine(type, line, undefined, 'single')
  }

  getOptionButtons = () => {
    const btns = [
      { text: i18n.get('同意'), name: 'agree', type: 'primary' },
      { text: i18n.get('驳回'), name: 'reject', minorGroup: false, dangerous: true },
      { text: i18n.get('导出选中'), name: 'export_selected' },
      { text: i18n.get('打印单据'), name: 'print' },
      { text: i18n.get('打印单据和发票'), name: 'printInvoice' },
      { text: i18n.get('打印提醒'), name: 'print_remind' },
      { text: i18n.get('转交审批'), name: 'approve_transfer' },
      { text: i18n.get('加签审批'), name: 'approve_sign_transfer' },
      { text: i18n.get('收到打印'), name: 'recive_print' },
      { text: i18n.get('暂挂审批'), name: 'hangUp' },
    ]
    const { showPrintBtn, userInfo } = this.props
    let showButtons = showPrintBtn ? btns : btns.filter(v => v.name !== 'printInvoice')
    if (userInfo?.staff?.external) {
      showButtons = showButtons.filter(v => !externalStaffActionBlackList.includes(v.name))
    }

    return showButtons
  }

  buttons = this.getOptionButtons()

  selectAllBtnStyles = { color: 'var(--brand-base)' }

  render() {
    const { baseDataProperties, invoiceReviewPower, KA_GLOBAL_SEARCH_2 } = this.props
    const { scenes, activeSceneIndex, isVisibleSaveDiff, level, detailReadable, feeTypeMode } = this.state
    if (!scenes.length) {
      return null
    }
    if (feeTypeMode) {
      return <ApproveFeeTypeWrapper {...this.props} setListMode={this.handleListModeChange} />
    }
    if (!activeSceneIndex) return <></>
    this.fnfilterMCButs()
    return (
      <LoaderWithLegacyData
        newSearch={true}
        searchOptions={KA_GLOBAL_SEARCH_2 ? globalSearchOptions() : searchOptions()}
        enableGlobalSearch={KA_GLOBAL_SEARCH_2}
        lightingMode={this.state.isLightingMode}
        scenes={scenes}
        activeSceneIndex={activeSceneIndex}
        fetch={this.fetchPending}
        scenesType={scenesType}
        status={'APPROVING'}
        menuBar={this.renderMenuBar}
        buttons={this.buttons}
        onButtonClick={this.handleButtonsClickPre}
        onSelectedAll={this.handleSelectAllBtnClick}
        prefixColumns={prefixColumns}
        bus={this.bus}
        resource={scenesFilter}
        baseDataProperties={baseDataProperties}
        createAction={createActionColumn4Approve}
        mapping={fnFilterMapping(mapping, invoiceReviewPower)}
        saveDiffText={i18n.get('保存变更')}
        isVisibleSaveDiff={isVisibleSaveDiff}
        saveSceneWithGroupIndex={true}
        refreshState={this.refreshState}
        createNodeNameColumn={() => createNodeNameColumn({ dataIndex: 'nodeName', filterType: 'text', sorter: true })}
        createRiskWarningColumn={() => createRiskWarningColumn(true)}
        createHangUpColumn={createHangUpColumn}
        createCreditColumn={level?.length ? () => createCreditColumn(level, detailReadable) : undefined}
        getInstance={instance => (this.datagridInstance = instance)}
        preRenderCellHandler={this.preRenderCellHandler}
        useNewFieldSet
      />
    )
  }
}
