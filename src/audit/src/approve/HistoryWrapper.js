/**************************************************
 * Created by nanyuantingfeng on 20/09/2017 12:19.
 **************************************************/
 import React from 'react'
 import { EnhanceConnect } from '@ekuaibao/store'
 import OperationTable from '../record-table/operation-table'
 import moment from 'moment'
 import { app as api } from '@ekuaibao/whispered'
 import * as actions from '../audit-action'
 import { map } from 'lodash'
 import key from '../key'
 import { BaseHistoryComponent } from '../BaseHistoryComponent'
 import './HistoryWrapper.less'
 import { connect } from '@ekuaibao/mobx-store'
 import MessageCenter from '@ekuaibao/messagecenter'
 import { Fetch } from '@ekuaibao/fetch'
 import { QuerySelect } from 'ekbc-query-builder'
 import { getLinkNodeElement, triggerClick } from '@ekuaibao/sdk-bridge/sdk/utils'
 import { showMessage } from '@ekuaibao/show-util'
 const prefixColumns = { state: 'flowId', '*': 'flowId.form' }
 const dataGridSelection = { allowSelectAll: true, mode: 'multiple' }
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')
 
 // 默认的导出config
 const defaultConfig = {
   exportAll: false, // 是否导出全部
   includeDetails: false,
   exportRefCode: false,
   includeTrips: false,
   exportTime: false,
   detailsType: 'part',
 }

 @connect(store => ({ size: store.states['@layout'].size }))
 @EnhanceConnect(state => ({
   paymentRecords: state[key.ID].payment_record_list,
   dataRecords: state[key.ID].operation_records,
   dataBills: state[key.ID].operation_bills,
 }))
 export default class OperationView extends BaseHistoryComponent {
   pageMode = 'pagination'
   bus = new MessageCenter()
   constructor() {
     super()
     const sdate = moment().subtract(1, 'months').format('YYYY-MM-DD') + ' 00:00:00'
     const edate = moment().format('YYYY-MM-DD') + ' 23:59:59'
     this.state = {
       queryType: 'operation', //record:历史记录 operation:操作纪录
       pagination: { current: 1, pageSize: 10 },
       sdate: sdate,
       edate: edate,
       billType: 'all',
       payState: 'all',
       searchText: '',
       inSearch: false,
       sorter: { order: 'descend', field: 'finishTime' },
       queryParams: {},
     }
     this.filterMap = {
       date: `finishTime>${moment(sdate).valueOf()}&&finishTime<${moment(edate).valueOf()}`,
       state: `state.in("SUCCESS","FAILURE")`,
     }
     this.initSorter = { order: 'descend', field: 'finishTime' }
     this.initPage = { current: 1, pageSize: 10 }
     this.initDate = {
       sdate,
       edate,
     }
   }

   buttons = [{ text: i18n.get('导出选中'), name: 'export_selected' }]

   componentDidMount() {
     if (this.state.queryType !== 'payment') {
       this.handleTableChange(this.state.pagination, this.state.queryType)
     } else {
       this.handlePaymentTableChange(this.state.pagination, this.state.queryType)
     }
     this.bus.on('table:row:click', this.handleRowClick)
   }

   componentWillUnmount() {
     this.bus.un('table:row:click', this.handleRowClick)
   }

   componentWillReceiveProps(nextProps) {
     let total
     if (this.state.queryType === 'operation') {
       total = nextProps.dataRecords.count
     } else if (this.state.queryType === 'record') {
       total = nextProps.dataBills.count
     } else {
       total = nextProps.paymentRecords.count
     }
     const pager = this.state.pagination
     pager.total = total
     this.setState({ pagination: pager })
   }

   formatFilterParams = filter => {
     if (!filter) return
     const params = {}
     const { updateTime, ...others } = filter
     if (updateTime) {
       params.sdate = updateTime[0].valueOf()
       params.edate = updateTime[1].valueOf()
     }
     let str = ''
     const keys = Object.keys(others)
     keys.forEach((key, index) => {
      if (others[key]) {
        let s = ''
        if (key.endsWith('.id')) {
          const valueStr = others[key].map(v => `"${v}"`).join(',')
          s = `${key}.in(${valueStr})`
        }else {
          s = `${key}.contains("${others[key]}")`
        }
        str += index === keys.length - 1 ? s : `${s}&&`
      }
     })
     return { ...params, filterStr: str }
   }

   formatSorterParams = sorter => {
     if (!sorter || !sorter.field || sorter.field === 'undefined' || sorter.field === 'status')
       return
     return { field: sorter.field, order: sorter.order === 'ascend' ? 'ASC' : 'DESC' }
   }

   handleTableChange = (
     pagination,
     queryType,
     filterData,
     billType,
     searchText,
     bySearch = false,
   ) => {
     const filter = filterData ?? {}
     billType = billType || this.state.billType
     this._setPager(pagination)
     const tableFilter = this.formatFilterParams(pagination.filter)
     const mfilter = tableFilter || filter
     const { sdate, edate, filterStr } = mfilter
     let filterParam = { filter: filterStr }
     if (queryType === 'operation') {
       filterParam.startApproveTime =
         (sdate && moment(sdate).valueOf()) || moment(this.state.sdate).valueOf()
       filterParam.endApproveTime =
         (edate && moment(edate).valueOf()) || moment(this.state.edate).valueOf()
     } else {
       filterParam.startSubmitTime =
         (sdate && moment(sdate).valueOf()) || moment(this.state.sdate).valueOf()
       filterParam.endSubmitTime =
         (edate && moment(edate).valueOf()) || moment(this.state.edate).valueOf()
     }

     if (filter.sdate && filter.edate) {
       this.setState({ sdate: filter.sdate, edate: filter.edate })
     }

     this.setState({ billType })

     if (billType !== 'all') {
       filterParam.formTypes = billType
     } else {
       filterParam.formTypes = ''
     }
     if (!!searchText) {
       filterParam.searchText = searchText
     }
     const sorter = this.formatSorterParams(pagination.sorter)

     const params = {
       start: (pagination.current - 1) * pagination.pageSize,
       count: pagination.pageSize,
       ...filterParam,
       sorter: sorter,
     }
     this.setState({ queryParams: params })
     this.getTableSource(queryType, params, pagination.pageMode, bySearch)
   }

   handlePaymentTableChange(paginationData, filter, sorter, isSearch) {
     const pagination = paginationData ?? this.initPage
     this._setPager(pagination)

     if (filter.date) {
       let { sdate, edate } = filter.date
       this.setState({ sdate, edate })
     }

     if (filter.state) {
       this.setState({ payState: filter.state })
     }

     if (isSearch) {
       this.setState({
         searchText: filter.custom,
       })
     }

     let order = this._setSorter(sorter)
     this._setFilterMap(filter)
     let filterMap = this.filterMap
     let filterStr = map(filterMap, (v, key) => v).join('&&')
     let params = {
       start: (pagination.current - 1) * pagination.pageSize,
       count: pagination.pageSize,
       filter: filterStr,
       order,
     }
     this.setState({ queryParams: params })
     this.getTableSource('payment', params, pagination.pageMode)
   }

   getTableSource(queryType, params, pageMode, bySearch) {
     this.pageMode = pageMode ?? this.pageMode
     switch (queryType) {
       case 'operation':
         api.dispatch(actions.getBackLogListByAction(params, bySearch, pageMode === 'scroll'))
         return
       case 'payment':
         api.dispatch(actions.getPaymentRecord(params, void 0, pageMode === 'scroll'))
         return
     }
   }

   handleRowClick = async flow => {
    startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()
     const action = await actions.getFlowInfo(flow.flowId.id)
     api.dispatch(action).then(flow => {
       api.open(
         '@bills:BillInfoPopup',
         {
           title: i18n.get('审批记录'),
           backlog: { id: -1, flowId: flow },
           onFooterButtonsClick: this.handlePrint,
           invokeService: '@audit:get:history-flow:info',
           params: flow.id,
           scene: 'APPROVER',
           mask: false
         },
         true,
       )
     })
   }

   /** 按钮点击 */
   handleButtonsClick = async ({ name, data }) => {
     const config = { ...defaultConfig }
     data = data ? data : this.bus.getSelectedRowData() // 选中数据

     this._actionTitle =
       Object.values(data)
         .map(item => item.flowId?.form?.title)
         .join(',') || ''

     switch (name) {
       case 'export_selected':
         this.__handleInsertAssist(this._actionTitle ? `导出${this._actionTitle}单据` : '') // @i18n-ignore
         return this.exportSelectedRecords({ data, config })
       case 'export_all': {
         const params = {
           data: [],
           config: { ...config, exportAll: false },
           filterStr: this.getFilterStr(this.state.queryParams),
         }
         this.__handleInsertAssist(this._actionTitle ? `导出所有审批记录` : '') // @i18n-ignore
         return this.asyncExport(params)
       }
     }
   }

   /** 选择全部 */
   handleSelectAllBtnClick = () => {
     const buttons = [
       {
         name: i18n.get('导出'),
         type: 'normal',
         key: 'export_all',
       },
     ]
     api
       .open('@layout:DataGridSelectAllModal', {
         totalCount: ` ${this.state.pagination.total} `,
         buttons,
         isBillsTotalMoney: true,
       })
       .then(name => {
         this.handleButtonsClick({ name })
       })
   }

   __handleInsertAssist = title => {
     !!title &&
       api.invokeService('@common:insert:assist:record', {
         title,
       })
   }

   exportSelectedRecords = (param) => {
     let { data, config } = param
     if (data.keys && !data.length) {
       data = data.keys
     }
     const params = {
       ids: this.filterRowIds(data),
       format: 'xlsx',
     }
     if (config) {
       params.config = this.getConfigString(config, false)
     }
     const el = getLinkNodeElement()
     const url = `${Fetch.fixOrigin(location.origin)}/api/flow/v2/export/batchApproverecord?corpId=${
       Fetch.ekbCorpId
     }&accessToken=${Fetch.accessToken}`
     fetch(url, {
       method: 'POST',
       body: JSON.stringify(params),
       headers: new Headers({
         Accept: 'application/octet-stream',
         'Content-Type': 'application/json',
       }),
     })
       .then(res =>
         res.blob().then(blob => {
           const url = window.URL.createObjectURL(blob)
           const contentDisposition = res.headers.get('Content-Disposition')
           const filename = decodeURI(
             contentDisposition.substring(
               contentDisposition.indexOf('filename=') + 10,
               contentDisposition.length - 1,
             ),
           )
           const name = decodeURIComponent(filename)
           el.setAttribute('href', url)
           el.setAttribute('download', name)
           triggerClick(el)
         }),
       )
       .catch(err => showMessage.error(i18n.get(err.msg || err.errorMessage)))
   }

   asyncExport = async params => {
     let { config, filterStr } = params
     if (config) {
       config = this.getConfigString(config, false)
     }
     const val = await api.open('@layout:AsyncExportModal')
     const { taskName } = val
     const path = '/api/flow/v2/export/approverecord/$xlsx/async'
     Fetch.GET(path, {
       filter: decodeURIComponent(filterStr),
       taskName,
       config,
     })
   }

   filterRowIds = data => {
     return Object.keys(data).map(key => {
       const v = data[key]
       return typeof v === 'string' ? v : v.id
     })
   }

   getConfigString = (config, needEncode = true) => {
     const {
       includeDetails,
       includeTrips,
       fields = [],
       exportRefCode,
       exportAll,
       exportTime,
       detailsType,
     } = config
     let str = JSON.stringify({
       includeDetails,
       includeTrips,
       fields,
       exportRefCode,
       exportAll,
       exportTime,
       detailsType,
     })
     if (needEncode) {
       str = encodeURIComponent(str)
     }
     return str
   }

   getFilterStr = params => {
     const query = new QuerySelect()
       .filterBy(`type!="permit"`)
       .filterBy(`state.in("PROCESSED")`)
       .filterBy(`updateTime > ${params.startApproveTime}`)
       .filterBy(`updateTime < ${params.endApproveTime}`)
       .filterBy(
         `flowId.form.title.contains("${params.searchText || ''}")` +
           `||flowId.form.code.contains("${params.searchText || ''}")` +
           `||flowId.form.submitterId.name.contains("${params.searchText || ''}")`,
       )
       .filterBy(params.filter)
     if (params.formTypes) {
       query.filterBy(`(type == "${params.formTypes}")`)
     }
     return encodeURIComponent(query.value().filterBy)
   }

   render() {
     return (
       <div className="approve-history-wrapper">
         <OperationTable
           {...this.props}
           bus={this.bus}
           sdate={this.state.sdate}
           edate={this.state.edate}
           buttons={this.buttons}
           prefixColumns={prefixColumns}
           pagination={this.state.pagination}
           billType={this.state.billType}
           queryType={this.state.queryType}
           dataGridSelection={dataGridSelection}
           onTableChange={this.handleTableChange}
           onButtonClick={this.handleButtonsClick}
           onSelectedAll={this.handleSelectAllBtnClick}
         />
       </div>
     )
   }
 }
