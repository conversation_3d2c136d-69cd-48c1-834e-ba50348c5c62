import React, { PureComponent } from 'react'
import { app as api } from '@ekuaibao/whispered'

import { cloneDeep } from 'lodash'

export class BaseAuditApprovedComponent extends PureComponent {
  componentWillUnmount() {
    this.bus.un('buttons:click', this.handleButtonsClick)
    this.bus.un('table:row:click', this.handleTableRowClick)
    this.bus.un('table:row:action', this.handleActions)
    this.bus.un('selectAllBtn:click', this.handleSelectAllBtnClick)
    this.bus.un('initScenes:action', this.initScenes)
  }

  fnGetScene = () => {
    const { scenes, scene } = this.state
    let cloneScenes = cloneDeep(scenes)
    let findScenes = cloneScenes.find(s => s.sceneIndex === scene)
    if (findScenes) {
      findScenes.scene = findScenes.scene !== 'waitInvoice' ? findScenes.scene : ''
    }
    if (findScenes && findScenes.scene) {
      return scene
    }
    return findScenes
  }
  _handlePrintList = (keys, data, fn, printInvoice) => {
    api.invokeService('@audit:handle:multipeprint', keys, data, fn, printInvoice)
  }
}
