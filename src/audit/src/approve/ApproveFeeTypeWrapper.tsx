/**************************************
 * Created By LinK On 2023/7/24 15:21.
 **************************************/
import React, { PureComponent } from 'react'
import { mapping } from '../util/mapping4ApproveDetails'
import {app, app as api} from '@ekuaibao/whispered'
import { fetchApproveDetails } from '../util/fetchUtil'
import { cloneDeep, get } from 'lodash'
import { Resource } from '@ekuaibao/fetch'
import MessageCenter from '@ekuaibao/messagecenter'
import { Button,  message } from '@hose/eui'
import approveDetailColumns from '../util/approveDetailColumns'
import { createActionColumn4ApproveDetail } from '../util/columnsAndSwitcherUtil'
import qs from 'qs'
import { addTag, batchAddTag } from '../audit-action'
const { searchOptionFeeName, searchOptionSubmitter } = api.require<any>('@lib/data-grid-v2/CustomSearchUtil')

interface Scene {
  text: string
  scene: string
  sceneIndex: string
  active: boolean
  defaultColumns: any[]
  groupIndexMap: any
}

const LoaderWithLegacyData = api.require('@elements/data-grid-v2/LoaderWithLegacyData')
const scenesType = 'APPROVE_DETAIL'
const prefixColumns = { state: '$', '*': 'form' }
const approve = new Resource('/api/flow/v2/filter')
const { canModifyApproveMoney, parseFormValueAsParam, parseFlowRisk, parseFlowRiskV2 } =
  api.require('@bills/util/parse')

const urlParams = qs.parse(location.search.slice(1))
const { isEmail } = urlParams || {}

export default class ApproveFeeTypeWrapper extends PureComponent<any, any> {
  private bus = new MessageCenter()
  private _actionTitle = ''
  private buttons = [{ text: i18n.get('批量添加审批意见'), name: 'batchTag', type: 'primary' }]
  state = {
    scenes: [],
    activeSceneIndex: '',
    level: [],
    detailReadable: false,
    continuousApproval: false,
    notAllowBatchApproveIds: {},
    allowBatchApproveIds: {},
    feeTypeMode: false,
  }

  componentDidMount() {
    this.bus.on('buttons:click', this.handleButtonsClick)
    this.bus.on('table:row:click', this.handleTableRowClick)
    this.bus.on('table:row:action', this.handleActions)
    this.bus.on('initScenes:action', this.initScenes)
    // 获取场景列表
    approve.GET('/$type', { type: scenesType }).then(async res => {
      const { value } = res
      const { specifications = [] } = this.props
      if (specifications.length === 0) {
        await api.invokeService(
          '@custom-specification:get:specificationGroups:withSpecificationVersioned',
        )
      }
      this.initScenes(value)
    })
  }

  fnSetActionTitle = (data: any) => {
    const backlogIdMap: any = {}
    Object.values(data).forEach((el: any) => {
      const backlogId = el?.nodeState?.backlogId
      const title = el?.form?.title
      if (backlogId && !backlogIdMap[backlogId]) {
        backlogIdMap[backlogId] = title
      }
    })
    this._actionTitle = Object.values(backlogIdMap).join(',') || ''
  }

  handleButtonsClick = async ({ name, data  }) => {
    data = data ? data : this.bus.getSelectedRowData()
    this.fnSetActionTitle(data)
    switch (name) {
      case 'batchTag': // 批量添加标签
        const result = await api.open('@audit:AddTagModal', { data: Object.values(data) })
        try {
          await batchAddTag(result.newTag)
          message.success(i18n.get('添加成功'))
          api.invokeService('@layout5:refresh:menu:data')
          this.reloadAndClearSelecteds()
          this.handleAddTagLog(_, result.newTag)
        } catch (e) {
          this.handleAddTagLog(e, result.newTag)
        }
        break
      default:
        break
    }
  }

  fnGetScene = () => {
    const { scenes, scene } = this.state
    const cloneScenes = cloneDeep(scenes)
    const findScenes = cloneScenes.find(s => s.sceneIndex === scene)
    if (findScenes && findScenes.scene) {
      return scene
    }
    return findScenes
  }

  loadFlowRiskInfo = async (dataSource: any) => {
    const { baseDataProperties } = this.props
    const form = get(dataSource, 'form', {})
    const details = get(dataSource, 'form.details', [])
    const currentSpecification =
      get(dataSource, 'currentSpecification') || get(dataSource, 'form.specificationId')
    const components = currentSpecification.components || []
    const detailCmp = components.find(el => el.field === 'details')
    if (
      detailCmp?.realtimeCalculateBudget &&
      !!details.length &&
      ['approving', 'paying'].includes(dataSource.state)
    ) {
      this.tempId = new Date().getTime()
      const currentValue =
        parseFormValueAsParam(form, currentSpecification, dataSource, baseDataProperties) || {}
      const riskData = await api.invokeService('@bills:get:flow:risk:info', {
        formType: dataSource.formType,
        form: currentValue.form,
        state: dataSource.state,
        version: this.tempId,
      })
      if (this.tempId.toString() === riskData.flowId) {
        return parseFlowRisk(riskData).form?.details
      }
    }
    return Promise.resolve()
  }
  handleTableRowClick = async (record: any) => {
    // 打开费用明细详情，收集参数；
    // 与BillInfoReadonly.js中的fnDetailsLineClick方法相同
    const flowId = record?.sourceId
    const detailId = record?.code
    const billState = get(record, 'form.stage')
    const backlogId = get(record, 'nodeState.backlogId')
    const dataSource = await api.invokeService('@audit:get:backlog-info', {
      id: backlogId,
      hiddenMsg: true,
    })
    const form = get(dataSource, 'flowId.form', {})
    const details = form.details || []
    const detail = details.find((el: any) => el?.feeTypeForm?.detailId === detailId)
    if (!detail) return
    const { flowRulePerformLogs, ownerId } = dataSource
    const { specificationId: billSpecification, submitterId } = form

    const modifyApproveMoney = canModifyApproveMoney(billSpecification)

    const canEditNote = billState !== 'draft' && billState !== 'rejected'
    const riskInfo = await this.loadFlowRiskInfo(dataSource?.flowId)
    const singleRiskData = await api.invokeService('@bills:get:flow:risk:warning', { id: flowId })
    singleRiskData.isForbid = false
    let external
    if (singleRiskData?.value) {
      const riskWarning = parseFlowRiskV2(singleRiskData).form
      external = riskWarning?.details
    }
    const detailParam = {
      dataSource: detail,
      title: i18n.get('查看消费详情'),
      billSpecification,
      details,
      flowId,
      billState,
      flowRulePerformLogs,
      isEdit: false,
      submitterId,
      suppleInvoiceBtn: true, // 补充发票按钮
      source: 'approving', // 进入费用明细详情有确认发票按钮
      external,
      riskInfo,
      isInHistory: false, // 是否在历史版本中查看界面修改
      isForbid: false,
      ownerId,
      canEditNote,
      modifyApproveMoney, // 是否可以编辑核发金额
      backLogOwnerId: ownerId?.id || ownerId, // BillDetail入口的backLogOwnerId有时是undefined
      isFlowEditable: false, // 当前节点是否允许审批中修改
      riskData: { ...singleRiskData },
      formAllData: form,
      fullDataSource: dataSource,
      allowAddTag: true, //允许明细详情添加标签
      showPreAndNext: false, //是否显示上一页下一页
      updateDetailsCallBack: () => {
        const { reload } = this.bus as any
        reload && reload()
      }, // 更新列表数据
    }
    api?.logger?.info('待审批明细维度查看消费详情', { record, detailParam })
    api.open('@bills:FeeDetailViewPopup', detailParam)
  }

  handleAddTagLog = (result: any, payload: any) => {
    api?.logger?.info('待审批明细维度添加标签', { result, payload })
  }

  handleActions = (type: string, line: any) => {
    if (type === 'addTag') {
      //添加审批意见
      api.open('@audit:AddTagModal', { data: [line] }).then((res: any) => {
        //调用添加接口
        batchAddTag(res.newTag)
          .then(_ => {
            message.success(i18n.get('添加成功'))
            api.invokeService('@layout5:refresh:menu:data')
            this.reloadAndClearSelecteds()
            this.handleAddTagLog(_, res.newTag)
          })
          .catch(err => {
            this.handleAddTagLog(err, res.newTag)
          })
      })
      return
    }
  }

  reloadAndClearSelecteds = () => {
    const { clearSelectedRowKeys, reload } = this.bus as any
    clearSelectedRowKeys && clearSelectedRowKeys()
    setTimeout(() => reload && reload(), 1000)
  }

  fetchDetails = async (params = {}, dimensionItems = {}) => {
    const { scenes } = this.state
    const { scene } = params
    const findScene = scenes.find((s: any) => s.sceneIndex === scene)
    scene && this.setState({ scene, fetchParams: params, dimensionItems })
    const config = await app.dataLoader('@tpp-v2-action.tagGlobalConfig').load()
    return fetchApproveDetails(params, findScene, dimensionItems, true, config)
  }

  initScenes = (data: Scene[]) => {
    const allScenes = { text: i18n.get('全部'), scene: 'all', active: true, sceneIndex: 'all' }
    const filter = data ? data?.filter?.map((d: string) => JSON.parse(d)) : []
    const scenes = !!~filter.findIndex((el: Scene) => el.scene === 'all')
      ? filter
      : [allScenes, ...filter]
    this.setState({ scenes, activeSceneIndex: scenes[0].sceneIndex })
  }

  renderMenuBar = () => {
    return (
      <Button category="secondary" className="ml-8" onClick={this.props.setListMode}>
        {i18n.get('单据维度')}
      </Button>
    )
  }

  getInstance = (instance: any) => {
    this.instance = instance
  }

  render() {
    const { scenes, activeSceneIndex } = this.state
    if (!activeSceneIndex) return <></>
    const { baseDataProperties } = this.props
    return (
      <LoaderWithLegacyData
        newSearch={true}
        searchOptions={[searchOptionFeeName(), searchOptionSubmitter('form')]}
        otherColumns={approveDetailColumns}
        lightingMode={false}
        scenes={scenes}
        activeSceneIndex={activeSceneIndex}
        fetch={this.fetchDetails}
        scenesType={scenesType}
        status={'APPROVING'}
        menuBar={this.renderMenuBar}
        buttons={this.buttons}
        onButtonClick={this.handleButtonsClick}
        prefixColumns={prefixColumns}
        bus={this.bus}
        resource={approve}
        baseDataProperties={baseDataProperties}
        createAction={createActionColumn4ApproveDetail}
        mapping={mapping}
        saveDiffText={i18n.get('保存变更')}
        saveSceneWithGroupIndex={true}
        searchPlaceholder={i18n.get('搜索费用明细或提交人')}
        getInstance={this.getInstance}
        inFeeTypeMode={true}
        useNewFieldSet
      />
    )
  }
}
