import React from 'react'
import { Icon, Tooltip, Popover } from 'antd'
import { app, app as api } from '@ekuaibao/whispered'
const EkbHighLighter = app.require('@elements/EkbHighLighter')
const { exportBillsByPaymentBatchId } = app.require('@lib/export-excel-service')
import { exportPaymentBatchesExcel } from './util/fetchUtil'
import moment from 'moment'
const EKBIcon = app.require('@elements/ekbIcon')
import { PayStatus, PaymentBatchActionMap, PaymentActionMap } from './util/Utils'
const Money = app.require('@elements/puppet/Money')
const { channelMap, filterChannels } = app.require('@components/utils/fnFilterPaymentChannel')
import { cloneDeep, get } from 'lodash'
import { Resource } from '@ekuaibao/fetch'
const accounts = new Resource('/api/pay/v2/accounts')
const getStaffShowByConfig = app.require('@elements/staffs/staffShowFn')
const accountType = () => ({
  BANK: i18n.get('银行卡'),
  ALIPAY: i18n.get('支付宝'),
  WEIXIN: i18n.get('微信账户'),
  OVERSEABANK: i18n.get('海外账号'),
  CHECK: i18n.get('支票'),
  ACCEPTANCEBILL: i18n.get('承兑汇票'),
  OTHER: i18n.get('其它'),
})

import { getDiffDays, getExpenseRiskTip, getLoanRiskTip, getLoanRisk, renderPayDetail } from './view-util-index-ref'

export { getDiffDays, getExpenseRiskTip, getLoanRiskTip, getLoanRisk, renderPayDetail }

let actionMap = () => ({
  agree: i18n.get('同意'),
  autoAgree: i18n.get('同意(自动同意)'),
  'select-approver': i18n.get('修改审批人'),
  reject: i18n.get('驳回'),
  pay: i18n.get('支付'),
  confirm: i18n.get('确认收款'),
  addnode: i18n.get('转交审批'),
  skip: i18n.get('跳过寄送'),
  send: i18n.get('添加寄送'),
  receive: i18n.get('确认收单'),
  nullify: i18n.get('作废'),
})

const addnodeMap = () => ({
  PRE_ADD_NODE: i18n.get('加签审批'),
  AFT_ADD_NODE: i18n.get('加签审批'),
  SHIFT_NODE: i18n.get('转交审批'),
})

const payPlanState = () => {
  return {
    READY: i18n.get('待支付'),
    FAILURE: i18n.get('支付失败'),
    SUCCESS: i18n.get('支付成功'),
    PROCESSING: i18n.get('支付中'),
    REEXCHANGE: i18n.get('已退汇'),
    REVIEWING: i18n.get('复核中'),
    CANCEL: i18n.get('取消支付'),
  }
}

const payPlanReviewState = () => {
  return {
    FINISH: i18n.get('完成'),
    REVIEWING: i18n.get('复核中'),
    AGREE: i18n.get('同意'),
    REJECT: i18n.get('驳回'),
  }
}

const paymentReviewState = () => {
  return {
    REJECT: i18n.get('驳回'),
    AGREE: i18n.get('同意'),
  }
}

export function prepareRender(columnsData, dynamicChannelMapData, channelList) {
  const columns = columnsData ?? []
  const dynamicChannelMap = dynamicChannelMapData ?? {}
  return columns.forEach(item => {
    switch (
      item.label.replace('(支付计划)', '') //@i18n-ignore
    ) {
      case '收款账号类别': //@i18n-ignore
      case '付款账号类别': //@i18n-ignore
        const accountTypes = Object.keys(accountType()).map(item => {
          return { label: accountType()[item], value: item }
        })
        ;(item.filters = accountTypes),
          (item.filterType = 'list'),
          (item.lookup = {
            dataSource: accountTypes,
            displayExpr: 'label',
            valueExpr: 'value',
          })
        item.render = (text, item) => {
          return text ? <div>{accountType()[text]}</div> : '-'
        }
        break
      case '支付方式': //@i18n-ignore
        const dcChannelList = cloneDeep(channelList) || []
        const payTypes = filterChannels(dcChannelList).map(item => {
          return { label: item.label, value: item.value }
        })
        ;(item.filters = payTypes),
          (item.filterType = 'list'),
          (item.lookup = {
            dataSource: payTypes,
            displayExpr: 'label',
            valueExpr: 'value',
          })
        item.render = (text, item) => {
          return text ? <div className="td-line">{dynamicChannelMap[text].name}</div> : '-'
        }
        break
      case '收款账号性质': //@i18n-ignore
        const types = [
          {
            label: i18n.get('个人账户'),
            value: 'PERSONAL',
          },
          {
            label: i18n.get('对公账户'),
            value: 'PUBLIC',
          },
        ]
        ;(item.filters = types),
          (item.filterType = 'list'),
          (item.lookup = {
            dataSource: types,
            displayExpr: 'label',
            valueExpr: 'value',
          })
        const payeeMap = {
          PERSONAL: i18n.get('个人账户'),
          PUBLIC: i18n.get('对公账户'),
        }
        item.render = text => {
          return <div>{payeeMap[text] ? payeeMap[text] : '-'}</div>
        }
        break
      case '支付状态': //@i18n-ignore
        const states = [
          {
            label: i18n.get('待支付'),
            value: 'READY',
          },
          {
            label: i18n.get('支付失败'),
            value: 'FAILURE',
          },
          {
            label: i18n.get('支付成功'),
            value: 'SUCCESS',
          },
          {
            label: i18n.get('支付中'),
            value: 'PROCESSING',
          },
          {
            label: i18n.get('已退汇'),
            value: 'REEXCHANGE',
          },
          {
            label: i18n.get('复核中'),
            value: 'REVIEWING',
          },
          {
            label: i18n.get('取消支付'),
            value: 'CANCEL',
          },
        ]
        ;(item.filters = states),
          (item.filterType = 'list'),
          (item.lookup = {
            dataSource: states,
            displayExpr: 'label',
            valueExpr: 'value',
          })
        item.render = text => {
          return text ? <div>{payPlanState()[text]}</div> : '-'
        }
        break
      case '付款信息':
        item.render = text => {
          return <div>{text ? text?.detail?.remarkName || text?.detail?.name : '-'}</div>
        }
        break
      case '收款信息':
        item.render = text => {
          return typeof text === 'string' ? text || '-' : accountInfo(text)
        }
        break
      case '生成日期':
        item.render = text => {
          return tdDateTime(text)
        }
        break
      case '支付结果':
        const filters = [
          {
            label: i18n.get('支付成功'),
            value: 'SUCCESS',
          },
          {
            label: i18n.get('支付失败'),
            value: 'FAILURE',
          },
        ]
        const payResults = {
          SUCCESS: i18n.get('支付成功'),
          FAILURE: i18n.get('支付失败'),
        }
        ;(item.filters = filters),
          (item.filterType = 'list'),
          (item.lookup = {
            dataSource: filters,
            displayExpr: 'label',
            valueExpr: 'value',
          })
        item.render = text => {
          return text ? <div>{payResults[text]}</div> : '-'
        }
        break
      case '合并状态': //@i18n-ignore
        const _filters = [
          {
            label: i18n.get('已被合并支付'),
            value: '已被合并支付',
          },
        ]
        const results = {
          已被合并支付: i18n.get('已被合并支付'),
        }
        ;(item.filters = _filters),
          (item.filterType = 'list'),
          (item.lookup = {
            dataSource: _filters,
            displayExpr: 'label',
            valueExpr: 'value',
          })
        item.render = text => {
          return text ? <div>{results[text]}</div> : '-'
        }
        break
      case '法人实体':
        item.render = data => {
          return data ? <div className="td-line">{data?.name}</div> : '-'
        }
        break
      case '支付复核状态':
        const statesReview = [
          {
            label: i18n.get('完成'),
            value: 'FINISH',
          },
          {
            label: i18n.get('复核中'),
            value: 'REVIEWING',
          },
          {
            label: i18n.get('同意'),
            value: 'AGREE',
          },
          {
            label: i18n.get('驳回'),
            value: 'REJECT',
          },
        ]
        ;(item.filters = statesReview),
          (item.filterType = 'list'),
          (item.lookup = {
            dataSource: statesReview,
            displayExpr: 'label',
            valueExpr: 'value',
          })
        item.render = text => {
          return text ? <div>{payPlanReviewState()[text]}</div> : '-'
        }
        break
    }

    if (item?.label === '支付金额(支付计划)') {
      item.render = (text, line) => {
        return text ? (
          <div>
            <Money
              currencySize={12}
              valueSize={12}
              value={line?.form?.E_system_paymentPlan_支付金额 || line?.paymentId?.form?.E_system_paymentPlan_支付金额}
            />
          </div>
        ) : (
          '-'
        )
      }
    }
  })
}

export function prepareRenderReceipt(columnsData, dynamicChannelMap) {
  const columns = columnsData ?? []
  return columns.forEach(item => {
    switch (item.label) {
      case '回单文件': //@i18n-ignore
        const types = [
          { label: i18n.get('已获取'), value: true },
          { label: i18n.get('未获取'), value: false },
        ]
        item.filters = types
        item.filterType = 'list'
        item.lookup = {
          dataSource: types,
          displayExpr: 'label',
          valueExpr: 'value',
        }
        item.render = text => {
          return text ? <div>已获取</div> : <div>未获取</div>
        }
        break
      case '支付方式':
        const payMethodTypes = Object.keys(dynamicChannelMap).map(key => {
          return { value: key, label: dynamicChannelMap[key]?.name }
        })
        item.filters = payMethodTypes
        item.filterType = 'list'
        item.lookup = {
          dataSource: payMethodTypes,
          displayExpr: 'label',
          valueExpr: 'value',
        }
        item.render = text =>
          dynamicChannelMap && dynamicChannelMap[text] ? (
            <div className="td-line">{dynamicChannelMap[text].name}</div>
          ) : (
            '-'
          )
        break
    }
  })
}

function check4Amount(line) {
  let bool = false
  if (line.budgetmessage) {
    bool = true
    return bool
  }

  let msg = line.budgetmessage1
  let msgList = JSON.parse(msg || '[]')
  let i = -1
  while (++i < msgList.length) {
    if (msgList[i].type * 1 > 0) {
      bool = true
      break
    }
  }

  return bool
}

export function tdAccountName(text, line) {
  const _name = line?.account?.name || text
  return <div className="td-line">{_name}</div>
}

export function channelTradeNoText(text, line) {
  let searchText = this.state.searchText || this.props.searchText
  if (line.url && line.channel !== 'WALLET') {
    return (
      <a className="color-blue" href="javascript:void(0)" onClick={() => api.emit('@vendor:open:link', line.url)}>
        <Tooltip placement="topLeft" overlay={<div>{text}</div>}>
          <div className="td-text-wrap wb-bw">
            {text ? (
              <EkbHighLighter highlightClassName="highlight" searchWords={[searchText]} textToHighlight={text} />
            ) : (
              text
            )}
          </div>
        </Tooltip>
      </a>
    )
  } else {
    return (
      <Tooltip placement="topLeft" overlay={<div>{text}</div>}>
        <div className="td-text-wrap wb-bw">
          {text ? (
            <EkbHighLighter highlightClassName="highlight" searchWords={[searchText]} textToHighlight={text} />
          ) : (
            text
          )}
        </div>
      </Tooltip>
    )
  }
}

export function tdText(text) {
  let searchText = this.state.searchText || this.props.searchText
  return (
    <Tooltip placement="topLeft" overlay={<div>{text}</div>}>
      <div className="text-nowrap-ellipsis">
        {text ? (
          <EkbHighLighter highlightClassName="highlight" searchWords={[searchText]} textToHighlight={text} />
        ) : (
          text
        )}
      </div>
    </Tooltip>
  )
}

export function tdPureSubmitterText(text, line) {
  let str =
    line.flowId.ownerId.id === line.flowId.form.submitterId.id
      ? getStaffShowByConfig(line.flowId.form.submitterId)
      : i18n.get('submitted-by', {
          name: getStaffShowByConfig(line.flowId.form.submitterId),
          ownerName: getStaffShowByConfig(line.flowId.ownerId),
        })
  return (
    <Tooltip overlay={<div>{str}</div>}>
      <div className="td-text-wrap">{str}</div>
    </Tooltip>
  )
}

export function tdChannel(text) {
  const { dynamicChannelMap = {} } = this.props
  return dynamicChannelMap[text] ? <div className="td-line">{dynamicChannelMap[text].name}</div> : '-'
}

export function tdChannelType(text) {
  return channelMap()[text] ? <div className="td-line">{channelMap()[text]}</div> : null
}

export function tdSort(text) {
  return accountType()[text] ? <div className="td-line">{accountType()[text]}</div> : null
}

const purpose = {
  EXPENSE: i18n.get('员工报销'),
  TO_BUSINESS: i18n.get('对企业付款'),
  TO_PERSON: i18n.get('对个人付款'),
}

export function tdPurpose(text) {
  let label = purpose[text] || '-'
  return <div className="td-line">{label}</div>
}

export function td(text) {
  return (
    <Tooltip overlay={<div>{text}</div>}>
      <div className="td-line">{text}</div>
    </Tooltip>
  )
}

export function tdDate(text) {
  return <span>{moment(text).format('YYYY-MM-DD')}</span>
}

export function tdDateTime(text) {
  return <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>
}

export function tdActiconTime(text, backlog) {
  let log = backlog.flowId.logs[backlog.logId] //logid is logs index
  if (!log) return null
  return moment(log.time).format('YYYY-MM-DD HH:mm:ss')
}

export function tdAccountInfo(payeeInfo, line) {
  if (line?.multiplePayeesMode) return text(i18n.get('多收款人'))
  const searchText = this.state.searchText || ''
  return accountInfo(payeeInfo, searchText)
}

export function accountInfo(payeeInfo, searchText = '') {
  return payeeInfo ? (
    <Popover content={renderPayDetail.call(this, payeeInfo)}>
      <div className="account">
        <div className="text-nowrap-ellipsis">{payeeInfo?.accountName || payeeInfo?.name}</div>
        <div className="color-gray">{payeeInfo?.type === 'PERSONAL' ? i18n.get('个人账户') : i18n.get('对公账户')}</div>
      </div>
    </Popover>
  ) : (
    <span>-</span>
  )
}

export function tdAmount(text, line) {
  return (
    <div className="td-text-amount">
      <div>{check4Amount(line) && <Icon type="exclamation-circle" />}</div>
      <Money currencySize={12} valueSize={12} value={text} />
    </div>
  )
}

export function tdToolTipFailureText(text, failureReason, channelTradeNo) {
  return (
    <Tooltip
      overlay={
        <div>
          <p>
            {i18n.get('失败原因')}
            {i18n.get('：')}
            {failureReason}
          </p>
          {channelTradeNo && (
            <p>
              ({i18n.get('批次号')}
              {channelTradeNo})
            </p>
          )}
        </div>
      }
    >
      <div className="agree mr-16 error color-red">
        {text}
        <Icon className="ml-5 color-red" type="exclamation-circle-o" />
      </div>
    </Tooltip>
  )
}

// 打开汇丰银行补充信息弹窗
const openHsbcModal = async line => {
  const { account, flowIds, channel, ekbTradeNo, channelTradeNo, remark } = line
  const params = {
    start: 0,
    count: 300,
    filter: `(asPayee==false&&active==true&&id=="${account?.id}")`,
  }
  const payerAccount = await accounts.GET('', params)
  const items = get(payerAccount, 'items', [])
  const paymentAccount = items[0] || accountCompany
  return api.open('@audit:HSBC_SupplyPaymentDrawer', {
    flowIds,
    getPayRemark: () => ({ remark }),
    channel,
    paymentAccount,
    accountId: account?.id,
    ekbTradeNo,
    channelTradeNo,
    inPendingPaying: true,
  })
}

export function paymentBatchAction(type, propsValue) {
  const powers = api.getState()['@common'].powers
  const batchPrint = powers.batchPrint
  return (actionsList, line) => {
    const actions = actionsList ?? []
    let a = []
    if (type !== 'paymentReviewBatch') {
      a = [
        {
          className: 'color-blue mr-16',
          onClick: _ =>
            type === 'payPlan'
              ? exportPaymentBatchesExcel(line?.id, 'PROCESSING')
              : exportBillsByPaymentBatchId({ data: line.id }),
          label: i18n.get('导出Excel'),
        },
      ]
    }
    if (type === 'payPlan' && line.channel === 'OFFLINE') {
      a.push({
        className: 'color-blue mr-16',
        onClick: _ => api.open('@audit:TransferVouchersSelectModal', { batchId: line.id }),
        label: i18n.get('下载转账文件'),
      })
    }
    if (type === 'paymentBatch' && batchPrint) {
      a.push({
        className: 'agree mr-12',
        onClick: _ => exportBillsByPaymentBatchId({ data: line.id, type: 'print' }),
        label: i18n.get('汇总打印'),
      })
    }
    if (type === 'paymentBatch' && line.channel === 'HSBCPAY') {
      a.push({
        className: 'mr-16',
        onClick: _ => openHsbcModal(line),
        label: i18n.get('下载付款文件'),
      })
    }
    actions.forEach(item => {
      const action = PaymentBatchActionMap(line, type, propsValue)[item]
      !!action && a.push(action)
    })
    return renderActions(a.filter(item => !!item))
  }
}
export function paymentAction(type) {
  return (_, line) => {
    const actions = line?.actions ?? []
    const a = actions
      .map(item => {
        const action = PaymentActionMap(line, type)[item]
        return action
      })
      .filter(item => !!item)
    return renderActions(a)
  }
}

function renderActions(actions) {
  return (
    <div className="actions" style={{ whiteSpace: 'normal' }}>
      {actions.map((item, idx) => {
        const { disable, label, ...others } = item
        if (disable) {
          return (
            <Tooltip key={idx} title={i18n.get('系统升级，暂不支持取消支付')}>
              <span {...others}> {label}</span>
            </Tooltip>
          )
        }
        return (
          <a key={idx} {...others}>
            {label}
          </a>
        )
      })}
    </div>
  )
}

export function tdStatus(backlog) {
  let log = backlog.flowId.logs[backlog.logId] //logid is logs index
  if (!log) return null
  let action = log.action.replace('freeflow.', '').replace('.', '-')
  let text = actionMap()[action]
  if (action === 'send' && !log.attributes.expressNum) {
    text = actionMap()['skip']
  }
  if (action === 'addnode') {
    text = addnodeMap()[log.attributes.addNodeType]
  }
  return <div className={'td-status-' + action}>{text}</div>
}

export function renderPayStatus(status, tipMessage) {
  const style = { width: 16, height: 16, marginRight: 4 }
  const failed = <EKBIcon name="#EDico-plaint-circle-o" style={{ ...style, color: 'red' }} />
  const success = <EKBIcon name="#EDico-check-circle-o" style={{ ...style, color: 'green' }} />
  const showMsg = status !== 'SUCCESS' && tipMessage
  const icon = showMsg ? failed : status === 'SUCCESS' ? success : ''
  const text = PayStatus()[status]
  if (showMsg) {
    return (
      <Tooltip title={tipMessage} placement="topLeft">
        <div className="dis-f jc-c">
          {icon}
          {text}
        </div>
      </Tooltip>
    )
  }
  return (
    <div className="dis-f jc-c">
      {icon}
      {text}
    </div>
  )
}

export function feetypeInfo(feetype) {
  const { name, code, icon, color } = feetype
  return (
    <div className="dis-f ai-c">
      <div className="payment-feetype-icon" style={{ background: color }}>
        <img width={28} height={28} src={icon} alt="" />
      </div>
      <div className="mr-8">{name}</div>
      <div className="color-gray">({code})</div>
    </div>
  )
}

export function text(text) {
  return <div className="td-line">{text}</div>
}

export function paymentReview(state) {
  return state ? <div>{paymentReviewState()[state]}</div> : '-'
}

export const externalStaffActionBlackList = ['print', 'approve_transfer', 'approve_sign_transfer']
