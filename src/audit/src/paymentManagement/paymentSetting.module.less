.payment-setting {
  flex: 1;
  display: flex;
  justify-content: center;
  background: linear-gradient(0deg, rgba(37, 85, 255, 0.04), rgba(37, 85, 255, 0.04)), #ffffff;

  &-footer {
    height: 56px;
    background-color: #fff;
    border-top: 1px solid rgba(39, 46, 59, 0.12);
  }

  &-addBtn {
    position: absolute;
    height: 32px;
    right: 24px;
    top: 60px;
    font-size: 14px;

    .eui-icon {
      font-size: 16px;
    }
  }

  &-list {
    margin-top: 24px;
    width: 868px;
    transition: width 0.2s ease;
  }

  @media screen and (max-width: 1024px) {
    .payment-setting-list {
      width: 600px;
    }
  }

  &-item {
    margin-bottom: 16px;
    &:last-child {
      margin-bottom: 21px;
    }
  }

  :global {
    .empty {
      width: 100%;
      height: 100%;
      margin: auto;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;

      > img {
        width: 121px;
      }

      > span {
        margin-top: 12px;
        font-size: 14px;
        color: rgba(39, 46, 59, 0.8);
        line-height: 20px;
      }

      .ant-btn {
        padding: 5px 12px;
        margin-top: 12px;
        font-size: 14px;
        border-radius: 4px;
      }
    }
  }
}

.skeleton {
  padding: 24px;
  width: 868px;
  height: 164px;
  background-color: #fff;

  :global {
    .skeleton-item {
      margin: 0;
      margin-bottom: 20px;
      width: 88px;
      height: 32px;
      border-radius: 23px;
      background: rgba(39, 46, 59, 0.06);

      &.long {
        margin-bottom: 16px;
        width: 100%;
        height: 24px;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.commonWrapper {
  padding: 16px;
  background-color: var(--eui-bg-filler);
  flex: 1;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  :global {
    .tableWrapper {
      background-color: white;
      border-radius: 8px;
      margin-top: 8px;
      display: flex;
      overflow: hidden;
      flex-direction: column;
      flex: 1;
      .tableTop {
        padding: 16px;
        display: flex;
        flex-direction: row;
        font: var(--eui-font-head-b1);
        color: var(--eui-text-title);
        align-items: center;
        justify-content: space-between;
      }
      .tableContent {
        width: 100%;
        display: flex;
        flex-direction: column;
        padding: 0 16px 16px;
        overflow: hidden;
        .eui-pro-card-body {
          padding: 0 !important;
        }
        .bankInfo_table_item {
          display: inline-flex;
          padding: 2px 8px 2px 4px;
          align-items: center;
          gap: 4px;
          border-radius: 4px;
          background: var(--eui-transparent-n900-10, rgba(29, 33, 41, 0.1));
          img{
            width: 16px;
            height: 16px;
          }
          .bankInfo_table_item_text{
            color: var(--eui-text-title);
            font: var(--eui-font-body-r1);
          }
        }
      }
      .tabs-top-no-padding {
        .eui-tabs-nav .eui-tabs-nav-wrap {
          padding: 0;
        }
      }
      .table-row-click {
        :hover {
          cursor: pointer;
        }
      }
      .table-row-disabled {
        color: var(--eui-text-disabled) !important;
        .__person-wrapper {
          color: var(--eui-text-disabled) !important;
        }
      }
    }
  }
}
