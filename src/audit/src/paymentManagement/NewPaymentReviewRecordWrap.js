import { app, app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
const LoaderWithLegacyData = app.require('@elements/data-grid-v2/LoaderWithLegacyData')
import { fetchPayPlanReviewBackLogs, getScenes, scenesFilter } from '../util/fetchUtil'
import MessageCenter from '@ekuaibao/messagecenter'
import { mapping } from '../util/mapping4paid'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import { createActionDetailsColumn4Pay } from '../util/columnsAndSwitcherUtil'
import { EnhanceConnect } from '@ekuaibao/store'
import { customColumnPropertyMapping4PayPlan, fnFilterMapping, getInitScenes } from '../util/Utils'
import { fnInitColumns } from '../util/tableUtil'
import { prepareRender, getLoanRisk } from '../view-util'
import { getBackLogByFlowId, getReviewPayPlanFields, getFlowInfo } from '../audit-action'
const { searchOptionTitleAndCode, searchOptionSubmitter } = api.require('@lib/data-grid-v2/CustomSearchUtil')
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

const scenesType = 'NEW_PAYMENT_REVIEW_RECORD'
const prefixColumns = { state: 'flowId', '*': '' }

@EnhanceConnect(state => ({
  specifications: state['@custom-specification'].specificationGroupsList,
  invoiceReviewPower: state['@common'].powers.invoiceReview,
  dynamicChannelMap: state['@audit'].dynamicChannelMap,
  channelList: state['@audit'].channelList,
}))
export default class NewPaymentReviewRecordWrap extends PureComponent {
  bus = new MessageCenter()

  state = {
    scenes: [],
    columns: [],
    columnsList: {},
  }

  async componentDidMount() {
    this.bus.on('table:row:click', this.handleTableRowClick)
    this.bus.on('initScenes:action', this.initScenes)
    this.props?.p_bus?.on('audit-payment:tab:change:reviewRecord', this.__actionDone)
    const { dynamicChannelMap, channelList } = this.props
    const { value } = await getReviewPayPlanFields()
    const { columns, columnsList } = fnInitColumns({
      flowForm: value.flowId,
      payPlanForm: value.paymentId,
    })
    prepareRender(columns, dynamicChannelMap, channelList)
    this.setState({
      columns: [...columns],
      columnsList: { ...columnsList },
    })
    // 获取场景列表
    getScenes(scenesType).then(res => {
      this.initScenes(res?.value)
    })
  }

  componentWillUnmount() {
    this.bus.un('table:row:click', this.handleTableRowClick)
    this.bus.un('initScenes:action', this.initScenes)
    this.props?.p_bus?.un('audit-payment:tab:change:reviewRecord', this.__actionDone)
  }

  initScenes = data => {
    const { specifications } = this.props
    const {
      columnsList: { payPlanForm },
    } = this.state
    const defaultColumns = [...payPlanForm.map(v => v.key).slice(0)]
    const scenesData = getInitScenes({
      data,
      prefix: 'flowId',
      specifications,
      isHasWaitInvoice: false,
      defaultColumns,
    })
    this.setState({ scenes: scenesData.scenes })
  }

  __actionDone = () => {
    api.invokeService('@layout5:refresh:menu:data')
    this.reloadAndClearSelecteds()
  }

  reloadAndClearSelecteds = () => {
    this.bus.clearSelectedRowKeys && this.bus.clearSelectedRowKeys()
    this.bus.reload &&
      this.bus.reload().then(() => {
        setTimeout(() => {
          this.bus?.emit?.('table:select:current:row')
        }, 500)
      })
  }

  fetchPaying = async (params = {}, dimensionItems = {}) => {
    params.status = { state: ['FINISH', 'AGREE', 'REJECT'] }
    const { scenes } = this.state
    const { scene = 'all' } = params
    const findScene = scenes.find(s => s.sceneIndex === scene)
    findScene && (params.scene = findScene.scene || '')
    scene && this.setState({ scene, fetchParams: params, dimensionItems })

    const res = await fetchPayPlanReviewBackLogs(params, findScene, dimensionItems, false)
    this._currentDataSource = res.dataSource
    return res
  }

  __billInfoPopup = async backlog => {

    if (api.require('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
      api.open('@bills:BillInfoDrawerV2', {
        currentId: backlog.flowId.id,
        flows: this._currentDataSource.map(v => v.flowId),
        bus: this.bus,
        hideFooter: true,
      })
      return
    }

    const action = await getFlowInfo(backlog?.flowId?.id)
    app.dispatch(action).then(flow => {
      const title = `${i18n.get(billTypeMap()[flow.formType])}${i18n.get('详情')}`
      api.open(
        '@bills:BillInfoPopup',
        {
          title,
          backlog: { id: -1, flowId: flow },
          invokeService: '@expense-manage:get:backlog:info:byId',
          params: flow.id,
          showFooter: false,
        },
        true,
      )
    })
  }

  handleTableRowClick = row => {
    startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()
    this.__billInfoPopup(row)
  }

  render() {
    const { scenes, columns, columnsList } = this.state
    const { invoiceReviewPower } = this.props
    const customColumnPropertyMapping = customColumnPropertyMapping4PayPlan()
    if (!scenes.length) {
      return null
    }
    return (
      <LoaderWithLegacyData
        newSearch={true}
        isMultiSelect={false}
        isGroup
        searchOptions={[searchOptionTitleAndCode(), searchOptionSubmitter()]}
        activeSceneIndex="all"
        columns={columns}
        columnsList={columnsList}
        scenes={scenes}
        fetch={this.fetchPaying}
        scenesType={scenesType}
        prefixColumns={prefixColumns}
        customColumnPropertyMapping={customColumnPropertyMapping}
        bus={this.bus}
        resource={scenesFilter}
        mapping={fnFilterMapping(mapping, invoiceReviewPower)}
        useNewFieldSet
      />
    )
  }
}
