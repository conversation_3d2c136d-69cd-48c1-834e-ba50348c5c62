import { app, app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
const LoaderWithLegacyData = app.require('@elements/data-grid-v2/LoaderWithLegacyData')
import { fetchPayPlanReviewBackLogs, getScenes, scenesFilter } from '../util/fetchUtil'
import MessageCenter from '@ekuaibao/messagecenter'
import { mapping } from '../util/mapping4paid'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import { handleActionImplementation, handleRejectBills } from '../service'
import { createActionDetailsColumn4Agree } from '../util/columnsAndSwitcherUtil'
import { EnhanceConnect } from '@ekuaibao/store'
import { customColumnPropertyMapping4PayPlan, fnFilterMapping, getInitScenes } from '../util/Utils'
import Animation from '../elements/Animation/Animation'
import { fnInitColumns } from '../util/tableUtil'
import { prepareRender, getLoanRisk } from '../view-util'
import { getBackLogByFlowId, getFlowInfo, getReviewPayPlanFields, postPaymentManagementReview } from '../audit-action'
import { showMessage } from '@ekuaibao/show-util'
const { searchOptionTitleAndCode, searchOptionSubmitter } = api.require('@lib/data-grid-v2/CustomSearchUtil')
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

const scenesType = 'NEW_PAYMENT_REVIEW'
const prefixColumns = { state: 'flowId', '*': '' }

@EnhanceConnect(state => ({
  specifications: state['@custom-specification'].specificationGroupsList,
  invoiceReviewPower: state['@common'].powers.invoiceReview,
  dynamicChannelMap: state['@audit'].dynamicChannelMap,
  channelList: state['@audit'].channelList,
}))
export default class NewPaymentReviewWrap extends PureComponent {
  bus = new MessageCenter()

  state = {
    scenes: [],
    columns: [],
    columnsList: {},
  }

  async componentDidMount() {
    this.bus.on('buttons:click', this.handleButtonsClick)
    this.bus.on('table:row:click', this.handleTableRowClick)
    this.bus.on('table:row:action', this.handleActions)
    this.bus.on('initScenes:action', this.initScenes)
    this.props?.p_bus?.on('audit-payment:tab:change:paymentReview', this.__actionDone)
    const { dynamicChannelMap, channelList } = this.props
    const { value } = await getReviewPayPlanFields()
    const { columns, columnsList } = fnInitColumns({
      flowForm: value.flowId,
      payPlanForm: value.paymentId,
    })
    prepareRender(columns, dynamicChannelMap, channelList)
    this.setState({
      columns: [...columns],
      columnsList: { ...columnsList },
    })
    // 获取场景列表
    getScenes(scenesType).then(res => {
      this.initScenes(res?.value)
    })
  }

  componentWillUnmount() {
    this.bus.un('buttons:click', this.handleButtonsClick)
    this.bus.un('table:row:click', this.handleTableRowClick)
    this.bus.un('table:row:action', this.handleActions)
    this.bus.un('initScenes:action', this.initScenes)
    this.props?.p_bus?.un('audit-payment:tab:change:paymentReview', this.__actionDone)
  }

  initScenes = data => {
    const { specifications } = this.props
    const {
      columnsList: { payPlanForm },
    } = this.state
    const defaultColumns = [...payPlanForm.map(v => v.key).slice(0), 'action']
    const scenesData = getInitScenes({
      data,
      prefix: 'flowId',
      specifications,
      isHasWaitInvoice: false,
      defaultColumns,
    })
    this.setState({ scenes: scenesData.scenes })
  }

  fetchPaying = async (params = {}, dimensionItems = {}) => {
    params.status = { state: ['REVIEWING'] }
    const { scenes } = this.state
    const { scene = 'all' } = params
    const findScene = scenes.find(s => s.sceneIndex === scene)
    findScene && (params.scene = findScene.scene || '')
    scene && this.setState({ scene, fetchParams: params, dimensionItems })

    const res = await fetchPayPlanReviewBackLogs(params, findScene, dimensionItems, false)
    this._currentDataSource = res.dataSource
    return res
  }

  handleButtonsClick = ({ name, data, keys }) => {
    keys = keys ? keys : this.bus.getSelectedRowKeys()
    data = data ? data : this.bus.getSelectedRowData()
    switch (name) {
      case 'agree':
        return this.fnPostPaymentManagementReview(keys, 'AGREE')
      case 'reject':
        return this.fnPostPaymentManagementReview(keys, 'REJECT')
    }
  }

  fnPostPaymentManagementReview = async (ids, action) => {
    const res = await postPaymentManagementReview({ ids, action })
    if (res?.value?.isSuccess) {
      this.__actionDone()
      showMessage.success(i18n.get('操作成功'))
    } else {
      showMessage.error(res?.errorMsg || i18n.get('操作失败'))
    }
  }

  __actionDone = () => {
    api.invokeService('@layout5:refresh:menu:data')
    this.reloadAndClearSelecteds()
  }

  reloadAndClearSelecteds = () => {
    this.bus.clearSelectedRowKeys && this.bus.clearSelectedRowKeys()
    this.bus.reload &&
      this.bus.reload().then(() => {
        setTimeout(() => {
          this.bus?.emit?.('table:select:current:row')
        }, 500)
      })
  }

  __handleLine({ type, line, riskData, fetchParams, isByDetail, tableSource }) {
    handleActionImplementation.call(this, {
      type,
      backlog: line,
      fn: this.__actionDone,
      riskData,
      fetchParams,
      isByDetail,
      tableSource,
    })
  }

  __billInfoPopup = async backlog => {
    const action = await getFlowInfo(backlog?.flowId?.id)
    app.dispatch(action).then(flow => {
      const title = `${i18n.get(billTypeMap()[flow.formType])}${i18n.get('详情')}`
      api.open(
        '@bills:BillInfoPopup',
        {
          title,
          backlog: { id: -1, flowId: flow },
          invokeService: '@expense-manage:get:backlog:info:byId',
          params: flow.id,
          isEditConfig: this.props.state === 'APPROVING',
          isShowCondition: true,
          reload: this.bus.reload,
          needConfigButton: true,
          privilegeId: true,
          isFromLoanManage: true,
          bottomToolbar: ['freeflow.agree', 'freeflow.reject','freeflow.comment'],
          onOpenOwnerLoanList: line => {
            this.__handleLine({ type: 9, line })
          },
          onFooterButtonsClick: type => {
            this.fnPostPaymentManagementReview([backlog?.id], type === 3 ? 'AGREE' : 'REJECT')
            api.close()
          },
        },
        true,
      )
    })
  }

  handleTableRowClick = row => {
    startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()

    if (api.require('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
      api.open('@bills:BillInfoDrawerV2', {
        currentId: row.flowId.id,
        flows: this._currentDataSource.map(v => v.flowId),
        bus: this.bus,
        billDetailsProps: {
          privilegeId: true,
          isEditConfig: this.props.state === 'APPROVING',
          isFromLoanManage: true,
          onOpenOwnerLoanList: line => {
            this.__handleLine({ type: 9, line })
          },
        },
        onFooterButtonsClick: (type) => {
          this.fnPostPaymentManagementReview([row?.id], type === 3 ? 'AGREE' : 'REJECT')
          api.close()
        },
      })
      return
    }

    this.__billInfoPopup(row)
  }

  handleActions = (type, line) => {
    this.fnPostPaymentManagementReview([line?.id], type === 6 ? 'AGREE' : 'REJECT')
  }

  buttons = [
    { text: i18n.get('同意'), name: 'agree', type: 'primary' },
    { text: i18n.get('驳回'), name: 'reject', dangerous: true },
  ]

  render() {
    const { scenes, columns, columnsList } = this.state
    const { invoiceReviewPower } = this.props
    const customColumnPropertyMapping = customColumnPropertyMapping4PayPlan()
    if (!scenes.length) {
      return null
    }
    return (
      <LoaderWithLegacyData
        newSearch={true}
        searchOptions={[searchOptionTitleAndCode(), searchOptionSubmitter()]}
        isGroup
        activeSceneIndex="all"
        columns={columns}
        columnsList={columnsList}
        scenes={scenes}
        fetch={this.fetchPaying}
        scenesType={scenesType}
        buttons={this.buttons}
        onButtonClick={this.handleButtonsClick}
        prefixColumns={prefixColumns}
        customColumnPropertyMapping={customColumnPropertyMapping}
        bus={this.bus}
        resource={scenesFilter}
        mapping={fnFilterMapping(mapping, invoiceReviewPower)}
        createAction={createActionDetailsColumn4Agree}
        useNewFieldSet
      />
    )
  }
}
