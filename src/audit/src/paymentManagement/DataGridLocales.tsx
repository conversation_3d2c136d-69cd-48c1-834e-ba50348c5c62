import React from 'react'
import { Provider } from '@ekuaibao/datagrid/esm/State'
import { Language } from '@ekuaibao/datagrid/esm/i18n/Language'

export interface LocaleProviderProps {}
export interface LocaleProviderState {}
export class LocaleProvider extends React.PureComponent<LocaleProviderProps, LocaleProviderState> {
  i18nText = {
    noData: i18n.get('暂无数据'),
    prevPage: i18n.get('上一页'),
    nextPage: i18n.get('下一页'),
    manualOptionJump: i18n.get('翻页，每页加载'),
    manualSizeForOnePage: (args: any) => i18n.get('manualSizeForOnePage', args),
    manualNumbers: i18n.get('条'),
    cancel: i18n.get('取消'),
    submit: i18n.get('确定'),
  }
  render() {
    return (
      <Provider>
        <Language texts={this.i18nText} />
        {this.props.children}
      </Provider>
    )
  }
}
