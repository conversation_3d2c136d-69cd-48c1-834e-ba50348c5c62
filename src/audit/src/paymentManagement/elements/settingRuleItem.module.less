.setting-item-card {
  position: relative;
  :global {
    .ant-card-head {
      border: none;
      .ant-card-head-title {
        padding-top: 24px;
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        color: #272E3B;
      }
    }

    .ant-form-item {
      margin-bottom: 16px;

      &.staff-form-item {
        margin-bottom: 24px;
      }

      .ant-form-item-label {
        text-align: left;
      }
    }

    .ant-card-body {
      padding-top: 0;
      padding-bottom: 0;
      padding-right: 46px;
    }

    .ant-form-item-required {
      font-size: 14px;
      line-height: 20px;
      &::after {
        font-family: 'PingFang SC', sans-serif;
      }
    }

    .text-tip {
      position: absolute;
      right: -20px;
      top: -8px;
    }

    .edit-btn-wrap {
      position: absolute;
      top: 0;
      right: -32px;
      display: flex;
      flex-direction: column;
      .setting-rule-btn {
        margin-top: 10px;
        padding: 0 !important;
        width: 24px;
        height: 24px;
        border: none;
        border-radius: 4px;
        font-size: 16px;
        color: rgba(39, 46, 59, 0.8) !important;

        .icon {
          font-size: 24px;
          color: rgba(39, 46, 59, 0.8) !important;
        }

        &:first-child {
          margin: 0;
        }

        &:hover,
        &:focus {
          color: rgba(39, 46, 59, 0.8);
          background-color: rgba(39, 46, 59, 0.08) !important;
        }
      }
    }

    .account-selector {
      .ant-select-selection--multiple {
        min-height: 32px;

        .ant-select-selection__choice {
          height: 24px;
          border-color: transparent;
          background: rgba(39, 46, 59, 0.03);

          .ant-select-selection__choice__content {
            margin-right: 8px;
          }

          .ant-select-selection__choice__remove {
            right: 8px;
            font-size: 14px;
            line-height: 22px;
            color: rgba(39, 46, 59, 0.35);
          }
        }
      }

      .ant-select-selection__placeholder {
        height: 24px;
        line-height: 24px;
        font-size: 14px;
      }

      .account-options {
        > img {
          width: 16px;
          height: 16px;
        }

        .accountNo {
          display: none;
        }

        .accountNo-cut {
          display: inline;
        }

        .account-info {
          display: flex;
          align-items: center;

          > .divider {
            display: block;
          }

          > p {
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            color: #272E3B;
            > .divider {
              display: none;
            }
          }
        }

      }

      &.ant-select-disabled {
        .account-options {
          .account-info {
            > p {
              color: rgba(39, 46, 59, 0.56);
            }
          }
        }
      }
    }

    .ant-input {
      height: 32px;
      font-size: 14px;
      color: #272E3B;
      &.ant-input-disabled {
        color: rgba(39, 46, 59, 0.56);
      }
    }
  }
}

.account-select-dropdown-menu {
  :global {
    .ant-select-dropdown-menu-item {
      padding: 12px 16px 12px 55px;

      &::before {
        position: absolute;
        content: '';
        bottom: 0;
        left: 55px;
        height: 1px;
        width: calc(100% - 55px - 16px);
        background-color: rgba(39, 46, 59, 0.06);;
      }
    
      &::after {
        display: block !important;
        left: 16px;
        right: unset !important;
        width: 16px;
        height: 16px;
        line-height: 18px;
        text-align: center;
        font-weight: normal !important;
        color: transparent !important;
        border: 1px solid rgba(39, 46, 59, 0.2);
        border-radius: 2px;
      }

      &.ant-select-dropdown-menu-item-selected {
        &::after {
          border: none;
          color: #fff !important;
          background-color: var(--brand-base);
        }
        &:hover {
          &::after {
            color: #fff !important;
          }
        }
      }

      &.ant-select-dropdown-menu-item-disabled {
        opacity: .4;
        &::after {
          background: rgba(78, 89, 105, 0.04);
        }
      }
    }
  }
}

.account-options {
  display: flex;
  align-items: center;

  :global {
    img {
      width: 21px;
      height: 21px;
    }
  
    .account-info {
      margin-left: 9px;
      
      > p {
        margin: 0;
        &:first-of-type {
          font-weight: 500;
          font-size: 14px;
          line-height: 20px;
          color: #272E3B;
        }
  
        &:last-of-type {
          font-weight: 400;
          font-size: 12px;
          line-height: 18px;
          color: rgba(39, 46, 59, 0.8);
        }
      }
      
      .accountNo {
        display: inline;
      }
  
      .accountNo-cut {
        display: none;
      }
  
      > .divider {
        display: none;
      }
    }
  
    .divider {
      display: inline-block;
      margin: 0 8px;
      width: 1px;
      height: 10px;
      background: rgba(39, 46, 59, 0.12);
    }
  }
}

.payment-setting-del-confirm {
  :global {
    .ant-popover-inner-content {
      padding: 16px;
      width: 280px;
      height: 128px;
      border: 1px solid rgba(39, 46, 59, 0.06);

      .ant-popover-message {
        padding-top: 0;

        .ant-popover-message-title {
          padding-left: 28px;
        }
      }

      .payment-setting-delpop-title {
        font-size: 14px;
        line-height: 20px;
        .title {
          margin: 0;
          font-weight: 500;
          color: #272E3B;
        }
  
        .content {
          margin: 0;
          margin-top: 8px;
          font-weight: 400;
          color: rgba(39, 46, 59, 0.8);
        }
      }

      .ant-popover-buttons {
        margin-top: 10px;
      }

      .anticon-exclamation-circle {
        font-size: 14px;
        line-height: 1.5;
        color: #FAAD14;
      }
    }

    .ant-popover-buttons {
      margin-bottom: 0;
    }

    .ant-btn {
      border-radius: 4px;
      height: 26px;
      &:last-child {
        border-color: #F4664A !important;
        background-color: #F4664A !important;
      }
    }
  }
}
