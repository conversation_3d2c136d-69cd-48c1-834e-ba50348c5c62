import styles from './settingRuleItem.module.less'
import React, { FC, useState } from 'react'
import { Button, Form, Card, Input, Select, Popconfirm } from 'antd'
import { Tooltip } from '@hose/eui'
import { app as api } from '@ekuaibao/whispered'
import { FormComponentProps } from 'antd/es/form'
import { showMessage } from '@ekuaibao/show-util'
import { RuleType, RuleItem } from '../PaymentSetting'
import { isEqual } from 'lodash'
import { PayeeInfoIF } from '@ekuaibao/ekuaibao_types'

const prefixCls = 'payment-setting'
const layout = { labelCol: { span: 6 }, wrapperCol: { span: 18 } }
const EKBIcon: any = api.require('@elements/ekbIcon')

const formatCardNo = (cardNo: string) => {
  if (/[^0-9]/.test(cardNo)) {
    return cardNo
  }
  return cardNo.replace(/(\d{4})(?=\d)/g, '$1   ')
}

interface ISettingRuleItem {
  index: number
  ruleData: RuleItem
  ruleList: RuleItem[]
  accountList: PayeeInfoIF[]
  editRuleIndex: number | undefined
  disabledAccountIds?: string[]
  onSave?: (ruleData: RuleType) => void
  onEdit?: (index: number) => void
  onCancel?: () => void
  onDelete?: (index: number, configId?: string) => void
}

const SettingRuleCard: FC<ISettingRuleItem & FormComponentProps> = props => {
  const {
    form,
    index,
    ruleData,
    accountList,
    editRuleIndex,
    disabledAccountIds,
    onSave,
    onEdit,
    onCancel,
    onDelete,
  } = props

  const [editStatus, setEditStatus] = useState<boolean>(false)

  const handleSelectStaff = () => {
    api
      .open('@layout:SelectStaffsModal', {
        checkedList: [
          {
            type: 'department-member',
            checkedKeys: [],
          },
        ],
      })
      .then((res: any) => {
        const selectedStaff = res?.checkedList?.[0]?.checkedData?.[0]
        form.setFieldsValue({ staffName: selectedStaff?.name, staffId: selectedStaff?.id })
      })
  }

  const handleDelete = () => {
    onDelete?.(index, ruleData?.id)
  }

  const handleSave = () => {
    form.validateFields((error, values) => {
      if (error) {
        showMessage.error(i18n.get('保存失败，规则未填写完'))
        return
      }

      if (
        ruleData?.staffId?.id === values.staffId &&
        isEqual(ruleData?.payerIds, values.payerIds)
      ) {
        handleCancel()
        return
      }

      setEditStatus(!editStatus)
      onSave?.({
        configId: ruleData?.id,
        staffId: values.staffId,
        payerIds: values.payerIds,
      })
    })
  }

  const handleEdit = () => {
    setEditStatus(true)
    onEdit?.(index)
  }

  const handleCancel = () => {
    setEditStatus(false)
    if (!ruleData?.id) {
      onDelete?.(index)
      return
    }

    form.resetFields()
    onCancel?.()
  }

  const renderRightBtn = () => {
    const renderDelBtn = () => {
      return ruleData?.id ? (
        <Popconfirm
          title={
            <div className="payment-setting-delpop-title">
              <p className="title">{i18n.get('确定删除') + i18n.get('？')}</p>
              <p className="content">{i18n.get('删除后，此条规则不可恢复')}</p>
            </div>
          }
          okText={i18n.get('确认删除')}
          placement="bottom"
          onConfirm={handleDelete}
          overlayClassName={styles[`${prefixCls}-del-confirm`]}>
          <Tooltip placement="right" title={i18n.get('删除')}>
            <Button type="ghost" size="small" className="setting-rule-btn">
              <EKBIcon name="#EDico-zf-delete" />
            </Button>
          </Tooltip>
        </Popconfirm>
      ) : (
        <Tooltip placement="right" title={i18n.get('删除')}>
          <Button type="ghost" size="small" onClick={handleDelete} className="setting-rule-btn" data-testid="pay-settingRuleItem-delete-btn">
            <EKBIcon name="#EDico-zf-delete" />
          </Button>
        </Tooltip>
      )
    }

    return (
      <div className="edit-btn-wrap">
        {ruleData?.id && !editStatus ? (
          <Tooltip placement="right" title={i18n.get('编辑')}>
            <Button type="ghost" size="small" onClick={handleEdit} className="setting-rule-btn" data-testid="pay-settingRuleItem-edit-btn">
              <EKBIcon name="#EDico-zf-edit" />
            </Button>
          </Tooltip>
        ) : (
          <>
            <Tooltip placement="right" title={i18n.get('保存')}>
              <Button type="ghost" size="small" onClick={handleSave} className="setting-rule-btn" data-testid="pay-settingRuleItem-save-btn">
              <EKBIcon name="#EDico-zf-save" />
            </Button>
            </Tooltip>
            <Tooltip placement="right" title={i18n.get('取消')}>
              <Button type="ghost" size="small" onClick={handleCancel} className="setting-rule-btn" data-testid="pay-settingRuleItem-cancel-btn">
              <EKBIcon name="#EDico-zf-off" />
            </Button>
            </Tooltip>
          </>
        )}
        {editRuleIndex === index ? void 0 : renderDelBtn()}
      </div>
    )
  }

  return (
    <Form className={styles[`${prefixCls}-ruleForm`]}>
      <Card
        title={i18n.get('规则') + (index + 1)}
        bordered={false}
        className={styles['setting-item-card']}>
        <Form.Item label={i18n.get('当付款账户为')} required {...layout}>
          {form.getFieldDecorator(`payerIds`, {
            rules: [{ required: true, message: i18n.get('未选择付款账户') }],
            initialValue: ruleData?.payerIds,
          })(
            <Select
              className="account-selector"
              mode="multiple"
              disabled={ruleData?.id && !editStatus}
              placeholder={i18n.get('请选择账户（可多选）')}
              optionFilterProp="label"
              dropdownClassName={styles['account-select-dropdown-menu']}>
              {accountList?.map(account => (
                <Select.Option
                  key={account?.id}
                  value={account?.id}
                  label={account?.name || account?.accountName}
                  disabled={
                    disabledAccountIds?.includes(account?.id) &&
                    !ruleData?.payerIds?.includes(account?.id)
                  }>
                  <div className={`${styles['account-options']} account-options`}>
                    <img src={account?.icon} alt="" />
                    <div className="account-info">
                      <p>{account?.name || account?.accountName}</p>
                      <span className="divider"></span>
                      <p>
                        {account?.bank}
                        {account?.accountNo && <span className="divider"></span>}
                        <span className="accountNo">{formatCardNo(account?.accountNo)}</span>
                        {account?.accountNo?.length ? (
                          <span className="accountNo-cut">
                            (
                            {account?.accountNo?.length > 4
                              ? account?.accountNo?.substring(account?.accountNo?.length - 4)
                              : account?.accountNo}
                            )
                          </span>
                        ) : (
                          void 0
                        )}
                      </p>
                    </div>
                  </div>
                </Select.Option>
              ))}
            </Select>,
          )}
          <span className="text-tip">{i18n.get('时')}</span>
        </Form.Item>
        <Form.Item
          className="staff-form-item"
          label={i18n.get('指定复核人员为')}
          required
          {...layout}>
          {form.getFieldDecorator(`staffName`, {
            rules: [{ required: true, message: i18n.get('未选择复核人员') }],
            initialValue: ruleData?.staffId?.name,
          })(
            <Input
              readOnly
              onClick={handleSelectStaff}
              disabled={ruleData?.id && !editStatus}
              placeholder={i18n.get('请选择人员')}
              data-testid="pay-settingRuleItem-staff-input"
            />,
          )}
        </Form.Item>
        <Form.Item
          style={{ display: 'none' }}
          label={i18n.get('指定复核人员为')}
          required
          {...layout}>
          {form.getFieldDecorator(`staffId`, {
            rules: [{ required: true, message: i18n.get('未选择复核人员') }],
            initialValue: ruleData?.staffId?.id,
          })(<Input />)}
        </Form.Item>

        {(editRuleIndex === undefined || editRuleIndex === index) && renderRightBtn()}
      </Card>
    </Form>
  )
}

const SettingRuleFormCard = Form.create()(SettingRuleCard)
export default SettingRuleFormCard
