import React, { <PERSON> } from 'react'
import { Breadcrumb } from '@hose/eui'
const BreadcrumbItem = Breadcrumb.Item
import { uuid } from '@ekuaibao/helpers'
export interface PathItem {
  name: string
  onClick?: () => void
}
interface IProps {
  path: PathItem[]
}
const CustomBreadcrumb: FC<IProps> = ({ path }) => {
  return (
    <Breadcrumb>
      {path.map((item, index) => {
        const { name, onClick } = item
        return (
          <BreadcrumbItem key={uuid(8)}>
            {onClick ? (
              <a onClick={onClick} data-testid={`pay-customBreadcrumb-link-${index}`}>
                {i18n.get(name)}
              </a>
            ) : (
              i18n.get(name)
            )}
          </BreadcrumbItem>
        )
      })}
    </Breadcrumb>
  )
}

export default CustomBreadcrumb
