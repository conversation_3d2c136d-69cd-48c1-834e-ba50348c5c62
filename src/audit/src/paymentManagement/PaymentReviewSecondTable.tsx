import React from 'react'
import * as DataGrid from '@ekuaibao/datagrid'
import { Column } from '@ekuaibao/datagrid/esm/types/column'
import styles from '../payment/table.module.less'
import { PaymentReviewColumnIcon } from '../util/tableUtil'
import PaymentReviewItemsTable from './PaymentReviewItemsTable'

export interface PaymentReviewSecondTableProps {
  columns: Column[]
  data: any
  payPlanDataSource?: any[]
}

export interface PaymentReviewSecondTableState {
  dataSource: Array<any>
  payPlanColumns: any[]
  detailDataMap: any
}
export class PaymentReviewSecondTable extends React.PureComponent<
  PaymentReviewSecondTableProps,
  PaymentReviewSecondTableState
> {
  private instance: any
  constructor(props: PaymentReviewSecondTableProps) {
    super(props)
    this.state = {
      dataSource: this.props.payPlanDataSource || [],
      payPlanColumns: this.props.columns || [],
      detailDataMap: {},
    }
  }
  componentDidMount() {
    if (this.instance) {
      this.instance.on('initialized', () => {
        const scrollable = this.instance.getScrollable()
        if (scrollable) {
          scrollable.option('rowRenderingMode', 'standard')
        }
      })
    }
  }

  componentWillReceiveProps(nextProps: any) {
    if (this.props.payPlanDataSource !== nextProps.payPlanDataSource) {
      this.setState({ dataSource: nextProps?.payPlanDataSource || [] })
    }
  }

  fnChangePayPlanDataSource = (line: any, res: any) => {
    const map = this.state.detailDataMap
    map[line?.id] = res
    this.setState({ detailDataMap: { ...map } })
  }

  getInstance = (instance: any) => {
    this.instance = instance
    if (instance) {
      const iconCol = {
        title: '',
        dataIndex: 'id',
        allowReordering: false,
        allowGrouping: false,
        allowResizing: false,
        allowEditing: false,
        allowFiltering: false,
        sorter: false,
        width: 40,
        minWidth: 40,
        render: (_: any, record: any) => {
          return (
            <PaymentReviewColumnIcon
              from="PaymentReviewPayPlan"
              line={record}
              instance={instance}
              fnChangePayPlanDataSource={this.fnChangePayPlanDataSource}
            />
          )
        },
      }
      // @ts-ignore
      this.setState({ payPlanColumns: [iconCol, ...this.state.payPlanColumns] })
    }
  }

  renderDetailTemplate = (props: any) => {
    const { detailDataMap } = this.state
    const detailData = detailDataMap[props?.data?.id] || []
    return <PaymentReviewItemsTable {...props} detailData={detailData} />
  }

  render() {
    const { dataSource, payPlanColumns } = this.state
    return (
      <div className={styles.subContainer}>
        <div className={styles.body}>
          <DataGrid.TableWrapper
            scrolling={{ mode: 'standard' }}
            standard
            getInstance={this.getInstance}
            rowKey="id"
            className={styles.tableWrapper}
            dataSource={dataSource}
            columns={payPlanColumns}
            allowColumnReordering
            allowColumnResizing
            isMultiSelect={false}
            isSingleSelect={false}
            RenderDetailTemplate={this.renderDetailTemplate}
            enabledDetailTemplate={false}
            pageIndex={1}
            pageSize={dataSource.length}
          />
        </div>
      </div>
    )
  }
}
