import React from 'react'
import { app } from '@ekuaibao/whispered'
import styles from './PaymentReviewRecordWrap.module.less'
// @ts-ignore
import classNames from 'classnames'
import { EnhanceConnect } from '@ekuaibao/store'
import * as viewUtil from '../view-util'
import PaymentReviewRecordHeader from './PaymentReviewRecordHeader'
import moment from 'moment'
import { PaginationConfig } from '@ekuaibao/datagrid/esm/types/pagination'
import { getYearFirstDay } from '../util/Utils'

const withLoader: any = app.require('@elements/data-grid-v2/withLoader')
const PaymentReviewRecordTable = withLoader(() => import('./PaymentReviewRecordTable'))

export interface PaymentReviewRecordWrapProps {
  isNeedHeight?: boolean
  getInstance?: (instance: any) => void
  bus?: any
}

export interface PaymentReviewRecordWrapState {
  dataSource: any[]
  filterBy: string
  sdate: any
  edate: any
  reviewState: string
  searchFilter: string
  size: number
  total?: number
  current: number
  controlDate:boolean
}

function tableColumns(this: any) {
  return [
    {
      title: i18n.get('复核日期'),
      dataIndex: 'auditTime',
      width: '120px',
      render: viewUtil.tdDateTime.bind(this),
    },
    {
      title: i18n.get('批次号'),
      dataIndex: 'batchChannelTradeNo',
      width: '120px',
    },
    {
      title: i18n.get('编号'),
      dataIndex: 'payment.channelTradeNo',
      width: '120px',
    },
    {
      title: i18n.get('付款账号'),
      dataIndex: 'payerAccountId',
      width: '120px',
      render: viewUtil.tdAccountInfo.bind(this),
    },
    {
      title: i18n.get('收款账号'),
      dataIndex: 'payeeAccountId',
      width: '120px',
      render: viewUtil.tdAccountInfo.bind(this),
    },
    {
      title: i18n.get('支付方式'),
      dataIndex: 'payment.paymentChannel',
      width: '120px',
      render: viewUtil.tdChannel.bind(this),
    },
    {
      title: i18n.get('支付金额'),
      dataIndex: 'payment.balance',
      width: '120px',
      render: viewUtil.tdAmount.bind(this),
    },
    {
      title: i18n.get('发起支付人员'),
      dataIndex: 'cashierId.name',
      width: '120px',
    },
    {
      title: i18n.get('复核状态'),
      dataIndex: 'state',
      width: '120px',
      render: viewUtil.paymentReview.bind(this),
    },
  ]
}

@EnhanceConnect((state: any) => ({
  dynamicChannelMap: state['@audit'].dynamicChannelMap,
}))
export class PaymentReviewRecordWrap extends React.PureComponent<
  PaymentReviewRecordWrapProps,
  PaymentReviewRecordWrapState
> {
  instance: any
  tableColumns: any
  constructor(props: PaymentReviewRecordWrapProps) {
    super(props)
    this.tableColumns = tableColumns.call(this).map((c: any) => {
      c.sorter = false
      return c
    })

    const sdate = getYearFirstDay()
    const edate = moment().format('YYYY-MM-DD') + ' 23:59:59'
    const filterBy = this.fnGetFilterBy(sdate, edate, 'all')
    this.state = {
      dataSource: [],
      filterBy,
      sdate: sdate,
      edate: edate,
      reviewState: 'all',
      searchFilter: '',
      size: 20,
      total: 0,
      current: 1,
      controlDate: false,
    }
  }

  componentDidMount() {
    app.invokeService('@audit:get:ppayment:plans:control').then((res:any) => {
      this.setState({ controlDate: res.value })
    })
    const { bus } = this.props
    bus.on('audit-payment:tab:change:reviewRecord', this.tabChange)
    const { filterBy } = this.state
    this.fnGetData({ filter: filterBy })
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('audit-payment:tab:change:reviewRecord', this.tabChange)
  }

  tabChange = () => {
    const filterBy = this.fnGetFilterBy()
    this.fnGetData({ filter: filterBy })
  }

  fnGetFilterBy = (sdate?: any, edate?: any, reviewState?: string) => {
    const _sdate = sdate ?? this.state.sdate
    const _edate = edate ?? this.state.edate
    const _reviewState = reviewState ?? this.state.reviewState
    const s = moment(_sdate).valueOf()
    const e = moment(_edate).valueOf()
    return _reviewState !== 'all'
      ? `(auditTime > ${s})&&(auditTime < ${e})&&(state).in(\"${_reviewState}\")`
      : `(auditTime > ${s})&&(auditTime < ${e})`
  }

  fnGetData = async (data: { current?: number; filter?: string; size?: number }) => {
    const { searchFilter } = this.state
    let { current, filter, size } = data

    filter ?? (filter = this.fnGetFilterBy())
    size ?? (size = this.state.size)
    if (!current) {
      current = 1
      this.setState({ current: 1 })
    }

    const fetchData: { limit?: { start?: number; count?: number }; filterBy?: string } = {}
    const count = size
    const start = (current - 1) * size
    const filterBy = searchFilter ? `${filter}&&${searchFilter}` : filter

    fetchData.limit = { start, count }
    fetchData.filterBy = filterBy

    const res = await app.invokeService('@audit:get:payment:management:reviewRecord', fetchData)
    this.setState({ dataSource: res?.items || [], total: res?.count ?? 0 })
  }

  getInstance = (instance: any) => {
    this.instance = instance
  }

  handleChange = (e: any) => {
    if (!e.target.value) {
      this.handleSearch(e.target.value)
    }
  }

  handleSearch = (value: any) => {
    const searchFilter = value ? `(batchChannelTradeNo).contains(\"${value}\")` : ''
    this.setState({ searchFilter }, () => {
      const filterBy = this.fnGetFilterBy()
      this.fnGetData({ filter: filterBy })
    })
  }

  handleSetStateData = (data: any) => {
    this.setState({ ...data })
    const sdate = data?.sdate ?? this.state.sdate
    const edate = data?.edate ?? this.state.edate
    const reviewState = data?.reviewState ?? this.state.reviewState
    const filterBy = this.fnGetFilterBy(sdate, edate, reviewState)
    this.fnGetData({ filter: filterBy })
  }

  handlePaginationChange = (pagination: PaginationConfig) => {
    this.setState({ current: pagination.current, size: pagination.size }, () => {
      this.fnGetData({ current: pagination.current, size: pagination.size })
    })
  }

  render() {
    const { isNeedHeight = true } = this.props
    const { dataSource = [], sdate, edate, reviewState, total, size, current } = this.state
    return (
      <div className={styles['peyment_review_record_wrap']}>
        <div className="header">
          <PaymentReviewRecordHeader
            sdate={sdate}
            shouldDisabledDate={this.state.controlDate}
            edate={edate}
            reviewState={reviewState}
            onSearch={this.handleSearch}
            onSearchInputChange={this.handleChange}
            onSetStateData={this.handleSetStateData}
          />
        </div>
        <div
          className={classNames(
            styles.container,
            isNeedHeight ? styles.containerHeight : styles.payPlanContainerHeight,
          )}>
          <PaymentReviewRecordTable
            {...this.props}
            rowKey="id"
            isNeedHeight={false}
            dataSource={dataSource}
            columns={this.tableColumns}
            getInstance={this.getInstance}
            paginationProps={{
              totalLength: total,
              pagination: { current, size },
            }}
            onPaginationChange={this.handlePaginationChange}
          />
        </div>
      </div>
    )
  }
}

export default PaymentReviewRecordWrap
