import React from 'react'
import { app as api } from '@ekuaibao/whispered'
import MessageCenter from '@ekuaibao/messagecenter'
import PaymentReviewWrap from './PaymentReviewWrap'
import NewPaymentReviewWrap from './NewPaymentReviewWrap'
import PaymentReviewRecordWrap from './PaymentReviewRecordWrap'
import NewPaymentReviewRecordWrap from './NewPaymentReviewRecordWrap'
const { getBoolVariation } = api.require<{ getBoolVariation: (key: string, defaultValue?: boolean) => boolean }>(
  '@lib/featbit',
)

const ETabs: any = api.require('@elements/ETabs')

export class PaymentReview extends React.PureComponent {
  bus = new MessageCenter()

  handleTabChange = (type: string) => {
    this.bus.emit(`audit-payment:tab:change:${type}`)
  }
  render() {
    const isActive = getBoolVariation('NewPaymentReview')
    const dataSource: any = [
      {
        tab: i18n.get('待我复核'),
        children: isActive ? (
          <NewPaymentReviewWrap {...this.props} p_bus={this.bus} />
        ) : (
          <PaymentReviewWrap {...this.props} bus={this.bus} from="paymentReview" />
        ),
        key: 'paymentReview',
      },

      {
        tab: i18n.get('复核记录'),
        children: isActive ? (
          <NewPaymentReviewRecordWrap {...this.props} p_bus={this.bus} />
        ) : (
          <PaymentReviewRecordWrap {...this.props} bus={this.bus} />
        ),
        key: 'reviewRecord',
      },
    ]

    return (
      <ETabs
        className="ekb-tab-line-left"
        isHoseEUI={true}
        defaultActiveKey="paymentReview"
        tabBarStyle={{ paddingTop: 6, height: 40, width: '100%' }}
        onChange={this.handleTabChange}
        dataSource={dataSource}
      />
    )
  }
}

export default PaymentReview
