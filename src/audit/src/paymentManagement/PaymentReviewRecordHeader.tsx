import React, { Component } from 'react'
import { DatePicker } from 'antd'
import moment from 'moment'
import { Input, Select, Space } from '@hose/eui'
import { getEnableRange } from '../util/Utils'
const RangePicker = DatePicker.RangePicker as any
const Option = Select.Option
const { Search } = Input
interface Props {
  onSearch: (value: any) => void
  onSearchInputChange: (value: any) => void
  onSetStateData: (value: any) => void
  sdate: any
  shouldDisabledDate: boolean
  edate: any
  reviewState: string
}

interface State {
  startDay: any
  endDay: any
}

export default class PaymentReviewRecordHeader extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      startDay: getEnableRange().startDay,
      endDay: getEnableRange().endDay,
    }
  }
  handleDateChange = (_: any, dateString: string) => {
    if (dateString[0] && dateString[1]) {
      let sdate = dateString[0] + ' 00:00:00'
      let edate = dateString[1] + ' 23:59:59'
      this.props.onSetStateData?.({ sdate, edate })
    }
  }

  handlePaymentSelectChange = (reviewState: string) => {
    this.props.onSetStateData?.({ reviewState })
  }
  disabledDate = (current: any) => {
    const { shouldDisabledDate } = this.props
    const { startDay, endDay } = this.state
    if (shouldDisabledDate) {
      return current.valueOf() > endDay || current.valueOf() < startDay
    } else {
      return false
    }
  }
  onCalendarChange = (dates: any[]) => {
    if (dates?.length === 1) {
      const { startDay, endDay } = getEnableRange(dates[0])
      this.setState({
        startDay,
        endDay,
      })
    }
  }
  onOpenChange = (status: boolean) => {
    if (status) {
      const { startDay, endDay } = getEnableRange()
      this.setState({
        startDay,
        endDay,
      })
    }
  }
  render() {
    const { onSearch, sdate, edate, reviewState, shouldDisabledDate } = this.props
    return (
      <>
        <Space>
          <span>{i18n.get('复核日期')}</span>
          <RangePicker
            size="large"
            onOpenChange={this.onOpenChange}
            onCalendarChange={this.onCalendarChange}
            renderExtraFooter={
              shouldDisabledDate ? () => i18n.get('筛选范围最多可选择2年，如需更多请联系企业管理员') : null
            }
            disabledDate={this.disabledDate}
            style={{ width: 220 }}
            allowClear={false}
            value={[moment(sdate), moment(edate)]}
            onChange={this.handleDateChange}
            data-testid="pay-paymentReviewRecordHeader-datePicker"
          />
          <span>{i18n.get('复核状态')}</span>
          <Select value={reviewState} style={{ width: 200 }} onChange={this.handlePaymentSelectChange} data-testid="pay-paymentReviewRecordHeader-select">
            <Option value="all">{i18n.get('全部')}</Option>
            <Option value="AGREE">{i18n.get('同意')}</Option>
            <Option value="REJECT">{i18n.get('驳回')}</Option>
          </Select>
        </Space>
        <Search style={{ width: 250 }} placeholder={i18n.get('搜索批次号')} onSearch={onSearch} data-testid="pay-paymentReviewRecordHeader-search" />
      </>
    )
  }
}
