import styles from './paymentSetting.module.less'
import React, { FC, useState, useEffect } from 'react'
import { Button, Space, Input, Modal, Popconfirm, message } from '@hose/eui'
import { app } from '@ekuaibao/whispered'
import {
  queryAllPaymentManagementSetting,
  postInsertPaymentManagementSetting,
  postUpdatePaymentManagementSetting,
  deletePaymentManagementSetting,
  exportAllPaymentManagemenSettings,
  exportPaymentManagemenSettingByIds,
} from '../audit-action'
import { OutlinedTipsAdd } from '@hose/eui-icons'
import { PayeeInfoIF } from '@ekuaibao/ekuaibao_types'
import CustomBreadcrumb, { PathItem } from './elements/CustomBreadcrumb'
const { Search } = Input
import { ProTable } from '@hose/pro-table'
import { getBankInfo } from '../layers/AddReviewRuleModal'
import Person from './elements/Person'
//@ts-ignore
import { QuerySelect } from 'ekbc-query-builder'
import ConfigProviderWrapper from '../elements/ConfigProviderWrapper'

export type RuleType = {
  staffId: string
  payerIds: string[]
  configId?: string
}

export type RuleItem = Partial<{
  id: string
  staffId: PayeeInfoIF
  payerIds: string[]
  timestamp: number
  createTime: number
  updateTime: number
  operatorId: string
  operatorName: string
}>

interface SearchInfo {
  current: number
  pageSize: number
  searchText: string
}
const PaymentSetting: FC = () => {
  const [loading, setLoading] = useState<boolean>(false)
  const [ruleList, setRuleList] = useState<RuleItem[]>([])
  const [searchInfo, setSearchInfo] = useState<SearchInfo>({ current: 1, pageSize: 10, searchText: '' })
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [total, setTotal] = useState<number>(0)
  const path: PathItem[] = [
    { name: i18n.get('资金管理') },
    { name: i18n.get(`企业支付配置`) },
    { name: i18n.get('支付复核配置') },
  ]

  useEffect(() => {
    queryRuleList()
  }, [searchInfo])

  const queryRuleList = () => {
    setLoading(true)
    const query = new QuerySelect()
    query
      .select(`staffId(id,name,avatar),payerId(...),...`)
      .limit(searchInfo.pageSize * (searchInfo.current - 1), searchInfo.pageSize)
    if (searchInfo.searchText) {
      query.filterBy(
        `lower(payerId.detail.name).contains(lower("${searchInfo.searchText}")) || lower(payerId.detail.bank).contains(lower("${searchInfo.searchText}")) || payerId.detail.number.contains("${searchInfo.searchText}") || lower(staffId.name).contains(lower("${searchInfo.searchText}"))`,
      )
    }
    queryAllPaymentManagementSetting(query.value()).then(res => {
      setLoading(false)
      setRuleList(res?.value?.items || [])
      setTotal(res?.value?.total ?? 0)
    })
  }

  const handleOnSearch = (value: string) => {
    setSearchInfo({ ...searchInfo, searchText: value, current: 1 })
  }

  const handleAdd = () => {
    app.open('@audit:AddReviewRuleModal', { title: i18n.get('添加复核规则') }).then(res => {
      postInsertPaymentManagementSetting(res)
        .then(() => {
          message.success(i18n.get('添加成功'))
          setSearchInfo({ ...searchInfo, current: 1 })
        })
        .catch(res => {
          message.error(i18n.get(res.errorMessage || i18n.get('添加失败')))
        })
    })
  }
  const handleEdit = (record: any) => {
    app.open('@audit:AddReviewRuleModal', { record, title: i18n.get('编辑复核规则') }).then((res: any) => {
      postUpdatePaymentManagementSetting({ ...res, id: record.id })
        .then(() => {
          message.success(i18n.get('编辑成功'))
          queryRuleList()
        })
        .catch(res => {
          message.error(i18n.get(res.errorMessage || i18n.get('添加失败')))
        })
    })
  }

  const columns = [
    {
      title: i18n.get('开户名称'),
      dataIndex: 'name',
      key: 'name',
      render: (_name: any, record: any) => {
        return record.payerId?.detail?.name || '-'
      },
    },
    {
      title: i18n.get('付款银行'),
      dataIndex: 'payerId',
      key: 'payerId',
      render: (payerInfo: any) => {
        const detail = payerInfo?.detail
        if (!detail) return '-'
        return (
          <div className="bankInfo_table_item">
            <img src={detail.icon} />
            <span className="bankInfo_table_item_text">
              {`${getBankInfo({ bank: detail.bank, accountNo: detail.number })}${
                !payerInfo.active ? '（已停用）' : ''
              }`}
            </span>
          </div>
        )
      },
    },
    {
      title: i18n.get('复核人员'),
      dataIndex: 'staffId',
      key: 'staffId',
      render: (stafInfo: any) => {
        return <Person src={stafInfo.avatar} label={stafInfo.name} />
      },
    },
    {
      title: i18n.get('操作'),
      dataIndex: 'action',
      key: 'action',
      render: (_: any, record: any) => {
        return (
          <Space>
            <Button category="text" size="small" onClick={() => handleEdit(record)} theme="highlight" data-testid="pay-paymentSetting-edit-btn">
                  {i18n.get('编辑')}
                </Button>
            <Popconfirm
              title={i18n.get('确定删除吗')}
              onConfirm={() => {
                deletePaymentManagementSetting([record.id])
                  .then(() => {
                    message.success(i18n.get('删除成功'))
                    setSearchInfo({ ...searchInfo, current: 1 })
                  })
                  .catch(res => {
                    message.error(i18n.get(res.errorMessage || i18n.get('删除失败')))
                  })
              }}
              okText={i18n.get('删除')}
              okButtonProps={{ theme: 'danger' }}
              content={i18n.get('删除后，本条规则不可恢复')}
              cancelText={i18n.get('取消')}
            >
              <Button category="text" size="small" theme="highlight" data-testid="pay-paymentSetting-delete-btn">
                  {i18n.get('删除')}
                </Button>
            </Popconfirm>
          </Space>
        )
      },
    },
  ]
  const handlePageChange = (page: number, pageSize: number) => {
    setSearchInfo({ current: page, pageSize: pageSize, searchText: searchInfo.searchText })
  }
  const handleImport = () => {
    app
      .open('@bills:ImportDetailByExcel', {
        type: 'paymentSetting',
      })
      .then(() => {
        setSearchInfo({ ...searchInfo, current: 1 })
      })
  }
  const handleExportAll = () => {
    const query = new QuerySelect()
    if (searchInfo.searchText) {
      query.filterBy(
        `lower(payerId.detail.name).contains(lower("${searchInfo.searchText}")) || lower(payerId.detail.bank).contains(lower("${searchInfo.searchText}")) || payerId.detail.number.contains("${searchInfo.searchText}") || lower(staffId.name).contains(lower("${searchInfo.searchText}"))`,
      )
    }
    exportAllPaymentManagemenSettings(query.value())
  }

  const handleExportBySelected = () => {
    exportPaymentManagemenSettingByIds(selectedRowKeys)
  }
  const handleDeleteBySelected = () => {
    Modal.confirm({
      title: i18n.get('确认删除选中的 {__k0} 条规则吗？', { __k0: selectedRowKeys.length }),
      content: i18n.get('删除后，所选规则不可恢复'),
      okText: i18n.get('删除'),
      okButtonProps: { theme: 'danger' },
      cancelText: i18n.get('取消'),
      onOk: () => {
        deletePaymentManagementSetting(selectedRowKeys)
          .then(() => {
            message.success(i18n.get('批量删除成功'))
            setSelectedRowKeys([])
            setSearchInfo({ ...searchInfo, current: 1 })
          })
          .catch(res => {
            message.error(i18n.get(res.errorMessage || i18n.get('批量删除失败')))
          })
      },
    })
  }
  return (
    <div className={styles['commonWrapper']}>
      <CustomBreadcrumb path={path} />
      <div className="tableWrapper">
        <div className="tableTop">
          <span style={{ flexShrink: 0 }}>{i18n.get('支付复核配置')}</span>
          <Space style={{ flexShrink: 0 }}>
            <Space>
              <Search
                style={{ width: 300 }}
                onSearch={handleOnSearch}
                placeholder={i18n.get(`搜索开户名称、银行名称、卡号或人员`)}
                data-testid="pay-paymentSetting-search"
              />
              <Button category="secondary" onClick={handleImport} data-testid="pay-paymentSetting-import-btn">
                {i18n.get('导入')}
              </Button>
              <Button disabled={total === 0} category="secondary" onClick={handleExportAll} data-testid="pay-paymentSetting-exportAll-btn">
                {i18n.get('导出全部')}
              </Button>
              <Button onClick={handleAdd} icon={<OutlinedTipsAdd />} data-testid="pay-paymentSetting-add-btn">
                {i18n.get('添加复核规则')}
              </Button>
            </Space>
          </Space>
        </div>
        <div className="tableContent">
          <ConfigProviderWrapper>
            <ProTable
              loading={loading}
              search={false}
              toolbar={{ style: { display: 'none' } }}
              columns={columns}
              dataSource={ruleList}
              scroll={{ y: selectedRowKeys.length > 0 ? 'calc(100vh - 356px)' : 'calc(100vh - 292px)', x: true }}
              rowKey="id"
              rowSelection={{
                selectedRowKeys: selectedRowKeys,
                preserveSelectedRowKeys: true,
                onChange: (selectedRowKeys: React.Key[]) => {
                  setSelectedRowKeys(selectedRowKeys)
                },
              }}
              tableAlertRender={({ selectedRowKeys, onCleanSelected }) => (
                <Space size={32}>
                  <Space size={8}>
                    <span>已选 {selectedRowKeys.length} 项</span>
                    <Button onClick={onCleanSelected} category="text" size="small" theme="highlight" data-testid="pay-paymentSetting-cancelSelect-btn">
                    {i18n.get('取消选择')}
                  </Button>
                  </Space>
                </Space>
              )}
              tableAlertOptionRender={() => {
                return (
                  <Space size={8}>
                    <Button category="text" onClick={handleDeleteBySelected} theme="danger" size="small" data-testid="pay-paymentSetting-batchDelete-btn">
                    {i18n.get('批量删除')}
                  </Button>
                    <Button category="text" onClick={handleExportBySelected} theme="highlight" size="small" data-testid="pay-paymentSetting-exportSelected-btn">
                    {i18n.get('导出所选')}
                  </Button>
                  </Space>
                )
              }}
              pagination={{
                total,
                current: searchInfo.current,
                pageSize: searchInfo.pageSize,
                showTotal: total => i18n.get('共 {__k0} 条', { __k0: total }),
                onChange: handlePageChange,
                showSizeChanger: true,
                showQuickJumper: true,
              }}
            />
          </ConfigProviderWrapper>
        </div>
      </div>
    </div>
  )
}

export default PaymentSetting
