import React from 'react'
import { app } from '@ekuaibao/whispered'
import styles from './PaymentReviewWrap.module.less'
// @ts-ignore
import classNames from 'classnames'
import { EnhanceConnect } from '@ekuaibao/store'
import * as viewUtil from '../view-util'
import { PaginationConfig } from '@ekuaibao/datagrid/esm/types/pagination'
import * as actions from '../audit-action'
import { LocaleProvider } from './DataGridLocales'

const withLoader: any = app.require('@elements/data-grid-v2/withLoader')
const PaymentReviewTable = withLoader(() => import('./PaymentReviewTable'))
const { startOpenFlowPerformanceStatistics } = app.require('@lib/flowPerformanceStatistics')

import { Input } from '@hose/eui'
const { Search } = Input

const { injectErrorDom } = app.require<any>('@elements/LoadingError')

export interface PaymentReviewWrapProps {
  isNeedHeight?: boolean
  getInstance?: (instance: any) => void
  bus?: any
  unUseAction?: boolean
  from?: string
}

export interface PaymentReviewWrapState {
  size: number
  total?: number
  current: number
  dataSource: any[]
  searchText: string
  loadingError: boolean
}

function tableColumns(this: any, props: any) {
  const { unUseAction } = props
  let columns: any = [
    {
      title: i18n.get('支付批次号'),
      dataIndex: 'channelTradeNo',
      width: '16%',
      render: viewUtil.channelTradeNoText.bind(this),
    },
    {
      title: i18n.get('单据数量'),
      dataIndex: 'flowCount',
      width: '8%',
    },
    {
      title: i18n.get('付款账户'),
      dataIndex: 'account',
      width: '12%',
      render: viewUtil.tdAccountInfo.bind(this),
    },
    {
      title: i18n.get('支付方式'),
      width: '10%',
      dataIndex: 'channel',
      render: viewUtil.tdChannel.bind(this),
    },
    {
      title: i18n.get('批次金额'),
      width: '10%',
      dataIndex: 'balance',
      render: viewUtil.tdAmount.bind(this),
    },
    {
      title: i18n.get('发起支付时间'),
      dataIndex: 'payTime',
      width: '15%',
      render: viewUtil.tdDateTime.bind(this),
    },
    {
      title: i18n.get('复核人员'),
      dataIndex: 'ownerId.name',
      width: '10%',
    },
    {
      title: i18n.get('发起支付人员'),
      dataIndex: 'payerId.name',
      width: '10%',
    },
  ]
  if (!unUseAction) {
    columns.push({
      title: i18n.get('操作'),
      width: '24%',
      dataIndex: 'actions',
      className: 'actions-wrapper',
      render: viewUtil.paymentBatchAction('paymentReviewBatch', props),
    })
  }
  return columns
}
function secondTableColumns(this: any) {
  let columns = [
    {
      title: i18n.get('标题'),
      width: '16%',
      dataIndex: 'title',
      render: viewUtil.tdText.bind(this),
    },
    {
      title: i18n.get('单号'),
      width: '14%',
      dataIndex: 'code',
      render: viewUtil.tdText.bind(this),
    },
    {
      title: i18n.get('单据类型'),
      width: '14%',
      dataIndex: 'specificationName',
      render: viewUtil.tdText.bind(this),
    },
    {
      title: i18n.get('提交日期'),
      width: '14%',
      dataIndex: 'submitDate',
      render: viewUtil.tdDate.bind(this),
    },
    {
      title: i18n.get('收款账户'),
      width: '14%',
      dataIndex: 'payee',
      render: viewUtil.tdAccountInfo.bind(this),
    },
    {
      title: i18n.get('支付金额'),
      width: '14%',
      dataIndex: 'payMoney',
      render: viewUtil.tdAmount.bind(this),
    },
    {
      title: i18n.get('操作'),
      width: '14%',
      dataIndex: 'actions',
      className: 'actions-wrapper',
      render: (_: any, line: any) => {
        const handleClickToOpenBill = async (line: any) => {
          startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics

          if (app.require<any>('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
            app.open('@bills:BillInfoDrawerV2', {
              currentId: line.id,
              flows: [],
              hideFooter: true,
              showUpDown: false,
            })
            return
          }

          const action = await actions.getFlowInfo(line.id)
          app.dispatch(action).then((flow: any) => {
            app.open(
              '@bills:BillInfoPopup',
              {
                title: i18n.get('详情'),
                backlog: { id: -1, flowId: flow },
                invokeService: '@audit:get:history-flow:info',
                params: flow.id,
                showFooter: false,
                mask: false
              },
              true,
            )
          })
        }
        return (
          <a className="color-blue mr-16" onClick={() => handleClickToOpenBill(line)} data-testid="pay-paymentReviewWrap-viewBill">
            {i18n.get('查看单据')}
          </a>
        )
      },
    },
  ]
  return columns
}

@EnhanceConnect((state: any) => ({
  dynamicChannelMap: state['@audit'].dynamicChannelMap,
}))
export class PaymentReviewWrap extends React.PureComponent<PaymentReviewWrapProps, PaymentReviewWrapState> {
  instance: any
  tableColumns: any
  secondTableColumns: any
  retryFn: null | (() => void) = null
  hideErrorUI: null | (() => void) = null
  constructor(props: PaymentReviewWrapProps) {
    super(props)
    this.tableColumns = tableColumns.call(this, props).map((c: any) => {
      c.sorter = false
      return c
    })
    this.secondTableColumns = secondTableColumns.call(this).map((c: any) => {
      c.sorter = false
      return c
    })
    this.state = {
      size: 20,
      total: 0,
      current: 1,
      dataSource: [],
      searchText: '',
      loadingError: false,
    }
  }

  componentDidMount() {
    const { bus } = this.props
    bus.on('audit-payment:tab:change:paymentReview', this.tabChange)
    bus.on('audit-payment:get:paymentReview:data', this.fnGetData)
    const { searchText } = this.state
    this.fnGetData({ searchValue: searchText })
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('audit-payment:tab:change:paymentReview', this.tabChange)
    bus.un('audit-payment:get:paymentReview:data', this.fnGetData)
  }

  tabChange = () => {
    this.instance?.collapseAll(-1)
    const { searchText } = this.state
    this.fnGetData({ searchValue: searchText })
  }

  fnGetData = async (data: { current?: number; searchValue?: string; size?: number } = { searchValue: '' }) => {
    const fetch = async (data: { current?: number; searchValue?: string; size?: number } = { searchValue: '' }) => {
      let { current, searchValue, size } = data
      const fetchData: { start?: number; count?: number; searchValue?: string } = {}
      size ?? (size = this.state.size)

      if (!current) {
        current = 1
        this.setState({ current: 1 })
      }

      fetchData.count = size
      fetchData.start = (current - 1) * size
      fetchData.searchValue = searchValue ?? ''

      const { from } = this.props
      let res: any = {}

      if (from === 'paymentReview') {
        res = await app.invokeService('@audit:get:payment:management:batch', fetchData)
      } else {
        res = await app.invokeService('@audit:get:all:payment:management:batch', fetchData)
      }

      const dataSource = res?.items || []
      this.setState({ dataSource, total: res?.count ?? 0 })
    }

    this.retryFn = async () => {
      try {
        this.clearErrorUI()
        this.instance && this.instance.beginCustomLoading()
        await fetch(data)
        this.clearErrorUI()
        this.setState({ loadingError: false })
        this.retryFn = null
      } catch (error) {
        this.setState({ loadingError: true, dataSource: [], total: 0 }, () => {
          this.showErrorUI()
        })
      } finally {
        this.instance && this.instance.endCustomLoading()
      }
    }

    this.retryFn()
  }

  getInstance = (instance: any) => {
    this.instance = instance
  }

  handleSearch = (value: any) => {
    this.setState({ searchText: value }, () => {
      this.fnGetData({ searchValue: value })
    })
  }

  handlePaginationChange = (pagination: PaginationConfig) => {
    this.setState({ current: pagination.current, size: pagination.size }, () => {
      this.fnGetData({ current: pagination.current, size: pagination.size })
    })
  }

  showErrorUI() {
    setTimeout(() => {
      if (this.instance) {
        const container = this.instance.element?.()?.querySelector('.dx-datagrid-rowsview')
        this.hideErrorUI = injectErrorDom(container, {
          refreshFn: () => {
            this.clearErrorUI()
            return this.retryFn?.()
          }
        })
      }
    }, 300)
  }

  clearErrorUI() {
    if (this.hideErrorUI) {
      this.hideErrorUI?.()
      this.hideErrorUI = null
    }
  }

  render() {
    const { total, size, current, dataSource = [], loadingError } = this.state
    return (
      <div className={styles['peyment_review_wrap']}>
        <div className="header">
          <Search
            className="search"
            placeholder={i18n.get('输入搜索批次号、标题、单号')}
            onSearch={this.handleSearch}
            data-testid="pay-paymentReviewWrap-search"
          />
        </div>

        <div className={classNames(styles.container)}>
          <LocaleProvider>
            <PaymentReviewTable
              {...this.props}
              rowKey="id"
              isNeedHeight={false}
              dataSource={dataSource}
              columns={this.tableColumns}
              getInstance={this.getInstance}
              secondTableColumns={this.secondTableColumns}
              paginationProps={{
                totalLength: total,
                pagination: { current, size },
              }}
              onPaginationChange={this.handlePaginationChange}
              loadingError={loadingError}
            />
          </LocaleProvider>
        </div>
      </div>
    )
  }
}

export default PaymentReviewWrap
