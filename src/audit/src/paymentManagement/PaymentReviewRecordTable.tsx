import React from 'react'
import * as DataGrid from '@ekuaibao/datagrid'
import styles from './PaymentReviewRecordWrap.module.less'
// @ts-ignore
import classNames from 'classnames'
import { PaginationConfig } from '@ekuaibao/datagrid/esm/types/pagination'

export interface PaymentReviewRecordTableProps {
  isNeedHeight?: boolean
  getInstance: (instance: any) => void
  columns: any
  dataSource: any[]
  paginationProps: {
    totalLength: number
    pagination: {
      current: number
      size: number
    }
  }
  onPaginationChange?: (pagination: PaginationConfig) => void
}

export interface PaymentReviewRecordTableState {
  columns: any[]
}
export class PaymentReviewRecordTable extends React.PureComponent<
  PaymentReviewRecordTableProps,
  PaymentReviewRecordTableState
> {
  instance: any
  constructor(props: PaymentReviewRecordTableProps) {
    super(props)
    this.state = {
      columns: this.props.columns || [],
    }
  }

  getInstance = (instance: any) => {
    this.instance = instance
    this.props.getInstance(instance)
  }

  handlePageChange = (pagination: PaginationConfig) => {
    this.props?.onPaginationChange?.(pagination)
  }

  render() {
    const { dataSource, paginationProps } = this.props
    const { columns } = this.state
    return (
      <>
        <DataGrid.TableWrapper
          rowKey="id"
          scrolling={{ mode: 'standard' }}
          standard
          className={styles.tableWrapper}
          dataSource={dataSource}
          columns={columns}
          allowColumnReordering
          allowColumnResizing
          getInstance={this.getInstance}
          isMultiSelect={false}
          isSingleSelect={false}
          pageIndex={paginationProps?.pagination?.current ?? 1}
          pageSize={paginationProps?.pagination?.size ?? 20}
        />
        {paginationProps ? (
          <footer className={styles['table-footer-wrapper']}>
            <DataGrid.Pagination
              {...paginationProps}
              pageMode="pagination"
              onChange={this.handlePageChange}
              disabledScroll
            />
          </footer>
        ) : (
          void 0
        )}
      </>
    )
  }
}

export default PaymentReviewRecordTable
