import React from 'react'
import * as DataGrid from '@ekuaibao/datagrid'
import styles from './PaymentReviewWrap.module.less'
import { PaymentReviewSecondTable } from './PaymentReviewSecondTable'
// @ts-ignore
import classNames from 'classnames'
import { PaymentReviewColumnIcon } from '../util/tableUtil'
import { PaginationConfig } from '@ekuaibao/datagrid/esm/types/pagination'

export interface PaymentTableProps {
  isNeedHeight?: boolean
  getInstance: (instance: any) => void
  from?: string
  columns: any
  secondTableColumns: any[]
  dataSource: any[]
  rowKey?: string
  paginationProps: {
    totalLength: number
    pagination: {
      current: number
      size: number
    }
  }
  onPaginationChange?: (pagination: PaginationConfig) => void
  loadingError?: boolean
}

export interface PaymentTableState {
  PaymentBatchColumns: any[]
  payPlanDataSourceMap: any
}
export class PaymentReviewTable extends React.PureComponent<PaymentTableProps, PaymentTableState> {
  instance: any
  constructor(props: PaymentTableProps) {
    super(props)
    this.state = {
      PaymentBatchColumns: this.props.columns || [],
      payPlanDataSourceMap: {},
    }
  }

  getInstance = (instance: any) => {
    this.instance = instance
    if (instance) {
      const iconCol = {
        title: '',
        dataIndex: 'id',
        allowReordering: false,
        allowGrouping: false,
        allowResizing: false,
        allowEditing: false,
        allowFiltering: false,
        sorter: false,
        width: 40,
        minWidth: 40,
        render: (_: any, record: any) => {
          return (
            <PaymentReviewColumnIcon
              from="PaymentReviewBatch"
              line={record}
              instance={instance}
              fnChangePayPlanDataSource={this.fnChangePayPlanDataSource}
            />
          )
        },
      }
      // @ts-ignore
      this.setState({ PaymentBatchColumns: [iconCol, ...this.state.PaymentBatchColumns] })
    }
    this.props.getInstance(instance)
  }

  fnChangePayPlanDataSource = (line: any, res: any) => {
    const map = this.state.payPlanDataSourceMap
    map[line?.id] = res
    this.setState({ payPlanDataSourceMap: { ...map } })
  }

  renderDetailTemplate = (props: any) => {
    const { secondTableColumns } = this.props
    const { payPlanDataSourceMap } = this.state
    const payPlanDataSource = payPlanDataSourceMap[props?.data?.id] || []
    return (
      <PaymentReviewSecondTable
        {...props}
        columns={secondTableColumns || []}
        payPlanDataSource={payPlanDataSource}
      />
    )
  }

  handlePageChange = (pagination: PaginationConfig) => {
    this.props?.onPaginationChange?.(pagination)
  }

  render() {
    const { dataSource, paginationProps, loadingError } = this.props
    const { PaymentBatchColumns } = this.state
    return (
      <>
        <DataGrid.TableWrapper
          rowKey="id"
          scrolling={{ mode: 'standard' }}
          standard
          className={`${styles.tableWrapper} ${loadingError ? styles.tableWrapperError : ''}`}
          dataSource={dataSource}
          columns={PaymentBatchColumns}
          allowColumnReordering
          allowColumnResizing
          getInstance={this.getInstance}
          isMultiSelect={false}
          isSingleSelect={false}
          RenderDetailTemplate={this.renderDetailTemplate}
          enabledDetailTemplate={false}
          pageIndex={paginationProps?.pagination?.current ?? 1}
          pageSize={paginationProps?.pagination?.size ?? 20}
          loadPanel={{
            enabled: true,
            showPane: false,
            text: i18n.get('加载中…'),
          }}
        />
        {paginationProps ? (
          <footer className={styles['table-footer-wrapper']}>
            <DataGrid.Pagination
              {...paginationProps}
              pageMode="pagination"
              onChange={this.handlePageChange}
              disabledScroll
            />
          </footer>
        ) : (
          void 0
        )}
      </>
    )
  }
}

export default PaymentReviewTable
