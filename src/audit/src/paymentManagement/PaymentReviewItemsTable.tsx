import React from 'react'
import * as DataGrid from '@ekuaibao/datagrid'
import { Column } from '@ekuaibao/datagrid/lib/types/column'
import styles from '../payment/table.module.less'
import * as viewUtil from '../view-util'

export function payingTableFeeColumns(this: any) {
  let columns: Array<any> = [
    {
      title: i18n.get('编号'),
      dataIndex: 'channelTradeNo',
      render: viewUtil.tdText.bind(this),
    },
    {
      title: i18n.get('收款信息'),
      dataIndex: 'payee',
      render: viewUtil.accountInfo.bind(this),
    },
    {
      title: i18n.get('支付金额'),
      dataIndex: 'createMoneyObj',
      render: viewUtil.tdAmount.bind(this),
    },
  ]
  return columns
}

export interface PaymentReviewItemsTableProps {
  detailData?: any[]
}

export interface PaymentReviewItemsTableState {
  dataSource: any[]
}

export class PaymentReviewItemsTable extends React.PureComponent<
  PaymentReviewItemsTableProps,
  PaymentReviewItemsTableState
> {
  private instance: any
  private columns: Column[]

  constructor(props: PaymentReviewItemsTableProps) {
    super(props)
    this.columns = payingTableFeeColumns.call(this).map((c: Column) => {
      c.sorter = false
      return c
    })
    this.state = {
      dataSource: props?.detailData || [],
    }
  }

  componentDidMount() {
    if (this.instance) {
      this.instance.on('initialized', () => {
        const scrollable = this.instance.getScrollable()
        if (scrollable) {
          scrollable.option('rowRenderingMode', 'standard')
        }
      })
    }
  }

  componentWillReceiveProps(nextProps: any) {
    if (this.props.detailData !== nextProps.detailData) {
      this.setState({ dataSource: nextProps?.detailData || [] })
    }
  }

  private getInstance = (instance: any) => {
    this.instance = instance
  }

  render() {
    const { dataSource } = this.state
    return (
      <div className={styles.subContainer}>
        <div className={styles.body}>
          <DataGrid.TableWrapper
            scrolling={{
              mode: 'standard',
            }}
            standard
            getInstance={this.getInstance}
            rowKey={'id'}
            className={styles.tableWrapper}
            dataSource={dataSource}
            pageIndex={1}
            pageSize={dataSource.length}
            columns={this.columns}
            isMultiSelect={false}
            isSingleSelect={false}
          />
        </div>
      </div>
    )
  }
}

export default PaymentReviewItemsTable
