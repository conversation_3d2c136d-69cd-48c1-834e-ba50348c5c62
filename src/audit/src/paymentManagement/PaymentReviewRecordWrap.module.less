@import '~@ekuaibao/eui-styles/less/token';
@import '~@ekuaibao/web-theme-variables/styles/default';
@import '~@ekuaibao/web-theme-variables/styles/colors';

.peyment_review_record_wrap {
  display: flex;
  flex-direction: column;
  flex: 1;
  border-radius: 4px;
  width: 100%;
  background-color: white;
  overflow: hidden;

  :global{
    .mr-16 {
      margin-right: 16px;
    }
    .header {
      width: 100%;
      display: flex;
      padding: 12px 16px;
      justify-content: space-between;
    }
  }
}

.container {
  display: flex;
  width: 100%;
  flex-direction: column;
  height: 100%;
  padding: 0 16px;
  overflow: auto;
}

.tableWrapper {
  position: relative;
  flex: 1;
  overflow: hidden;

  &:before {
    content: ' ';
    position: absolute;
    border-top: 1px solid var(--eui-line-divider-default, rgba(29, 33, 41, 0.10));
    width: 100%;
    z-index: 10;
  }
}

.table-footer-wrapper {
  display: flex;
  align-items: center;
  padding: 12px 0;
  height: 32px;
  box-sizing: content-box;
}
