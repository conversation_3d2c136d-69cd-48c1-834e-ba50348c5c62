import { app } from '@ekuaibao/whispered'
import MessageCenter from '@ekuaibao/messagecenter'
import { CheckboxChangeEvent } from 'antd/es/checkbox'
import styles from './AuditSend.module.less'
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
const ETabs = app.require<any>('@elements/ETabs')
import { connect } from '@ekuaibao/mobx-store'
import { Checkbox } from '@hose/eui'
import WaitWrapper from './sendExpress/wrapper/wait-express-wrapper'
import SendWrapper from './sendExpress/wrapper/send-express-wrapper'
import LightningSend from './sendExpress/express-view/lightning-send'

interface AuditSendProps {
  size: number
  baseDataProperties: any
  staffs: any
  specifications: any
  userInfo: any
  bus?: any
}

interface AuditSendState {
  isLightingMode: boolean
}

// @ts-ignore
@connect((store: any) => ({ size: store.states['@layout'].size }))
@EnhanceConnect((state: any) => ({
  baseDataProperties: state['@common'].globalFields.data,
  staffs: state['@common'].staffs,
  specifications: state['@custom-specification'].specificationGroupsList,
  userInfo: state['@common'].userinfo.data,
}))
export default class AuditSend extends PureComponent<AuditSendProps, AuditSendState> {
  private bus = this.props.bus || new MessageCenter()
  state = {
    isLightingMode: false,
  }

  handleTabChange = (type: string) => {
    if (type === 'wait') {
      this.bus.reload()
    }
  }

  toggleLightingMode = (e: CheckboxChangeEvent) => {
    this.setState({ isLightingMode: e.target.checked })
  }

  render() {
    const { isLightingMode } = this.state

    const dataSource = [
      {
        tab: i18n.get('待寄送'),
        children: <WaitWrapper {...this.props} bus={this.bus} />,
        key: 'wait',
        isHidden: isLightingMode,
      },
      {
        tab: i18n.get('已寄送'),
        children: <SendWrapper {...this.props} />,
        key: 'send',
        isHidden: isLightingMode,
      },
      {
        tab: i18n.get('闪电寄送'),
        children: <LightningSend {...this.props} bus={this.bus} />,
        key: 'lightning',
        isHidden: !isLightingMode,
      },
    ]
    return (
      <div id="audit-send" className={styles.send_wrapper}>
        <ETabs
          isHoseEUI={true}
          className="ekb-tab-line-left"
          defaultActiveKey={this.state.isLightingMode ? 'lightning' : 'wait'}
          tabBarStyle={{ paddingTop: 6, height: 40, width: '100%' }}
          onChange={this.handleTabChange}
          dataSource={dataSource}
        />
        <Checkbox
          className="lightning-send-btn lightning-send-content"
          onChange={this.toggleLightingMode}>
          {i18n.get('闪电寄送')}
        </Checkbox>
      </div>
    )
  }
}
