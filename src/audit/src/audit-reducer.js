import { catchError } from '@ekuaibao/lib/lib/lib-util'
import { showMessage } from '@ekuaibao/show-util'
import { Reducer } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'
import key from './key'

const reducer = new Reducer(key.ID, {
  approve_loan: { count: 0, bills: [] },
  payment_expense: { count: 0, bills: [] },
  payment_loan: { count: 0, bills: [] },
  operation_records: { count: 0, bills: [], append: false },
  operation_bills: { count: 0, bills: [] },
  orgExpenseBills: { count: 0, bills: [] },
  //new
  approve_expense: { count: 0, bills: [] },
  paying_expense: { count: 0, bills: [] },
  current_bill_info: {},
  payment_record_list: { count: 0, items: [], append: false },
  payment_batch_list: { count: 0, items: [] },
  deptList: [],
  specList: [],
  bankAccountList: [],
  transferFileTemplates: [],
  inBatchPaying: false,
  commentWordsData: [],
  offlineFinishTimeAvailable: false,
})

reducer.handle(key.GET_BACKLOG_LIST)(
  catchError((state, action) => {
    let list = {
      count: action.payload.count,
      bills: action.payload.items,
    }
    if (action.state === 'APPROVING') {
      api.invokeService('@layout:change:badge', 'audit-pending-expense', list.count)
      return { ...state, approve_expense: list }
    }

    if (action.state === 'PAYING') {
      api.invokeService('@layout:change:badge', 'audit-payment-expense', list.count)
      return { ...state, paying_expense: list }
    }

    return { ...state }
  }),
)

reducer.handle(key.GET_BACKLOG_INFO)(
  catchError((state, action) => {
    let current_bill_info = action.payload.value
    return { ...state, current_bill_info }
  }),
)

reducer.handle(key.GET_DATALINK_ENTITIES)(
  catchError((state, action) => {
    const dataLinkEntities = action.payload.items || []
    const dataLinkEntity = dataLinkEntities.find(item => item.id === action.id)
    return { ...state, dataLinkEntity }
  }),
)

reducer.handle(key.GET_RECEIPT_DATALINK_ENTITIES)(
  catchError((state, action) => {
    const dataLinkEntities = action.payload.items || []
    const dataLinkEntity = dataLinkEntities[0]
    return { ...state, dataLinkEntity }
  }),
)

reducer.handle(key.SET_FLOW_PLAN_NODE)(
  catchError((state, action) => {
    return state
  }),
)

reducer.handle(key.AGREE_BACKLOG)(
  catchError((state, action) => {
    let { payload } = action
    if (!payload.errors || payload.errors.length === 0) {
      showMessage.success(i18n.get('审批成功'))
    }

    return state
  }),
)
reducer.handle(key.RECEIVE_BACKLOG)(
  catchError((state, action) => {
    let { payload } = action
    if (
      payload.value.errors &&
      payload.value.errors.filter(o => o.resultCode !== 'OK').length === 0
    ) {
      showMessage.success(i18n.get('收单成功'))
    }
    return state
  }),
)
reducer.handle(key.RECEIVE_EXCEPTION_BACKLOG)(
  catchError((state, action) => {
    let { payload } = action
    if (
      payload.value.errors &&
      payload.value.errors.filter(o => o.resultCode !== 'OK').length === 0
    ) {
      showMessage.success(i18n.get('收单异常成功'))
    }
    return state
  }),
)
reducer.handle(key.CANCEL_RECEIVE_EXCEPTION_BACKLOG)(
  catchError((state, action) => {
    let { payload } = action
    if (
      payload.value.errors &&
      payload.value.errors.filter(o => o.resultCode !== 'OK').length === 0
    ) {
      showMessage.success(i18n.get('取消收单异常成功'))
    }
    return state
  }),
)

reducer.handle(key.REJECT_BACKLOG)(
  catchError((state, action) => {
    let { payload } = action
    if (!payload.errors || payload.errors.length === 0) {
      showMessage.success(i18n.get('驳回成功'))
    }

    return state
  }),
)

reducer.handle(key.TRANSFER_FLOW)(
  catchError((state, action) => {
    let { error, payload } = action
    if (!error && payload.errors && payload.errors.length === 0) {
      showMessage.success(i18n.get('操作成功'))
    }
    return state
  }),
)

reducer.handle(key.PAY_BACKLOG)(
  catchError((state, action) => {
    return state
  }),
)
reducer.handle(key.LIGHTNING_RECEIVE)(
  catchError((state, action) => {
    return state
  }),
)
reducer.handle(key.GET_RECEIVE_BACKLOG)(
  catchError((state, action) => {
    return state
  }),
)
reducer.handle(key.GET_BACKLOG_LIST_BY_ACTION)(
  catchError((state, action) => {
    const list = {
      count: action.payload.count,
      bills: action.payload.items,
      append: false,
    }
    return { ...state, operation_records: list }
  }),
)

reducer.handle(key.APPEND_BACKLOG_LIST_BY_ACTION)(
  catchError((state, action) => {
    const list = {
      count: action.payload.count,
      bills: state.operation_records.append
        ? [...state.operation_records.bills, ...action.payload.items]
        : action.payload.items,
      append: true,
    }
    return { ...state, operation_records: list }
  }),
)

reducer.handle(key.GET_APPROVE_LOAN_BILLS)((state, action) => {
  let data = { count: 10, bills: mockData }
  return { ...state, approve_loan: data }
})

reducer.handle(key.GET_PAYMENT_LOAN_BILLS)((state, action) => {
  let data = { count: 10, bills: mockData } ///
  return { ...state, payment_loan: data }
})

//#region 借款 action
reducer.handle(key.GET_LOAN_BACKLOG_LIST)(
  catchError((state, action) => {
    let list = {
      count: action.payload.count,
      bills: action.payload.items,
    }
    if (action.state === 'APPROVING') {
      api.invokeService('@layout:change:badge', 'audit-pending-loan', list.count)
      return { ...state, approve_loan: list }
    }

    if (action.state === 'PAYING') {
      api.invokeService('@layout:change:badge', 'audit-payment-loan', list.count)
      return { ...state, paying_loan: list }
    }

    return { ...state }
  }),
)

reducer.handle(key.GET_LOAN_BACKLOG_INFO)(
  catchError((state, action) => {
    let current_bill_info = action.payload.value
    return { ...state, current_bill_info }
  }),
)

reducer.handle(key.GET_LOAN_PACKAGE_LIST_BY_ID)(
  catchError((state, action) => {
    let loanPackageList = action.payload
    return { ...state, loanPackageList }
  }),
)
//#endregion

//searh
reducer.handle(key.SEARCH_BACKLOGS)(
  catchError((state, action) => {
    let list = {
      count: action.payload.count,
      bills: action.payload.items,
    }
    if (action.state === 'APPROVING') {
      !action.filter &&
        (action.billType === 'expense'
          ? api.invokeService('@layout:change:badge', 'audit-pending-expense', list.count)
          : api.invokeService('@layout:change:badge', 'audit-pending-loan', list.count))

      let res = { ...state }
      let key = `approve_${action.billType}`
      res[key] = list
      return res
    }

    if (action.state === 'PAYING') {
      action.billType === 'expense'
        ? api.invokeService('@layout:change:badge', 'audit-payment-expense', list.count)
        : api.invokeService('@layout:change:badge', 'audit-payment-loan', list.count)
      let res = { ...state }
      let key = `paying_${action.billType}`
      res[key] = list
      return res
    }
  }),
)

reducer.handle(key.GET_DIMS)(
  catchError((state, action) => {
    let data = action.payload
    let deptList = []
    let specList = []
    let bankAccountList = []
    data.deptList.forEach(v => {
      deptList.push({
        text: v.name,
        value: v.id,
      })
    })
    data.specList.forEach(v => {
      if (!specList.find(o => o.value === v.name)) {
        specList.push({
          text: v.name,
          value: v.name,
        })
      }
    })
    data.bankList.forEach(v => {
      if (!bankAccountList.find(o => o.value === v.bank)) {
        bankAccountList.push({
          text: v.bank,
          value: v.bank,
        })
      }
    })
    return { ...state, deptList, specList, bankAccountList }
  }),
)

reducer.handle(key.GET_PAY_BIND_PHONE_INFO)(
  catchError((state, action) => {
    let bindPhoneInfo = action.payload.value
    return { ...state, bindPhoneInfo }
  }),
)

reducer.handle(key.SET_PAY_BIND_IPHONE)(
  catchError((state, action) => {
    showMessage.success(i18n.get('绑定成功'))
    return { ...state }
  }),
)

reducer.handle(key.GET_PAY_PHONE_CAPTCHA)(
  catchError((state, action) => {
    let captha = action.payload.items
    return { ...state, captha }
  }),
)

reducer.handle(key.GET_THIRD_PHONE_CAPTCHA)(
  catchError((state, action) => {
    let captha = action.payload.items
    return { ...state, captha }
  }),
)

reducer.handle(key.OPEN_PAY_MESSAGE)((state, action) => {
  action.error && showMessage.error(action.error.message)
  return { ...state }
})

reducer.handle(key.CONFIRM_PAY)(
  catchError((state, action) => {
    showMessage.success(i18n.get('确认成功'))
    api.invokeService(`@common:get:backlog:count:payment`)
    return { ...state }
  }),
)

reducer.handle(key.CONFIRM_PAYMENT)(
  catchError((state, action) => {
    showMessage.success(i18n.get('确认成功'))
    api.invokeService(`@common:get:backlog:count:payment`)
    return { ...state }
  }),
)

reducer.handle(key.CANCEL_PAY)(
  catchError((state, action) => {
    !action.error && showMessage.success(i18n.get('取消成功'))
    api.invokeService(`@common:get:backlog:count:payment`)
    return { ...state }
  }),
)

reducer.handle(key.CANCEL_PAY_BATCH)(
  catchError((state, action) => {
    !action.error && showMessage.success(i18n.get('取消成功'))
    api.invokeService(`@common:get:backlog:count:payment`)
    return { ...state }
  }),
)

reducer.handle(key.GET_DYNAMIC_CHANNEL)(
  catchError((state, action) => {
    const channelList = action.payload && Object.values(action.payload)
    return { ...state, dynamicChannelMap: action.payload, channelList }
  }),
)

reducer.handle(key.ZERO_PAY)(
  catchError((state, action) => {
    !action.error && showMessage.success(i18n.get('支付成功'))
    return { ...state }
  }),
)

reducer.handle(key.GET_PAYMENT_BATCH)(
  catchError((state, action) => {
    let items = action.payload
    api.invokeService('@layout:change:badge', 'pending-payment', items.count)
    return { ...state, payment_batch_list: items }
  }),
)

reducer.handle(key.GET_PAYMENT_RECORD)(
  catchError((state, action) => {
    const items = {
      ...action.payload,
      append: false,
    }
    return { ...state, payment_record_list: items }
  }),
)

reducer.handle(key.APPEND_PAYMENT_RECORD)(
  catchError((state, action) => {
    const items = {
      count: action.payload.count,
      items: state.payment_record_list.append
        ? [...state.payment_record_list.items, ...action.payload.items]
        : action.payload.items,
      append: true,
    }
    return { ...state, payment_record_list: items }
  }),
)

reducer.handle(key.UPDATE_PAYMENT_STATE)(
  catchError((state, action) => {
    return { ...state }
  }),
)
reducer.handle(key.PRINT_REMIND)((state, action) => {
  let { error, payload } = action
  if (!error && payload.errors && payload.errors.length === 0) {
    showMessage.success(i18n.get('提醒成功'))
  }
  return { ...state }
})
reducer.handle(key.MARKED_READ_SELECTED)(
  catchError((state, action) => {
    api.dataLoader('@common.carbonCopyCount').reload()
    return { ...state }
  }),
)
reducer.handle(key.MARKED_READ_ALL)(
  catchError((state, action) => {
    api.dataLoader('@common.carbonCopyCount').reload()
    return { ...state }
  }),
)
reducer.handle(key.GET_CARBON_COPY_FLOW_INFO_BY_ID)(
  catchError((state, action) => {
    let current_bill_info = action.payload.value
    return { ...state, current_bill_info }
  }),
)

reducer.handle(key.GET_CARBON_COPY_FLOW_INFO_BY_FLOW_ID)(
  catchError((state, action) => {
    return { ...state }
  }),
)

//获取单据的所有快递单号
reducer.handle(key.GET_EXPRESS_NUMS)(
  catchError((state, action) => {
    let expressNums = action.payload.items
    return { ...state, expressNums }
  }),
)
//获取快递信息
reducer.handle(key.GET_EXPRESS)(
  catchError((state, action) => {
    //为了找到并直接对其修改
    let expressNums = state.expressNums.map(item => {
      if (item.express.id === action.expressId) {
        // 获取错误状态供页面显示
        if (action.payload.msg) {
          //仅用于 后台直接返回错误 没有返回值的时候 默认显示用
          item.express = { ...item.express, ...action.payload.value, msg: action.payload.msg }
        } else {
          item.express = { ...item.express, ...action.payload.value }
        }
        item.isLoading = true
      }
      return item
    })

    return { ...state, expressNums }
  }),
)

reducer.handle(key.CHECK_APPROVE_PROGRESS)((state, action) => {
  return { ...state }
})
reducer.handle(key.REPAY)(
  catchError((state, action) => {
    // showMessage.success(i18n.get('重新发起支付成功'))
    return { ...state }
  }),
)

reducer.handle(key.PAYPLAN_REPAY)(
  catchError((state, action) => {
    return { ...state }
  }),
)

//获取转账模板
reducer.handle(key.GET_TRANSFERFILE_TEMPLATE)(
  catchError((state, action) => {
    return { ...state, transferFileTemplates: action.payload.items }
  }),
)

reducer.handle(key.UPDATE_IMAGE_SIGN)(
  catchError((state, action) => {
    return { ...state }
  }),
)

reducer.handle(key.SAVE_IMAGE_SIGN)(
  catchError((state, action) => {
    return { ...state }
  }),
)

reducer.handle(key.NULLIFY_BACKLOG)(
  catchError((state, action) => {
    return { ...state }
  }),
)
reducer.handle(key.SET_APPROVAL_CONFIG)(
  catchError((state, action) => {
    return { ...state }
  }),
)
reducer.handle(key.GET_APPROVAL_CONFIG)(
  catchError((state, action) => {
    return { ...state }
  }),
)

reducer.handle(key.SET_IN_BATCH_PAYING)(
  catchError((state, action) => {
    return { ...state, ...action.payload }
  }),
)
reducer.handle(key.GET_COMMENT_WORDS)(
  catchError((state, action) => {
    return { ...state, commentWordsData: action.payload }
  }),
)
reducer.handle(key.NEW_COMMENT_WORDS)(
  catchError((state, action) => {
    return { ...state, commentWordsData: action.payload }
  }),
)
reducer.handle(key.EDIT_COMMENT_WORDS)(
  catchError((state, action) => {
    return { ...state, commentWordsData: action.payload }
  }),
)
reducer.handle(key.OFFLINE_FINISHED_TIME_AVAILABLE)(
  catchError((state, action) => {
    return { ...state, ...action.payload }
  }),
)

reducer.handle(key.GET_CAPTCHA)(
  catchError((state, action) => {
    if (action.payload?.status !== 0) {
      showMessage.error(action.payload.message);
      return state;
    }

    showMessage.success(i18n.get('验证码发送成功'));
    return { ...state };
  }),
)

export default reducer
