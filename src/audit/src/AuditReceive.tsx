import MessageCenter from '@ekuaibao/messagecenter'
import { CheckboxChangeEvent } from 'antd/es/checkbox'
import { app as api } from '@ekuaibao/whispered'
import styles from './AuditReceive.module.less'
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
const ETabs = api.require<any>('@elements/ETabs')
import { connect } from '@ekuaibao/mobx-store'
import { Checkbox } from '@hose/eui'
import ReceiveWrapper from './receiveExpress/ReceiveWrapper'
import ReceivedWrapper from './receiveExpress/ReceivedWrapper'
import ReceiveExceptionWrapper from './receiveExpress/ReceiveExceptionWrapper'
import LightningReceive from './receiveExpress/LightningReceive'

interface ExpressNumberResult {
  useExpressNumber: boolean
  expressNumber?: number
  value?: { backlogIds: string[] }
  cnt?: number
}

interface AuditReceiveProps {
  size: number
  baseDataProperties: any
  budgetPower: any
  staffs: any
  specifications: any
  userInfo: any
  bus?: any
  Express?: boolean
}

interface AuditReceiveState {
  isLightingMode: boolean
  expressNumberResult: ExpressNumberResult
  tabKey: string
}

// @ts-ignore
@connect((store: any) => ({ size: store.states['@layout'].size }))
@EnhanceConnect((state: any) => ({
  baseDataProperties: state['@common'].globalFields.data,
  budgetPower: state['@common'].powers.Budget,
  Express: state['@common'].powers.Express,
  staffs: state['@common'].staffs,
  specifications: state['@custom-specification'].specificationGroupsList,
  userInfo: state['@common'].userinfo.data,
}))
export default class AuditReceive extends PureComponent<AuditReceiveProps, AuditReceiveState> {
  private bus = this.props.bus || new MessageCenter()
  state = {
    isLightingMode: false,
    expressNumberResult: { useExpressNumber: false },
    tabKey: 'receive',
  }

  componentWillMount() {
    api.dataLoader('@common.staffs').load()
  }

  handleTabChange = (type: string) => {
    // 迁移的业务 并没有看到 receiving 的 key?
    if (type === 'receiving') {
      this.bus.reload()
    }
    this.setState({
      tabKey: type,
    })
  }

  toggleLightingMode = (e: CheckboxChangeEvent) => {
    const checked = e.target.checked
    if (checked) {
      api.open('@audit:ExpressNumberModal').then((result: ExpressNumberResult | any) => {
        this.setState({
          expressNumberResult: result,
          isLightingMode: true,
          tabKey: 'lightning',
        })
      })
    }
    this.setState({
      tabKey: 'receive',
      isLightingMode: false,
      expressNumberResult: { useExpressNumber: false },
    })
  }

  render() {
    const { isLightingMode, expressNumberResult, tabKey } = this.state

    const dataSource = [
      {
        tab: i18n.get('待收单'),
        children: tabKey === 'receive' && <ReceiveWrapper {...this.props} bus={this.bus} />,
        key: 'receive',
        isHidden: isLightingMode,
      },
      {
        tab: i18n.get('已收单'),
        children:  tabKey === 'received' && <ReceivedWrapper {...this.props} />,
        key: 'received',
        isHidden: isLightingMode,
      },
      {
        tab: i18n.get('闪电收单'),
        children: tabKey === 'lightning' && (
          <LightningReceive {...this.props} {...expressNumberResult} bus={this.bus} />
        ),
        key: 'lightning',
        isHidden: !isLightingMode,
      },
    ]
    if (this.props.Express) {
      dataSource.splice(2, 0, {
        tab: i18n.get('收单异常'),
        children: tabKey === 'receive_exception' && (
          <ReceiveExceptionWrapper {...this.props} bus={this.bus} />
        ),
        key: 'receive_exception',
        isHidden: isLightingMode,
      },)
    }
    return (
      <div id="audit-receive" className={styles.receive_wrapperls}>
        <ETabs
          isHoseEUI={true}
          className="ekb-tab-line-left"
          defaultActiveKey={this.state.isLightingMode ? 'lightning' : 'receive'}
          tabBarStyle={{ paddingTop: 6, height: 40, width: '100%' }}
          onChange={this.handleTabChange}
          dataSource={dataSource}
        />
        <Checkbox
          className="lightning-send-btn lightning-send-content"
          onChange={this.toggleLightingMode}>
          {i18n.get('闪电收单')}
        </Checkbox>
      </div>
    )
  }
}
