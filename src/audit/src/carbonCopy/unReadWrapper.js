/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/2/28 上午11:44.
 */
import React, { PureComponent } from 'react'
import BaseCarbonCopyWrapper from './baseCarbonCopyWrapper'
import { fetchCarbonCopyBills } from '../util/fetchUtil'
export default class ApprovedWrapper extends PureComponent {
  _status = { state: ['UNREAD'] }
  fetchPending = (params, findScene, dimensionItems) => {
    params.status = this._status
    return fetchCarbonCopyBills(params, findScene, dimensionItems)
  }

  render() {
    return (
      <BaseCarbonCopyWrapper
        {...this.props}
        status={this._status}
        carbonTabType={'UNREAD'}
        fetchPending={this.fetchPending}
      />
    )
  }
}
