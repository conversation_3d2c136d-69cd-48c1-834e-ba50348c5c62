/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/2/28 上午11:44.
 */
import React, { PureComponent } from 'react'
import BaseCarbonCopyWrapper from './baseCarbonCopyWrapper'
import { fetchCarbonCopyBills } from '../util/fetchUtil'
export default class ReadWrapper extends PureComponent {
  _status = { state: ['READ'] }
  fetchPending = (params, findScene, dimensionItems) => {
    params.status = this._status
    return fetchCarbonCopyBills(params, findScene, dimensionItems)
  }

  render() {
    return (
      <BaseCarbonCopyWrapper
        {...this.props}
        status={this._status}
        carbonTabType={'READ'}
        fetchPending={this.fetchPending.bind(this)}
      />
    )
  }
}
