import { app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import MessageCenter from '@ekuaibao/messagecenter'
import { mapping } from '../util/mapping4Approved'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import { handlePrint, handleActionImplementation } from '../service'
const { exportExcel } = api.require('@lib/export-excel-service')
import { showModal } from '@ekuaibao/show-util'
const LoaderWithLegacyData = api.require('@elements/data-grid-v2/LoaderWithLegacyData')
const { createNodeNameColumn, createNodeStaffColumn } = api.require('@elements/data-grid-v2/CreateColumn')
const { searchOptions, globalSearchOptions } = api.require('@lib/data-grid-v2/CustomSearchUtil')
import { createActionsColumn4Send, createRiskWarningColumn } from '../util/columnsAndSwitcherUtil'
import { Resource } from '@ekuaibao/fetch'
import { fnFilterMapping, getDefaultScenes, getInitScenes } from '../util/Utils'
import { EnhanceConnect } from '@ekuaibao/store'
const approved = new Resource('/api/flow/v2/filter')
import { cloneDeep } from 'lodash'
import { isEmptyObject } from '@ekuaibao/helpers'
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

const scenesType = 'CARBONCOPY'
const prefixColumns = { state: 'flowId', '*': 'flowId.form' }

@EnhanceConnect(state => ({
  specifications: state['@custom-specification'].specificationGroupsList,
  invoiceReviewPower: state['@common'].powers.invoiceReview,
  legalEntityCurrencyPower: state['@common'].powers.legalEntityCurrency,
  // 涉及需求 https://hose2019.feishu.cn/wiki/wikcncXiMtXZQwNcS64OMEwSsJd#fTSuk8
  KA_GLOBAL_SEARCH_2: state['@common'].powers.KA_GLOBAL_SEARCH_2,
  showPrintBtn: state['@common'].showPrintBtn,
}))
export default class BaseCarbonCopyWrapper extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
    this.state = {
      scenes: [],
    }
    this.initButtons()
    window.__AUDIT_FIRST_TIME = Date.now()
    this.totalCount = 0
  }

  initButtons() {
    let { carbonTabType } = this.props
    this.buttons = [
      { text: i18n.get('导出选中'), name: i18n.get('导出选中'), key: 'export_selected' },
      { text: i18n.get('打印单据'), name: i18n.get('打印单据'), key: 'print' },
    ]
    if (this.props?.showPrintBtn)
      this.buttons.push({
        text: i18n.get('打印单据和发票'),
        name: i18n.get('打印单据和发票'),
        key: 'printInvoice',
      })
    if (carbonTabType === 'UNREAD') {
      this.buttons.unshift({
        text: i18n.get('标为已读'),
        name: i18n.get('标为已读'),
        key: 'marked_read_selected',
      })
      this.buttons.unshift({
        text: i18n.get('全部设为已读'),
        name: i18n.get('全部设为已读'),
        key: 'marked_read_all',
        isActive: true,
        isBindTotal: true,
        type: 'primary',
      })
    }
  }

  componentDidMount() {
    this.bus.on('buttons:click', this.handleButtonsClick)
    this.bus.on('table:row:click', this.handleTableRowClick)
    this.bus.on('table:row:action', this.handleActions)
    this.bus.on('selectAllBtn:click', this.handleSelectAllBtnClick)
    this.bus.on('initScenes:action', this.initScenes)

    // 获取场景列表
    if (this.props.carbonTabType === 'UNREAD') {
      approved.GET('/$type', { type: scenesType }).then(async res => {
        const { value } = res
        const { specifications = [] } = this.props
        if (specifications.length === 0) {
          await api.invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned')
        }
        this.initScenes(value)
      })
    } else {
      approved.GET('/$type', { type: scenesType }).then(res => {
        const { value } = res
        this.initScenes(value)
      })
    }
  }

  initScenes = data => {
    const { specifications } = this.props
    const scenesData = getInitScenes({ data, prefix: 'flowId', specifications, isHasWaitInvoice: false })
    this.setState({ scenes: scenesData.scenes })
  }

  fnGetScene = () => {
    const { scenes, scene } = this.state
    let cloneScenes = cloneDeep(scenes)
    let findScenes = cloneScenes.find(s => s.sceneIndex === scene)
    if (findScenes) {
      findScenes.scene = findScenes.scene !== 'waitInvoice' ? findScenes.scene : ''
    }
    if (findScenes && findScenes.scene) return scene
    return findScenes
  }

  componentWillUnmount() {
    this.bus.un('buttons:click', this.handleButtonsClick)
    this.bus.un('table:row:click', this.handleTableRowClick)
    this.bus.un('table:row:action', this.handleActions)
    this.bus.un('selectAllBtn:click', this.handleSelectAllBtnClick)
    this.bus.un('initScenes:action', this.initScenes)
  }

  handleButtonsClick = ({ key, data, keys }, fetchParams) => {
    let { carbonTabType } = this.props
    keys = keys ? keys : this.bus.getSelectedRowKeys()
    data = data ? data : this.bus.getSelectedRowData()
    switch (key) {
      case 'print':
        return handlePrint(keys, data, undefined, false, '0')
      case 'printInvoice':
        return handlePrint(keys, data, undefined, false, '1')
      case 'export_selected':
        data = { ...data, ...status }
        return exportExcel({ exportType: 'export_selected', funcType: 'carbonCopy', data }, this.bus)
      case 'export_all':
        let params = !isEmptyObject(fetchParams) ? fetchParams : this.bus.getFilterParam()
        let { status } = params
        params.status = { ...status, state: [carbonTabType] }
        const scene = this.fnGetScene()
        params.scene = scene
        let exportParam = {
          exportType: 'export_all',
          funcType: 'carbonCopy',
          data: params,
          onlyAsyncExport: true,
          format: 'xlsx',
          others: { filter: `(type != "permit")` },
        }
        if (scene && scene.sceneIndex === 'waitInvoice') {
          exportParam.others = { filter: `(type != "permit")`, queryString: 'waitInvoice=true' }
        }
        return exportExcel(exportParam, this.bus)
      case 'marked_read_all':
        showModal.confirm({
          title: i18n.get('你确定要将所有抄送我的单据设为已读？'),
          onOk: () => {
            let ids = this.handleGetFlowIds(data)
            const param = keys && keys.length ? { type: key, ids } : { type: key }
            return api.invokeService('@audit:marked:read', param).then(() => this.updateCarbonCopyList())
          },
        })
        break
      case 'marked_read_selected':
        let ids = this.handleGetFlowIds(data)
        return api.invokeService('@audit:marked:read', { type: key, ids }).then(() => this.updateCarbonCopyList())
    }
  }

  handleGetFlowIds(map = {}) {
    let ids = []
    for (let key in map) {
      ids.push(map[key].flowId.id)
    }
    return ids
  }

  handleTableRowClick = data => {
    startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()

    const { carbonTabType } = this.props
    if (api.require('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
      api.open('@bills:BillInfoDrawerV2', {
        currentId: data.flowId.id,
        flows: this._currentDataSource.map(v => v.flowId),
        bus: this.bus,
        callBack: () => {
          api.close()
          this.updateCarbonCopyList()
        },
      })
      return
    }

    // 抄送id
    api.invokeService('@audit:get:carbonCopy', { id: data.id }).then(resp => {
      this.__billInfoPopup(resp.value)
    })
  }
  __billInfoPopup = flow => {
    let { flowId } = flow
    let title = `${i18n.get(billTypeMap()[flowId.formType])}${i18n.get('详情')}`
    api.open(
      '@bills:BillInfoPopup',
      {
        backlogType: 'carbonCopy',
        title,
        backlog: flow,
        invokeService: '@audit:get:carbonCopy',
        params: { id: flow.id },
        reload: this.bus.reload,
        mask: false,
        onFooterMarkedReadClick: () => {
          //flowId
          api.invokeService('@audit:marked:read', { ids: flowId.id }).then(_ => {
            api.close()
            this.updateCarbonCopyList()
          })
        },
      },
      true,
    )
  }

  fetchPending = async (params = {}, dimensionItems = {}) => {
    const { scenes } = this.state
    const { scene } = params
    const findScene = scenes.find(s => s.sceneIndex === scene)
    findScene && (params.scene = findScene.scene || '')
    scene && this.setState({ scene, fetchParams: params, dimensionItems })
    const { fetchPending } = this.props
    const data = await fetchPending(params, findScene, dimensionItems)
    this._currentDataSource = data.dataSource
    this.totalCount = data?.total
    return data
  }

  handleSelectAllBtnClick = (params = {}) => {
    const { status } = this.props
    params.status = status
    const { scenes, scene, fetchParams } = this.state
    const findScene = scenes.find(s => s.sceneIndex === scene)
    findScene && (params.scene = findScene.scene || '')
    if (fetchParams) {
      if (fetchParams.filters) params.filters = fetchParams.filters
      if (fetchParams.searchText) params.searchText = fetchParams.searchText
    }
    const keys = {}
    const blackList = ['marked_read_selected', 'export_selected', 'print', 'printInvoice']
    const buttons = this.buttons.filter(line => blackList.indexOf(line.key) < 0)
    buttons.push({ text: i18n.get('导出全部'), name: i18n.get('导出全部'), key: 'export_all' })
    api
      .open('@layout:DataGridSelectAllModal', {
        keys,
        buttons,
        totalCount: this.totalCount,
        isBillsTotalMoney: true,
      })
      .then(key => {
        this.handleButtonsClick({ key, data: {}, keys }, params)
      })
  }

  handleActions = (type, line) => {
    handleActionImplementation.call(this, {
      type,
      backlog: line,
      fn: () => {
        this.updateCarbonCopyList()
      },
    })
  }

  updateCarbonCopyList() {
    if (window.isNewHome) {
      api.invokeService('@layout5:refresh:menu:data')
    }
    this.bus.clearSelectedRowKeys()
    this.bus.reload().then(() => {
      setTimeout(() => {
        this.bus.emit('table:select:current:row')
      }, 500)
    })
  }

  selectAllBtnStyles = { color: 'var(--brand-base)' }

  render() {
    const { baseDataProperties, budgetPower, staffs, size, invoiceReviewPower, KA_GLOBAL_SEARCH_2, carbonTabType } =
      this.props
    const { scenes } = this.state
    if (!scenes.length) {
      return null
    }
    return (
      <LoaderWithLegacyData
        newSearch={true}
        searchOptions={KA_GLOBAL_SEARCH_2 ? globalSearchOptions() : searchOptions()}
        enableGlobalSearch={KA_GLOBAL_SEARCH_2}
        lightingMode={this.state.isLightingMode}
        scenes={scenes}
        fetch={this.fetchPending}
        buttons={this.buttons}
        onButtonClick={this.handleButtonsClick}
        onSelectedAll={this.handleSelectAllBtnClick}
        prefixColumns={prefixColumns}
        bus={this.bus}
        resource={approved}
        scenesType={scenesType}
        size={size}
        baseDataProperties={baseDataProperties}
        createAction={createActionsColumn4Send}
        mapping={fnFilterMapping(mapping, invoiceReviewPower)}
        createRiskWarningColumn={createRiskWarningColumn}
        showOutsideButtonCount={4}
        createNodeNameColumn={() => createNodeNameColumn({ dataIndex: 'flowId.nodeState.nodeName' })}
        createNodeStaffColumn={() =>
          createNodeStaffColumn({ dataIndex: 'flowId.nodeState.staffName', filterType: false })
        }
        useNewFieldSet
        carbonTabType={carbonTabType}
      />
    )
  }
}
