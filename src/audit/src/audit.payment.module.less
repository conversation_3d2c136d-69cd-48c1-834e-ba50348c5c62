/**************************************************
 * Created by nanyuantingfeng on 14/09/2017 13:13.
 **************************************************/
@import "~@ekuaibao/eui-styles/less/token";
@import '~@ekuaibao/web-theme-variables/styles/colors';

.payment_wrapper {
  display: flex;
  flex: 1;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  border: solid 1px #e5e5e5;

  &>div {
    height: auto;
    display: flex;
    flex-direction: column;
  }

  :global {
    .ekb-tab-content {
      display: flex;
    }

    .ant-tabs-bar {
      margin-left: 24px;
    }
  }
}

.common_payment_wrapper {
  overflow-y: hidden;
}

.payment_wrapper_layout5 {
  border: none;
}

.receipt_container {
  display: flex;
  flex-direction: column;
  :global {
    #receiptFilterBar {
      padding-right: 15px;
      margin-bottom: -15px;
    }

    .ant-row.ant-form-item {
      display: flex;
      margin-left: 15px;
      margin-bottom: 10px;
    }

    .ant-form-item-control-wrapper {
      flex: 1
    }
    .span-link-btns{
      span{
        padding: 0 10px;
        cursor: pointer;
        &.active-btn{
          color: var(--brand-base);
        }
      }
    }
  }

}

.dimension_btn {
  position: absolute !important;
  right: 15px;
  top: 155px;
}

.bindPaymentPlanHeader_actions {
  flex-shrink: 0;
  :global {
    .icon {
      width: 16px;
      height: 16px;
      margin-left: @space-4;
      color: @color-black-2;
      cursor: pointer;
    }
  }
}

.reexchangeTable-search {
  width: 360px;
  margin-right: 16px;
}