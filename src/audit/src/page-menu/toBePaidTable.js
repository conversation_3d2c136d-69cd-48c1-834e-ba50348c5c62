import React, { PureComponent } from 'react'
import { app as api } from '@ekuaibao/whispered'
const LoaderWithLegacyData = api.require('@elements/data-grid-v2/LoaderWithLegacyData')
import { fetchPayPlanInfo, exportPayPlanExcel } from '../util/fetchUtil'
import MessageCenter from '@ekuaibao/messagecenter'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import { handleActionImplementation } from '../service'
import { createActionColumn4Pay } from '../util/columnsAndSwitcherUtil'
import { getFlowInfo } from '../audit-action'
import { EnhanceConnect } from '@ekuaibao/store'
const { parseColumns } = api.require('@elements/DataLinkTable/tableUtil')
import { parseOptions } from '../util/tableUtil'
import { parseFields, parseSorters, parseFilter } from '@ekuaibao/lib/lib/entityUtil/entityTableUtil'
import { cloneDeep } from 'lodash'
import { prepareRender } from '../view-util'
import FilterBar from '../record-table/filter-bar'
import { getYearFirstDay, searchOptionPayPlanCode, searchOptionPayPlanName, searchOptionSubmitter } from '../util/Utils'
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')
import moment from 'moment'
import { get } from 'lodash'
import { openNewBillInfoDrawer } from './new-bill-info-drawer'

@EnhanceConnect(state => ({
  specifications: state['@custom-specification'].specificationGroupsList,
  dynamicChannelMap: state['@audit'].dynamicChannelMap,
}))
export default class PayingWrapper extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
    this.pathMap = {}
    this.fieldMap = {}
    this.state = {
      scenes: [{ text: i18n.get('全部'), scene: 'all', active: true, sceneIndex: 'all' }],
      columns: [],
      startDate: getYearFirstDay(),
      controlDate: false,
      endDate: moment().format('YYYY-MM-DD') + ' 23:59:59',
    }
  }

  formatColumns = data => {
    const { bus, entityInfoMap = {}, dataLinkEntity, dynamicChannelMap } = this.props
    if (data) {
      const { template, path } = data
      const type = 'DATA_LINK'
      const fields = parseFields({ res: template, type, dataLinkEntity })
      const { columns, fieldMap } = parseColumns({
        entityInfoMap,
        fields,
        bus,
        path,
        platformType: type,
        dataLinkEntity,
      })
      prepareRender(columns, dynamicChannelMap)
      this.fieldMap = fieldMap
      this.pathMap = { ...path, entityId: 'entityId' }
      this.setState({ columns })
    }
  }

  componentDidMount() {
    api.invokeService('@audit:get:ppayment:plans:control').then(res => {
      this.setState({ controlDate: res.value })
    })
    this.bus.on('buttons:click', this.handleButtonsClick)
    this.bus.on('table:row:click', this.handleTableRowClick)
    this.bus.on('table:row:action', this.handleActions)
  }

  componentWillUnmount() {
    this.bus.un('buttons:click', this.handleButtonsClick)
    this.bus.un('table:row:click', this.handleTableRowClick)
    this.bus.un('table:row:action', this.handleActions)
  }

  fetchToBePaid = async (params = {}, dimensionItems = {}) => {
    const { dataLinkEntity } = this.props
    const { startDate, endDate } = this.state
    // 由于界面上面显示的字段和传回到后台的字段名称不一致，为了不更改字段名称拷贝一份。。。
    const __options = cloneDeep(params)
    if (!!Object.keys(params.sorters).length || !!Object.keys(params.filters).length) {
      Object.keys(__options.filters).forEach(item => {
        if (typeof params.filters[item] === 'object') {
          params.filters[item] = params.filters[item][item]
        }
      })
      __options.sorters = parseSorters(params.sorters, this.pathMap)
      __options.filters = parseFilter(params.filters, this.pathMap)
    }
    __options.filters['form.E_system_paymentPlan_生成日期'] = {
      start: moment(startDate).valueOf(),
      end: moment(endDate).valueOf(),
    }
    const query = parseOptions({
      options: __options,
      entityInfo: dataLinkEntity,
      fieldMap: this.fieldMap,
    })
    const { columns } = this.state
    const res = await fetchPayPlanInfo(query, { READY: 'READY' }, result => {
      !columns.length && this.formatColumns(result)
    })
    this._currentDataSource = res.dataSource
    return res
  }

  handleButtonsClick = ({ params, name, data, keys }) => {
    switch (name) {
      case 'export':
        return exportPayPlanExcel(keys, 'READY')
    }
  }

  reloadAndClearSelecteds = () => {
    this.bus.clearSelectedRowKeys && this.bus.clearSelectedRowKeys()
    this.bus.reload && this.bus.reload()
  }

  __actionDone = () => {
    api.invokeService('@layout5:refresh:menu:data')
    this.reloadAndClearSelecteds()
  }

  __handleLine(type, line) {
    handleActionImplementation.call(this, { type, backlog: line, fn: this.__actionDone })
  }

  __billInfoPopup = flow => {
    const title = `${i18n.get(billTypeMap()[flow.formType])}${i18n.get('详情')}`
    api.open(
      '@bills:BillInfoPopup',
      {
        title,
        backlog: { id: -1, flowId: flow },
        invokeService: '@audit:get:backlog-info',
        params: flow.flowId,
        isShowCondition: true,
        reload: this.bus.reload,
        noCheckPermissions: true,
        scene: 'APPROVER',
        onOpenOwnerLoanList: line => {
          this.__handleLine(9, line)
        },
        onFooterButtonsClick: (type, line) => {
          api.close()
          setTimeout(() => {
            this.__handleLine(type, line)
          }, 0)
        },
      },
      true,
    )
  }

  getFlowId = (item) => {
    return get(item, 'dataLink.E_system_paymentPlan_flowId')
  }

  handleTableRowClick = async data => {
    const flowId = this.getFlowId(data)
    startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()

    if (api.require('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
      openNewBillInfoDrawer(flowId, {
        flows: this._currentDataSource?.map((item) => ({
          id: this.getFlowId(item),
        })),
        bus: this.bus,
      })
      return
    }

    const action = await getFlowInfo(flowId, null, true)
    api.dispatch(action).then(flow => {
      this.__billInfoPopup(flow)
    })
  }

  handleActions = (type, line) => {
    handleActionImplementation.call(this, { type, backlog: line, fn: this.__actionDone })
  }

  buttons = [{ text: i18n.get('导出'), name: 'export' }]
  handleFilterBarChange = date => {
    this.setState({ startDate: date.sdate, endDate: date.edate }, () => {
      this.bus.reload(true)
    })
  }
  onSearch = value => {
    this.bus.search(value)
  }
  onNewSearch = value => {
    this.bus.searchCustom(value)
  }
  render() {
    const { baseDataProperties } = this.props
    const { columns } = this.state
    return (
      <LoaderWithLegacyData
        columns={columns}
        isNeedHeight={false}
        rowKey="dataLink.id"
        searchPlaceholder={i18n.get('搜索编号、支付概要或提交人')}
        scenes={[]}
        disabledScenes
        isHiddenSearch={true}
        fetch={this.fetchToBePaid}
        buttons={this.buttons}
        onButtonClick={this.handleButtonsClick}
        bus={this.bus}
        headerTopRender={() => (
          <FilterBar
            newSearch={true}
            searchOptions={[searchOptionPayPlanName('form'), searchOptionPayPlanCode('form'), searchOptionSubmitter('form')]}
            onNewSearch={this.onNewSearch}
            shouldDisabledDate={this.state.controlDate}
            style={{ marginBottom: '10px' }}
            sdate={this.state.startDate}
            edate={this.state.endDate}
            queryType={'tobepaid'}
            searchInputPlaceholder={i18n.get('搜索编号、支付概要或提交人')}
            onSearch={this.onSearch}
            showBillType={false}
            onChange={this.handleFilterBarChange}
          />
        )}
        baseDataProperties={baseDataProperties}
        createAction={createActionColumn4Pay}
        isHiddencolumnChooser
      />
    )
  }
}
