import styles from '../audit.payment.module.less'
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import ReceiptTable from './receiptTable'
import MessageCenter from '@ekuaibao/messagecenter'
import { app as api } from '@ekuaibao/whispered'
import { connect } from '@ekuaibao/mobx-store'
import classnames from 'classnames'
import { getReceiptDataLinkEntities } from '../audit-action'
import ReceiptFilterBar from './receiptFilterBar'
import { cloneDeep, unionBy } from 'lodash'
import { showModal } from '@ekuaibao/show-util'
import { Tooltip } from '@hose/eui'
import { parseFields, parseSorters, parseFilter } from '@ekuaibao/lib/lib/entityUtil/entityTableUtil'
import { parseOptions } from '../util/tableUtil'
import {
  fetchReceiptSearch,
  fetchAccounts,
  getReceipt,
  downloadReceipt,
  printReceipt,
  unbindPaymentPlan,
  getReceiptBindedPlanId,
  printReceiptLine,
} from '../util/fetchUtil'
import moment from 'moment'
import { prepareRenderReceipt } from '../view-util'
const { parseColumns } = api.require('@elements/DataLinkTable/tableUtil')
const EKBIcon = api.require('@elements/ekbIcon')
import { Resource } from '@ekuaibao/fetch'
import { getV } from '@ekuaibao/lib/lib/help'
import { newTrack } from '../util/trackAudit'

const { getBoolVariation } = api.require('@lib/featbit')

const colorStyles = {
  color: 'rgb(244, 82, 107)',
}

const receiptResource = new Resource('/api/flow/v2/filter')
const scenesType = 'RECEIPT'
const isWx = window.__PLANTFORM__ === 'WEIXIN'

@connect(store => ({ size: store.states['@layout'].size }))
@EnhanceConnect(state => ({
  baseDataProperties: state['@common'].globalFields.data,
  dataLinkEntity: state['@audit'].dataLinkEntity,
  dynamicChannelMap: state['@audit'].dynamicChannelMap,
  userInfo: state['@common'].userinfo.data,
  BankReceiptPdfModel: state['@common'].powers.BankReceiptPdfModel,
}))
export default class Receipt extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
    this.state = {
      scenes: [],
      dataSource: {},
      columns: [],
      payers: [],
      fields: {
        tranDate: {
          value: [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
        },
        payerAccountNo: {
          value: undefined,
        },
        payChannel: {
          value: undefined,
        },
      },
      total: 0,
      failMessage: {},
      storageObject: {
        hasObj: false,
        hasAct: '',
        hasAccoutNo: undefined,
        payChannels: [],
      },
    }
    this.fieldMap = {}
    this.pathMap = {}
  }
  componentWillMount() {
    api.invokeService('@common:get:userinfo')
    this.initStorage()
  }

  async componentDidMount() {
    newTrack('entrance_audit_payment_receipt')
    this.bus.on('table:row:action', this.handleActions)
    api.dispatch(getReceiptDataLinkEntities())
    const accountRes = await fetchAccounts({
      filter: decodeURIComponent('(asPayer==true)'),
      start: 0,
      count: 2999,
    })
    // 获取场景列表
    const scenesRes = await receiptResource.GET('/$type', { type: scenesType })
    const mPayers = unionBy(accountRes?.items, 'accountNo')
    this.setState({
      payers: mPayers.filter(v => v.accountNo),
      scenes: scenesRes?.value?.filter.map(v => JSON.parse(v)),
    })
  }

  fnInitValue = storageObj => {
    if (!storageObj?.tranDate) {
      return [moment().subtract(1, 'days'), moment().subtract(1, 'days')]
    }
    return storageObj?.tranDate.length > 0 ? [moment(storageObj?.tranDate[0]), moment(storageObj?.tranDate[1])] : []
  }

  initStorage = () => {
    const storageObj = this.handleGetStorage()
    if (storageObj) {
      this.setState({
        fields: {
          tranDate: {
            value: this.fnInitValue(storageObj),
          },
          payerAccountNo: {
            value: storageObj?.payerAccountNo ? storageObj?.payerAccountNo : undefined,
          },
          payChannel: {
            value: storageObj?.payChannel ? storageObj?.payChannel : undefined,
          },
        },
        storageObject: {
          hasObj: true,
          hasAct: storageObj.active,
          hasAccoutNo: storageObj?.payerAccountNo,
        },
      })
    }
  }

  handleSetStorage = obj => {
    const { userInfo } = this.props
    const key = userInfo?.staff?.id + 'ReceiptFilterData'
    if (isWx) {
      session.set(key, JSON.stringify(obj))
    } else {
      localStorage.setItem(key, JSON.stringify(obj))
    }
  }

  handleGetStorage = () => {
    const { userInfo } = this.props
    const key = userInfo?.staff?.id + 'ReceiptFilterData'
    const oldStorage = isWx ? session.get(key) : localStorage.getItem(key)
    return oldStorage ? JSON.parse(oldStorage) : undefined
  }

  componentWillUnmount() {
    this.bus.un('table:row:action', this.handleActions)
  }

  handleUpdateFailMessage = messages => {
    const { failMessage } = this.state
    messages.map(message => {
      failMessage[message.billNo] = message.failMessage
    })
    this.setState({
      failMessage,
    })
  }

  handleActions = (type, line) => {
    console.log(type, line)
    const flag = getBoolVariation('pay-receipt-print-flag')

    switch (type) {
      case 'download':
        return downloadReceipt(line.dataLink.id)
      case 'print':
        return flag ? printReceiptLine(line.dataLink.id) : printReceipt([line.dataLink.id])
      case 'reacquire':
        return getReceipt([line.dataLink.id], res => {
          this.handleUpdateFailMessage(res)
          this.reloadAndClearSelecteds()
        })
      case 'bind':
        getReceiptBindedPlanId(line.dataLink.id).then(res => {
          newTrack('audit_payment_record_bind')
          return api.open('@audit:ReceiptBindPaymentPlanModal', { dataLink: line.dataLink, planIds: res }).then(() => {
            this.reloadAndClearSelecteds()
          })
        })
        return
      case 'unbind':
        return showModal.confirm({
          content: i18n.get('确定要解除回单与支付计划的绑定关系吗？'),
          okText: i18n.get('解绑'),
          cancelText: i18n.get('取消'),
          onOk: () => {
            newTrack('audit_payment_record_unbind')
            unbindPaymentPlan(line.dataLink.id).then(res => {
              this.reloadAndClearSelecteds()
            })
          },
        })
      case 'preview':
        const previewUrl = getV(line, 'dataLink.E_system_电子回单_预览地址.E_system_电子回单_webPreUrl')
        if (previewUrl) {
          return api.invokeService('@bills:check:baiwang:previewUrl', previewUrl).then(url => {
            if (this.props.BankReceiptPdfModel) {
              return api.open('@audit:PreviewByIframe', { url })
            }
            return api.emit('@vendor:open:link', url)
          })
        } else {
          console.log(line)
        }
    }
  }

  reloadAndClearSelecteds = () => {
    this.bus.clearSelectedRowKeys && this.bus.clearSelectedRowKeys()
    this.bus.reload && this.bus.reload()
  }

  handleReset = () => {
    this.setState({
      fields: {
        tranDate: {
          value: [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
        },
        payerAccountNo: {
          value: undefined,
        },
        payChannel: {
          value: undefined,
        },
      },
    })
  }

  renderReceiptAction = line => {
    const E_system_fileKey = getV(line, 'dataLink.E_system_fileKey')
    if (E_system_fileKey) {
      return (
        <>
          <a className="ant-dropdown-link mr-16" onClick={e => this.bus.emit('table:row:action', 'download', line)}>
            {i18n.get('下载')}
          </a>
          <a className="ant-dropdown-link mr-16" onClick={e => this.bus.emit('table:row:action', 'print', line)}>
            {i18n.get('打印')}
          </a>
        </>
      )
    }
    const previewUrl = getV(line, 'dataLink.E_system_电子回单_预览地址.E_system_电子回单_webPreUrl')
    if (previewUrl) {
      return (
        <a className="ant-dropdown-link mr-16" onClick={e => this.bus.emit('table:row:action', 'preview', line)}>
          {i18n.get('预览')}
        </a>
      )
    }
    return (
      <>
        <a
          className="ant-dropdown-link"
          style={this.state.failMessage[line.dataLink.E_system_回单编号] ? colorStyles : {}}
          onClick={e => this.bus.emit('table:row:action', 'reacquire', line)}
        >
          {i18n.get('重新获取文件')}
        </a>
        <Tooltip
          title={this.state.failMessage[line.dataLink.E_system_回单编号] || i18n.get('渠道未生成回单文件')}
          placement="bottomRight"
        >
          <span className="mr-16">
            <EKBIcon name={'#ico-7-question'} />
          </span>
        </Tooltip>
      </>
    )
  }

  formatColumns = data => {
    const { bus, entityInfoMap = {}, dataLinkEntity, dynamicChannelMap } = this.props
    if (data) {
      const { template, path } = data
      const type = 'DATA_LINK'
      const fields = parseFields({ res: template, type, dataLinkEntity })
      let { columns, fieldMap } = parseColumns({
        entityInfoMap,
        fields,
        bus,
        path,
        platformType: type,
        dataLinkEntity,
      })
      this.fieldMap = fieldMap
      this.pathMap = { ...path, entityId: 'entityId', E_system_fileKey: 'hasReceipt' }
      prepareRenderReceipt(columns, dynamicChannelMap)
      columns = [
        {
          title: i18n.get('序号'),
          dataIndex: 'serialNumber',
          filterType: false,
          key: 'serialNumber',
          label: i18n.get('序号'),
          sorter: false,
        },
        ...columns,
        {
          title: i18n.get('操作'),
          dataIndex: 'receipt.actions',
          className: 'actions-wrapper',
          width: 240,
          filterType: false,
          sorter: false,
          key: 'action',
          fixed: 'right',
          render: (text, line) => {
            return (
              <div
                className="actions"
                onClick={e => {
                  e.persist()
                  e.nativeEvent.stopImmediatePropagation()
                  e.stopPropagation()
                  e.preventDefault()
                  return false
                }}
              >
                {this.renderReceiptAction(line)}
                <a className="ant-dropdown-link mr-16" onClick={e => this.bus.emit('table:row:action', 'bind', line)}>
                  {i18n.get('绑定')}
                </a>
                {line.dataLink.E_system_单号_编号 ? (
                  <a
                    className="ant-dropdown-link"
                    style={colorStyles}
                    onClick={e => this.bus.emit('table:row:action', 'unbind', line)}
                  >
                    {i18n.get('解绑')}
                  </a>
                ) : null}
              </div>
            )
          },
        },
      ]
      this.setState({ columns })
    }
  }
  handleFormChange = changedFields => {
    this.setState(({ fields }) => ({
      fields: { ...fields, ...changedFields },
    }))
  }

  handleFilter = () => {
    this.bus.reload && this.bus.reload()
  }

  handleSearch = (params = {}) => {
    const { dataLinkEntity } = this.props
    console.log(dataLinkEntity, 'dataLinkEntity')
    // 由于界面上面显示的字段和传回到后台的字段名称不一致，为了不更改字段名称拷贝一份。。。
    const __options = cloneDeep(params)
    if (!!Object.keys(params.sorters).length || !!Object.keys(params.filters).length) {
      Object.keys(__options.filters).forEach(item => {
        if (typeof params.filters[item] === 'object') {
          params.filters[item] = params.filters[item][item]
        }
      })
      __options.sorters = parseSorters(params.sorters, this.pathMap)
      __options.filters = parseFilter(params.filters, this.pathMap)
    }
    __options.name = 'receipt'
    const query = parseOptions({
      options: __options,
      entityInfo: dataLinkEntity,
      fieldMap: this.fieldMap,
    })
    this.setState({
      query,
    })
    return this.handleFetch(query)
  }

  handleFetch = (query = {}) => {
    const { columns, fields } = this.state
    const startDate = moment(fields?.tranDate?.value[0]).startOf('day').valueOf()
    const endDate = moment(fields?.tranDate?.value[1]).endOf('day').valueOf()
    const filters = {
      payChannel: `payChannel=="${fields?.payChannel?.value}"`,
      payerAccountNo: `payerAccountNo=="${fields?.payerAccountNo?.value}"`,
      tranDate: `tranDate>= ${startDate} && tranDate<= ${endDate}`,
    }
    let filterBy = Object.keys(filters)
      ?.filter(key => fields[key]?.value?.toString())
      ?.map(key => filters[key])
    if (query.filterBy) {
      filterBy.push(query.filterBy)
    }
    filterBy.push('(form.E_system_借贷标志=="d" || form.E_system_借贷标志=="D")') // 过滤借贷的
    filterBy = filterBy.join('&&')
    return fetchReceiptSearch({ ...query, filterBy: filterBy ? `(${filterBy})` : undefined }, {}, result => {
      !columns.length && this.formatColumns(result)
      this.setState({ dataSource: result.data, total: result.total })
    }).then(res => {
      return res
    })
  }

  render() {
    const cls = classnames(
      styles.common_payment_wrapper,
      styles.payment_wrapper_layout5,
      styles.payment_wrapper,
      styles.receipt_container,
    )
    const { fields, storageObject } = this.state
    return (
      <div id="receipt-container" className={cls}>
        <ReceiptFilterBar
          onSearch={this.handleFilter}
          onStorage={this.handleSetStorage}
          payers={this.state.payers}
          onReset={this.handleReset}
          onChange={this.handleFormChange}
          dynamicChannelMap={this.props.dynamicChannelMap}
          storageObject={storageObject}
          {...fields}
        />
        <ReceiptTable
          {...this.state}
          {...this.props}
          onSearch={this.handleSearch}
          bus={this.bus}
          onUpdate={this.handleUpdateFailMessage}
          scenesType={scenesType}
        />
      </div>
    )
  }
}
