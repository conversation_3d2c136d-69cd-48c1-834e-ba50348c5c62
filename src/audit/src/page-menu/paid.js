/*
 * @Author: Onein
 * @Date: 2020-03-13 17:42:39
 * @Last Modified by: Onein
 * @Last Modified time: 2020-05-26 17:06:05
 */

import styles from '../audit.payment.module.less'
import React, { PureComponent } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import PaidTable from './paidTable'
import MessageCenter from '@ekuaibao/messagecenter'
import { connect } from '@ekuaibao/mobx-store'
import classnames from 'classnames'
import { getDataLinkEntities } from '../audit-action'
const EKBIcon = api.require('@elements/ekbIcon')
import { Resource } from '@ekuaibao/fetch'
import { newTrack } from '../util/trackAudit'

const paidResource = new Resource('/api/flow/v2/filter')
const scenesType = 'PAYMENTPLAN_PAID'

@connect(store => ({ size: store.states['@layout'].size }))
@EnhanceConnect(state => ({
  baseDataProperties: state['@common'].globalFields.data,
  dataLinkEntity: state['@audit'].dataLinkEntity,
}))
export default class Paid extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
    this.state = {
      dataSource: {},
      isExpend: false,
      scenes: [],
    }
  }
  async componentDidMount() {
    newTrack('entrance_audit_payment_paid')
    api.dataLoader('@common.channelList').load()
    api.dispatch(getDataLinkEntities({ id: 'system_paymentPlan' }))

    if (this.props.layer) {
      // 获取场景列表
      const scenesRes = await paidResource.GET('/$type', { type: scenesType })
      this.setState({
        scenes: scenesRes?.value?.filter.map(v => JSON.parse(v)),
      })
    }
  }

  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  handleChangeModalSize = isExpend => {
    const domArray = document.getElementsByClassName('ant-modal')
    let dom = domArray.length > 0 ? domArray[domArray.length - 1] : domArray[0]
    if (!dom) return
    if (isExpend) {
      dom.className = dom.className.replace(/ fullScreenModal/g, '')
    } else {
      dom.className = dom.className + ' fullScreenModal'
    }
    // 为配合实现单据多列的响应式布局，触发mbox上的windowSizeChange方法
    window.onresize(undefined)
    this.setState({ isExpend: !isExpend })
  }

  render() {
    const cls = classnames(styles.common_payment_wrapper, styles.payment_wrapper_layout5, styles.payment_wrapper)
    const inModal = !!this.props.layer
    const { isExpend, scenes } = this.state
    const iconName = isExpend ? '#EDico-compress1' : '#EDico-expend1'
    return (
      <>
        {inModal && (
          <div className="modal-header">
            <span class="flex">请选择绑定支付计划</span>
            <div class={styles.bindPaymentPlanHeader_actions}>
              <EKBIcon className="cross-icon" name={iconName} onClick={() => this.handleChangeModalSize(isExpend)} />
              <EKBIcon className="cross-icon" name="#EDico-close-default" onClick={this.handleModalClose} />
            </div>
          </div>
        )}
        <div id="payments" className={cls}>
          <PaidTable {...this.props} bus={this.bus} scenes={scenes} scenesType={scenesType} />
        </div>
      </>
    )
  }
}
