import React from 'react'
import { <PERSON>, Select, But<PERSON>, DatePicker, Row, Col } from 'antd'
import moment from 'moment'
import { cloneDeep, unionBy } from 'lodash'
import {
  fetchAccounts
} from '../util/fetchUtil'
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered';

const { RangePicker } = DatePicker
const dateFormat = 'YYYY-MM-DD'
let statusAct = ''  // state更新滞后，全局存昨日、最近7天、最近30天状态

@EnhanceConnect(state => ({
  KA_REEXCHANGE_PROCESSING: state['@common'].powers.KA_REEXCHANGE_PROCESSING
}))
class ReceiptFilterBarForm extends React.Component {
  state = {
    active: 1,
    payChannels: [],
  }

  async componentDidMount(){
    const accountRes = await fetchAccounts({
      filter: decodeURIComponent('(asPayer==true)'),
      start: 0,
      count: 300,
    })
    const mPayers = unionBy(accountRes?.items, 'accountNo')
    let ownPayers = mPayers.filter(v => v.accountNo)

    const { storageObject} = this.props
    if(storageObject.hasObj){
      this.setState({
        active: storageObject.hasAct,
        payChannels:(storageObject?.hasAccoutNo) ? ownPayers?.find(v => v.accountNo === storageObject?.hasAccoutNo)?.channels || []: []
      },()=>{
        this.handleSubmit()
      })
    }
  }

  handleSubmit = e => {
    e && e.preventDefault()
    const { active } = this.state
    this.props.form.validateFields((err, values) => {
      if (!err) {
        this.props.onSearch(values)
        const obj = cloneDeep(values)
        if(statusAct){  // 昨日、最近7天、最近30天状态缓存下
          let act = statusAct
          obj.active = act
          statusAct = ''
        }else{
          obj.active = active
        }
        this.props.onStorage(obj)
      }
    })
  }
  handleReset = () => {
    this.props.onReset()
    this.setState({
      active: 1,
    })
  }

  handleReExchange = () => {
    api.open('@audit:ReExchangeModal', {})
  }

  handleLink = type => {
    this.setState({
      active: type,
    })
    statusAct = type
    switch (type) {
      case 1:
        this.props.form.setFieldsValue({
          tranDate: [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
        })
        break
      case 7:
        this.props.form.setFieldsValue({
          tranDate: [moment().subtract(6, 'days'), moment()],
        })
        break
      case 30:
        this.props.form.setFieldsValue({
          tranDate: [moment().subtract(29, 'days'), moment()],
        })
        break
    }
    this.handleSubmit()
  }

  handleSelectChange = value => {
    console.log(value, this.props.payers)
    this.props.form.setFieldsValue({
      payChannel: undefined,
    })
    this.setState({
      payChannels: this.props.payers?.find(v => v.accountNo === value)?.channels || [],
    })
  }

  render() {
    const { getFieldDecorator } = this.props.form
    const { active, payChannels } = this.state
    const { dynamicChannelMap, KA_REEXCHANGE_PROCESSING } = this.props
    return (
      <Form id="receiptFilterBar" onSubmit={this.handleSubmit} layout="horizontal">
        <Row gutter={[15, 0]}>
          <Col span={12} style={{ textAlign: 'right' }}>
            <Form.Item label={i18n.get('付款账户')}>
              {getFieldDecorator('payerAccountNo')(
                <Select 
                  placeholder={i18n.get('请选择支付账户')}  
                  showSearch 
                  allowClear 
                  onChange={this.handleSelectChange}
                  optionFilterProp="children"
                  filterOption={(input, option) => {
                    return option.props.children.indexOf(input) >= 0
                  }}
                >
                  {this.props.payers.map(v => {
                    const name = v.name || v.accountName
                    return (
                      <Select.Option value={v.accountNo} key={v.accountNo}>
                        {`${name}(${v.accountNo})`}
                      </Select.Option>
                    )
                  })}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={12} style={{ textAlign: 'right' }}>
            <Form.Item label={i18n.get('支付方式')}>
              {getFieldDecorator('payChannel')(
                <Select placeholder={i18n.get('请选择支付方式')} allowClear>
                  {payChannels.map(v => {
                    return (
                      <Select.Option value={v} key={v}>
                        {dynamicChannelMap && dynamicChannelMap[v] && dynamicChannelMap[v].name}
                      </Select.Option>
                    )
                  })}
                </Select>,
              )}
            </Form.Item>
          </Col>

          <Col span={24} style={{ display: 'flex' }}>
            <Form.Item label={i18n.get('交易日期')} style={{ textAlign: 'left' }}>
              {getFieldDecorator('tranDate')(
                <RangePicker
                  format={dateFormat}
                  onChange={() => {
                    this.setState({
                      active: '',
                    })
                  }}
                />,
              )}
            </Form.Item>
            <Form.Item style={{ textAlign: 'right', flex: 1 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div className="span-link-btns">
                  <span className={active == 1 && 'active-btn'} onClick={() => this.handleLink(1)}>
                    {i18n.get('昨日')}
                  </span>
                  <span className={active == 7 && 'active-btn'} onClick={() => this.handleLink(7)}>
                    {i18n.get('最近7天')}
                  </span>
                  <span
                    className={active == 30 && 'active-btn'}
                    onClick={() => this.handleLink(30)}>
                    {i18n.get('最近30天')}
                  </span>
                </div>
                <div className="span-link-btns">
                  {
                    KA_REEXCHANGE_PROCESSING && <span style={{ marginRight: '15px' }} className="active-btn" onClick={this.handleReExchange}>
                      {i18n.get('退汇管理')}
                    </span>
                  }
                  <Button type="primary" htmlType="submit">
                    {i18n.get('查询')}
                  </Button>
                  <Button style={{ marginLeft: '15px' }} onClick={this.handleReset}>
                    {i18n.get('重置')}
                  </Button>
                </div>
              </div>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    )
  }
}

const ReceiptFilterBar = Form.create({
  name: 'receipt_filter_bar',
  onFieldsChange(props, changedFields) {
    props.onChange(changedFields)
  },
  mapPropsToFields(props) {
    return {
      tranDate: Form.createFormField({
        ...props.tranDate,
        value: props.tranDate.value,
      }),
      payerAccountNo: Form.createFormField({
        ...props.payerAccountNo,
        value: props.payerAccountNo.value,
      }),
      payChannel: Form.createFormField({
        ...props.payChannel,
        value: props.payChannel.value,
      }),
    }
  },
})(ReceiptFilterBarForm)


export default ReceiptFilterBar
