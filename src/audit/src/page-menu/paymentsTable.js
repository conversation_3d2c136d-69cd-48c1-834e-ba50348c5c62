import styles from '../payment/PendingPayingTable.module.less'
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'
import PayingPendingTableHead from '../elements/pending-paying-table-head'
import { fetchPayPlanProcessingInfo } from '../util/fetchUtil'
const { parseColumns } = api.require('@elements/DataLinkTable/tableUtil')
import { showMessage } from '@ekuaibao/show-util'
import * as viewUtil from '../view-util'
import { getYearFirstDay } from '../util/Utils'
import moment from 'moment'
const withLoader = api.require('@elements/data-grid-v2/withLoader')
const PayingTable = withLoader(() => import('../payment/PayingTable'))

function tableColumns() {
  const documentWidth = document.body.getBoundingClientRect().width
  return [
    {
      title: i18n.get('批次号'),
      dataIndex: 'channelTradeNo',
      width: documentWidth > 1952 ? '16%' : 180,
      render: viewUtil.channelTradeNoText.bind(this),
    },
    {
      title: i18n.get('付款账户'),
      dataIndex: 'account.accountName',
      width: documentWidth > 1952 ? '14%' : 140,
    },
    {
      title: i18n.get('付款账户类别'),
      dataIndex: 'account.sort',
      width: documentWidth > 1952 ? '14%' : 140,
      render: viewUtil.tdSort.bind(this),
    },
    {
      title: i18n.get('支付方式'),
      width: documentWidth > 1952 ? '9%' : 110,
      dataIndex: 'channel',
      render: viewUtil.tdChannel.bind(this),
    },
    {
      title: i18n.get('支付用途'),
      width: documentWidth > 1952 ? '9%' : 110,
      dataIndex: 'purpose',
      render: viewUtil.tdPurpose.bind(this),
    },
    {
      title: i18n.get('支付总金额'),
      width: documentWidth > 1952 ? '9%' : 110,
      dataIndex: 'balance',
      render: viewUtil.tdAmount.bind(this),
    },
    {
      title: i18n.get('发起支付时间'),
      dataIndex: 'createTime',
      width: documentWidth > 1952 ? '16%' : 150,
      render: viewUtil.tdDateTime.bind(this),
    },
    {
      title: i18n.get('操作'),
      width: documentWidth > 1952 ? '24%' : 256,
      dataIndex: 'actions',
      className: 'actions-wrapper',
      render: viewUtil.paymentBatchAction('payPlan'),
    },
  ]
}

function secondTableColumns() {
  const documentWidth = document.body.getBoundingClientRect().width
  let secondTableColumns = [
    {
      title: i18n.get('编号'),
      label: i18n.get('编号'),
      dataIndex: 'code',
      filterType: 'text',
      width: documentWidth > 1952 ? '9%' : 180,
    },
    {
      title: i18n.get('支付摘要'),
      label: i18n.get('支付摘要'),
      dataIndex: 'comment',
      filterType: 'text',
      width: documentWidth > 1952 ? '14%' : 140,
    },
    {
      title: i18n.get('生成日期'),
      label: i18n.get('生成日期'),
      dataIndex: 'createTime',
      filterType: 'date',
      width: documentWidth > 1952 ? '14%' : 140,
    },
    {
      title: i18n.get('支付金额'),
      label: i18n.get('支付金额'),
      filterType: 'money',
      dataIndex: 'createMoneyObj.standard',
      width: documentWidth > 1952 ? '9%' : 110,
    },
    {
      title: i18n.get('币种'),
      label: i18n.get('币种'),
      filterType: 'text',
      dataIndex: 'currency',
      width: documentWidth > 1952 ? '9%' : 110,
    },
    {
      title: i18n.get('收款账号类别'),
      label: i18n.get('收款账号类别'),
      dataIndex: 'payee.sort',
      width: documentWidth > 1952 ? '9%' : 110,
    },
    {
      title: i18n.get('收款账号性质'),
      label: i18n.get('收款账号性质'),
      dataIndex: 'payee.type',
      width: documentWidth > 1952 ? '9%' : 150,
    },
    {
      title: i18n.get('收款信息'),
      label: i18n.get('收款信息'),
      dataIndex: 'payee',
      filterType: 'text',
      width: documentWidth > 1952 ? '16%' : 150,
    },
    {
      title: i18n.get('摘要'),
      label: i18n.get('摘要'),
      dataIndex: 'remark',
      filterType: 'text',
      width: documentWidth > 1952 ? '16%' : 150,
    },
    {
      title: i18n.get('渠道批次号'),
      label: i18n.get('渠道批次号'),
      dataIndex: 'channelTradeNo',
      filterType: 'text',
      width: documentWidth > 1952 ? '16%' : 150,
    },
    {
      title: i18n.get('支付状态'),
      label: i18n.get('支付状态'),
      dataIndex: 'state',
      width: documentWidth > 1952 ? '9%' : 150,
    },
  ]
  return secondTableColumns
}

@EnhanceConnect(state => ({
  dynamicChannelMap: state['@audit'].dynamicChannelMap,
}))
export default class Payments extends PureComponent {
  tableColumns = tableColumns.call(this).map(c => {
    c.sorter = false
    return c
  })

  secondTableColumns = secondTableColumns.call(this).map(c => {
    c.sorter = true
    return c
  })

  constructor(props) {
    super(props)
    this.state = {
      searchText: '',
      secondColumns: [],
      dataSource: [],
      size: 20,
      total: 0,
      current: 1,
      controlDate: false,
      startDate: getYearFirstDay(),
      endDate: moment().format('YYYY-MM-DD') + ' 23:59:59',
    }
  }

  shouldComponentUpdate(nextProps, nextState) {
    for (let i in nextProps) {
      if (nextProps[i] !== this.props[i]) {
        return i !== 'size'
      }
    }
    for (let i in nextState) {
      if (nextState[i] !== this.state[i]) {
        return true
      }
    }
    return false
  }

  componentDidMount() {
    api.invokeService('@audit:get:ppayment:plans:control').then(res => {
      this.setState({ controlDate: res.value })
    })
    this.getPaymentsInfo({ searchValue: this.state.searchText })
    this.formatColumns()
  }

  getPaymentsInfo = params => {
    let { current, size, searchValue } = params
    size ?? (size = this.state.size)
    if (!current) {
      current = 1
      this.setState({ current: 1 })
    }

    const fetchData = {}
    fetchData.start = (current - 1) * size
    fetchData.count = size
    fetchData.searchValue = searchValue

    fetchPayPlanProcessingInfo(fetchData).then(
      result => {
        this.setState({ dataSource: result.items, total: result.count })
      },
      err => {
        showMessage.error(err.message)
      },
    )
    setTimeout(() => {
      if (this.instance) {
        this.instance.resize()
        const scrollable = this.instance.getScrollable()
        if (scrollable) {
          scrollable.option('rowRenderingMode', 'standard')
        }
      }
    }, 500)
  }

  formatColumns = () => {
    const secondColumns = this.secondTableColumns
    viewUtil.prepareRender(secondColumns)
    this.setState({ secondColumns })
  }

  handleSearch = value => {
    this.setState({ searchText: value }, () => {
      this.getPaymentsInfo({ start: 0, count: this.state.size, searchValue: value })
    })
  }

  getInstance = instance => {
    this.instance = instance
  }

  handlePaginationChange = pagination => {
    this.setState({ current: pagination.current, size: pagination.size }, () => {
      this.getPaymentsInfo({ current: pagination.current, size: pagination.size })
    })
  }
  handleFilterBarChange = date => {
    this.setState({ startDate: date.sdate, endDate: date.edate })
  }
  render() {
    const { dataSource, secondColumns, size, total, current } = this.state
    return (
      <div className={styles['pennding-approve-expense-bills-view']}>
        <div className="table-def">
          <PayingPendingTableHead
            {...this.props}
            searchPlaceholder={i18n.get('输入搜索批次号、编号、支付概要')}
            isShowRightBindButton
            shouldDisabledDate={this.state.controlDate}
            startDate={this.state.startDate}
            endDate={this.state.endDate}
            handleFilterBarChange={this.handleFilterBarChange}
            onSearch={this.handleSearch}
          />
          <PayingTable
            from="payments"
            rowKey="id"
            isNeedHeight={false}
            dataSource={dataSource}
            columns={this.tableColumns}
            getInstance={this.getInstance}
            secondTableColumns={secondColumns}
            paginationProps={{
              totalLength: total,
              pagination: { current, size },
            }}
            onPaginationChange={this.handlePaginationChange}
          />
        </div>
      </div>
    )
  }
}
