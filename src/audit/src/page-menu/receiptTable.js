import React, { PureComponent } from 'react'
import { app as api } from '@ekuaibao/whispered'
const LoaderWithLegacyData = api.require('@elements/data-grid-v2/LoaderWithLegacyData')
import { exportReceiptExcel, downloadReceipt, getReceipt, printReceipt } from '../util/fetchUtil'
import MessageCenter from '@ekuaibao/messagecenter'
import { EnhanceConnect } from '@ekuaibao/store'
const { exportExcel } = api.require('@lib/export-excel-service');
import { showMessage } from '@ekuaibao/show-util'
import { Resource } from '@ekuaibao/fetch'
import { newTrack } from '../util/trackAudit';

const filter = new Resource('/api/flow/v2/filter')

@EnhanceConnect(state => ({
  specifications: state['@custom-specification'].specificationGroupsList,
  dynamicChannelMap: state['@audit'].dynamicChannelMap,
}))
export default class PayingWrapper extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
  }

  componentDidMount() {
    this.bus.on('buttons:click', this.handleButtonsClick)
  }

  componentWillUnmount() {
    this.bus.un('buttons:click', this.handleButtonsClick)
  }

  buttons = [
    { text: i18n.get('回单下载'), name: 'download', type:'primary' },
    { text: i18n.get('打印'), name: 'print' },
    { text: i18n.get('导出数据'), name: 'export' },
    { text: i18n.get('重新获取文件'), name: 'reacquire' },
  ]

  reloadAndClearSelecteds = () => {
    this.bus.clearSelectedRowKeys && this.bus.clearSelectedRowKeys()
    this.bus.reload && this.bus.reload()
  }

  handleButtonsClick = ({ params, name, data, keys }, fetchParam) => {
    keys = keys ? keys : this.bus.getSelectedRowKeys();
    data = data ? data : this.bus.getSelectedRowData();
    console.log(params, name, data, keys, 'params, name, data, keys')
    // const fileKeys = Object.values(data).map(v => v.dataLink.E_system_fileKey)
    const fileIds = Object.values(data).map(v => v.dataLink.id)
    switch (name) {
      case 'download':
        return downloadReceipt(fileIds, () => {
          this.reloadAndClearSelecteds()
        })
      case 'print':
        return printReceipt(fileIds, () => {
          this.reloadAndClearSelecteds()
        })
      case 'export':
        return exportReceiptExcel(keys, () => {
          this.reloadAndClearSelecteds()
        })
      case 'reacquire':
        keys = Object.values(data).filter(v => !v.dataLink.E_system_fileKey).map(v => v.dataLink.id)
        if (keys.length <= 0) {
          return showMessage.error(i18n.get('渠道已生成回单文件，可直接下载或打印！'))
        }
        return getReceipt(keys, (res) => {
          newTrack('audit_payment_record_reacquire')
          this.props.onUpdate(res)
          this.reloadAndClearSelecteds()
        })
    }
  }

  render() {
    const { baseDataProperties, onSearch, columns, scenes, scenesType } = this.props
    const defaultColumns = scenes?.length > 0  && scenes[0].defaultColumns.length >0 ? scenes[0].defaultColumns : columns.map(v => v.dataIndex)
    const curScenes =  [{ "text": "全部", "scene": "all", "active": true, "sceneIndex": "all", "defaultColumns": defaultColumns }]
    return (
      <LoaderWithLegacyData
        columns={columns}
        isNeedHeight={false}
        rowKey="dataLink.id"
        searchPlaceholder={i18n.get('搜索单号、回单编号')}
        scenes={curScenes}
        resource={filter}
        disabledScenes
        fetch={onSearch}
        buttons={this.buttons}
        onButtonClick={this.handleButtonsClick}
        bus={this.bus}
        baseDataProperties={baseDataProperties}
        columnMinWidth={80}
        scenesType={scenesType}
        isOnlyShowSaveDiff={true}
      />
    )
  }
}
