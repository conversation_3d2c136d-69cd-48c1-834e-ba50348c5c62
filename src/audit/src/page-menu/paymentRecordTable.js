import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import PaymentTable from '../record-table/payment-record-table'
import { app as api } from '@ekuaibao/whispered'
import * as actions from '../audit-action'
import './payplan-paymentRecordTable.less'
import { fetchPayPlanRecordInfo } from '../util/fetchUtil'
const { parseColumns } = api.require('@elements/DataLinkTable/tableUtil')
import { connect } from '@ekuaibao/mobx-store'
import { showMessage } from '@ekuaibao/show-util'
import { prepareRender } from '../view-util'
import { parseOptions } from '../util/tableUtil'
import { parseFields, parseSorters, parseFilter } from '@ekuaibao/lib/lib/entityUtil/entityTableUtil'
import { getV } from '@ekuaibao/lib/lib/help'
import { createActionColumn4Paid } from '../util/columnsAndSwitcherUtil'
import { getYearFirstDay } from '../util/Utils'
import moment from 'moment'
import { get } from 'lodash'
const filterKeyName = 'E_system_paymentRecord_支付结果' //@i18n-ignore
const filterDateKeyName = 'E_system_paymentRecord_发起支付日期' //@i18n-ignore
const PAYMENTSTATE = ['SUCCESS', 'FAILURE']
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')
import { openNewBillInfoDrawer } from './new-bill-info-drawer'

const { injectErrorDom } = api.require('@elements/LoadingError')

@connect(store => ({ size: store.states['@layout'].size }))
@EnhanceConnect(state => ({
  dynamicChannelMap: state['@audit'].dynamicChannelMap,
  channelList: state['@audit'].channelList,
}))
export default class PaymentRecordTable extends PureComponent {
  _LAST_CLICK_ROW_INDEX = null

  hideErrorUI = null
  retryFn = null
  retryFnWrapper = fn => {
    const retryFn = async (...args) => {
      this.retryFn = () => {
        return retryFn(...args)
      }
      this.clearErrorUI()
      this.instance && this.instance.beginCustomLoading()
      try {
        await fn.call(this, ...args)
        this.clearErrorUI()
        this.retryFn = null
        this.setState({ loadingError: false })
      } catch (error) {
        this.setState({ loadingError: true }, () => {
          this.showErrorUI()
        })
      } finally {
        this.instance && this.instance.endCustomLoading()
      }
    }
    return retryFn
  }

  constructor() {
    super()
    this.state = {
      queryType: 'payment', //record:历史记录 operation:操作纪录
      pagination: { current: 1, pageSize: 10 },
      sdate: getYearFirstDay(),
      edate: moment().format('YYYY-MM-DD') + ' 23:59:59',
      payState: 'all',
      searchText: '',
      defaultSorter: { order: 'DESC', field: 'E_system_paymentRecord_支付完成日期' }, //@i18n-ignore
      columns: [],
      dataSource: [],
      paymentState: PAYMENTSTATE,
      loadingError: false,
    }
    this.initPage = { current: 1, pageSize: 20 }
  }

  formatColumns = data => {
    const { bus, entityInfoMap = {}, dataLinkEntity, dynamicChannelMap, channelList, from } = this.props
    const { template, path } = data
    const type = 'DATA_LINK'
    const fields = parseFields({ res: template, type, dataLinkEntity })
    let { columns, fieldMap } = parseColumns({
      entityInfoMap,
      fields,
      bus,
      path,
      platformType: type,
      dataLinkEntity,
    })
    if (from === 'auditPayment') {
      const action = createActionColumn4Paid('RECORD', from)
      columns = [...columns, action]
    }
    prepareRender(columns, dynamicChannelMap, channelList)
    this.fieldMap = fieldMap
    this.pathMap = { ...path, entityId: 'entityId' }
    return columns
  }

  componentDidMount = () => {
    const { bus } = this.props
    bus?.on('audit-payment:tab:change:history', this.tabChange)
    this.tabChange()
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus?.un('audit-payment:tab:change:history', this.tabChange)
  }

  tabChange = () => {
    api
      .dataLoader('@common.channelList')
      .load()
      .then(_ => {
        this.handlePaymentTableChange(this.state.pagination)
      })
  }

  _fnSetPaymentState = filter => {
    if (!!filter?.[filterKeyName]) {
      return filter[filterKeyName] === 'all' ? PAYMENTSTATE : [filter[filterKeyName]]
    } else {
      return this.state.paymentState
    }
  }

  handlePaymentTableChange = this.retryFnWrapper((
    pagination = this.initPage,
    filter = {},
    sorter = {},
    isSearch = false,
    fresh = false,
  ) => {
    const { sdate, edate, defaultSorter, searchText } = this.state
    const { dataLinkEntity, dynamicChannelMap = {}, from } = this.props
    const paymentState = this._fnSetPaymentState(filter)
    if (filter.date) {
      this.setState({ sdate: filter.date.sdate, edate: filter.date.edate })
    }
    this.setState({ pagination, paymentState })
    const params = {
      page: { ...pagination },
      filterTimes: [
        {
          field: 'form.E_system_paymentRecord_支付完成日期', //@i18n-ignore
          value: [moment(filter?.date?.sdate ?? sdate).valueOf(), moment(filter?.date?.edate ?? edate).valueOf()],
        },
      ],
      order: defaultSorter,
    }
    params.state = paymentState
    if (isSearch || searchText) {
      const temp = filter.custom || filter.custom === '' ? filter.custom : searchText
      params.searchText = temp
      this.setState({ searchText: temp })
    }
    const map = {}
    if (sorter.field) {
      map[getV(sorter, 'column.property.name', sorter.field)] = sorter.order
    }
    if (filter.date) delete filter.date
    if (filter.custom) delete filter.custom
    const channelKey = 'dataLink.E_system_paymentRecord_支付方式'
    const active = dynamicChannelMap['CHANPAYV2']?.active
    if (filter[channelKey] && active) {
      //兼容新旧银企联数据查询
      if (filter[channelKey].includes('CHANPAYV2') && !filter[channelKey].includes('CHANPAY')) {
        filter[channelKey].push('CHANPAY')
      } else if (filter[channelKey].includes('CHANPAY')) {
        const index = filter[channelKey].indexOf('CHANPAY')
        filter[channelKey].splice(index, 1)
      }
    }
    const __options = { sorters: map, filters: filter }
    if (!!Object.keys(__options.sorters).length || !!Object.keys(__options.filters).length) {
      __options.sorters = parseSorters(__options.sorters, this.pathMap)
      __options.filters = parseFilter(__options.filters, this.pathMap)
      if (filter[filterKeyName] === 'all') delete __options.filters['form.' + filterKeyName]
      Object.keys(__options.filters).forEach(key => {
        if (key.endsWith('日期')) {
          //@i18n-ignore
          params.filterTimes.push({
            field: key,
            value: [
              ...__options.filters[key].map(value => {
                return typeof value === 'object' ? value.format('x') : value
              }),
            ],
          })
          delete __options.filters[key]
        }
      })
    }
    const query = parseOptions({
      options: __options,
      entityInfo: dataLinkEntity,
      fieldMap: this.fieldMap,
    })
    if (filter[filterKeyName]) {
      this.setState({ payState: filter[filterKeyName] })
    }
    return fetchPayPlanRecordInfo(query, params, from).then(
      result => {
        const { pagination, columns } = this.state
        pagination.total = result.total

        let _colummns = columns
        if (!columns.length) {
          _colummns = this.formatColumns(result)
        }

        this.setState({ dataSource: result.data, pagination, columns: _colummns })
      }
    )
  })

  rowColorRender = (index, color) => {
    const rows = document.querySelectorAll(`tr[aria-rowindex="${index + 1}"]`)
    if (!rows && rows.length <= 0) {
      return
    }
    rows.forEach(row => {
      if (!row) {
        return
      }
      row.style.backgroundColor = color
    })
  }

  getFlowId = (item) => {
    return get(item, 'dataLink.E_system_paymentRecord_flowId')
  }

  handleRowClick = async data => {
    const currentIndex = this.state.dataSource.findIndex(item => item.sourceId === data.sourceId)
    this.rowColorRender(this._LAST_CLICK_ROW_INDEX, 'unset')
    this._LAST_CLICK_ROW_INDEX = currentIndex
    this.rowColorRender(currentIndex, 'var(--eui-fill-hover)')
    if (data.dataLink.E_system_paymentRecord_flowId.split(',').length > 1) {
      showMessage.error(i18n.get('该记录为合并支付记录，不支持查看关联单据。'))
    } else {
      const flowId = this.getFlowId(data)
      startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()

      if (api.require('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
        openNewBillInfoDrawer(flowId, {
          flows: this.state.dataSource?.map(v => ({
            id: this.getFlowId(v),
          })) || [],
          bus: this.bus,
        })
        return
      }

      const action = await actions.getFlowInfo(flowId, null, true)
      api.dispatch(action).then(flow => {
        api.open(
          '@bills:BillInfoPopup',
          {
            title: i18n.get('支付记录'),
            backlog: { id: -1, flowId: flow },
            onFooterButtonsClick: this.handlePrint,
            invokeService: '@audit:get:history-flow:info',
            noCheckPermissions: true,
            params: flow.flowId,
            mask: false,
          },
          true,
        )
      })
    }
  }

  waitForInstance = () => {
    return new Promise(resolve => {
      const timer = setInterval(() => {
        if (this.instance) {
          clearInterval(timer)
          resolve(this.instance)
        }
      }, 100)
    })
  }

  showErrorUI() {
    setTimeout(async () => {
      if (!this.instance) {
        this.instance = await this.waitForInstance()
      }

      const container = this.instance.element?.()?.querySelector('.dx-datagrid-rowsview')
      this.hideErrorUI = injectErrorDom(container, {
        refreshFn: () => {
          this.clearErrorUI()
          console.log(this.retryFn, 'retryfn')
          return this.retryFn?.()
        }
      })
    }, 500)
  }

  clearErrorUI() {
    if (this.hideErrorUI) {
      this.hideErrorUI?.()
      this.hideErrorUI = null
    }
  }

  getInstance = instance => {
    this.instance = instance
  }

  render() {
    const { dataSource, columns } = this.state
    return (
      <div className="payplan-paymentRecordTable ovr-h">
        <PaymentTable
          rowKey="dataLink.id"
          filterKeyName={filterKeyName}
          filterDateKeyName={filterDateKeyName}
          dataSource={dataSource}
          isNeedHeight={false}
          columns={columns}
          {...this.props}
          searchInputPlaceholder={i18n.get('搜索编号或批次号')}
          payResultLabel={i18n.get('支付结果')}
          sdate={this.state.sdate}
          edate={this.state.edate}
          pagination={this.state.pagination}
          queryType={this.state.queryType}
          searchText={this.state.searchText || ''}
          payState={this.state.payState}
          onRowClick={this.handleRowClick.bind(this)}
          onTableChange={this.handlePaymentTableChange.bind(this)}
          getInstance={this.getInstance.bind(this)}
          loadingError={this.state.loadingError}
        />
      </div>
    )
  }
}
