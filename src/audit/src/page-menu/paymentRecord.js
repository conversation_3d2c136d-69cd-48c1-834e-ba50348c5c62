/*
 * @Author: Onein
 * @Date: 2020-03-13 17:42:39
 * @Last Modified by: Onein
 * @Last Modified time: 2020-04-27 19:02:26
 */

import styles from '../audit.payment.module.less'
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import PaymentRecordTable from './paymentRecordTable'
import MessageCenter from '@ekuaibao/messagecenter'
import { connect } from '@ekuaibao/mobx-store'
import classnames from 'classnames'
import { getDataLinkEntities } from '../audit-action'
import { app as api } from '@ekuaibao/whispered'
import { newTrack } from '../util/trackAudit'

@connect(store => ({ size: store.states['@layout'].size }))
@EnhanceConnect(state => ({
  baseDataProperties: state['@common'].globalFields.data,
  dataLinkEntity: state['@audit'].dataLinkEntity,
}))
export default class PaymentRecord extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
    this.state = {}
  }

  componentDidMount() {
    newTrack('entrance_audit_payment_record')
    api.dispatch(getDataLinkEntities({ id: 'system_paymentRecord' }))
  }

  render() {
    const cls = classnames(styles.common_payment_wrapper, styles.payment_wrapper_layout5, styles.payment_wrapper)
    return (
      <div id="payment-record" className={cls}>
        <PaymentRecordTable {...this.props} bus={this.bus} />
      </div>
    )
  }
}
