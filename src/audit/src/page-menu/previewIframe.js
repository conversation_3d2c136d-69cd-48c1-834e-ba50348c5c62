import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import React from 'react'
import { app } from '@ekuaibao/whispered'
import { fetchBlobUrl } from '../util/Utils'
const EKBIcon = app.require('@elements/ekbIcon')
import './previewByIframe.less'
@EnhanceModal({
  title: '',
  footer: [],
  className: 'respond-modal-layer',
})
export default class PreviewByIframe extends React.PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      _url: '',
    }
  }
  componentDidMount = async () => {
    const res = await fetchBlobUrl({ url: this.props.url })
    this.setState({
      _url: res,
    })
  }
  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  render() {
    return (
      <div
        className="auto-merger-plan-model"
        onContextMenu={e => {
          e.preventDefault()
        }}>
        <div className="modal-header">
          <div className="select-payment-modal-header-title">{i18n.get('预览')}</div>
          <EKBIcon
            className="cross-icon"
            name="#EDico-close-default"
            onClick={this.handleModalClose.bind(this)}
          />
        </div>
        <div className="model-content">
          <iframe id="pdfIframe" src={this.state._url} frameBorder="0" width="100%" height="100%" />
        </div>
      </div>
    )
  }
}
