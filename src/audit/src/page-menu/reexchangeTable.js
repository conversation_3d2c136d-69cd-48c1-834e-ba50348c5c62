import React, { PureComponent } from 'react'
import { app as api } from '@ekuaibao/whispered'
import styles from '../audit.payment.module.less'
const LoaderWithLegacyData = api.require('@elements/data-grid-v2/LoaderWithLegacyData')
import MessageCenter from '@ekuaibao/messagecenter'
import { getFlowInfo } from '../audit-action'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import { fetchPayPlanInfo, exportPayPlanExcel, bindPaymentPlan } from '../util/fetchUtil'
const { parseColumns } = api.require('@elements/DataLinkTable/tableUtil')
import { parseOptions } from '../util/tableUtil'
import { parseFields, parseSorters, parseFilter } from '@ekuaibao/lib/lib/entityUtil/entityTableUtil'
import { EnhanceConnect } from '@ekuaibao/store'
import { cloneDeep, get } from 'lodash'
import * as viewUtil from '../view-util'
import { Resource } from '@ekuaibao/fetch'
import { showMessage } from '@ekuaibao/show-util'
import { searchOptionPayPlanCodeAndName, searchOptionSubmitter } from "../util/Utils";
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')
import { openNewBillInfoDrawer } from './new-bill-info-drawer'

const paidResource = new Resource('/api/flow/v2/filter')

@EnhanceConnect(state => ({
  specifications: state['@custom-specification'].specificationGroupsList,
  dynamicChannelMap: state['@audit'].dynamicChannelMap,
  channelList: state['@audit'].channelList
}))
export default class PaidTable extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
    this.pathMap = {}
    this.fieldMap = {}
    this.state = {
      columns: [],
    }
  }

  formatColumns = data => {
    const { bus, entityInfoMap = {}, dataLinkEntity, dynamicChannelMap, channelList } = this.props
    const { template, path } = data
    const type = 'DATA_LINK'
    const fields = parseFields({ res: template, type, dataLinkEntity })
    const { columns, fieldMap } = parseColumns({
      entityInfoMap,
      fields,
      bus,
      path,
      platformType: type,
      dataLinkEntity
    })
    viewUtil.prepareRender(columns, dynamicChannelMap, channelList)
    this.fieldMap = fieldMap
    this.pathMap = { ...path, entityId: 'entityId' }
    this.setState({ columns })
  }

  componentDidMount() {
    this.bus.on('buttons:click', this.handleButtonsClick)
    if (!this.props.layer) {
      this.bus.on('table:row:click', this.handleTableRowClick)
    }
  }

  componentWillUnmount() {
    this.bus.un('buttons:click', this.handleButtonsClick)
    if (!this.props.layer) {
      this.bus.un('table:row:click', this.handleTableRowClick)
    }
  }

  fetchPaid = async (params = {}, dimensionItems = {}) => {
    const { dataLinkEntity, layer, dataLink, enhancer } = this.props

    // 由于界面上面显示的字段和传回到后台的字段名称不一致，为了不更改字段名称拷贝一份。。。
    const __options = cloneDeep(params)
    if (!!Object.keys(params.sorters) || !!Object.keys(params.filters)) {
      Object.keys(__options.filters).forEach(item => {
        if (typeof params.filters[item] === 'object') {
          params.filters[item] = params.filters[item][item]
        }
      })
      __options.sorters = parseSorters(params.sorters, this.pathMap)
      __options.filters = parseFilter(params.filters, this.pathMap)
    }
    // params = { ...param, ...__options ,limit: { start: params.page.currentPage, count: params.page.pageSize } }
    const query = parseOptions({
      options: __options,
      entityInfo: dataLinkEntity,
      fieldMap: this.fieldMap
    })
    const defaultSort = !!layer && !!dataLink.E_system_单号_编号 ? { "orderBy": [{ "value": `form.E_system_paymentPlan_回单编号.contains("${dataLink?.E_system_回单编号}")`, "order": "DESC" }] } : {}
    const res = await fetchPayPlanInfo({ ...defaultSort, ...query }, { READY: 'REEXCHANGE', isOperator: enhancer !== 'modal' }, result => {
      this.formatColumns(result)
    })
    this._currentDataSource = res.dataSource
    return res
  }

  handleButtonsClick = ({ name, data, keys }) => {
    switch (name) {
      case 'export':
        return exportPayPlanExcel(keys, 'REEXCHANGE')
      case 'confirm':
        return bindPaymentPlan({
          receiptId: this.props?.dataLink?.id,
          paymentPlanIds: keys
        }).then(res => {
          this.props.layer.emitOk({})
        })
    }
  }

  __billInfoPopup = flow => {
    let title = `${i18n.get(billTypeMap()[flow.formType])}${i18n.get('详情')}`
    const params = {
      title,
      invokeService: '@bills:get:flow-info',
      params: { id: flow.id },
      backlog: { id: -1, flowId: flow },
      noCheckPermissions: true,
      reload: this.bus.reload,
      scene: 'APPROVER',
    }
    api.open('@bills:BillInfoPopup', params, true)
  }

  getFlowId = (item) => {
    return get(item, 'dataLink.E_system_paymentPlan_flowId')
  }

  handleTableRowClick = async data => {
    if (data.dataLink.E_system_paymentPlan_来源 === 'MANUAL') {
      showMessage.error(i18n.get('该记录为合并支付记录，不支持查看关联单据。'))
    } else {
      const flowId = this.getFlowId(data)
      startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()

      if (api.require('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
        openNewBillInfoDrawer(flowId, {
          flows: this._currentDataSource?.map(v => ({
            id: this.getFlowId(v),
          })) || [],
          bus: this.bus,
        })
        return
      }

      const action = await getFlowInfo(flowId, null, true)
      api.dispatch(action).then(flow => {
        this.__billInfoPopup(flow)
      })
    }
  }

  buttons = [
    { text: i18n.get('导出'), name: 'export' }
  ]
  modalButtons = [
    { text: i18n.get('确定'), name: 'confirm', type:'primary' }
  ]

  render() {
    const { columns } = this.state
    const { baseDataProperties, enhancer, layer,scenes, scenesType, planIds} = this.props
    const defaultColumns = scenes?.length > 0  && scenes[0].defaultColumns.length >0 ? scenes[0].defaultColumns : columns.map(v => v.dataIndex)
    const curScenes =  [{ "text": "全部", "scene": "all", "active": true, "sceneIndex": "all", "defaultColumns": defaultColumns }]
    return (
      <LoaderWithLegacyData
        newSearch={true}
        searchClassname={styles['reexchangeTable-search']}
        searchOptions={[searchOptionPayPlanCodeAndName('form'), searchOptionSubmitter('form')]}
        columns={columns}
        isNeedHeight={!!layer}
        searchPlaceholder={i18n.get('搜索编号、支付概要或提交人')}
        rowKey="dataLink.id"
        scenes={curScenes}
        resource={paidResource}
        disabledScenes
        fetch={this.fetchPaid}
        buttons={enhancer === 'modal' ? this.modalButtons : this.buttons}
        onButtonClick={this.handleButtonsClick}
        bus={this.bus}
        baseDataProperties={baseDataProperties}
        selectedRowKeys={planIds}
        scenesType={scenesType}
        isHiddencolumnChooser={!layer}
        isOnlyShowSaveDiff={!!layer}
      />
    )
  }
}
