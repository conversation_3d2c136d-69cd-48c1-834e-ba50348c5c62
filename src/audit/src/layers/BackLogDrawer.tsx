import React from 'react'
import BacklogDetail from '../BacklogDetail'
import {ILayerProps} from "@ekuaibao/enhance-layer-manager";

interface Props extends ILayerProps{
  onClose?: () => void
  backLogId: string
  [key: string]: any
}

export const BackLogDrawer: React.FC<Props> = ({ onClose, layer, ...others }) => {
  const handleClose = () => {
    onClose?.()
    layer.emitOk()
  }
  const handleChangeTitle = (backlog: any) => {
    const title = backlog?.flowId?.form?.specificationId?.name
    if (title && layer) {
      layer.override({ title: `${title}${i18n.get('详情')}` })
    }
  }
  return <BacklogDetail {...others} onChangeTitle={handleChangeTitle} onClose={handleClose} isBackLog={true} />
}
export default BackLogDrawer
