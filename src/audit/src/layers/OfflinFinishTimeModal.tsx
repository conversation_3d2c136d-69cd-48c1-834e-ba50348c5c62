import React, { useEffect, useState } from 'react'
import styles from './OfflinFinishTimeModal.module.less'
import { DatePicker, Input, Tooltip, Popover, Button, Form } from '@hose/eui'
import {
  OutlinedDirectionDown,
  OutlinedDirectionRefresh,
  OutlinedEditSearch,
  OutlinedTipsClose,
  OutlinedTipsInfo,
} from '@hose/eui-icons'
import { getCurrencybyBatcheId, getCurrencybyPayAccount, getCurrencyModifiable } from '../audit-action'
import Big from 'big.js'
import classNames from 'classnames'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import moment from 'moment'
import { app } from '@ekuaibao/whispered'

const FormItem = Form.Item

interface OfflinFinishTimeModalProps {
  layer?: any
  line?: any
}

const OfflinFinishTimeModal: React.FC<OfflinFinishTimeModalProps> = props => {
  const [form] = Form.useForm()
  const { layer, line } = props
  const currency = app.getState('@common').powers.Currency

  const [selectCurrency, setSelectCurrency] = useState<any>({})
  const [currencyList, setCurrencyList] = useState<any>([])
  const [originalList, setOriginalList] = useState<any>([])
  const [rate, setRate] = useState<any>(null)
  const [canChangeRate, setCanChangeRate] = useState<boolean>(true)
  const [currencySetInfo, setCurrencySetInfo] = useState<any>({})

  const is_en_US = i18n.currentLocale === 'en-US'

  useEffect(() => {
    fnGetCurrency()
  }, [])

  const fnGetCurrency = async () => {
    let currencySetInfo: any = {}
    let list: any = []
    if (line?.channel === 'OFFLINE' && currency) {
      const modifiableValue = await getCurrencyModifiable()
      setCanChangeRate(modifiableValue?.value)
      currencySetInfo = await getCurrencybyBatcheId({ batcheId: line?.id })
      list = await fnGetCurrencyList({ info: currencySetInfo })
      const i = list?.find((item: any) => item?.numCode === currencySetInfo?.paidCurrency?.standardNumCode)

      setCurrencyList(list)
      setOriginalList(list)
      setCurrencySetInfo(currencySetInfo)
      if (i) {
        const paidRate = currencySetInfo?.paidRate?.replace(/(\.\d*?)0+$/, '$1').replace(/\.$/, '')
        form.setFieldsValue({
          paidCurrencyCode: currencySetInfo?.paidCurrency?.standardNumCode,
          paidRate,
          paidAmount: currencySetInfo?.paidCurrency?.standard,
          paidOffLineFinishedTime: currencySetInfo?.paidOffLineFinishedTime
            ? moment(currencySetInfo?.paidOffLineFinishedTime)
            : moment(),
        })
        setRate(paidRate)
        fnCalculateAmount(paidRate, currencySetInfo)
        setSelectCurrency(i)
      } else {
        const firstCurrency = list?.[0]
        fnCalculateAmount(firstCurrency?.rate, currencySetInfo)
        setRate(firstCurrency?.rate)
        setSelectCurrency(firstCurrency)
        let params: any = {
          paidCurrencyCode: firstCurrency?.numCode,
          paidRate: firstCurrency?.rate,
          paidOffLineFinishedTime: currencySetInfo?.paidOffLineFinishedTime
            ? moment(currencySetInfo?.paidOffLineFinishedTime)
            : moment(),
        }
        if (currencySetInfo?.paidCurrencyType === 'useAmount') {
          params = { ...params, paidAmount: null }
        }
        form.setFieldsValue(params)
      }
    }
  }

  const fnGetCurrencyList = async ({ info, time }: any) => {
    const res = await getCurrencybyPayAccount({
      accountId: line?.account?.id,
      orignialId: info ? info?.standardCurrency?.standardNumCode : currencySetInfo?.standardCurrency?.standardNumCode,
      time: time || moment().valueOf(),
    })

    return res?.items || []
  }

  const fnCalculateAmount = (rate: any, info?: any) => {
    if (isNaN(Number(rate)) || Number(rate) < 0) {
      return
    }
    const standard = info ? info?.standardCurrency?.standard : currencySetInfo?.standardCurrency?.standard
    if (standard && Number(rate) > 0) {
      const amount = new Big(Number(standard)).div(Number(rate)).toFixed(2)
      form.setFieldValue('paidAmount', amount)
    }
  }

  const handleItemClick = (item: any) => {
    fnSetRateAndMoney(item?.rate)
    setSelectCurrency(item)
    form.setFieldValue('paidCurrencyCode', item?.numCode)
    if (!item?.rate) {
      form.setFieldsValue({ paidAmount: undefined })
    }
  }

  const fnSetRateAndMoney = (rate: any) => {
    fnCalculateAmount(rate)
    setRate(rate)
    form.setFieldValue('paidRate', rate)
  }

  const handleSearchChange = (e: any) => {
    const searchText = e.target.value
    const _searchText = searchText.trim().toLowerCase()
    if (_searchText) {
      const list = originalList.filter(
        (el: any) =>
          (el.name && !!~el.name.toLowerCase().indexOf(_searchText)) ||
          (el.strCode && !!~el.strCode.toLowerCase().indexOf(_searchText)),
      )
      setCurrencyList(list)
    } else {
      setCurrencyList(originalList)
    }
    form.setFieldsValue({ paidCurrencyCode: selectCurrency?.numCode })
  }

  const handleRateChange = (e: any) => {
    const value = e.target.value
    const _value = value.trim()
    fnSetRateAndMoney(_value)
  }

  const handleDatePickerChange = async (date: any) => {
    const list = await fnGetCurrencyList({ time: date?.valueOf() })
    const item = list?.find((item: any) => item?.numCode === selectCurrency?.numCode)
    const data = item ? item : list?.[0]
    setCurrencyList(list)
    setOriginalList(list)
    setRate(data?.rate)
    setSelectCurrency(data)
    if (currencySetInfo?.paidCurrencyType === 'useRate') {
      fnCalculateAmount(data?.rate)
    }
    form.setFieldsValue({ paidCurrencyCode: data?.numCode, paidRate: data?.rate })
  }

  const handleValidatorRateb = (_: any, value: any, callback: any) => {
    if (Number(value) > 0) return callback()
    callback(i18n.get('汇率不能小于0'))
  }

  const handleValidatorAmount = (_: any, value: any, callback: any) => {
    if (Number(value) > 0) return callback()
    callback(i18n.get('金额不能小于0'))
  }

  const handleModalClose = () => {
    layer.emitCancel()
  }

  const handleOk = () => {
    const flag = line?.channel === 'OFFLINE' && currency && line?.account?.recevingCurrencyEnable
    form.validateFields().then(result => {
      let params = { ...result, paidOffLineFinishedTime: result?.paidOffLineFinishedTime?.valueOf() }
      if (flag) {
        let paidRate = result?.paidRate
        if (currencySetInfo?.paidCurrencyType === 'useAmount') {
          paidRate = new Big(Number(currencySetInfo?.standardCurrency?.standard))
            ?.div(Number(result?.paidAmount))
            ?.toFixed(32)
            ?.replace(/(\.\d*?)0+$/, '$1')
            ?.replace(/\.$/, '')
        }
        params = { ...params, paidRate }
      }
      layer.emitOk(params)
    })
  }

  const renderRateTooltip = () => {
    return rate
      ? `${rate}=${currencySetInfo?.standardCurrency?.standardStrCode}: ${selectCurrency?.strCode}`
      : canChangeRate
      ? i18n.get('核算汇率不可为空')
      : i18n.get('企业未维护核算汇率，请联系管理员维护核算汇率或申请核算汇率手动编辑权限')
  }

  const renderPopConten = () => {
    return (
      <div className={styles['pop-conten']}>
        <Input
          className="pop-input"
          prefix={<OutlinedEditSearch />}
          placeholder={i18n.get('搜索币种名称或代码')}
          onChange={handleSearchChange}
        />
        <div className="item-wrap">
          {currencyList.map((item: any) => (
            <div
              onClick={() => handleItemClick(item)}
              className={classNames('currency-item', { 'check-item': item?.numCode === selectCurrency?.numCode })}
            >
              <div className="left">
                <img className="currency-img" src={item?.icon} />
              </div>
              <div className="right">
                <div className="name">{item?.name}</div>
                <div className="code">{`${i18n.get('代码')}：${item?.strCode}（${item?.numCode}）`}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  const disabledDate = (current: moment.Moment) => {
    return current && current > moment().endOf('day')
  }

  const payableCurrencyCode = currencySetInfo?.payableCurrency?.standardStrCode ?? '-'
  const payableCurrencyStandard = currencySetInfo?.payableCurrency?.standard ?? '-'
  const paidCurrencyType = currencySetInfo?.paidCurrencyType
  const flag = line?.channel === 'OFFLINE' && currency && line?.account?.recevingCurrencyEnable
  const isUseAmount = paidCurrencyType === 'useAmount'
  if (!paidCurrencyType) {
    return null
  }
  return (
    <div className={styles['offlin-finish-modal']}>
      <div className="header">
        <div>{i18n.get('更新线下支付时间')}</div>
        <OutlinedTipsClose className="cross-icon" onClick={handleModalClose} />
      </div>
      <Form className="offlin-finish-form" layout="vertical" form={form}>
        <FormItem
          label={i18n.get('实际支付时间')}
          name="paidOffLineFinishedTime"
          style={{ marginBottom: 16 }}
          rules={[{ required: true, message: i18n.get('实际支付时间不可为空') }]}
        >
          <DatePicker
            style={{ width: '100%' }}
            format="YYYY-MM-DD HH:mm:ss"
            showTime
            disabledDate={disabledDate}
            onChange={handleDatePickerChange}
          />
        </FormItem>

        {flag && (
          <FormItem label={i18n.get("实际支付金额")} className={'currency-money-wrap'} rules={[{ required: true, message: '' }]}>
            <div className="currency-money-content">
              <div className="amount-deal">
                <Input
                  value={`${i18n.get('应付币种')}（${payableCurrencyCode}）`}
                  className={classNames('deal-currency-input', { 'deal-currency-input-is-en-US': is_en_US })}
                  style={{ cursor: 'default' }}
                  disabled
                />
                <Input
                  value={`${payableCurrencyStandard}`}
                  className="deal-money-input"
                  disabled
                  placeholder={i18n.get('应付金额')}
                />
              </div>
              <div className={classNames('currency-money-real', { 'currency-money-real-is-en-US': is_en_US })}>
                <div className="amount-left">
                  <Popover
                    overlayClassName={styles['currency-overlay']}
                    placement="bottomLeft"
                    title={null}
                    content={renderPopConten}
                  >
                    <FormItem label={null} name="paidCurrencyCode">
                      <div className="paid-currency-code">
                        {`${i18n.get('实付币种')}（${selectCurrency?.strCode ?? '-'}）`}
                        <OutlinedDirectionDown />
                      </div>
                    </FormItem>
                  </Popover>
                  <FormItem
                    className={classNames({ 'paid-amount': isUseAmount })}
                    label={null}
                    name="paidAmount"
                    rules={[
                      { required: true, message: i18n.get('支付金额不可为空') },
                      { pattern: /^(?!0\d)\d+(\.\d{1,2})?$/, message: i18n.get('请输入有效的数字，最多两位小数') },
                      { validator: handleValidatorAmount },
                    ]}
                  >
                    <Input
                      className={classNames('left-money-input', { 'left-money-input-useAmount': isUseAmount })}
                      disabled={!isUseAmount}
                      placeholder={i18n.get('支付金额')}
                    />
                  </FormItem>
                </div>

                {!isUseAmount && (
                  <FormItem className="rate-form-item" label={null}>
                    <div className="amount-right">
                      <div className="right-wrap">
                        <div className={classNames('rate-tips', { 'rate-tips-is-en-US': is_en_US })}>
                          {i18n.get('核算汇率')}
                          <Tooltip placement="top" title={renderRateTooltip}>
                            <OutlinedTipsInfo className="tips-icon" />
                          </Tooltip>
                        </div>
                        <FormItem
                          label={null}
                          name="paidRate"
                          rules={[
                            { required: true, message: i18n.get('核算汇率不可为空') },
                            { pattern: /^\d+(\.\d+)?$/, message: i18n.get('输入格式不正确') },
                            { max: 32, message: i18n.get('汇率的精度是0~32位') },
                            { validator: handleValidatorRateb },
                          ]}
                        >
                          <Input
                            className="rate-input"
                            name="paidRate"
                            disabled={!canChangeRate}
                            onChange={handleRateChange}
                            placeholder={i18n.get('汇率')}
                          />
                        </FormItem>
                      </div>

                      {canChangeRate && (
                        <Tooltip placement="top" title={i18n.get('恢复汇率初始设定')}>
                          <OutlinedDirectionRefresh
                            onClick={() => fnSetRateAndMoney(selectCurrency?.rate)}
                            className="reset-rate"
                          />
                        </Tooltip>
                      )}
                    </div>
                  </FormItem>
                )}
              </div>
            </div>
          </FormItem>
        )}
      </Form>

      <div className="footer">
        <Button category="secondary" className="mr-8" onClick={handleModalClose}>
          {i18n.get('取消')}
        </Button>
        <Button category="primary" onClick={handleOk}>
          {i18n.get('确定')}
        </Button>
      </div>
    </div>
  )
}

export default EnhanceModal({
  title: '',
  footer: [],
  className: styles['offline-wrap'],
})(OfflinFinishTimeModal)
