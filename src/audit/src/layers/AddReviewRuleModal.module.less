@import '~@eku<PERSON>bao/eui-styles/less/token.less';

.add_review_rule_modal {
  :global {
    .bank_account_options-item-title {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 100%;
      padding: 6px 0;
      .bank_account_item_title_icon {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-right: 8px;
      }
      .bank_account_item_title_account {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font: var(--eui-font-body-r1);
        color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
      }
      .bank_account_item_title_bank {
        font: var(--eui-font-body-r1);
        color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
      }
    }
    .eui-select-single.eui-select-open .eui-select-selection-item .bank_account_options-item-title {
      .bank_account_item_title_account {
        color: var(--eui-text-placeholder);
      }
      .bank_account_item_title_bank {
        color: var(--eui-text-placeholder);
      }
      .bank_account_item_title_icon {
        opacity: 0.4;
      }
    }
    .modal-footer {
      padding: 16px 0;
      display: flex;
      justify-content: flex-end;
    }
  }
}
.bank_account_options-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  :global {
    .bank_account_options-item_left {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin-right: 14px;
    }
    .bank_account_options-item_right {
      display: flex;
      flex-direction: column;
      overflow: hidden;
      .bank_right_accountName {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
        font: var(--eui-font-body-r1);
      }
      .bank_right_bankInfo {
        color: var(--eui-text-placeholder, rgba(29, 33, 41, 0.5));
        font: var(--eui-font-body-r1);
      }
    }
  }
}
.bank_account_options-item-selected {
  display: flex;
  flex-direction: row;
  align-items: center;
  :global {
    .bank_account_options-item_left {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin-right: 14px;
    }
    .bank_account_options-item_right {
      display: flex;
      flex-direction: column;
      overflow: hidden;
      .bank_right_accountName {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font: var(--eui-font-body-r1);
      }
      .bank_right_bankInfo {
        font: var(--eui-font-body-r1);
      }
    }
  }
}
