import { app } from '@ekuaibao/whispered'
/**************************************************
 * Created by nanyuantingfeng on 18/09/2017 15:38.
 **************************************************/
export const isHoseEUI = true
export default [
  {
    key: 'FlowAllowModal',
    getComponent: () => Promise.resolve(app.require('@elements/flow-allow-modal')),
    width: 800,
    maskClosable: false,
    wrapClassName: '',
    footer: [],
    className: 'custom-modal-layer',
    isHoseEUI,
  },
  {
    key: 'PayMethodModal',
    getComponent: () => import('../elements/select-paymethod-modal'),
    width: 680,
    maskClosable: false,
    isHoseEUI,
  },
  {
    key: 'SelectPayAccountDrawer',
    getComponent: () => import('./SelectPayAccountDrawer'),
    title: i18n.get('选择付款账户'),
    className: 'drawer-base-wrap',
    isEUI: true,
  },
  {
    key: 'RejectBillModal',
    getComponent: () => import('../elements/reject-bill-modal'),
    width: 800,
    maskClosable: false,
    wrapClassName: '',
    footer: [],
    className: 'custom-modal-layer',
    isHoseEUI,
  },
  {
    key: 'ExportModal',
    getComponent: () => import('../elements/export-modal'),
    title: i18n.get('导出设置'),
    width: 600,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal',
    isHoseEUI,
  },
  {
    key: 'PrintModal',
    getComponent: () => Promise.resolve(app.require('@elements/print-modal')),
    isHoseEUI,
  },
  {
    key: 'AgreeBillModal',
    getComponent: () => import('../elements/agree-bill-modal/agree-bill-modal'),
    width: 800,
    maskClosable: false,
    wrapClassName: '',
    footer: [],
    className: 'custom-modal-layer',
    isHoseEUI,
  },
  {
    key: 'InfoModal',
    getComponent: () => import('../elements/info-modal'),
    isHoseEUI,
  },
  {
    key: 'ZeroPayModal',
    getComponent: () => import('../elements/zero-pay-modal'),
    isHoseEUI,
  },
  {
    key: 'PayingBindPhone',
    getComponent: () => import('../elements/paying-bind-phone'),
    width: 600,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal',
    isHoseEUI,
  },
  {
    key: 'ExpressNumberModal',
    getComponent: () => import('../receiveExpress/modal/ExpressNumberModal'),
    wrapClassName: 'vertical-center-modal',
    maskClosable: false,
    width: 600,
    isHoseEUI,
  },
  {
    key: 'AddExpressInfoModal',
    getComponent: () => import('../sendExpress/modal/AddExpressInfoModal'),
    width: 600,
    height: 440,
    maskClosable: false,
    wrapClassName: '',
    isHoseEUI,
  },
  {
    key: 'JumpExpressInfoModal',
    getComponent: () => import('../sendExpress/modal/JumpExpressInfo'),
    width: 584,
    height: 242,
    maskClosable: false,
    wrapClassName: '',
    isHoseEUI,
  },
  {
    key: 'DownloadSheetsModal',
    getComponent: () => import('../elements/download-sheets-modal'),
    width: 584,
    style: {
      display: 'flex',
      flexDirection: 'column',
    },
    isHoseEUI,
  },
  {
    key: 'SelectPaymentTypeModal',
    getComponent: () => import('../elements/select-refresh-type'),
    isHoseEUI,
  },
  {
    key: 'ApprovalCompleteModal',
    getComponent: () => import('../elements/agree-bill-modal/approval-complete-modal'),
    title: '',
    width: 600,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal',
    isHoseEUI,
  },
  {
    key: 'RepaymentModal',
    getComponent: () => import('../elements/repayment-modal'),
    width: 768,
    maskClosable: false,
    isHoseEUI,
  },
  {
    key: 'TransferVouchersSelectModal',
    getComponent: () => import('../elements/TransferVouchersModal/TransferVouchersSelectModal'),
    width: 600,
    maskClosable: false,
    isHoseEUI,
  },
  {
    key: 'TransferVouchersEditModal',
    getComponent: () => import('../elements/TransferVouchersModal/TransferVouchersEditModal'),
    width: 800,
    maskClosable: false,
    isHoseEUI,
  },
  {
    key: 'NulllifyBillModal',
    getComponent: () => import('../elements/nullify-bill-modal'),
    width: 400,
    maskClosable: false,
    isHoseEUI,
  },
  {
    key: 'CommonAccountModal',
    enhancer: 'modal',
    enhancerOptions: {
      title: '',
      footer: [],
      className: 'custom-modal-layer',
    },
    getComponent: () => import('../elements/CommonAccountModal'),
    width: 800,
    isHoseEUI,
  },
  {
    key: 'ReceiptBindPaymentPlanModal',
    width: 800,
    enhancer: 'modal',
    enhancerOptions: {
      title: '',
      footer: [],
      className: 'respond-modal-layer',
    },
    maskClosable: false,
    getComponent: () => import('../page-menu/paid'),
    isHoseEUI,
  },
  {
    key: 'AdjustPaymentPlan',
    enhancer: 'drawer',
    enhancerOptions: {
      title: '',
      footer: [],
      placement: 'bottom',
      className: 'custom-drawer-layer',
      closable: false,
      width: '100%',
      height: 'calc(100% - 48px)',
    },
    getComponent: () => import('../elements/AdjustPaymentPlan'),
    isHoseEUI,
  },
  {
    key: 'HSBC_SupplyPaymentDrawer',
    enhancer: 'drawer',
    enhancerOptions: {
      title: '',
      footer: [],
      placement: 'bottom',
      className: 'custom-drawer-layer',
      closable: false,
      width: '100%',
      height: 'calc(100% - 48px)',
    },
    getComponent: () => import('../elements/HSBC_SupplyPaymentDrawer'),
  },
  {
    key: 'AdjustPaymentPlanInner',
    enhancer: 'modal',
    enhancerOptions: {
      title: '',
      footer: [],
      className: 'custom-modal-layer custom-modal-layer-for-select-modal',
      closable: false,
      width: 600,
    },
    getComponent: () => import('../elements/AdjustPaymentPlan/components/Layer'),
  },
  {
    key: 'CheckPaySuccessModal',
    getComponent: () => import('../elements/checkPaySuccessModal'),
    width: 400,
    maskClosable: false,
    wrapClassName: '',
    isHoseEUI,
  },
  {
    key: 'CheckPayFailedRiskModal',
    getComponent: () => import('../elements/checkPayFailedRiskModal'),
    width: 600,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal select-member-modal',
    isHoseEUI,
  },
  {
    key: 'PayLogModal',
    getComponent: () => import('../elements/PayLogModal/PayLogModal'),
    width: 770,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal select-member-modal',
    isHoseEUI,
  },
  {
    key: 'PayTipsModal',
    getComponent: () => import('../elements/payTipsModal'),
    width: 480,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal select-member-modal',
    isHoseEUI,
  },
  {
    key: 'AutoMergerPlanModel',
    getComponent: () => import('../elements/AdjustPaymentPlan/components/Layer/AutoMergerPlanModel'),
    width: 800,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal select-member-modal',
    isHoseEUI,
  },
  {
    key: 'HSBC_SupportFileModal',
    getComponent: () => import('../elements/HSBC_SupplyPaymentDrawer/modal/HSBC_SupportFileModal'),
    width: 480,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal select-member-modal',
    isHoseEUI,
  },
  {
    key: 'HSBC_TableFillingModal',
    getComponent: () => import('../elements/HSBC_TableFillingModal'),
    width: '700',
    timeout: 500,
    isHoseEUI,
  },
  {
    key: 'HSBC_InstructionFileDownloadModal',
    getComponent: () => import('../elements/HSBC_SupplyPaymentDrawer/modal/HSBC_InstructionFileDownloadModal'),
    width: 480,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal select-member-modal',
    isHoseEUI,
  },
  {
    key: 'HSBC_SupportFileIntroductionsModal',
    getComponent: () => import('../elements/HSBC_SupplyPaymentDrawer/modal/HSBC_SupportFileIntroductionsModal'),
    width: 480,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal select-member-modal',
    isHoseEUI,
  },
  {
    key: 'PreviewByIframe',
    getComponent: () => import('../page-menu/previewIframe'),
    width: 800,
    // maskClosable: false,
    wrapClassName: 'vertical-center-modal select-member-modal',
    isHoseEUI,
  },
  {
    key: 'ReExchangeModal',
    getComponent: () => import('../elements/ReExchangeModal/index'),
    width: 1100,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal select-member-modal',
    isHoseEUI,
  },
  {
    key: 'PayPlanModal',
    getComponent: () => import('../elements/ReExchangeModal/payPlanModal'),
    width: 800,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal select-member-modal',
    isHoseEUI,
  },
  {
    key: 'upLoadFiles',
    getComponent: () => import('../elements/ReExchangeModal/upLoad'),
    width: 500,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal select-member-modal',
    isHoseEUI,
  },
  {
    key: 'BrowModal',
    getComponent: () => Promise.resolve(app.require('@elements/BrowModal')),
    width: 400,
    maskClosable: false,
    isHoseEUI,
  },
  {
    key: 'CheckPayMethodStatusModal',
    getComponent: () => import('../elements/checkPayMethodStatusModal'),
    width: 424,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal select-member-modal',
    isHoseEUI,
  },
  {
    key: 'RejectPaymentReviewModal',
    getComponent: () => import('../elements/rejectPaymentReviewModal'),
    width: 800,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal select-member-modal',
    isHoseEUI,
  },
  {
    key: 'AddTagModal',
    getComponent: () => import('./AddTagModal'),
    width: 650,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal select-member-modal',
    isHoseEUI,
  },
  {
    key: 'AddReviewRuleModal',
    getComponent: () => import('./AddReviewRuleModal'),
    width: 520,
    maskClosable: false,
    isHoseEUI,
  },
  {
    key: 'VerificationPhoneModal',
    getComponent: () => Promise.resolve(app.require('@elements/phone-verification/verification-phone')),
    width: 675,
    maskClosable: false,
  },
  {
    key: 'BacklogInfoDrawer',
    getComponent: () => import('./BackLogDrawer'),
    width: 800,
    enhancer: 'drawer',
    title: i18n.get('单据详情'),
    class: {
      left: 'drag-hover',
    },
  },
  {
    key: 'SplitPlanModal',
    getComponent: () => import('../elements/AdjustPaymentPlan/components/Layer/SplitPlanModal'),
    width: 800,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal select-member-modal',
    isHoseEUI,
  },
  {
    key: 'OfflinFinishTimeModal',
    getComponent: () => import('./OfflinFinishTimeModal'),
    width: 800,
    maskClosable: false,
    isHoseEUI,
  },
]
