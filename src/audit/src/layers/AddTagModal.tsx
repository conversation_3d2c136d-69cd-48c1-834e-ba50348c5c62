import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Button, Form, Cascader, Space, Tag } from '@hose/eui'
import styles from './AddTagModal.module.less'
import { OutlinedTipsClose } from '@hose/eui-icons'
import { getBatchList, getBatchTagsByDetailId, getCommonTag } from '../audit-action'
import { app } from '@ekuaibao/whispered'
import { ICheckingBillDetail } from '@ekuaibao/ekuaibao_types/dist/CheckingBill'

enum tagType {
  MANUAL = 'MANUAL',
  AUTO = 'AUTO',
}

type IProps = {
  layer: any
  data: any
}
type IState = {
  tags: any[]
  options: Option[]
  tagMap: any
  commonTags: any[]
}

interface Option {
  value: string
  label: string
  children?: Option[]
}

@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer',
})
export default class AddTagModal extends PureComponent<IProps, IState> {
  state: Readonly<IState> = { tags: [], options: [], tagMap: undefined, commonTags: [] }
  formRef = React.createRef<any>()

  //   api.open("@audit:AddTagModal")   调用对话框代码

  componentDidMount(): void {
    this.initData()
  }

  initData = async () => {
    const { detailIds, feeTypeIds, specificationIds } = getFetchParams(this.props.data)
    if (!!detailIds.length) {
      const [tagsValue, tagListValue, commonTagValue, tagConfigValue] = await Promise.all([
        getBatchTagsByDetailId({ detailIds }),
        getBatchList({ specificationIds, feeTypeIds }),
        getCommonTag({ specificationIds, feeTypeIds }),
        app.dataLoader('@tpp-v2-action.tagGlobalConfig').load()
      ])
      const value = tagsValue?.items || []
      const list = tagListValue?.items || []
      const tagConfig = tagConfigValue || {}
      const displayTagValue = tagConfig.displayTagValue
      const commonTags = (commonTagValue?.items ?? []).map(item => {
        const showLabel = displayTagValue
          ? item.tagValueId.label
          : `${item.tagId.name}/${item.tagValueId.label}`
        return {
          ...item,
          showLabel,
        }
      })
      const tags: any[] = value.map(item => {
        const label = displayTagValue ? item.label : `${item.tagName}/${item.label}`
        return {
          label,
          tagType: item.tagType,
          status: item.status,
        }
      })
      const tagMap: any = {}
      const newList = list.map((item: any) => {
        const { tagValues = [] } = item
        const children = tagValues.map((o: any) => {
          tagMap[o.id] = { ...o, parent: item }
          return { value: o.id, label: o.label }
        })
        return {
          value: item.id,
          label: item.name,
          children: children,
        }
      })
      this.setState({ tags, options: newList, tagMap, commonTags })
    }
  }

  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  handleModalSave = () => {
    this.formRef.current.validateFields().then((res: any) => {
      const { tagMap } = this.state
      const newTag = tagMap[res.new.pop()]
      this.props.layer.emitOk({
        newTag: {
          tagId: newTag.parent.id,
          tagName: newTag.parent.name,
          tagValueId: newTag.id,
          label: newTag.label,
          tagType: newTag.parent.type, //MANUAL：手动， AUTO：自动
          tagRelationType: 'FLOW_DETAIL', //FLOW_DETAIL：费用明细，FLOW:单据
          tagBatch: getBatchParams(this.props.data),
        },
      })
    })
  }
  getColor = (type: tagType, status: number) => {
    if (status === 0) {
      return 'neu'
    }
    switch (type) {
      case tagType.AUTO:
        return 'cyan'
      case tagType.MANUAL:
        return 'pri'
      default:
        return 'pri'
    }
  }

  render() {
    const { options, tags = [], commonTags = [] } = this.state
    console.log(options)
    return (
      <div className={styles['add_tag_modal']}>
        <div className="modal-header">
          <div className="flex">{i18n.get('审批意见')}</div>
          <OutlinedTipsClose className="cross-icon" onClick={this.handleModalClose} />
        </div>
        <div className="modal-content">
          <Form name="vertical" layout="vertical" style={{ width: '100%' }} ref={this.formRef}>
            {!!tags?.length && (
              <Form.Item name="old" label={i18n.get('已有标签')}>
                <Space wrap>
                  {tags.map((item, idx) => {
                    const color = this.getColor(item.tagType, item.status)
                    return (
                      <Tag key={item?.tagId || idx} color={color}>
                        {item.label}
                      </Tag>
                    )
                  })}
                </Space>
              </Form.Item>
            )}
            <Form.Item
              name="new"
              label={i18n.get('添加标签')}
              rules={[{ required: true, message: '标签不能为空!' }]}>
              <AddTag options={options} commonTags={commonTags} />
            </Form.Item>
          </Form>
        </div>
        <div className="modal-footer">
          <Button category="secondary" className="btn mr-10" onClick={this.handleModalClose}>
            {i18n.get('取消')}
          </Button>
          <Button className="btn" style={{ marginRight: '8px' }} onClick={this.handleModalSave}>
            {i18n.get('确定')}
          </Button>
        </div>
      </div>
    )
  }
}
const AddTag: React.FC<any> = ({ value, options, commonTags, onChange }) => {
  const handleClick = item => {
    console.log(item)
    onChange([item.tagId.id, item.tagValueId.id])
  }
  return (
    <div>
      <Cascader
        value={value}
        placeholder={i18n.get('请选择标签')}
        options={options}
        onChange={onChange}
      />
      <div style={{ marginTop: 8 }}>
        {commonTags.map(item => {
          return (
            <Tag
              style={{ cursor: 'pointer', marginRight: 8 }}
              key={item?.id}
              color="neu"
              onClick={() => handleClick(item)}>
              {item.showLabel}
            </Tag>
          )
        })}
      </div>
    </div>
  )
}

const getFetchParams = (details: ICheckingBillDetail[]) => {
  const detailIds: string[] = []
  const feeTypeIds: string[] = []
  const specificationIds: string[] = []
  const flowSpecificationIds: string[] = []
  if (!details?.length) return { specificationIds, feeTypeIds, flowSpecificationIds, detailIds }
  details?.forEach(item => {
    const detailId = getId(item?.form?.detailId)
    const feeTypeId = getId(item?.form?.feeTypeId)
    const specificationId = getId(item?.form?.specificationId)
    const flowSpecificationId = getId(item?.form?.flowSpecificationId)
    detailIds.push(detailId)
    feeTypeIds.push(feeTypeId)
    specificationIds.push(specificationId)
    flowSpecificationIds.push(flowSpecificationId)
  })
  return {
    detailIds: detailIds.filter(Boolean),
    feeTypeIds: feeTypeIds.filter(Boolean),
    specificationIds: specificationIds.filter(Boolean),
    flowSpecificationIds: flowSpecificationIds.filter(Boolean),
  }
}

const getBatchParams = (details: ICheckingBillDetail[]) => {
  const data = {}
  details.forEach(item => {
    const detailId = item?.form?.detailId
    const flowId = item?.form?.flow?.id
    const backLogId = item?.nodeState?.backlogId
    const flowCode = item?.form?.flow?.form?.code
    const saveData = data[flowId]
    if (!saveData) {
      data[flowId] = {
        backLogId,
        flowId,
        flowCode,
        flowDetailIds: [detailId],
      }
    } else {
      saveData.flowDetailIds.push(detailId)
    }
  })
  return Object.values(data)
}

const getId = data => {
  return typeof data === 'string' ? data : data?.id
}
