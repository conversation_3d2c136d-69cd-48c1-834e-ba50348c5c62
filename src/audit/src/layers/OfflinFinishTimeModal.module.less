.offlin-finish-modal {
  overflow: hidden;

  :global {
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;

      .cross-icon {
        color: var(--eui-icon-n2);
      }
    }

    .offlin-finish-form {
      padding: 0 16px 16px;
      min-height: 150px;

      .currency-money-wrap {
        .currency-money-content {
          width: 100%;

          .amount-deal {
            display: flex;
            margin-bottom: 12px;

            .deal-currency-input {
              cursor: default;
              border-radius: 6px 0 0 6px;
              width: 162px;
              background: var(--eui-bg-body-overlay, #F7F8FA);
              color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
            }

            .deal-currency-input-is-en-US {
              width: 258px;
            }

            .deal-money-input {
              border-left: none;
              border-radius: 0 6px 6px 0;
              background: var(--eui-decorative-neu-100);
              color: var(--eui-text-disabled, rgba(29, 33, 41, 0.30))
            }
          }
        }

        .eui-form-item-control-input-content {
          display: flex;
          width: 100%;
          align-items: flex-start;

          .currency-money-real {
            display: flex;

            .amount-left {
              display: flex;
              align-items: flex-start;

              .paid-currency-code {
                display: flex;
                width: 154px;
                justify-content: space-between;
                align-items: center;
                padding: 4px 10px;
                border-right: none !important;
                border-radius: 6px 0px 0px 6px;
                border: 1px solid var(--eui-line-border-component, rgba(29, 33, 41, 0.20));
                background: var(--eui-bg-body-overlay, #F7F8FA);
              }

              .left-money-input {
                border-radius: 0 6px 6px 0;
                margin-right: 12px;
                width: 148px;
              }

              .paid-amount {
                .eui-form-item-control-input {
                  width: 237%;

                  .left-money-input {
                    width: 613px;
                  }
                }
              }
            }

            .amount-right {
              display: flex;
              align-items: flex-start;

              .right-wrap {
                display: flex;
                align-items: flex-start;

                .rate-tips {
                  display: flex;
                  align-items: center;
                  width: 106px;
                  height: 32px;
                  padding: 8px;
                  border-radius: 6px 0 0 6px;
                  border: 1px solid var(--eui-line-border-component, rgba(29, 33, 41, 0.20));
                  border-right: none;
                  background: var(--eui-bg-body-overlay, #F7F8FA);

                  .eui-icon-OutlinedTipsInfo {
                    font-size: 12px;
                    margin-left: 4px;
                    color: var(--eui-icon-n2);
                  }
                }

                .rate-tips-is-en-US {
                  width: 172px;
                }

                .rate-input {
                  border-radius: 0 6px 6px 0;
                  width: 304px;
                }
              }

              .reset-rate {
                width: 32px;
                height: 32px;
                padding: 7px;
                border-radius: 6px;
                margin-left: 12px;
                font-size: 16px;
                border: 1px solid var(--eui-line-border-component, rgba(29, 33, 41, 0.20));
                background: var(--eui-bg-body, #FFF);
                color: var(--eui-icon-n1);
              }
            }
          }

          .currency-money-real-is-en-US {
            .paid-currency-code {
              width: 264px !important;
            }

            .left-money-input-useAmount {
              width: 504px !important;
            }
            .rate-input {
              width: 144px !important;
            }
          }
        }
      }
    }

    .footer {
      padding: 0 16px 16px;
      text-align: end;
      margin-top: -56px;
    }
  }
}

.currency-overlay {
  padding-top: 0;
  width: 468px;

  :global {
    .eui-popover-content {
      .eui-popover-arrow {
        display: none;
      }

      .eui-popover-inner {
        padding: 8px;

        .eui-popover-inner-content {
          overflow: hidden;
        }
      }
    }
  }
}

.pop-conten {
  width: 100%;
  max-height: 350px;
  display: flex;
  flex-direction: column;

  :global {
    .pop-input {
      margin-bottom: 8px;
    }

    .item-wrap {
      overflow: auto;
      flex: 1;

      .currency-item {
        cursor: pointer;
        display: flex;
        align-items: center;
        margin-bottom: 2px;
        padding: 8px 16px;

        .left {
          img {
            width: 32px;
            height: 32px;
            border-radius: 100%;
            margin-right: 12px;
          }
        }

        .right {
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;

          .name {
            color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
          }

          .code {
            color: var(--eui-text-placeholder, rgba(29, 33, 41, 0.50));
          }
        }
      }

      .check-item {
        border-radius: 8px;
        background: var(--eui-fill-active, rgba(37, 85, 255, 0.10));

        .right {
          color: var(--eui-primary-pri-500, #2555FF);
        }
      }
    }
  }
}

.offline-wrap {
  :global {
    .eui-modal-content {
      .eui-modal-body {
        padding: 0;
      }

      .eui-modal-close,
      .eui-modal-footer {
        display: none;
      }
    }
  }
}