import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Button, Divider, Form, Select, Space } from '@hose/eui'
import styles from './AddReviewRuleModal.module.less'
import { app } from '@ekuaibao/whispered'
import { getpaymentManagementAccount } from '../audit-action'
import Person from '../paymentManagement/elements/Person'

type IProps = {
  layer: any
  record: any
}
type IState = {
  staffOptions: any[]
  payeeAccounts: any[]
  payerId: string
}
export const getBankInfo = (bankInfo: any) => {
  return bankInfo.accountNo ? `${bankInfo.bank}（${bankInfo.accountNo.slice(-4)}）` : bankInfo.bank
}
@EnhanceModal({
  footer: null,
})
export default class AddReviewRuleModal extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props)
    const selectedStaff = this.props.record?.staffId
    this.state = {
      staffOptions: selectedStaff
        ? [{ value: selectedStaff?.id, label: selectedStaff?.name, avatar: selectedStaff?.avatar }]
        : [],
      payeeAccounts: [],
      payerId: props.record?.payerId?.id,
    }
  }
  formRef = React.createRef<any>()
  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  componentDidMount(): void {
    getpaymentManagementAccount().then(res => {
      const list = res?.items || []
      if (this.props.record?.payerId) {
        list.unshift(this.props.record.payerId)
      }
      this.setState({ payeeAccounts: list })
    })
  }

  handleModalSave = () => {
    this.formRef.current.validateFields().then((res: any) => {
      this.props.layer.emitOk(res)
    })
  }
  handleSelectStaff = async () => {
    const checkedData: any = await app.open('@organizationManagement:SelectStaff', {
      data: [{ type: 'department-member', checkIds: [this.formRef.current.getFieldValue('staffId')] }],
      multiple: false,
      required: true,
      followContactRules: false,
    })
    const staffsData = checkedData.find((item: any) => item.type === 'department-member')
    const selectedStaff = staffsData?.checkList?.[0]
    this.formRef.current.setFieldsValue({ staffId: selectedStaff?.id })
    this.setState({
      staffOptions: [{ value: selectedStaff?.id, label: selectedStaff?.name, avatar: selectedStaff?.avatar }],
    })
  }
  fieldFilterOption = (input: string, option: any) => {
    const itemData = option.other
    return itemData.name.includes(input) || itemData.bank.includes(input) || itemData.number.includes(input)
  }
  render() {
    const { record } = this.props
    const { payeeAccounts } = this.state
    return (
      <div className={styles['add_review_rule_modal']}>
        <Form name="vertical" layout="vertical" style={{ width: '100%' }} ref={this.formRef}>
          <Form.Item
            name="payerId"
            label={i18n.get('付款账户')}
            initialValue={record?.payerId?.id}
            rules={[{ required: true, message: i18n.get('付款账户不能为空!') }]}
          >
            <Select
              onChange={payerId => {
                this.setState({ payerId })
              }}
              showSearch={true}
              filterOption={this.fieldFilterOption}
              optionLabelProp={'info'}
              placeholder={i18n.get('请选择')}
            >
              {payeeAccounts.map(item => {
                const detail = item.detail
                const isSelected = this.state.payerId === item.id
                return (
                  <Select.Option
                    key={item.id}
                    other={detail}
                    title={detail.name}
                    info={
                      <div className={'bank_account_options-item-title'}>
                        <img className="bank_account_item_title_icon" src={detail.icon} />
                        <span className="bank_account_item_title_account">{detail.name}</span>
                        <Divider style={{ height: 16 }} type="vertical" />
                        <span className="bank_account_item_title_bank">{`${getBankInfo({
                          bank: detail.bank,
                          accountNo: detail.number,
                        })}`}</span>
                      </div>
                    }
                  >
                    <div
                      className={
                        styles[isSelected ? 'bank_account_options-item-selected' : 'bank_account_options-item']
                      }
                    >
                      <img src={detail.icon} className="bank_account_options-item_left" />
                      <div className="bank_account_options-item_right">
                        <div className="bank_right_accountName">{detail.name}</div>
                        <div className="bank_right_bankInfo">
                          {detail.bank}
                          {detail.bank && detail.number && <Divider style={{ height: 16 }} type="vertical" />}
                          {detail.number ?? ''}
                        </div>
                      </div>
                    </div>
                  </Select.Option>
                )
              })}
            </Select>
          </Form.Item>
          <Form.Item
            initialValue={record?.staffId?.id}
            name="staffId"
            label={i18n.get('指定复核人员')}
            rules={[{ required: true, message: i18n.get('指定复核人员不能为空!') }]}
          >
            <Select
              open={false}
              optionLabelProp={'info'}
              placeholder={i18n.get('请选择')}
              onClick={this.handleSelectStaff}
            >
              {this.state.staffOptions.map(item => {
                return (
                  <Select.Option
                    key={item.value}
                    title={item.value}
                    info={
                      <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                        <Person src={item.avatar} label={item.label} />
                      </div>
                    }
                  >
                    {item.label}
                  </Select.Option>
                )
              })}
            </Select>
          </Form.Item>
        </Form>
        <div className="modal-footer">
          <Space>
            <Button category="secondary" onClick={this.handleModalClose}>
              {i18n.get('取消')}
            </Button>
            <Button onClick={this.handleModalSave}>{i18n.get('确定')}</Button>
          </Space>
        </div>
      </div>
    )
  }
}
