import { app } from '@ekuaibao/whispered'
import styles from './AuditSignature.module.less'
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
const ETabs = app.require<any>('@elements/ETabs')
import { connect } from '@ekuaibao/mobx-store'
import classnames from 'classnames'
import AuditSignatureWrapper from './wrapper/AuditSignatureWrapper'
import AuditSignedWrapper from './wrapper/AuditSignedWrapper'

interface IProps {
  size: number
  baseDataProperties: any
  staffs: any
  specifications: any
  userInfo: any
  bus?: any
}

interface IState {
  checkedKey: string
}

// @ts-ignore
@connect((store: any) => ({ size: store.states['@layout'].size }))
@EnhanceConnect((state: any) => ({
  baseDataProperties: state['@common'].globalFields.data,
  staffs: state['@common'].staffs,
  specifications: state['@custom-specification'].specificationGroupsList,
  userInfo: state['@common'].userinfo.data,
}))
export default class AuditSignature extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props)
    this.state = { checkedKey: 'WAIT_SIGN' }
  }

  handleTabChange = (type: string) => {
    this.setState({ checkedKey: type })
  }

  render() {
    const { checkedKey } = this.state
    const dataSource = [
      {
        tab: i18n.get('待签署'),
        children: <AuditSignatureWrapper checkedKey={checkedKey} tabKey="WAIT_SIGN" {...this.props} />,
        key: 'WAIT_SIGN',
      },
      {
        tab: i18n.get('签署中'),
        children: <AuditSignatureWrapper checkedKey={checkedKey} tabKey="SIGNING" {...this.props} />,
        key: 'SIGNING',
      },
      {
        tab: i18n.get('已签署'),
        children: <AuditSignedWrapper checkedKey={checkedKey} tabKey="SIGNED" {...this.props} />,
        key: 'SIGNED',
      },
    ]
    const cls = classnames(styles.AuditSignatureWrapper, {
      //@ts-ignore
      [styles.approve_wrapper_layout5]: window.isNewHome,
    })
    return (
      <div id="audit-signature" className={cls}>
        <ETabs
          isHoseEUI={true}
          className="ekb-tab-line-left"
          defaultActiveKey={'WAIT_SIGN'}
          onChange={this.handleTabChange}
          tabBarStyle={{ height: 40, width: '100%' }}
          dataSource={dataSource}
        />
      </div>
    )
  }
}
