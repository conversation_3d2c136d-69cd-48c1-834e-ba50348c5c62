/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018-10-10 10:53.
 */
import React, { PureComponent } from 'react'
import { Table } from 'antd'
import { Button } from '@hose/eui'
import { getBaseCol } from '../../util/Utils'
import styles from './DataGridSimple.module.less'
import { app as api } from '@ekuaibao/whispered'

export default class DataGridSimple extends PureComponent {
  constructor(props) {
    super(props)
    this.state = { selectedRowKeys: [], selectedRows: [] }
  }
  getColumns = () => {
    const baseCol = getBaseCol()
    const { handleJumpExpress, handleAddExpress, isModal = false } = this.props
    const _this = this
    const action = {
      title: i18n.get('操作'),
      dataIndex: 'action',
      key: 'action',
      width: 220,
      fixed: isModal ? 'right' : false,
      render(text, line) {
        return (
          <div
            onClick={e => {
              e.persist()
              e.nativeEvent.stopImmediatePropagation()
              e.stopPropagation()
              e.preventDefault()
              return false
            }}>
            {line.state === 'SENDING' && (
              <a
                className="ant-dropdown-link mr-16"
                onClick={() => {
                  handleAddExpress && handleAddExpress(line, _this.clearSelected)
                }}>
                {i18n.get('添加寄送信息')}
              </a>
            )}
            <a
              className="ant-dropdown-link mr-16"
              onClick={() => {
                handleJumpExpress && handleJumpExpress(line, _this.clearSelected)
              }}>
              {i18n.get('跳过寄送')}
            </a>
          </div>
        )
      },
    }
    return [...baseCol, action]
  }
  handleOnRowClick = record => {
    api.invokeService('@bills:get:flow-info', { id: record.flowId.id }).then(({ value }) => {
      const { handleShowDetail } = this.props
      handleShowDetail && handleShowDetail(value)
    })
  }
  clearSelected = () => {
    this.setState({ selectedRowKeys: [], selectedRows: [] })
  }

  render() {
    const { selectedRowKeys } = this.state
    const rowSelection = {
      selectedRowKeys: selectedRowKeys,
      onChange: (selectedRowKeys, selectedRows) => {
        this.setState({ selectedRowKeys, selectedRows })
      },
      getCheckboxProps: record => ({
        id: record.flowId.id,
      }),
    }
    const { dataSource, isModal = false, handleAddExpressList } = this.props
    const scroll = isModal ? { x: 990, y: 654 } : { x: 990 }
    return (
      <div className={styles[`data-grid-simple-view${isModal ? '-modal' : ''}`]}>
        <Table
          rowKey="id"
          rowSelection={rowSelection}
          className="table-view"
          pagination={false}
          scroll={scroll}
          onRow={record => {
            return { onClick: () => this.handleOnRowClick(record) }
          }}
          columns={this.getColumns()}
          dataSource={dataSource}
        />
        <div className={'table-footer'}>
          <Button
            category="secondary"
            disabled={!(selectedRowKeys && selectedRowKeys.length > 0)}
            onClick={() =>
              handleAddExpressList(selectedRowKeys, 'add_express', this.clearSelected)
            }>
            {i18n.get('添加寄送信息')}
          </Button>
          <Button
            category="secondary"
            className="ml-12"
            disabled={!(selectedRowKeys && selectedRowKeys.length > 0)}
            onClick={() =>
              handleAddExpressList(selectedRowKeys, 'jump_express', this.clearSelected)
            }>
            {i18n.get('跳过寄送')}
          </Button>
          <span>
            {i18n.get('已选择 {__k0}/{__k1} 张', {
              __k0: selectedRowKeys.length,
              __k1: dataSource.length,
            })}
          </span>
        </div>
      </div>
    )
  }
}
