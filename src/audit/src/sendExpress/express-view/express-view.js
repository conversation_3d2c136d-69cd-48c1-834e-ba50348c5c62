import { app } from '@ekuaibao/whispered'
import styles from './express.module.less'
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
const ETabs = app.require('@elements/ETabs')
import SendWrapper from '../wrapper/send-express-wrapper'
import WaitWrapper from '../wrapper/wait-express-wrapper'
import { MessageCenter } from '@ekuaibao/messagecenter'
import { Button } from '@hose/eui'
import { connect } from '@ekuaibao/mobx-store/esm/index'
import classnames from 'classnames'
@connect(store => ({ size: store.states['@layout'].size }))
@EnhanceConnect(state => ({
  baseDataProperties: state['@common'].globalFields.data,
  staffs: state['@common'].staffs,
  specifications: state['@custom-specification'].specificationGroupsList,
  userInfo: state['@common'].userinfo.data,
}))
export default class ExpressView extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
  }

  handleTabChange = type => {
    if (type === 'wait') {
      this.bus.reload()
    }
  }

  openLightningSendView = () => {
    this.props.stackerManager.push('LightningExpress', {})
  }

  render() {
    const dataSource = [
      {
        tab: i18n.get('待寄送'),
        children: <WaitWrapper {...this.props} bus={this.bus} />,
        key: 'wait',
      },
      {
        tab: i18n.get('已寄送'),
        children: <SendWrapper {...this.props} />,
        key: 'send',
      },
    ]
    const cls = classnames(styles.express_wrapper, {
      [styles.express_wrapper_layout5]: window.isNewHome,
    })
    return (
      <div className={cls}>
        <ETabs
          className="ekb-tab-line-left"
          defaultActiveKey="wait"
          tabBarStyle={{ paddingTop: 6, height: 40, width: '100%' }}
          onChange={this.handleTabChange}
          dataSource={dataSource}
        />
        <Button
          className={`lightning-send-inline ${
            !window.isNewHome && styles['lightning-send-inline-right']
          }`}
          category="secondary"
          onClick={this.openLightningSendView}>
          {i18n.get('闪电寄送')}
        </Button>
      </div>
    )
  }
}
