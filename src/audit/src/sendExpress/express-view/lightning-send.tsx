import styles from './lightning-send.module.less'
import React, { PureComponent } from 'react'
import { message } from '@hose/eui'
import DataGridSimple from './DataGridSimple'
import { NewDataGridLoader } from './NewDataGridLoader'
import { app as api } from '@ekuaibao/whispered'
import { getBillInfoByCode } from '../../audit-action'
import { fnGetFlowIds, handleActionImplementation, handleSendExpressList } from '../../service'
import { showModal } from '@ekuaibao/show-util'
const LightingMode = api.require('@elements/data-grid-v2/LightingMode')

interface Props {
  bus: any
}

interface State {
  dataSource: any[]
  badge: string
  flowCode: string
  lightingErrorFeedback: boolean
}

export default class LightningSend extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { dataSource: [], badge: '', flowCode: '', lightingErrorFeedback: false }
  }

  isBillExist = (flowCode: string) => {
    const { dataSource } = this.state
    return dataSource.filter(o => o.flowId.form.code === flowCode).length > 0
  }

  onPressEnter = (e: any) => {
    const { value } = e.target
    if (value) {
      if (this.isBillExist(value)) {
        this.lightingSearchErrorFeedback(i18n.get('该单据已经添加'))
      } else {
        getBillInfoByCode({ code: value })
          .then((res: any) => {
            const { count, items } = res
            const { dataSource } = this.state
            if (count) {
              message.success(i18n.get('录入成功'))
              const temp: any[] = dataSource.concat(items)
              this.setState({ flowCode: '', dataSource: [...temp], badge: '+1' }, this.updateBadge)
            } else {
              this.lightingSearchErrorFeedback(i18n.get('录入失败'), 'error')
              this.setState({ flowCode: '' })
            }
          })
          .catch((e: any) => {
            this.lightingSearchErrorFeedback(i18n.get('录入失败'), 'error')
            this.setState({ flowCode: '' })
          })
      }
    }
  }

  updateBadge = () => {
    setTimeout(() => {
      this.setState({ badge: '' })
    }, 1000)
  }

  handleShowDetail = (item: any) => {
    api.open('@bills:BillStackerModal', {
      viewKey: 'BillInfoView',
      dataSource: item,
      scene: 'APPROVER',
    })
  }
  handleJumpExpress = (line: any, callBack: Function) => {
    handleActionImplementation.call(this, {
      type: 15,
      backlog: line,
      fn: (resp: any) => {
        const { errors } = resp.value
        callBack && callBack()
        if (errors[0].resultCode === 'OK') {
          this.deleteDataSource([line.id])
        }
      },
    })
  }
  handleAddExpress = (line: any, callBack: Function) => {
    handleActionImplementation.call(this, {
      type: 14,
      backlog: line,
      fn: (resp: any) => {
        const { errors } = resp.value
        callBack && callBack()
        if (errors[0].resultCode === 'OK') {
          this.deleteDataSource([line.id])
        }
      },
    })
  }
  handleAddExpressList = (keys: string[], name: string, callBack: Function, selectedData) => {
    const flowIds = fnGetFlowIds(selectedData)
    handleSendExpressList(
      keys,
      name,
      (resp: any) => {
        const { errors } = resp.value
        callBack && callBack()
        api.invokeService('@layout5:refresh:menu:data')
        if (errors[0].resultCode === 'OK') {
          this.deleteDataSource(keys)
        }
      },
      { flowIds },
    )
  }

  deleteDataSource = (keys: string[]) => {
    const { dataSource } = this.state
    const newDataSource: any[] = dataSource.filter((item: any) => {
      return !keys.includes(item.id)
    })
    this.setState({ dataSource: newDataSource })
  }

  handleInputChange = (e: any) => {
    this.setState({ flowCode: e.target.value })
  }

  handleCheckChange = e => {
    this.setState({ lightingErrorFeedback: e.target.checked })
  }

  private lightingSearchErrorFeedback = (msg: string, errType: string = 'warning') => {
    const { lightingErrorFeedback } = this.state
    if (lightingErrorFeedback) {
      errType === 'warning' ? showModal.warning({ title: msg }) : showModal.error({ title: msg })
    } else {
      errType === 'warning' ? message.warning(msg) : message.error(msg)
    }
  }

  render() {
    const { flowCode, dataSource, badge } = this.state
    return (
      <div className={styles['lightning-send-view']}>
        <div className="lightning-body">
          <div className="input-parent-div" style={{ paddingBottom: 25 }}>
            <LightingMode
              flowCode={flowCode}
              hasLightingCheck={true}
              onChange={this.handleInputChange}
              onPressEnter={this.onPressEnter}
              onCheckChange={this.handleCheckChange}
              additionComponent={badge ? <span className={styles['span-badge']}>+1</span> : null}
            />
          </div>
          {window.isNewHome ? (
            <NewDataGridLoader
              handleShowDetail={this.handleShowDetail}
              handleAddExpress={this.handleAddExpress}
              handleAddExpressList={this.handleAddExpressList}
              handleJumpExpress={this.handleJumpExpress}
              dataSource={dataSource}
            />
          ) : (
            <DataGridSimple
              handleShowDetail={this.handleShowDetail}
              handleJumpExpress={this.handleJumpExpress}
              handleAddExpress={this.handleAddExpress}
              handleAddExpressList={this.handleAddExpressList}
              dataSource={dataSource}
            />
          )}
        </div>
      </div>
    )
  }
}
