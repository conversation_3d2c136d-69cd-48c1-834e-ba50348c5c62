.data-grid-simple-view-modal {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin-top: 16px;
  :global {
    .table-view {
      flex: 1;
      overflow-x: hidden;
      overflow-y: auto;
      span,
      td,
      a {
        font-size: 14px;
      }
    }
    .table-footer {
      box-shadow: 0 1px 16px 0 rgba(0, 0, 0, 0.06);
      background-color: rgba(255, 255, 255, 0.95);
      z-index: 2;
      padding: 12px 16px;
      span {
        font-size: 14px;
      }
      > span {
        margin-left: 12px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
}

.data-grid-simple-view {
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  flex: 1;
  margin-top: 8px;
  :global {
    .table-view {
      width: 100%;
      span,
      td,
      a {
        font-size: 14px;
      }
      .ant-table-selection-column {
        > span > div:after {
          content: none;
        }
      }
    }
    .table-footer {
      padding-top: 15px;
      span {
        font-size: 14px;
      }
      > span {
        margin-left: 12px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
}
