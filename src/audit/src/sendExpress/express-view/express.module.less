/*
 * @Author: Onein
 * @Date: 2018-10-08 12:08:04
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2019-03-19 11:53:31
 */

.express_wrapper {
  display: flex;
  flex: 1;
  width: 100%;
  background-color: #ffffff;
  border: solid 1px #e5e5e5;
  & > div {
    height: auto;
  }
  @font-size: 14px;
  :global {
    .lightning-header {
      height: 90px;
      width: 100%;
      box-shadow: inset 0 0.5px 0 0 rgba(0, 0, 0, 0.09);
      background-color: #ffffff;
      padding: 16px 24px;

      .title {
        font-size: 20px;
        font-weight: 500;
        color: #000000;
      }
    }

    .lightning-body {
      width: 100%;
    }

    .ant-breadcrumb {
      font-size: @font-size;
    }

    .ekb-tab-content {
      display: flex;
      > div {
        height: auto;
      }
    }
  }
}

.express_wrapper_layout5 {
  border: none;
  > button{
    margin-top: 14px;
    font-size: 12px;
  }

}
.lightning-send-inline-right{
  right: 15px;
}