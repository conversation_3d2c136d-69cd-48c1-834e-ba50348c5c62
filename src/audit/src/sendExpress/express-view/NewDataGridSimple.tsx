import { Column } from '@ekuaibao/datagrid/lib/types/column'
import React, { PureComponent } from 'react'
import { Button } from '@hose/eui'
import { getBaseCol } from '../../util/Utils'
import styles from './DataGridSimple.module.less'
import { app as api } from '@ekuaibao/whispered'
import * as DataGrid from '@ekuaibao/datagrid'

type RecordData = any

export interface NewDataGridSimpleProps {
  dataSource: any[]
  handleJumpExpress(record: RecordData, callback: Function): void
  handleAddExpress(record: RecordData, callback: Function): void
  handleAddExpressList(keys: string[], name: string, callBack: Function, selectedData: any[]): void
  handleShowDetail(data: any): void
}

interface NewDataGridSimpleState {
  selectedRowKeys: string[]
  selectedRows: any[]
}

export class NewDataGridSimple extends PureComponent<
  NewDataGridSimpleProps,
  NewDataGridSimpleState
> {
  state: NewDataGridSimpleState = {
    selectedRowKeys: [],
    selectedRows: [],
  }

  getColumns = (): Column[] => {
    const baseCol = getBaseCol()
    const { handleJumpExpress, handleAddExpress } = this.props
    const _this = this
    const action = {
      title: i18n.get('操作'),
      dataIndex: 'action',
      key: 'action',
      width: 220,
      render(_text: string, line: RecordData) {
        return (
          <div
            onClick={e => {
              e.persist()
              e.nativeEvent.stopImmediatePropagation()
              e.stopPropagation()
              e.preventDefault()
              return false
            }}>
            {line.state === 'SENDING' && (
              <a
                className="ant-dropdown-link mr-16"
                onClick={() => {
                  handleAddExpress && handleAddExpress(line, _this.clearSelected)
                }}>
                {i18n.get('添加寄送信息')}
              </a>
            )}
            <a
              className="ant-dropdown-link mr-16"
              onClick={() => {
                handleJumpExpress && handleJumpExpress(line, _this.clearSelected)
              }}>
              {i18n.get('跳过寄送')}
            </a>
          </div>
        )
      },
    }
    return [...baseCol, action].map(col => ({
      ...col,
      label: col.dataIndex,
      sorter: false,
      allowReordering: false,
      allowHiding: true,
      allowGrouping: false,
      minWidth: 120,
    }))
  }
  handleRowClick = (record: any) => {
    api
      .invokeService('@bills:get:flow-info', { id: record.flowId.id })
      .then(({ value }: { value: any }) => {
        const { handleShowDetail } = this.props
        handleShowDetail && handleShowDetail(value)
      })
  }
  clearSelected = () => {
    this.setState({ selectedRowKeys: [], selectedRows: [] })
  }

  handleSelectedChange = (selectedRowKeys: string[], selectedRows: any) => {
    this.setState({ selectedRowKeys, selectedRows })
  }

  render() {
    const { selectedRowKeys, selectedRows } = this.state
    const { dataSource, handleAddExpressList } = this.props
    const columns = this.getColumns()
    return (
      <div className={styles['data-grid-simple-view']}>
        <DataGrid.TableWrapper
          rowKey="id"
          className="table-view"
          dataSource={dataSource}
          columns={columns}
          selectedRowKeys={selectedRowKeys}
          isMultiSelect
          onSelectedChange={this.handleSelectedChange}
          onRowClick={this.handleRowClick}
        />
        <div className={'table-footer'}>
          <Button
            category="secondary"
            disabled={!(selectedRowKeys && selectedRowKeys.length > 0)}
            onClick={() =>
              handleAddExpressList(selectedRowKeys, 'add_express', this.clearSelected, selectedRows)
            }>
            {i18n.get('添加寄送信息')}
          </Button>
          <Button
            category="secondary"
            className="ml-12"
            disabled={!(selectedRowKeys && selectedRowKeys.length > 0)}
            onClick={() =>
              handleAddExpressList(
                selectedRowKeys,
                'jump_express',
                this.clearSelected,
                selectedRows,
              )
            }>
            {i18n.get('跳过寄送')}
          </Button>
          <span>
            {i18n.get('已选择 {__k0}/{__k1} 张', {
              __k0: selectedRowKeys.length,
              __k1: dataSource.length,
            })}
          </span>
        </div>
      </div>
    )
  }
}
export default NewDataGridSimple
