import { app } from '@ekuaibao/whispered'
import styles from './express.view.container.module.less'
import React, { PureComponent } from 'react'
import { EnhanceStackerManager } from '@ekuaibao/enhance-stacker-manager'
const EKBBreadcrumb = app.require('@ekb-components/business/breadcrumb')
import { EnhanceConnect } from '@ekuaibao/store'

@EnhanceConnect(state => ({
  staffs: state['@common'].staffs
}))
@EnhanceStackerManager([
  {
    key: 'ExpressView',
    getComponent: () => import('./express-view'),
    title: i18n.get('寄送')
  },
  {
    key: 'LightningExpress',
    getComponent: () => import('./lightning-send'),
    title: i18n.get('闪电寄送')
  }
])
export default class ExpressViewContainer extends PureComponent {
  componentDidMount() {
    this.props.stackerManager.push('ExpressView', {})
  }
  handleMenuClick(line, i) {
    return this.props.stackerManager.open(i, {})
  }
  renderBreadcrumb() {
    const array = this.props.stackerManager.values()
    let items = []
    array.forEach((line, i) => {
      items.push({
        key: i,
        onClick: () => this.handleMenuClick(line, i),
        title: line.title
      })
    })
    return <EKBBreadcrumb items={items} {...this.props} />
  }

  render() {
    const length = this.props.stackerManager.keys().length
    return (
      <div className={styles['express-view-container']}>
        {length > 1 && (
          <div className="header">
            <div className="menu">{this.renderBreadcrumb()}</div>
          </div>
        )}
        <div className="express-content">{this.props.children}</div>
      </div>
    )
  }
}
