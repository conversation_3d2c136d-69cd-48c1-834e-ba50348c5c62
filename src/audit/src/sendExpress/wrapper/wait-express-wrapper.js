import { app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
const DataGridWrapper = api.require('@elements/data-grid/DataGridWrapper')
const { createNodeNameColumn, createNodeStaffColumn } = api.require('@elements/data-grid-v2/CreateColumn')
import { searchBackLogsCalcNoPage } from '../../audit-action'
import { fetchBackLogs } from '../../util/fetchUtil'
import { MessageCenter } from '@ekuaibao/messagecenter'
import { mapping } from '../../util/mappingWSend'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import {
  handlePrint,
  handleActionImplementation,
  handlePrintRemind,
  handleSendExpressList,
  handleReceivePrint,
  fnGetFlowIds,
} from '../../service'
import { createActionColumn4WaitToSend, createRiskWarningColumn } from '../../util/columnsAndSwitcherUtil'
const { exportExcel } = api.require('@lib/export-excel-service')
import { cloneDeep } from 'lodash'
import { Resource } from '@ekuaibao/fetch'
import * as viewUtil from '../../view-util'
import { getDefaultScenes } from '../../util/Utils'
const LoaderWithLegacyData = api.require('@elements/data-grid-v2/LoaderWithLegacyData')
const { searchOptions, globalSearchOptions } = api.require('@lib/data-grid-v2/CustomSearchUtil')
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

const WaitExpress = new Resource('/api/flow/v2/filter')
const scenesType = 'WaitExpress'
const prefixColumns = { state: 'flowId', '*': 'flowId.form' }
import { EnhanceConnect } from '@ekuaibao/store'
import { externalStaffActionBlackList } from '../../view-util'

@EnhanceConnect(state => ({
  legalEntityCurrencyPower: state['@common'].powers.legalEntityCurrency,
  // 涉及需求 https://hose2019.feishu.cn/wiki/wikcncXiMtXZQwNcS64OMEwSsJd#fTSuk8
  KA_GLOBAL_SEARCH_2: state['@common'].powers.KA_GLOBAL_SEARCH_2,
  showPrintBtn: state['@common'].showPrintBtn,
}))
export default class WaitExpressWrapper extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
    this.state = {
      scenes: [],
    }
  }

  componentDidMount() {
    this.bus.on('buttons:click', this.handleButtonsClick)
    this.bus.on('table:row:click', this.handleTableRowClick)
    this.bus.on('table:row:action', this.handleActions)
    this.bus.on('selectAllBtn:click', this.handleSelectAllBtnClick)
    this.bus.on('initScenes:action', this.initScenes.bind(this))

    WaitExpress.GET('/$type', { type: scenesType }).then(res => {
      const { value } = res
      if (this.props.specifications.length) {
        this.initScenes(value)
        api
          .invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned')
          .then(() => this.initScenes(value))
      } else {
        api
          .invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned')
          .then(() => this.initScenes(value))
      }
    })
  }

  componentWillUnmount() {
    this.bus.un('buttons:click', this.handleButtonsClick)
    this.bus.un('table:row:click', this.handleTableRowClick)
    this.bus.un('table:row:action', this.handleActions)
    this.bus.un('selectAllBtn:click', this.handleSelectAllBtnClick)
    this.bus.un('initScenes:action', this.initScenes)
  }

  initScenes(data) {
    const { specifications } = this.props
    const defaultScenes = getDefaultScenes('flowId', ['expense', 'loan', 'requisition'], specifications)
    const allScenes = { text: i18n.get('全部'), scene: 'all', active: true, sceneIndex: 'all' }
    const filter = data
      ? data.filter.map(d => {
          let temp = JSON.parse(d)
          temp.pageSize = data.pageSize || 20
          return temp
        })
      : defaultScenes
    const scenes = !!~filter.findIndex(el => el.scene === 'all') ? filter : [allScenes, ...filter]
    this.setState({ scenes })
  }

  fetchPending = async (params = {}, dimensionItems = {}) => {
    const { scenes } = this.state
    const { scene } = params
    const findScene = scenes.find(s => s.sceneIndex === scene)
    if (findScene) {
      params.scene = findScene.scene
    }
    params.sending = true
    scene && this.setState({ scene, fetchParams: params, dimensionItems })
    const res = await fetchBackLogs(params, findScene, dimensionItems)
    this._dataSource = res.dataSource
    return res
  }

  handleSelectAllBtnClick = (params = {}) => {
    const { scenes, scene, fetchParams, dimensionItems } = this.state
    const findScene = scenes.find(s => s.sceneIndex === scene)
    findScene && (params.scene = findScene.scene || '')

    if (findScene) {
      params.scene = findScene.scene
    }
    if (fetchParams) {
      if (fetchParams.filters) params.filters = fetchParams.filters
      if (fetchParams.searchText) params.searchText = fetchParams.searchText
    }
    params.sending = true
    const { legalEntityCurrencyPower, userInfo } = this.props
    return searchBackLogsCalcNoPage(params, findScene, dimensionItems, legalEntityCurrencyPower).then(resp => {
      let data = {}
      resp &&
        resp.value &&
        resp.value.flows.length > 0 &&
        resp.value.flows.forEach(flow => {
          data[flow.id] = flow
        })
      let sum = (resp && resp.value && resp.value.formMoney) || 0
      let keys = Object.keys(data)
      let buttons = [
        {
          name: i18n.get('添加寄送信息'),
          type: 'normal',
          key: 'add_express',
        },
        {
          name: i18n.get('跳过寄送'),
          type: 'normal',
          key: 'jump_express',
        },
        {
          name: i18n.get('导出'),
          type: 'normal',
          key: 'export_all',
        },
        {
          name: i18n.get('打印单据'),
          type: 'normal',
          key: 'print',
        },
        {
          name: i18n.get('打印提醒'),
          type: 'normal',
          key: 'print_remind',
        },
        {
          name: i18n.get('收到打印'),
          type: 'normal',
          key: 'recive_print',
        },
      ]

      if (this.props.showPrintBtn) {
        buttons = buttons
          .slice(0, 4)
          .concat([
            {
              name: i18n.get('打印单据和发票'),
              type: 'normal',
              key: 'printInvoice',
            },
          ])
          .concat(buttons.slice(4, buttons.length))
      }

      if (userInfo?.staff?.external) {
        buttons = buttons.filter(item => !externalStaffActionBlackList.includes(item.key))
      }

      api.open('@layout:DataGridSelectAllModal', { keys, buttons, sum }).then(name => {
        this.handleButtonsClick({ name, data, keys }, params)
      })
    })
  }

  fnGetScene() {
    const { scenes, scene } = this.state
    let cloneScenes = cloneDeep(scenes)
    let findScenes = cloneScenes.find(s => s.sceneIndex === scene)
    if (findScenes) {
      findScenes.scene = findScenes.scene !== 'waitInvoice' ? findScenes.scene : ''
    }
    if (findScenes && findScenes.scene) return scene
    return findScenes
  }

  handleButtonsClick = ({ name, data, keys }, fetchParams) => {
    keys = keys ? keys : this.bus.getSelectedRowKeys()
    data = data ? data : this.bus.getSelectedRowData()
    switch (name) {
      case 'add_express': {
        const flowIds = fnGetFlowIds(data)
        return this._handleSendExpressList(keys, name, { flowIds })
      }
      case 'jump_express': {
        const flowIds = fnGetFlowIds(data)
        return this._handleSendExpressList(keys, name, { flowIds })
      }
      case 'print':
        return this._handlePrintList(keys, data, this.reloadAndClearSelecteds, '0')
      case 'printInvoice':
        return this._handlePrintList(keys, data, this.reloadAndClearSelecteds, '1')
      case 'export_selected':
        return exportExcel({ exportType: 'export_selected', funcType: 'backlog', data }, this.bus)
      case 'export_all': {
        let params = fetchParams || this.bus.getFilterParam()
        params.status = { ...status, state: ['SENDING'] }
        const scene = this.fnGetScene()
        params.scene = scene
        let exportParam = {
          exportType: 'export_all',
          funcType: 'backlog',
          data: params,
          onlyAsyncExport: true,
        }
        return exportExcel(exportParam, this.bus)
      }
      case 'print_remind':
        this._handlePrintRemindList(keys, data)
        break
      case 'recive_print':
        const flowIds = Object.values(data).map(item => item.flowId.id)
        this.handleReceivePrint(flowIds, this.reloadAndClearSelecteds)
        break
    }
  }

  reloadAndClearSelecteds = () => {
    this.bus.clearSelectedRowKeys()
    this.bus.reload()
  }

  _handlePrintList(keys, data, fn, printInvoice) {
    handlePrint.call(this, keys, data, fn, false, printInvoice)
  }

  __billInfoPopup = backlog => {
    viewUtil.getLoanRisk(backlog).then(riskTip => {
      let title = `${billTypeMap()[backlog.type]}${i18n.get('详情')}`
      api.open(
        '@bills:BillInfoPopup',
        {
          title: title,
          backlog,
          riskTip: riskTip,
          isEditConfig: true,
          isShowCondition: true,
          invokeService: '@audit:get:backlog-info',
          showExpressButton: true,
          reload: this.bus.reload,
          params: {
            id: backlog.id,
            type: backlog.type,
          },
          scene: 'APPROVER',
          needConfigButton: true,
          mask: false,
          onOpenOwnerLoanList: line => {
            this.__handleLine(9, line)
          },

          onFooterButtonsClick: (type, line) => {
            api.close()
            setTimeout(() => {
              this.__handleLine(type, line)
            }, 0)
          },
        },
        true,
      )
    })
  }

  _handlePrintRemindList = (keys, data) => {
    const backLogs = Object.values(data)
    const flowIds = backLogs.map(element => element.flowId.id)
    handlePrintRemind.call(this, flowIds, this.reload)
  }

  __handleLine(type, line) {
    handleActionImplementation.call(this, { type, backlog: line, fn: this.reload })
  }

  handleReceivePrint = (key, fn) => {
    handleReceivePrint.call(this, key, fn)
  }

  handleTableRowClick = backlog => {
    startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()

    if (api.require('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
      api.open('@bills:BillInfoDrawerV2', {
        currentId: backlog.flowId.id,
        flows: this._dataSource?.map(item => item.flowId) || [],
        bus: this.bus,
        billDetailsProps: {
          isEditConfig: true,
          onOpenOwnerLoanList: line => {
            this.__handleLine(9, line)
          },
        },
        onFooterButtonsClick: (type, line) => {
          api.close()
          setTimeout(() => {
            this.__handleLine(type, line)
          }, 0)
        },
      })
      return
    }

    api
      .invokeService('@audit:get:backlog-info', {
        id: backlog.id,
        type: backlog.type,
      })
      .then(backlog => {
        this.__billInfoPopup(backlog)
      })
  }

  reload = _ => {
    api.invokeService('@layout5:refresh:menu:data')
    this.bus.clearSelectedRowKeys()
    this.bus.reload().then(() => {
      setTimeout(() => {
        this.bus.emit('table:select:current:row')
      }, 500);
    })
  }

  _handleSendExpressList = (keys, name, params) => {
    handleSendExpressList(keys, name, this.reload, params)
  }

  handleActions = (type, line) => {
    handleActionImplementation.call(this, { type, backlog: line, fn: this.reload })
  }

  getOptionButtons = () => {
    const btns = [
      { text: i18n.get('添加寄送信息'), name: 'add_express', type: 'primary' },
      { text: i18n.get('跳过寄送'), name: 'jump_express' },
      { text: i18n.get('导出选中'), name: 'export_selected' },
      { text: i18n.get('打印单据'), name: 'print' },
      { text: i18n.get('打印单据和发票'), name: 'printInvoice' },
      { text: i18n.get('打印提醒'), name: 'print_remind' },
      { text: i18n.get('收到打印'), name: 'recive_print' },
    ]
    const { showPrintBtn, userInfo } = this.props
    let showButtons = showPrintBtn ? btns : btns.filter(v => v.name !== 'printInvoice')
    if (userInfo?.staff?.external) {
      showButtons = showButtons.filter(v => !externalStaffActionBlackList.includes(v.name))
    }
    return showButtons
  }

  buttons = this.getOptionButtons()

  render() {
    const { baseDataProperties, KA_GLOBAL_SEARCH_2 } = this.props
    const { scenes } = this.state

    if (!scenes.length) {
      return null
    }
    return (
      <LoaderWithLegacyData
        newSearch={true}
        searchOptions={KA_GLOBAL_SEARCH_2 ? globalSearchOptions() : searchOptions()}
        enableGlobalSearch={KA_GLOBAL_SEARCH_2}
        scenes={scenes}
        fetch={this.fetchPending}
        scenesType={scenesType}
        buttons={this.buttons}
        onButtonClick={this.handleButtonsClick}
        onSelectedAll={this.handleSelectAllBtnClick}
        resource={WaitExpress}
        baseDataProperties={baseDataProperties}
        createAction={createActionColumn4WaitToSend}
        bus={this.bus}
        prefixColumns={prefixColumns}
        mapping={mapping}
        createRiskWarningColumn={() => createRiskWarningColumn(true)}
        showOutsideButtonCount={4}
        createNodeNameColumn={() => createNodeNameColumn({ dataIndex: 'flowId.nodeState.nodeName' })}
        createNodeStaffColumn={() => createNodeStaffColumn({ dataIndex: 'flowId.nodeState.staffName', filterType: false })}
        useNewFieldSet
      />
    )
  }
}
