import { app as api } from '@ekuaibao/whispered'
import { PureComponent } from 'react'
const DataGridWrapper = api.require('@elements/data-grid/DataGridWrapper')
const { createNodeNameColumn, createNodeStaffColumn } = api.require('@elements/data-grid-v2/CreateColumn')
import { MessageCenter } from '@ekuaibao/messagecenter'
import { mapping } from '../../util/mappingSend'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import { handlePrint, handleActionImplementation } from '../../service'
import { searchApprovedFlowsCalcNoPage } from '../../audit-action'
import { fetchApproved } from '../../util/fetchUtil'
import { createActionsColumn4Sent, createRiskWarningColumn } from '../../util/columnsAndSwitcherUtil'
const { exportExcel } = api.require('@lib/export-excel-service')
import { cloneDeep } from 'lodash'
import { Resource } from '@ekuaibao/fetch'
import { getDefaultScenes } from '../../util/Utils'
const LoaderWithLegacyData = api.require('@elements/data-grid-v2/LoaderWithLegacyData')
const { searchOptions, globalSearchOptions } = api.require('@lib/data-grid-v2/CustomSearchUtil')
import { EnhanceConnect } from '@ekuaibao/store'
import { externalStaffActionBlackList } from '../../view-util'
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

const approved = new Resource('/api/flow/v2/filter')
const scenesType = 'SendExpress'
const prefixColumns = { state: '$', '*': 'form' }

@EnhanceConnect(state => ({
  legalEntityCurrencyPower: state['@common'].powers.legalEntityCurrency,
  userInfo: state['@common'].userinfo.data,
  // 涉及需求 https://hose2019.feishu.cn/wiki/wikcncXiMtXZQwNcS64OMEwSsJd#fTSuk8
  KA_GLOBAL_SEARCH_2: state['@common'].powers.KA_GLOBAL_SEARCH_2,
  showPrintBtn: state['@common'].showPrintBtn,
}))
export default class ApprovedWrapper extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
    this.state = {
      scenes: [],
    }
  }

  componentDidMount() {
    this.bus.on('buttons:click', this.handleButtonsClick)
    this.bus.on('table:row:click', this.handleTableRowClick)
    this.bus.on('table:row:action', this.handleActions)
    this.bus.on('selectAllBtn:click', this.handleSelectAllBtnClick)
    this.bus.on('initScenes:action', this.initScenes.bind(this))

    // 获取场景列表
    approved.GET('/$type', { type: scenesType }).then(res => {
      const { value } = res
      if (this.props.specifications.length) {
        this.initScenes(value)
        api
          .invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned')
          .then(() => this.initScenes(value))
      } else {
        api
          .invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned')
          .then(() => this.initScenes(value))
      }
    })
  }

  componentWillUnmount() {
    this.bus.un('buttons:click', this.handleButtonsClick)
    this.bus.un('table:row:click', this.handleTableRowClick)
    this.bus.un('table:row:action', this.handleActions)
    this.bus.un('selectAllBtn:click', this.handleSelectAllBtnClick)
    this.bus.un('initScenes:action', this.initScenes)
  }

  initScenes(data) {
    const { specifications } = this.props
    const defaultScenes = getDefaultScenes('', ['expense', 'loan', 'requisition'], specifications)
    const allScenes = { text: i18n.get('全部'), scene: 'all', active: true, sceneIndex: 'all' }
    const filter = data
      ? data.filter.map(d => {
          let temp = JSON.parse(d)
          temp.pageSize = data.pageSize || 20
          return temp
        })
      : defaultScenes
    const scenes = !!~filter.findIndex(el => el.scene === 'all') ? filter : [allScenes, ...filter]
    this.setState({ scenes })
  }

  __status = { 'approvedFlow.action': 'SENT' }

  fetchPending = async (params = {}, dimensionItems = {}) => {
    params.status = this.__status
    const { scenes } = this.state
    const { scene } = params
    const findScene = scenes.find(s => s.sceneIndex === scene)
    if (findScene) {
      params.scene = findScene.scene
    }
    scene && this.setState({ scene, fetchParams: params, dimensionItems })
    const res = await fetchApproved(params, findScene, dimensionItems)
    this._currentDataSource = res.dataSource
    return res
  }

  handleSelectAllBtnClick = (params = {}) => {
    params.status = this.__status
    const { scenes, scene, fetchParams, dimensionItems } = this.state
    const findScene = scenes.find(s => s.sceneIndex === scene)
    findScene && (params.scene = findScene.scene || '')

    if (findScene) {
      params.scene = findScene.scene
    }
    if (fetchParams) {
      if (fetchParams.filters) params.filters = fetchParams.filters
      if (fetchParams.searchText) params.searchText = fetchParams.searchText
    }
    params.sent = true
    const { legalEntityCurrencyPower } = this.props
    return searchApprovedFlowsCalcNoPage(params, findScene, dimensionItems, legalEntityCurrencyPower).then(resp => {
      const data = {}
      resp &&
        resp.value &&
        resp.value.flows.length > 0 &&
        resp.value.flows.forEach(flow => {
          data[flow.id] = flow
        })
      const sum = (resp && resp.value && resp.value.formMoney) || 0
      const keys = Object.keys(data)
      const buttons = [
        {
          name: i18n.get('导出'),
          type: 'normal',
          key: 'export_all',
        },
        {
          name: i18n.get('打印单据'),
          type: 'normal',
          key: 'print',
        },
      ]

      if (this.props.showPrintBtn)
        buttons.push({
          name: i18n.get('打印单据和发票'),
          type: 'normal',
          key: 'printInvoice',
        })

      api.open('@layout:DataGridSelectAllModal', { keys, buttons, sum }).then(name => {
        this.handleButtonsClick({ name, data, keys }, params)
      })
    })
  }

  fnGetScene() {
    const { scenes, scene } = this.state
    const cloneScenes = cloneDeep(scenes)
    const findScenes = cloneScenes.find(s => s.sceneIndex === scene)
    if (findScenes) {
      findScenes.scene = findScenes.scene !== 'waitInvoice' ? findScenes.scene : ''
    }
    if (findScenes && findScenes.scene) return scene
    return findScenes
  }

  handleButtonsClick = ({ name, data, keys }, fetchParams) => {
    keys = keys ? keys : this.bus.getSelectedRowKeys()
    data = data ? data : this.bus.getSelectedRowData()
    const { userInfo } = this.props
    switch (name) {
      case 'print':
        return this._handlePrintList(
          keys,
          data,
          () => {
            this.bus.clearSelectedRowKeys()
            this.bus.reload()
          },
          '0',
        )
      case 'printInvoice':
        return this._handlePrintList(
          keys,
          data,
          () => {
            this.bus.clearSelectedRowKeys()
            this.bus.reload()
          },
          '1',
        )
      case 'export_selected':
        return exportExcel({ exportType: 'export_selected', funcType: 'approve', data }, this.bus)
      case 'export_all': {
        const params = fetchParams || this.bus.getFilterParam()
        params.status = {}
        const scene = this.fnGetScene()
        params.scene = scene
        params.filters = { ...params.filters }
        const exportParam = {
          exportType: 'export_all',
          funcType: 'backlog',
          data: params,
          onlyAsyncExport: true,
          others: {
            unNeedState: true,
            filter: `(flowId.approvedFlow.approverId=="${userInfo?.staff?.id}")&&(flowId.formType!="permit")&&(flowId.approvedFlow.action=="SENT")`,
          },
        }
        if (scene && scene.sceneIndex === 'waitInvoice') {
          exportParam.others.queryString = 'waitInvoice=true'
        }
        return exportExcel(exportParam, this.bus)
      }
    }
  }

  _handlePrintList(keys, data, fn, printInvoice) {
    handlePrint.call(this, keys, data, fn, false, printInvoice)
  }

  __billInfoPopup = flow => {
    const title = `${i18n.get(billTypeMap()[flow.formType])}${i18n.get('详情')}`
    api.open(
      '@bills:BillInfoPopup',
      {
        title,
        backlog: { id: -1, flowId: flow },
        invokeService: '@bills:get:flow-info',
        params: { id: flow.id },
        showExpressButton: true,
        reload: this.bus.reload,
        scene: 'APPROVER',
        mask: false,
      },
      true,
    )
  }

  handleTableRowClick = backlog => {
    startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()

    if (api.require('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
      api.open('@bills:BillInfoDrawerV2', {
        currentId: backlog.id,
        flows: this._currentDataSource || [],
        bus: this.bus,
      })
      return
    }

    api.invokeService('@bills:get:flow-info', { id: backlog.id }).then(resp => {
      this.__billInfoPopup(resp.value)
    })
  }

  handleActions = (type, line) => {
    handleActionImplementation.call(this, {
      type,
      backlog: line,
      fn: () => {
        this.bus.clearSelectedRowKeys()
        this.bus.reload()
      },
    })
  }

  getOptionButtons = () => {
    const btns = [
      { text: i18n.get('导出选中'), name: 'export_selected' },
      { text: i18n.get('打印单据'), name: 'print' },
      { text: i18n.get('打印单据和发票'), name: 'printInvoice' },
    ]
    const { showPrintBtn, userInfo } = this.props
    let showButtons = showPrintBtn ? btns : btns.filter(v => v.name !== 'printInvoice')
    if (userInfo?.staff?.external) {
      showButtons = showButtons.filter(v => !externalStaffActionBlackList.includes(v.name))
    }
    return showButtons
  }

  buttons = this.getOptionButtons()

  render() {
    const { baseDataProperties, KA_GLOBAL_SEARCH_2 } = this.props
    const { scenes } = this.state

    if (!scenes.length) {
      return null
    }
    return (
      <LoaderWithLegacyData
        newSearch={true}
        searchOptions={KA_GLOBAL_SEARCH_2 ? globalSearchOptions('form') : searchOptions('form')}
        enableGlobalSearch={KA_GLOBAL_SEARCH_2}
        scenes={scenes}
        fetch={this.fetchPending}
        scenesType={scenesType}
        buttons={this.buttons}
        onButtonClick={this.handleButtonsClick}
        onSelectedAll={this.handleSelectAllBtnClick}
        resource={approved}
        baseDataProperties={baseDataProperties}
        createAction={createActionsColumn4Sent}
        bus={this.bus}
        prefixColumns={prefixColumns}
        mapping={mapping}
        createRiskWarningColumn={createRiskWarningColumn}
        createNodeNameColumn={createNodeNameColumn}
        createNodeStaffColumn={createNodeStaffColumn}
        useNewFieldSet
      />
    )
  }
}
