/*
 * @Author: Onein 
 * @Date: 2018-10-09 16:06:19 
 * @Last Modified by: Onein
 * @Last Modified time: 2018-10-26 15:17:55
 */
 @import '~@ekuaibao/web-theme-variables/styles/colors.less';

.jump-express-modal-wrapper {
  height: 242px;

  :global {
    .modal-header {
      &.jump-express-header {
        font-size: 20px;
        font-weight: 500;
        line-height: 1.4;
        text-align: justify;
        color: #000000;
        border-bottom: none;

        .flex {
          padding-left: 8px;
        }
      }
    }

    .jump-express-content {
      padding: 0 56px;
      line-height: 1.57;
      color: rgba(0, 0, 0, 0.65);

      .ant-form-item {
        margin-bottom: 0;

        .ant-input {
          height: 40px;
        }

        .ant-select-selection--single,
        .ant-select-selection__rendered {
          height: 40px;
          line-height: 40px;
        }

        label,
        .ant-select-selection--single {
          font-size: 14px;
        }
      }
    }

    .modal-footer {
      &.jump-express-footer {
        width: 100%;
        height: 56px;
        padding: 12px 16px;
        border-top: none;
        position: absolute;
        right: 0;
        bottom: 0;

        button {
          font-size: 14px;
          line-height: 1.57;
        }
      }
    }
  }
}
