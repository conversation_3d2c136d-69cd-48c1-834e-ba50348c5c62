import React, { PureComponent } from 'react'

export class BaseSendExpressComponent extends PureComponent {
  constructor(props) {
    super(props)
    this.bus = props.bus
    this.getResult = this.getResult.bind(this)
    props.overrideGetResult(this.getResult)
    this.state = {}
  }

  setValue = () => {
    this.props.form.resetFields()
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  getResult() {
    return this.result
  }

  handleSubmit() {
    this.props.form.validateFields(err => {
      if (err) {
        return
      }
      this.props.layer.emitOk()
    })
  }
}
