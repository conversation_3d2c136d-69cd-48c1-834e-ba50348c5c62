import { app } from '@ekuaibao/whispered'
import { BaseSendExpressComponent } from './BaseSendExpressComponent'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Form } from 'antd'
import { Button, Input, Tooltip } from '@hose/eui'
import styles from './AddExpressInfo.module.less'
import { OutlinedTipsClose } from '@hose/eui-icons'
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')

const FormItem = Form.Item
const numberRule = {
  rules: [
    {
      required: true,
      pattern: new RegExp(/^[A-Za-z0-9]+$/),
      validator: function (rule, value, callback) {
        if (!value) {
          callback(i18n.get('请输入寄送单号'))
        } else if (!rule.pattern.test(value)) {
          callback(i18n.get('请输入正确格式寄送单号'))
        } else if (value.trim().length < 3) {
          callback(i18n.get('寄送单号长度必须大于3位'))
        } else if (value.trim().length > 30) {
          callback(i18n.get('寄送单号长度必须小于30位'))
        }
        callback()
      },
    },
  ],
}

const formItemLayout = {
  labelCol: {
    span: 24,
  },
  wrapperCol: {
    span: 24,
  },
}

const svg = (
  <Tooltip placement="top" title={i18n.get('支持扫码器录入')}>
    <svg aria-hidden="true" className="icon suffix">
      <use xlinkHref="#EDico-scanner" />
    </svg>
  </Tooltip>
)

@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer',
})
@EnhanceFormCreate()
export default class AddExpressInfoModal extends BaseSendExpressComponent {
  handleOk = () => {
    this.props.form.validateFields(err => {
      if (err) {
        return
      }
      let obj = {
        num: this.props.form.getFieldValue('number'),
        remark: this.props.form.getFieldValue('remark'),
        phone: this.props.form.getFieldValue('phone'),
      }
      this.props.layer.emitOk(obj)
    })
  }

  handleChange = e => {
    const val = e.target.value
    let phoneRequired
    if (val.startsWith('SF')) {
      phoneRequired = true
    } else {
      phoneRequired = false
    }
    this.setState({ phoneRequired })
  }

  render() {
    const { getFieldDecorator } = this.props.form
    const { expressTitle, data } = this.props
    const { phoneRequired } = this.state
    return (
      <div className={styles['add-express-modal-wrapper']}>
        <div className="modal-header add-express-header">
          <div className="flex">{i18n.get(expressTitle || i18n.get('添加寄送信息'))}</div>
          <OutlinedTipsClose className="cross-icon" onClick={this.handleCancel} />
        </div>
        <div className="add-express-content">
          <Form onSubmit={this.handleSubmit}>
            <FormItem {...formItemLayout} label={i18n.get('寄送单号')}>
              {getFieldDecorator('number', {
                ...numberRule,
                initialValue: data && data.express && data.express.num,
              })(
                <Input
                  placeholder={i18n.get('请输入寄送单号')}
                  suffix={svg}
                  onChange={this.handleChange}
                />,
              )}
            </FormItem>
            {phoneRequired && (
              <FormItem {...formItemLayout} label={i18n.get('寄件人或收件人手机号')}>
                {getFieldDecorator('phone', {
                  initialValue: data && data.phone,
                  rules: [
                    { required: true, message: '请输入寄件人或收件人手机号' },
                    {
                      pattern: /^1\d{10}$/,
                      message: '手机格式不正确',
                    },
                  ],
                })(<Input placeholder={i18n.get('请输入手机号')} />)}
              </FormItem>
            )}
            <FormItem {...formItemLayout} label={i18n.get('备注')}>
              {getFieldDecorator('remark', {
                initialValue: data && data.remark,
                rules: [{ max: 200, message: i18n.get('备注信息不能超过200个字符') }],
              })(<Input placeholder={i18n.get('请输入备注信息')} />)}
            </FormItem>
          </Form>
        </div>
        <div className="modal-footer add-express-footer">
          <Button category="secondary" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </Button>
          <Button className="btn-ml" onClick={this.handleOk}>
            {i18n.get('确定')}
          </Button>
        </div>
      </div>
    )
  }
}
