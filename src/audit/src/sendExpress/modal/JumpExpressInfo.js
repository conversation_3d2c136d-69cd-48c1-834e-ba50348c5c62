import { app } from '@ekuaibao/whispered'
import { BaseSendExpressComponent } from './BaseSendExpressComponent'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Form } from 'antd'
import { Button, Input } from '@hose/eui'
import styles from './JumpExpressInfo.module.less'
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')
import { OutlinedTipsClose } from '@hose/eui-icons'

const FormItem = Form.Item
const remarkRule = {
  rules: [
    {
      required: true,
      validator: function (rule, value, callback) {
        if (!value) {
          callback(i18n.get('请输入自定义备注信息'))
        } else if (value.trim().length > 100) {
          callback(i18n.get('备注长度不超过100字'))
        }
        callback()
      },
    },
  ],
}
const formItemLayout = {
  labelCol: {
    span: 24,
  },
  wrapperCol: {
    span: 24,
  },
}

@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer',
})
@EnhanceFormCreate()
export default class JumpExpressInfoModal extends BaseSendExpressComponent {
  handleOk = () => {
    this.props.form.validateFields(err => {
      if (err) {
        return
      }
      let obj = {
        num: null,
        remark: this.props.form.getFieldValue('remark'),
      }
      this.props.layer.emitOk(obj)
    })
  }

  render() {
    const { getFieldDecorator } = this.props.form
    return (
      <div className={styles['jump-express-modal-wrapper']}>
        <div className="modal-header jump-express-header">
          <div className="flex">{i18n.get('跳过寄送')}</div>
          <OutlinedTipsClose className="cross-icon" onClick={this.handleCancel} />
        </div>
        <div className="jump-express-content">
          <Form onSubmit={this.handleSubmit}>
            <FormItem {...formItemLayout} label={i18n.get('备注')}>
              {getFieldDecorator('remark', { ...remarkRule })(
                <Input placeholder={i18n.get('请输入自定义备注信息')} />,
              )}
            </FormItem>
          </Form>
        </div>
        <div className="modal-footer jump-express-footer">
          <Button category="secondary" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </Button>
          <Button className="btn-ml" onClick={this.handleOk}>
            {i18n.get('确定')}
          </Button>
        </div>
      </div>
    )
  }
}
