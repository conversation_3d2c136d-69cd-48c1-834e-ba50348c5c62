/*
 * @Author: Onein 
 * @Date: 2018-10-09 16:06:15 
 * @Last Modified by: Onein
 * @Last Modified time: 2018-10-26 14:32:26
 */
@import '~@ekuaibao/web-theme-variables/styles/colors.less';

.add-express-modal-wrapper {
  height: 440px;

  :global {
    .modal-header {
      &.add-express-header {
        font-size: 20px;
        font-weight: 500;
        line-height: 1.4;
        text-align: justify;
        color: #000000;
        border-bottom: none;

        .flex {
          padding-left: 8px;
        }
      }
    }

    .add-express-content {
      padding: 12px 56px;
      line-height: 1.57;
      color: rgba(0, 0, 0, 0.65);

      .suffix {
        color: rgba(0, 0, 0, 0.25);
        width: 20px;
        height: 20px;
      }
      .ant-form-item {
        margin-bottom: 0;

        &:last-child {
          margin-top: 24px;
        }
        &:first-child {
          margin-bottom: 24px;
        }
        .ant-input {
          height: 40px;
        }

        .ant-select-selection--single,
        .ant-select-selection__rendered {
          height: 40px;
          line-height: 40px;
        }

        label,
        .ant-select-selection--single {
          font-size: 14px;
        }
      }
    }

    .modal-footer {
      &.add-express-footer {
        width: 100%;
        height: 56px;
        padding: 12px 16px;
        border-top: none;
        position: absolute;
        right: 0;
        bottom: 0;

        button {
          font-size: 14px;
          line-height: 1.57;
        }
      }
    }
  }
}
