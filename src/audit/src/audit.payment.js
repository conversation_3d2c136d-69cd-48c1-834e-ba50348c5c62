import React, { PureComponent } from 'react'
import styles from './audit.payment.module.less'
import classnames from 'classnames'
import { app as api } from '@ekuaibao/whispered'
const ETabs = api.require('@elements/ETabs')
import { EnhanceConnect } from '@ekuaibao/store'
import PayingWrapper from './payment/PayingWrapper'
import PaidWrapper from './payment/PaidWrapper'
import PendingPayingWrapper from './payment/PendingPayingWrapper'
import PartialPaymentWrapper from './payment/PartialPaymentWrapper'
import PayingWrapperByDetail from './payment/PayingWrapperByDetail'
import MessageCenter from '@ekuaibao/messagecenter'
import { connect } from '@ekuaibao/mobx-store'
import { Checkbox, Button } from '@hose/eui'
import { isFunction } from '@ekuaibao/helpers'
import { Universal_Unique_Key } from './index'
import PaymentReviewWrap from './paymentManagement/PaymentReviewWrap'
import PaymentRecord from './page-menu/paymentRecord'
import { newTrack } from './util/trackAudit'
import { paymentPlanCreate } from './audit-action'

const { UniversalComponent } = api.require('@elements/UniversalComponent')

@connect(store => ({ size: store.states['@layout'].size }))
@EnhanceConnect(state => ({
  baseDataProperties: state['@common'].globalFields.data,
  budgetPower: state['@common'].powers.Budget,
  staffs: state['@common'].staffs,
  userInfo: state['@common'].userinfo.data,
  ADJUSTMENT_PAYMENT_PLAN: state['@common'].powers.ADJUSTMENT_PAYMENT_PLAN,
}))
export default class AuditPayment extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
    this.partialPaymentBus = new MessageCenter()
    this.state = {
      paymentType: 'unpaid',
      payingMode: 'flow',
      isLightingMode: false,
    }
  }

  componentWillMount() {
    newTrack('entrance_audit_payment')
    api.dataLoader('@common.staffs').load()
  }

  handleModeChange = () => {
    const { payingMode } = this.state
    this.setState({ payingMode: payingMode === 'flow' ? 'detail' : 'flow' })
  }

  handleTabChange = type => {
    this.setState({ paymentType: type })
    if (type === 'unpaid') {
      // 每次切换到待支付页面去刷新待支付的角标
      isFunction(this.bus.reload) && this.bus.reload()
      api.invokeService('@common:get:backlog:count:payment')
      paymentPlanCreate()
    }
    if (type === 'partial_payment') {
      isFunction(this.partialPaymentBus.reload) && this.partialPaymentBus.reload()
    }
    this.bus.emit(`audit-payment:tab:change:${type}`)
  }

  fnCreatePendingPaying() {
    return <PendingPayingWrapper {...this.props} bus={this.bus} />
  }

  fnCreatepaymentReview() {
    return <PaymentReviewWrap {...this.props} bus={this.bus} unUseAction={true} from="auditPayment" />
  }

  fnCreatePartialPaymentWrapper() {
    return <PartialPaymentWrapper {...this.props} bus={this.partialPaymentBus} />
  }

  fnCreateHistory() {
    return <PaymentRecord {...this.props} bus={this.bus} from="auditPayment" />
  }

  fnCreatePaidWrapper() {
    return <PaidWrapper {...this.props} />
  }

  fnCreatePayingWrapper() {
    const { isLightingMode, payingMode } = this.state
    const props = {
      ...this.props,
      bus: this.bus,
      isLightingMode,
    }
    return {
      flow: <PayingWrapper {...props} />,
      detail: <PayingWrapperByDetail {...props} />,
    }[payingMode]
  }

  fnChangeMode = e => {
    this.setState({
      payingMode: 'flow',
      isLightingMode: e.target.checked,
    })
  }

  render() {
    const { ADJUSTMENT_PAYMENT_PLAN } = this.props
    const { isLightingMode, payingMode, paymentType } = this.state
    const dataSource = [
      {
        tab: i18n.get('待支付'),
        children: this.fnCreatePayingWrapper(),
        key: 'unpaid',
        isHidden: isLightingMode,
      },
      {
        tab: i18n.get('部分支付'),
        children: this.fnCreatePartialPaymentWrapper(),
        key: 'partial_payment',
        isHidden: isLightingMode || !ADJUSTMENT_PAYMENT_PLAN,
      },
      {
        tab: i18n.get('复核中'),
        children: this.fnCreatepaymentReview(),
        key: 'paymentReview',
        isHidden: isLightingMode,
      },
      {
        tab: i18n.get('支付中'),
        children: this.fnCreatePendingPaying(),
        key: 'during',
        isHidden: isLightingMode,
      },
      {
        tab: i18n.get('已支付'),
        children: this.fnCreatePaidWrapper(),
        key: 'paid',
        isHidden: isLightingMode,
      },
      {
        tab: i18n.get('支付记录'),
        children: this.fnCreateHistory(),
        key: 'history',
        isHidden: isLightingMode,
      },
      {
        tab: i18n.get('闪电支付'),
        children: this.fnCreatePayingWrapper(),
        key: 'lightning',
        isHidden: !isLightingMode,
      },
    ]
    const cls = classnames(styles.payment_wrapper, {
      [styles.payment_wrapper_layout5]: window.isNewHome,
    })
    return (
      <div id="audit-payment" className={cls}>
        <ETabs
          isHoseEUI={true}
          className="ekb-tab-line-left"
          defaultActiveKey={isLightingMode ? 'lightning' : 'unpaid'}
          tabBarStyle={{ width: '100%' }}
          onChange={this.handleTabChange}
          dataSource={dataSource}
        />
        <UniversalComponent uniqueKey={`${Universal_Unique_Key}.lightningPay`}>
          <Checkbox className="lightning-send-btn lightning-send-content" onChange={this.fnChangeMode}>
            {i18n.get('闪电支付')}
          </Checkbox>
        </UniversalComponent>
        {paymentType === 'unpaid' && !isLightingMode && (
          <UniversalComponent uniqueKey={`${Universal_Unique_Key}.detailDimension`}>
            <Button category="secondary" className={styles.dimension_btn} onClick={this.handleModeChange}>
              {payingMode === 'flow' ? i18n.get('明细维度') : i18n.get('单据维度')}
            </Button>
          </UniversalComponent>
        )}
      </div>
    )
  }
}
