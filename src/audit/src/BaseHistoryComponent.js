import React, { PureComponent } from 'react'
import { printBill } from '@ekuaibao/lib/lib/lib-util'

import moment from 'moment'

export class BaseHistoryComponent extends PureComponent {
  _setPager(pagination) {
    const pager = this.state.pagination
    pager.current = pagination.current
    pager.pageSize = pagination.pageSize || pager.pageSize
    this.setState({ pagination: pager })
  }

  _setSorter(sorter = this.state.sorter) {
    if (!sorter || !sorter.order) {
      sorter = this.initSorter
    }
    if (sorter.field === 'flowDigest.paidAmount') {
      sorter.field = 'flowId.form.payMoney'
    }
    if (sorter.field === 'flowDigest.code') {
      sorter.field = 'flowId.form.code'
    }
    if (sorter.field === 'code') {
      sorter.field = 'flowCode'
    }
    if (sorter.field === 'paidAmount') {
      sorter.field = 'balance'
    }
    let order = sorter.order.replace(/end/, '')
    this.setState({ sorter })
    return order + '(' + sorter.field + ')'
  }

  _setFilterMap(filter) {
    if (!Object.keys(filter || {}).length) {
      this.filterMap = { date: this.filterMap.date, state: this.filterMap.state }
    }
    for (let key in filter) {
      switch (key) {
        case 'date':
          this.filterMap[key] = `finishTime>${moment(
            filter[key].sdate,
          ).valueOf()}&&finishTime<${moment(filter[key].edate).valueOf()}`
          break
        case 'state':
          if (filter[key] !== 'all') {
            this.filterMap[key] = `state.in("${filter[key]}")`
          } else {
            this.filterMap[key] = `state.in("SUCCESS","FAILURE")`
          }
          break
        case 'custom':
          let customFilter = JSON.stringify(filter[key])
          customFilter = customFilter.replace(/%/g, '\\\\%')
          if (customFilter) {
            this.filterMap[
              key
            ] = `(channelTradeNo.contains(${customFilter})||flowCode.contains(${customFilter}))`
          } else {
            delete this.filterMap[key]
          }
          break
        case 'accountCompany.name':
          if (filter[key] && filter[key].length) {
            let filterStr = filter[key].map(v => JSON.stringify(v))
            this.filterMap[key] = `payerAccountId.in(${filterStr})`
          } else {
            delete this.filterMap[key]
          }
          break
        case 'paymentChannel':
          if (filter[key] && filter[key].length) {
            let filterStr = filter[key].map(v => JSON.stringify(v))
            this.filterMap[key] = `${key}.in(${filterStr})`
          } else {
            delete this.filterMap[key]
          }
          break
      }
    }
  }

  handlePrint(type, backlog) {
    printBill(backlog.flowId)
  }
}
