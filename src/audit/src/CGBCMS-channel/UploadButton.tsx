import React, { forwardRef } from 'react'
import { OutlinedTipsAdd, OutlinedGeneralLoading } from '@hose/eui-icons'
import { FilesUploader } from '@ekuaibao/uploader'
import { FilesUploaderProps, File as OriginalFile } from '@ekuaibao/uploader/types/type'
import { message } from '@hose/eui'
import { getFileUrlByKey } from '../audit-action'

export interface IImageUploadProps extends Partial<FilesUploaderProps> {
  previewUrl?: string
}

export type File = OriginalFile

const fileMaxSize = 5

const ImageUpload: React.FC<IImageUploadProps> = forwardRef((props, _ref) => {
  const { maxSize = fileMaxSize, accept = [], onDone, onChange, uploadPrepare, previewUrl } = props
  const [loading, setLoading] = React.useState<boolean>(false)
  const [imgUrl, setImgUrl] = React.useState<string | undefined>(previewUrl)

  const handleUploadChange = (files: File[]) => {
    console.log('handleUploadChange', files)
    if (!files.length) return
    onChange?.(files)
    setLoading(true)
  }

  const handleBeforeUpload = (files: File[]): Promise<void> => {
    uploadPrepare?.(files)
    if (files?.[0]?.size && files?.[0]?.size / (1024 * 1024) > maxSize) {
      message.error(`支持格式为${accept}，大小不超过${maxSize}M`)
      return Promise.reject()
    }

    return Promise.resolve()
  }

  const handleUploadDone = (files: any) => {
    console.log('handleUploadDone', files)
    if (!files?.[0]) return

    if (files?.[0]?.status === 'done') {
      onDone?.(files)
      const key = files?.[0]?.response?.data?.fileKey
      console.log('key', key)

      key &&
        getFileUrlByKey(key).then(res => {
          setLoading(false)
          setImgUrl(res?.data?.url)
        })
    } else {
      message.error(i18n.get('上传错误'))
    }
  }

  return (
    // @ts-ignore
    <FilesUploader
      {...props}
      onChange={handleUploadChange}
      uploadPrepare={handleBeforeUpload}
      onDone={handleUploadDone}>
      <div className="upload-button">
        {imgUrl ? (
          <img src={imgUrl} alt="" width={'100%'} />
        ) : (
          <>
            {loading ? (
              <OutlinedGeneralLoading fontSize={24} spin />
            ) : (
              <OutlinedTipsAdd fontSize={24} />
            )}
            <div className="upload-text">{i18n.get('上传图片')}</div>
          </>
        )}
      </div>
    </FilesUploader>
  )
})

export default ImageUpload
