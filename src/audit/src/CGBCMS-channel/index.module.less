.uKey-application-container {
  position: relative;
  height: 100%;
  flex: 1;
  overflow: hidden;
  background-color: var(--eui-bg-base);

  ul, li {
    margin: 0;
  }

  :global {
    .loading-wrap {
      margin: 0 auto;
      margin-top: 40vh;
      width: 200px;

      > p {
        margin-top: 8px;
        font: var(--eui-font-body-r1);
        color: var(--eui-text-placeholder);
      }
    }

    .application-passed {
      margin: 0 auto;
      padding-top: 194px;
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 100%;
      background-color: #fff;
      align-items: center;
      text-align: center;

      .success-tip1 {
        margin: 16px 0 8px;
        font: var(--eui-font-head-b2);
        color: var(--eui-text-title);
      }

      .success-tip2 {
        font: var(--eui-font-head-r1);
        color: var(-eui-text-caption);
      }
    }

    .application-prompt {
      .prompt {
        margin-top: 4px;

        > p {
          margin-bottom: 12px;
          font: var(--eui-font-body-b1);
          color: rgba(39, 46, 59, 0.96);
        }
  
        > ul {
          font-style: normal;
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
          color: rgba(39, 46, 59, 0.72);
          li {
            margin-top: 4px;
          }
        }
      }

      .eui-button {
        margin-top: 24px;
      }
    }
  }
}
