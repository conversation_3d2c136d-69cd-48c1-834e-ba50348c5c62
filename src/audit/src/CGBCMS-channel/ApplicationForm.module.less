.apply-from-container {
  height: 100%;
  transform: translate(0, 0); // fixed 定位父级
  background-color: #fff;

  :global {
    h2 {
      margin-top: 24px;
      margin-bottom: 24px;
      font: var(--eui-font-body-b1);
      color: var(--eui-text-title);

      &.mt-40 {
        margin-top: 40px;
      }
    }

    .error-info {
      margin-bottom: 12px;
    }

    .form-wrapper,
    .alert-wrapper {
      padding: 0 16px;
    }

    .form-wrapper {
      height: calc(100% - 64px);
      overflow-y: auto;

      .ant-form {
        width: 548px;

        &-item {
          &-label {
            position: relative;
            font: var(--eui-font-body-r1);
            color: var(--eui-text-title);
            .ant-form-item-required {
              &::before {
                display: none;
              }
  
              &::after {
                content: '*';
                top: 0.5px;
                font: var(--eui-font-body-b1);
                color: var(--eui-function-danger-500);
              }
            }
          }

          &-control-wrapper {
            margin-top: 8px;
          }

          &-control {
            &.has-error {
              .ant-input,
              .eui-input {
                border-color: var(--eui-function-danger-500) !important;
              }
            }
  
            .ant-input {
              padding: 6px 10px;
              height: 32px;
              font: var(--eui-font-body-r1);
              color: var(--eui-text-title);
              border-color: var(--eui-line-border-component);
              border-radius: 6px;
  
              &:focus {
                border-color: var(--brand-base);
              }
  
              &::placeholder {
                color: var(--eui-text-placeholder);
              }
            }

            .ant-cascader-picker {
              .ant-input {
                box-shadow: 0 0 2px var(--brand-1);
              }
            }

            .ant-cascader-picker-focused {
              .ant-input {
                border-color: var(--brand-base);
              }
            }

            .eui-input {
              &:hover {
                border-color: var(--eui-primary-pri-500);
              }
            }
          }
        }

        &-explain {
          font: var(--eui-font-note-r2);
          color: var(-eui-function-danger-500);
        }

        .footer {
          position: fixed;
          width: 100%;
          height: 56px;
          bottom: 0;
          left: 0;
          padding: 12px 16px;
          background-color: #fff;
          box-shadow: var(--eui-shadow-up-2);
          border-top-left-radius: 8px;
          border-top-right-radius: 8px;
  
          .eui-button:last-child {
            margin-left: 8px;
          }
        }
      }

      .upload-wrapper {
        display: flex;
  
        .upload-tips {
          margin-left: 10px;
          font-style: normal;
          font-weight: 400;
          font-size: 12px;
          line-height: 16px;
          color: var(--eui-text-placeholder);
          > p {
            margin: 0;
            &:last-child {
              margin-top: 4px;
            }
          }
        }
      }
    }

    .ekb-files-uploader-wrapper {
      cursor: pointer;
      &:hover {
        .upload-button {
          border-color: var(--brand-base);
        }
      }
    }

    .upload-button {
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      width: 96px;
      height: 134px;
      background: rgba(39, 46, 59, 0.04);
      border: 1px dashed rgba(39, 46, 59, 0.12);
      border-radius: 4px;
      transition: border-color .3s ease-out;
      overflow: hidden;

      &:hover {
        border-color: var(--brand-base);
      }

      .eui-icon {
        color: rgba(39, 46, 59, 0.6);
      }

      .upload-text {
        margin-top: 8px;
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        color: rgba(39, 46, 59, 0.6);
      }
    }
  }
}
