import styles from './index.module.less'
import { <PERSON><PERSON>, <PERSON><PERSON>, message, Card } from '@hose/eui'
import React, { Component } from 'react'
import { FilledTipsYes, OutlinedGeneralLoading } from '@hose/eui-icons'
import ApplicationForm from './ApplicationForm'
import { queryUkeyApplication } from '../audit-action'
import { EnhanceConnect } from '@ekuaibao/store'

interface Props {
  userInfo?: any
}

interface State {
  loading?: boolean
  approved?: boolean
  applicationInfo?: any
  showApplicationForm?: boolean
}

const approvedStatus = [1, 2, 4] // 待认证 已审核 待邮寄
@EnhanceConnect((state: any) => ({
  userInfo: state['@common'].userinfo.data,
}))
export default class CGBCMSPay extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      loading: false,
      approved: false,
      applicationInfo: {},
      showApplicationForm: false,
    }
  }

  componentDidMount(): void {
    this.getApplicationInfo()
  }

  getApplicationInfo = () => {
    this.setState({ loading: true })
    queryUkeyApplication().then(res => {
      const approved = approvedStatus.includes(res?.data?.ukeyStatus)
      if (res.status === 0) {
        this.setState({
          approved,
          loading: false,
          applicationInfo: res?.data,
          showApplicationForm: res?.data?.ukeyStatus === 3, // 驳回状态
        })
      }
    })
  }

  handleApply = () => {
    if (!this.props.userInfo?.staff?.cellphone) {
      message.error(i18n.get('未绑定手机号，请先绑定手机号'))
      return
    }
    this.setState({ showApplicationForm: true })
  }

  handleConfirm = () => {
    this.setState({ approved: true })
  }

  handleCancel = () => {
    this.setState({ showApplicationForm: false })
  }

  renderContent = () => {
    if (this.state.loading)
      return (
        <div className="loading-wrap">
          <OutlinedGeneralLoading spin fontSize={32} color="var(--brand-base)" />
          <p>加载中...</p>
        </div>
      )

    if (this.state.approved) return <ApplyPassed />

    if (this.state.showApplicationForm)
      return (
        <ApplicationForm
          onCancel={this.handleCancel}
          onConfirm={this.handleConfirm}
          defaultPhone={this.props.userInfo?.staff?.cellphone}
          applicationInfo={this.state.applicationInfo}
        />
      )

    return (
      <Card style={{ margin: '48px auto 0', width: '422px' }} bordered={false}>
        <div className="application-prompt">
          <Alert message="为了不影响你正常的进行支付业务，请尽快完成UKEY申请" type="warning" />
          <div className="prompt">
            <p>申请所需资料</p>
            <ul>
              <li>
                1、提供真实合法的经营信息：企业名称、统一社会信用代码、法人信息、企业授权文件、营业执照；
              </li>
              <li>2、申请人信息：姓名、手机号、申请人身份证号码、邮箱；</li>
              <li>3、收件人信息：姓名、手机号、地址等；</li>
              <li>4、图片规则：图片需四角完整，清晰可辨，若加水印需保证图片重要信息清晰可辨；</li>
            </ul>
          </div>
          <Button onClick={this.handleApply}>{i18n.get('立即申请')}</Button>
        </div>
      </Card>
    )
  }

  render() {
    return <div className={styles['uKey-application-container']}>{this.renderContent()}</div>
  }
}

const ApplyPassed: React.FC = () => {
  return (
    <div className="application-passed">
      <FilledTipsYes fontSize={44} color="var(--eui-function-success-500)" />
      <div className="success-tip1">{i18n.get('UKEY申请完成')}</div>
      <div className="success-tip2">
        请关注本页面查看审核结果 <br />
        若审核不通过请根据失败原因重新提交，若审核通过UKEY会线下邮寄到你手中。
      </div>
    </div>
  )
}
