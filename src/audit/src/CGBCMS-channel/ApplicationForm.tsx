import styles from './ApplicationForm.module.less'
import { app } from '@ekuaibao/whispered'
import { Alert, Button, message, Input as EuiInput } from '@hose/eui'
import { Form, Input, Cascader, Checkbox } from 'antd'
import React, { useState, useEffect, forwardRef, useRef } from 'react'
import { CascaderOptionType } from 'antd/lib/cascader'
import { getAreaDict, postVerfyApplication, postUkeyApplication, getCaptcha } from '../audit-action'
import { Fetch } from '@ekuaibao/fetch'
import { WrappedFormUtils } from 'antd/es/form/Form'
import { checkCreditCode, checkIDCard, checkPhone, checkEmail } from '../util/validators'
import ImageUpload, { IImageUploadProps, File } from './UploadButton'
import { saveAs } from 'file-saver'

const INPUT: any = Input
const CASCADER: any = Cascader

const UKEY_APPLICATION_PDF =
  'https://static.ekuaibao.com/public/%E6%95%B0%E5%AD%97%E8%AF%81%E4%B9%A6%E6%9C%8D%E5%8A%A1%E5%8D%8F%E8%AE%AE.pdf'
const DOWNLOAD_URL =
  'https://static.ekuaibao.com/public/%E4%BC%81%E4%B8%9A%E7%BB%8F%E5%8A%9E%E4%BA%BA%E6%8E%88%E6%9D%83-%E6%95%B0%E5%AD%97%E8%AF%81%E4%B9%A6%E6%9C%8D%E5%8A%A1%E5%8D%8F%E8%AE%AE.pdf'

const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
const uploadImageUrl = `${Fetch.fixOrigin(
  location.origin,
)}/api/openapi-proxy/file/upload/image?corpId=${ekbCorpId}`

interface IApplyFormPageProps {
  form: WrappedFormUtils
  onCancel?: () => void
  onConfirm?: () => void
  applicationInfo?: any
  defaultPhone?: string
}

type AreaType = Partial<{
  id: string | number
  name: string
  nameSpell: string
  isLeaf: boolean
  level: number
  children: AreaType
}>

const ApplicationForm: React.FC<IApplyFormPageProps> = forwardRef((props, _ref) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const { form, onCancel, onConfirm, applicationInfo, defaultPhone } = props
  const { getFieldDecorator } = form
  const [failedMessage, setFailedMessage] = useState<string>('')
  const [area, setArea] = useState<AreaType[]>([]) // cascader options

  const handleLicenseUploadDone = (files: File[]) => {
    form?.setFieldsValue({ customerLicenseFile: files?.[0]?.response?.data?.fileKey })
  }

  const handleDownloadFile = (url: string) => {
    saveAs(url, '企业授权文件.pdf')
  }

  const handleAuthorizationFileUploadDone = (files: File[]) => {
    form?.setFieldsValue({ customerAuthorizationFile: files?.[0]?.response?.data?.fileKey })
  }

  const handleSubmit = (e: any) => {
    e.preventDefault()

    form.validateFieldsAndScroll(async (err: any, values: any) => {
      if (!err) {
        const areaData = form.getFieldValue('recipientArea')
        const [recipientProvince, recipientCity, recipientDistrict] = areaData

        values = { ...values, recipientProvince, recipientCity, recipientDistrict }

        const result = await postVerfyApplication(values).catch((err: any) => message.error(err))

        if (result?.status === 0) {
          const data: any = await app.open('@audit:VerificationPhoneModal', {
            extraHeaderComponent: (
              <Alert
                message="请确保登录本人的合思账号进行UKEY申请，如果登录他人的合思账号申请UKEY，你本人无法使用"
                type="warning"
              />
            ),
            getCaptcha: getCaptcha,
            fileKey: 'mobile',
            defaultPhone,
            title: '身份校验',
            disabled: !!defaultPhone,
          })

          if (data.code) {
            values.currentMobile = defaultPhone
            values.verifyCode = data.code

            const UkeyResult = await postUkeyApplication(values).catch((err: any) =>
              message.error(err),
            )

            if (UkeyResult?.status === 0) {
              onConfirm?.()
              setFailedMessage('')
            } else {
              setFailedMessage(UkeyResult?.message)
              containerRef?.current?.scrollTo(0, 0)
            }
          }

          return
        }

        setFailedMessage(result?.message)
        containerRef?.current?.scrollTo(0, 0)
      }
    })
  }

  const handleCancel = () => {
    onCancel?.()
  }

  const loadData = (selectedOptions?: CascaderOptionType[]) => {
    const targetOption = selectedOptions?.[selectedOptions?.length - 1]

    if (targetOption) {
      targetOption.loading = true
      getAreaDict(targetOption.id).then(res => {
        targetOption.loading = false
        if (res.data.length > 0) {
          const areaCurrentLevel = targetOption.level + 1

          res.data = res?.data?.map((item: AreaType) => ({
            ...item,
            isLeaf: areaCurrentLevel === 3,
            level: areaCurrentLevel,
          }))
          targetOption.children = res?.data
          setArea([...area])
        } else {
          targetOption.isLeaf = true
          setArea([...area])
        }
      })
    }
  }

  useEffect(() => {
    getAreaDict(0).then(res => {
      res?.data.forEach((item: AreaType) => {
        item.isLeaf = false
        item.level = 1
      })
      setArea(res?.data)
      // 已填写的数据 暂不需要回显
      if (applicationInfo) {
        form.setFieldsValue({ ...applicationInfo })
        const { recipientProvince, recipientCity, recipientDistrict } = applicationInfo

        const currentProvince = res?.data?.find((item: AreaType) => item.id === recipientProvince)
        const getAllAreaData = async () => {
          const result = await Promise.all([
            recipientProvince && getAreaDict(recipientProvince),
            recipientCity && getAreaDict(recipientCity),
          ])
          if (currentProvince) {
            currentProvince.children = result[0]?.data?.map((item: AreaType) => ({
              ...item,
              level: 2,
              isLeaf: false,
            }))
            const currentCity = currentProvince.children?.find(
              (item: AreaType) => item.id === recipientCity,
            )
            currentCity.children = result[1]?.data?.map((item: AreaType) => ({
              ...item,
              level: 3,
              isLeaf: true,
            }))
          }

          setArea(res?.data)
          setTimeout(() => {
            form.setFieldsValue({
              recipientArea: [recipientProvince, recipientCity, recipientDistrict],
            })
          })
        }
        getAllAreaData()
      }
    })

    if (applicationInfo?.ukeyStatus === 3) {
      setFailedMessage(`驳回原因：${applicationInfo.message}`)
    }
  }, [applicationInfo])

  const licenseUploaderProps: IImageUploadProps = {
    accept: '.jpg, .jpeg, .png',
    onDone: handleLicenseUploadDone,
    action: uploadImageUrl,
    headers: { staffId: Fetch.staffId },
    multiple: false,
    filename: 'file',
    previewUrl: applicationInfo?.customerLicenseUrl,
  }

  const AuthorizationFileUploaderProps: IImageUploadProps = {
    ...licenseUploaderProps,
    onDone: handleAuthorizationFileUploadDone,
    previewUrl: applicationInfo?.customerAuthorizationUrl,
  }

  return (
    <div className={styles['apply-from-container']}>
      <div className="form-wrapper" ref={containerRef}>
        {failedMessage && (
          <Alert
            className="error-info"
            message="申请失败"
            description={failedMessage}
            type="error"
          />
        )}
        <Alert message="填写信息审核通过后将无法修改，请准确填写。" type="warning" />
        <Form onSubmit={handleSubmit}>
          <h2>{i18n.get('企业主体信息')}</h2>
          <Form.Item label="企业名称">
            {getFieldDecorator('customerName', {
              rules: [{ required: true, message: '错误原因：与统一社会代码信息不匹配' }],
            })(<INPUT placeholder="请输入企业名称，需与「营业执照」上的一致" />)}
          </Form.Item>
          <Form.Item label="统一社会信用代码">
            {getFieldDecorator('customerCreditCode', {
              rules: [
                { required: true, message: '错误原因：1、与企业名称信息不匹配；2、其他错误' },
                { validator: checkCreditCode },
              ],
              validateFirst: true,
            })(<INPUT placeholder="请输入18位社会信用代码" />)}
          </Form.Item>
          <Form.Item label="法人姓名">
            {getFieldDecorator('legalName', {
              rules: [{ required: true, message: '请输入法人姓名' }],
            })(<INPUT placeholder="请输入法人姓名" />)}
          </Form.Item>
          <Form.Item label="法人身份证号">
            {getFieldDecorator('legalIdNo', {
              rules: [
                { required: true, message: '请输入法人身份证号' },
                { validator: checkIDCard },
              ],
              validateFirst: true,
            })(<INPUT placeholder="请输入法人身份证号" />)}
          </Form.Item>
          <Form.Item label="企业授权文件">
            <div className="upload-wrapper">
              {getFieldDecorator('customerAuthorizationFile', {
                rules: [{ required: true, message: '请上传企业授权文件' }],
              })(<ImageUpload {...(AuthorizationFileUploaderProps as any)} />)}
              <div className="upload-tips">
                <p>
                  {i18n.get('步骤一：请下载')}
                  <a
                    onClick={() => {
                      handleDownloadFile(DOWNLOAD_URL)
                    }}>
                    《企业授权文件》
                  </a>
                  {i18n.get('（点击文件名自动下载）')}
                </p>
                <p>
                  {i18n.get('步骤二：填写申请信息，申请人签字，并加盖企业公章后上传，大小不超过5M')}
                </p>
              </div>
            </div>
          </Form.Item>
          <Form.Item label="营业执照">
            <div className="upload-wrapper">
              {getFieldDecorator('customerLicenseFile', {
                rules: [{ required: true, message: '请上传营业执照' }],
              })(<ImageUpload {...(licenseUploaderProps as any)} />)}
              <div className="upload-tips">
                <p>{i18n.get('请上传营业执照原件，图片支持jpg、jpeg、png格式，大小不超过5M。')}</p>
              </div>
            </div>
          </Form.Item>

          <h2 className="mt-40">{i18n.get('申请人信息')}</h2>
          <Form.Item label="姓名">
            {getFieldDecorator('operatorName', {
              rules: [{ required: true, message: '请输入姓名' }],
            })(<INPUT placeholder="请输入姓名" />)}
          </Form.Item>
          <Form.Item label="身份证号">
            {getFieldDecorator('operatorIdNo', {
              rules: [{ required: true, message: '请输入身份证号' }, { validator: checkIDCard }],
              validateFirst: true,
            })(<INPUT placeholder="请输入身份证号" />)}
          </Form.Item>
          <Form.Item label="手机号">
            {getFieldDecorator('operatorMobile', {
              initialValue: defaultPhone,
              rules: [{ required: true, message: '请输入手机号' }, { validator: checkPhone }],
              validateFirst: true,
            })(<INPUT disabled={!!defaultPhone} placeholder="请输入手机号" />)}
          </Form.Item>
          <Form.Item label="邮箱">
            {getFieldDecorator('operatorEmail', {
              rules: [{ required: true, message: '请输入邮箱' }, { validator: checkEmail }],
              validateFirst: true,
            })(<INPUT placeholder="请输入邮箱" />)}
          </Form.Item>

          <h2 className="mt-40">{i18n.get('收件人信息')}</h2>
          <Form.Item label="姓名">
            {getFieldDecorator('recipientName', {
              rules: [{ required: true, message: '请输入姓名' }],
            })(<INPUT placeholder="请输入姓名" />)}
          </Form.Item>
          <Form.Item label="手机号">
            {getFieldDecorator('recipientMobile', {
              rules: [{ required: true, message: '请输入手机号' }, { validator: checkPhone }],
              validateFirst: true,
            })(<INPUT placeholder="请输入手机号" />)}
          </Form.Item>
          <Form.Item label="所在地区">
            {getFieldDecorator('recipientArea', {
              rules: [{ required: true, message: '请选择省/市/区' }],
            })(
              <CASCADER
                options={area}
                loadData={loadData}
                fieldNames={{ label: 'name', value: 'id', children: 'children' }}
                placeholder="请选择省/市/区"
                changeOnSelect
              />,
            )}
          </Form.Item>
          <Form.Item label="详细地址">
            {getFieldDecorator('recipientAddress', {
              rules: [
                {
                  required: true,
                  message: '请输入详细地址，如街道、小区、楼栋号、单元、门牌号等信息',
                },
              ],
            })(
              <EuiInput.TextArea
                autoSize
                showCount
                maxLength={100}
                placeholder="请输入详细地址，如街道、小区、楼栋号、单元、门牌号等信息"
              />,
            )}
          </Form.Item>

          <Form.Item required>
            {getFieldDecorator('read', {
              rules: [{ required: true, message: '请阅读并同意《数字证书服务协议》' }],
            })(
              <Checkbox>
                {i18n.get('我已阅读并同意')}
                <a
                  onClick={(e: React.MouseEvent) => {
                    e.stopPropagation()
                    e.preventDefault()
                    app.emit('@vendor:preview:pdf', UKEY_APPLICATION_PDF)
                  }}>
                  《数字证书服务协议》
                </a>
              </Checkbox>,
            )}
          </Form.Item>

          <div className="footer">
            <Button htmlType="submit" onClick={handleSubmit}>
              {i18n.get('提交')}
            </Button>
            <Button category="secondary" onClick={handleCancel}>
              {i18n.get('取消')}
            </Button>
          </div>
        </Form>
      </div>
    </div>
  )
})

export default Form.create()(ApplicationForm)
