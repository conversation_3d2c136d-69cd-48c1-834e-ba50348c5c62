@import '~@ekuaibao/web-theme-variables/styles/default';

.receive-express-view {
  display: flex;
  flex: 1;
  flex-direction: column;
  background: @component-background;
  overflow: hidden;
  :global {
    .header {
      display: flex;
      align-items: center;
      padding: 0 15px;
      font-size: 13px;
      z-index: 2;
      border-top: 1px solid rgba(0, 0, 0, 0.06);
      box-shadow: 0 1px 16px 0 rgba(0, 0, 0, 0.06);
      background: #ffffff;
      justify-content: space-between;
      flex-shrink: 0;
      .menu {
        .navigation-bar-breadcrumb-wrapper {
          padding-bottom: 8px;
          .navigation-bar-breadcrumb-content {
            width: inherit;
            height: inherit;
          }
        }
      }
    }
    .receive-express-content {
      display: flex;
      flex: 1;
      background-color: #ebeff2;
      overflow: auto;
    }
  }
}
