import { app as api } from '@ekuaibao/whispered'
import styles from './ExpressNumberModal.module.less'
import { Form } from 'antd'
import { Button, Input, Tooltip } from '@hose/eui'
import React from 'react'
import { getReceiveBackLogByNum } from '../../audit-action'
import { OutlinedTipsClose } from '@hose/eui-icons'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
const EnhanceFormCreate = api.require('@elements/enhance/enhance-form-create')

const formItemLayout = {
  labelCol: { span: 24, style: { fontSize: '14px' } },
  wrapperCol: { span: 24 },
}
const FormItem = Form.Item

@EnhanceModal({
  title: '',
  footer: [],
  maskClosable: false,
  className: 'custom-modal-layer',
})
@EnhanceFormCreate()
export default class ExpressNumberModal extends React.Component {
  constructor(props) {
    super(props)
    this.getResult = this.getResult.bind(this)
    props.overrideGetResult && props.overrideGetResult(this.getResult)
    this.result = { useExpressNumber: false }
  }

  handleModalSkip() {
    this.props.layer.emitOk()
  }
  handleModalClose() {
    this.props.layer.emitCancel()
  }
  getResult() {
    this.props.form.resetFields()
    return this.result
  }
  handleSubmit() {
    this.props.form.validateFieldsAndScroll((err, values) => {
      if (err) {
        return
      }
      api.dispatch(getReceiveBackLogByNum(values.expressNumber.trim())).then(res => {
        this.result = { useExpressNumber: true, ...values, ...res }
        this.props.layer.emitOk()
      })
    })
  }

  render() {
    const { getFieldDecorator } = this.props.form
    const expressNumber = getFieldDecorator('expressNumber', {
      initialValue: '',
      rules: [
        { max: 100, message: i18n.get('寄送单号不能超过100个字') },
        { required: true, whitespace: true, message: i18n.get('寄送单号不能为空') },
      ],
    })
    return (
      <div className={styles['express-number-modal']}>
        <div className="modal-header" style={{ borderBottom: 'none' }}>
          <div className="flex">{i18n.get('闪电收单')}</div>
          <OutlinedTipsClose className="cross-icon" onClick={this.handleModalClose.bind(this)} />
        </div>
        <div className="formWrapper">
          <FormItem label={i18n.get('按寄送单号收单')} {...formItemLayout}>
            {expressNumber(
              <Input
                autoFocus={true}
                placeholder={i18n.get('请录入寄送单号')}
                suffix={
                  <Tooltip placement="top" title={i18n.get('支持扫码器录入')}>
                    <svg
                      aria-hidden="true"
                      className="icon"
                      style={{ width: 20, height: 20, color: 'rgba(0,0,0,.25)' }}>
                      <use xlinkHref="#EDico-scanner" />
                    </svg>
                  </Tooltip>
                }
              />,
            )}
          </FormItem>
          <span className="tips">
            {i18n.get('用于区分本次收取单据是否在该寄送中；若无需区分可跳过')}
          </span>
        </div>
        <div className="modal-footer">
          <Button className='mr-16' category="secondary" onClick={this.handleModalSkip.bind(this)}>
            {i18n.get('跳过')}
          </Button>
          <Button onClick={this.handleSubmit.bind(this)}>{i18n.get('确定')}</Button>
        </div>
      </div>
    )
  }
}
