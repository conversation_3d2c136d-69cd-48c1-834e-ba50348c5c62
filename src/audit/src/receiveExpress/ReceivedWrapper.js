import { app } from '@ekuaibao/whispered'
import { handlePrint, handleActionImplementation } from '../service'
const { exportExcel } = app.require('@lib/export-excel-service')
import { BaseReceivedApprovedComponent } from './BaseReceivedApprovedComponent'
import { cloneDeep } from 'lodash'
import { Resource } from '@ekuaibao/fetch'
const LoaderWithLegacyData = app.require('@elements/data-grid-v2/LoaderWithLegacyData')
const { createNodeNameColumn, createNodeStaffColumn } = app.require(
  '@elements/data-grid-v2/CreateColumn',
)
const { searchOptions, globalSearchOptions } = app.require('@lib/data-grid-v2/CustomSearchUtil')
import { mapping } from '../util/mapping4Approved'
import {
  createActionColumn4Approved,
  createRiskWarningColumn,
} from '../util/columnsAndSwitcherUtil'
import { fnFilterMapping, getInitScenes } from '../util/Utils'
import { EnhanceConnect } from '@ekuaibao/store'

const approved = new Resource('/api/flow/v2/filter')
const scenesType = 'RECEIVED'
const prefixColumns = { state: '$', '*': 'form' }

@EnhanceConnect(state => ({
  invoiceReviewPower: state['@common'].powers.invoiceReview,
  legalEntityCurrencyPower: state['@common'].powers.legalEntityCurrency,
  // 涉及需求 https://hose2019.feishu.cn/wiki/wikcncXiMtXZQwNcS64OMEwSsJd#fTSuk8
  KA_GLOBAL_SEARCH_2: state['@common'].powers.KA_GLOBAL_SEARCH_2,
  showPrintBtn: state['@common'].showPrintBtn,
}))
export default class ReceivedWrapper extends BaseReceivedApprovedComponent {
  componentDidMount() {
    this.bus.on('buttons:click', this.handleButtonsClick)
    this.bus.on('table:row:click', this.handleTableRowClick)
    this.bus.on('table:row:action', this.handleActions)
    this.bus.on('selectAllBtn:click', this.handleSelectAllBtnClick)
    this.bus.on('initScenes:action', this.initScenes)

    // 获取场景列表
    approved.GET('/$type', { type: scenesType }).then(res => {
      const { value } = res
      if (this.props.specifications.length) {
        this.initScenes(value)
      }
    })
  }

  componentWillUnmount() {
    this.bus.un('buttons:click', this.handleButtonsClick)
    this.bus.un('table:row:click', this.handleTableRowClick)
    this.bus.un('table:row:action', this.handleActions)
    this.bus.un('selectAllBtn:click', this.handleSelectAllBtnClick)
    this.bus.un('initScenes:action', this.initScenes)
  }

  initScenes = data => {
    const { specifications, userInfo} = this.props
    const scenesData = getInitScenes({data,prefix:'',specifications,isHasWaitInvoice:false})
    this.setState({ scenes:scenesData.scenes})
  }

  __status = { 'approvedFlow.action': 'RECEIVED' }

  fnGetScene() {
    const { scenes, scene } = this.state
    const cloneScenes = cloneDeep(scenes)
    const findScenes = cloneScenes.find(s => s.sceneIndex === scene)
    if (findScenes) {
      findScenes.scene = findScenes.scene !== 'waitInvoice' ? findScenes.scene : ''
    }
    if (findScenes && findScenes.scene) return scene
    return findScenes
  }

  handleButtonsClick = ({ name, data, keys }, fetchParams) => {
    keys = keys ? keys : this.bus.getSelectedRowKeys()
    data = data ? data : this.bus.getSelectedRowData()
    switch (name) {
      case 'print':
        return this._handlePrintList(
          keys,
          data,
          () => {
            this.bus.clearSelectedRowKeys()
            this.bus.reload()
          },
          '0',
        )
      case 'printInvoice':
        return this._handlePrintList(
          keys,
          data,
          () => {
            this.bus.clearSelectedRowKeys()
            this.bus.reload()
          },
          '1',
        )
      case 'export_selected':
        return exportExcel({ exportType: 'export_selected', funcType: 'approve', data }, this.bus)
      case 'export_all': {
        const params = fetchParams || this.bus.getFilterParam()
        const { userInfo } = this.props
        params.status = {}
        const scene = this.fnGetScene()
        params.scene = scene
        params.filters = { ...params.filters }
        const exportParam = {
          exportType: 'export_all',
          funcType: 'approve',
          data: params,
          needAsyncExport: true,
          others: {
            unNeedState: true,
            filter: `(flowId.approvedFlow.approverId=="${userInfo?.staff?.id}")&&(flowId.formType!="permit")&&(flowId.approvedFlow.action=="RECEIVED")`,
          },
        }
        if (scene && scene.sceneIndex === 'waitInvoice') {
          exportParam.others.queryString = 'waitInvoice=true'
        }
        return exportExcel(exportParam, this.bus)
      }
    }
  }

  _handlePrintList(keys, data, fn, printInvoice) {
    handlePrint.call(this, keys, data, fn, false, printInvoice)
  }

  handleActions = (type, line) => {
    handleActionImplementation.call(this, {
      type,
      backlog: line,
      fn: () => {
        this.bus.clearSelectedRowKeys()
        this.bus.reload()
      },
    })
  }

  render() {
    const {
      baseDataProperties,
      budgetPower,
      staffs,
      size,
      invoiceReviewPower,
      KA_GLOBAL_SEARCH_2,
    } = this.props
    const { scenes } = this.state

    if (!scenes.length) {
      return null
    }
    return (
      <LoaderWithLegacyData
        newSearch={true}
        searchOptions={KA_GLOBAL_SEARCH_2 ? globalSearchOptions('form') : searchOptions('form')}
        enableGlobalSearch={KA_GLOBAL_SEARCH_2}
        lightingMode={this.state.isLightingMode}
        scenes={scenes}
        fetch={this.fetchPending}
        buttons={this.buttons}
        onButtonClick={this.handleButtonsClick}
        onSelectedAll={this.handleSelectAllBtnClick}
        prefixColumns={prefixColumns}
        bus={this.bus}
        resource={approved}
        scenesType={scenesType}
        baseDataProperties={baseDataProperties}
        createAction={createActionColumn4Approved}
        mapping={fnFilterMapping(mapping, invoiceReviewPower)}
        createRiskWarningColumn={createRiskWarningColumn}
        createNodeNameColumn={createNodeNameColumn}
        createNodeStaffColumn={createNodeStaffColumn}
        useNewFieldSet
      />
    )
  }
}
