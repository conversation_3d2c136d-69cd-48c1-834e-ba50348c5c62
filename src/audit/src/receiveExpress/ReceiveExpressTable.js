import { app as api } from '@ekuaibao/whispered'
import styles from './ReceiveExpressTable.module.less'
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
const ETabs = api.require('@elements/ETabs')
import ReceiveWrapper from './ReceiveWrapper'
import ReceivedWrapper from './ReceivedWrapper'
import { MessageCenter } from '@ekuaibao/messagecenter'
import { Button } from '@hose/eui'
import { connect } from '@ekuaibao/mobx-store'
import classnames from 'classnames'

@connect(store => ({ size: store.states['@layout'].size }))
@EnhanceConnect(state => ({
  baseDataProperties: state['@common'].globalFields.data,
  budgetPower: state['@common'].powers.Budget,
  staffs: state['@common'].staffs,
  specifications: state['@custom-specification'].specificationGroupsList,
  userInfo: state['@common'].userinfo.data,
}))
export default class ReceiveExpressTable extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
  }

  componentWillMount() {
    api.dataLoader('@common.staffs').load()
  }

  handleTabChange = type => {
    if (type === 'receiving') {
      this.bus.reload()
    }
  }
  openLightningReceiveView = () => {
    api.open('@audit:ExpressNumberModal').then(result => {
      this.props.stackerManager.push('LightningReceive', { ...result, staffs: this.props.staffs })
    })
  }
  render() {
    const dataSource = [
      {
        tab: i18n.get('待收单'),
        children: <ReceiveWrapper {...this.props} bus={this.bus} />,
        key: 'receive',
      },
      {
        tab: i18n.get('已收单'),
        children: <ReceivedWrapper {...this.props} />,
        key: 'received',
      },
    ]

    const cls = classnames('e-tabs-wrapper', { 'e-tabs-wrapper-layout5': window.isNewHome })
    return (
      <div className={styles['receive_express_wrapper']}>
        <div className={cls}>
          <ETabs
            className="ekb-tab-line-left"
            defaultActiveKey="receive"
            tabBarStyle={{ paddingTop: 6, height: 40, width: '100%' }}
            onChange={this.handleTabChange}
            dataSource={dataSource}
          />
        </div>
        <Button category="secondary" onClick={this.openLightningReceiveView}>
          {i18n.get('闪电收单')}
        </Button>
      </div>
    )
  }
}
