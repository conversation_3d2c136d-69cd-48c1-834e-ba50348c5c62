/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018-10-09 11:11.
 */
import styles from './Tabs.module.less'
import React, { PureComponent } from 'react'
import TabsItem from './TabsItem'
export default class Tabs extends PureComponent {
  constructor(props) {
    super(props)
  }

  handleOnClick = item => {
    const { onClick } = this.props
    onClick && onClick(item.type)
  }
  handleOnOtherClick = () => {
    const { onOtherClick } = this.props
    onOtherClick && onOtherClick()
  }
  render() {
    const { dataList, isExpress, expressList, selectedIndex } = this.props
    return (
      <div className={styles['table-tabs-view']}>
        {dataList.map((item, index) => {
          return (
            <TabsItem
              {...item}
              key={index}
              selected={selectedIndex === item.type}
              onClick={() => this.handleOnClick(item)}
            />
          )
        })}
        {isExpress && <div style={{ flex: 1 }} />}
        {isExpress && (
          <TabsItem
            count={expressList.length}
            title={i18n.get('相符(未收单)')}
            selected={false}
            onClick={this.handleOnOtherClick}
          />
        )}
      </div>
    )
  }
}
