import React, { PureComponent } from 'react'
import { Button } from '@hose/eui'
import { getBaseCol } from '../../util/Utils'
import styles from './DataGridSimple.module.less'
import * as DataGrid from '@ekuaibao/datagrid'

export default class DataGridSimple extends PureComponent {
  constructor(props) {
    super(props)
    this.state = { selectedRowKeys: [], selectedRows: [] }
  }
  getColumns = () => {
    let baseCol = getBaseCol()
    baseCol = baseCol.map(col => {
      const newProps = {
        sorter: false,
        allowReordering: false,
        allowHiding: true,
        allowGrouping: false,
        minWidth: 120,
      }

      return { ...col, ...newProps }
    })
    const { handleComment, handleReceive, isModal = false } = this.props
    const action = {
      title: i18n.get('操作'),
      dataIndex: 'action',
      key: 'action',
      minWidth: 150,
      fixed: isModal ? 'right' : false,
      render(text, line) {
        return (
          <div
            onClick={e => {
              e.persist()
              e.nativeEvent.stopImmediatePropagation()
              e.stopPropagation()
              e.preventDefault()
              return false
            }}>
            {line.state === 'RECEIVING' && (
              <a
                className="ant-dropdown-link mr-16"
                onClick={() => {
                  handleReceive && handleReceive(line)
                }}>
                {i18n.get('确认收单')}
              </a>
            )}
            <a
              className="ant-dropdown-link mr-16"
              onClick={() => {
                handleComment && handleComment(line)
              }}>
              {i18n.get('评论')}
            </a>
          </div>
        )
      },
    }
    return [...baseCol, action]
  }

  clearSelected = () => {
    this.setState({ selectedRowKeys: [], selectedRows: [] })
  }

  handleReceiveList = (selectedRowKeys, selectedRows) => {
    const { handleReceiveList } = this.props
    handleReceiveList && handleReceiveList(selectedRowKeys, selectedRows, this.clearSelected)
  }

  handleSelectedChange = (selectedKeys, selectedRowsData) => {
    this.setState({
      selectedRowKeys: selectedKeys,
      selectedRows: selectedRowsData,
    })
  }

  render() {
    const { selectedRowKeys, selectedRows } = this.state
    const { dataSource, selectedIndex, isModal = false, useExpressNumber } = this.props
    const showFooter = ('ALL' === selectedIndex || 'WAIT' === selectedIndex) && useExpressNumber

    return (
      <div className={styles[`data-grid-simple-view${isModal ? '-modal' : ''}`]}>
        <DataGrid.TableWrapper
          columns={this.getColumns()}
          dataSource={dataSource}
          selectedRowKeys={this.state.selectedRowKeys}
          isSingleSelect={false}
          isMultiSelect={showFooter || isModal ? true : false}
          onSelectedChange={this.handleSelectedChange}
          {...this.props}
        />
        {(showFooter || isModal) && (
          <div className={'table-footer'}>
            <Button
              category="secondary"
              disabled={!(selectedRowKeys && selectedRowKeys.length > 0)}
              onClick={() => this.handleReceiveList(selectedRowKeys, selectedRows)}>
              {i18n.get('确认收单')}
            </Button>
            <span>
              {i18n.get('已选择 {__k0}/{__k1} 张', {
                __k0: selectedRowKeys.length,
                __k1: dataSource.length,
              })}
            </span>
          </div>
        )}
      </div>
    )
  }
}
