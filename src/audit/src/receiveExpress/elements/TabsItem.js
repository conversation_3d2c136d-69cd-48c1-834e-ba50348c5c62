/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018-10-09 11:11.
 */
import './TabsItem.less'
import React, { PureComponent } from 'react'
export default class TabsItem extends PureComponent {
  constructor(props) {
    super(props)
    this.state = { badge: '' }
  }
  componentWillReceiveProps(nextProps) {
    const badge = nextProps.count - this.props.count
    const { selected } = this.props
    if (badge && !selected) {
      this.setState({ badge: badge > 0 ? `+${badge}` : badge }, this.updateBadge)
    }
  }
  updateBadge = () => {
    setTimeout(() => {
      this.setState({ badge: '' })
    }, 1000)
  }
  render() {
    const { badge } = this.state
    const { count = 0, title, selected = false, onClick } = this.props
    return (
      <div
        className={`table-tabs-item-view${selected ? '-selected' : ''}`}
        onClick={() => {
          onClick && onClick()
        }}
      >
        <span className="span-count">{count}</span>
        {badge && !selected && <span className="span-badge">{badge}</span>}
        <span className="span-title">{title}</span>
      </div>
    )
  }
}
