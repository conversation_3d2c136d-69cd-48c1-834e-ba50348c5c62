/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018-10-10 10:53.
 * 此文件是之前的 DataGridSimple.js，仅供旧版使用
 */
import React, { PureComponent } from 'react'
import { Table } from 'antd'
import { Button } from '@hose/eui'
import { getBaseCol } from '../../util/Utils'
import styles from './DataGridSimple.module.less'
import { app as api } from '@ekuaibao/whispered'

export default class DataGridSimple extends PureComponent {
  constructor(props) {
    super(props)
    this.state = { selectedRowKeys: [], selectedRows: [] }
  }
  getColumns = () => {
    const baseCol = getBaseCol()
    const { handleComment, handleReceive, isModal = false } = this.props
    const action = {
      title: i18n.get('操作'),
      dataIndex: 'action',
      key: 'action',
      width: 150,
      fixed: isModal ? 'right' : false,
      render(text, line) {
        return (
          <div
            onClick={e => {
              e.persist()
              e.nativeEvent.stopImmediatePropagation()
              e.stopPropagation()
              e.preventDefault()
              return false
            }}>
            {line.state === 'RECEIVING' && (
              <a
                className="ant-dropdown-link mr-16"
                onClick={() => {
                  handleReceive && handleReceive(line)
                }}>
                {i18n.get('确认收单')}
              </a>
            )}
            <a
              className="ant-dropdown-link mr-16"
              onClick={() => {
                handleComment && handleComment(line)
              }}>
              {i18n.get('评论')}
            </a>
          </div>
        )
      },
    }
    return [...baseCol, action]
  }
  handleOnRowClick = record => {
    api.invokeService('@bills:get:flow-info', { id: record.flowId.id }).then(({ value }) => {
      const { handleShowDetail } = this.props
      handleShowDetail && handleShowDetail(value)
    })
  }
  clearSelected = () => {
    this.setState({ selectedRowKeys: [], selectedRows: [] })
  }
  handleReceiveList = (selectedRowKeys, selectedRows) => {
    const { handleReceiveList } = this.props
    handleReceiveList && handleReceiveList(selectedRowKeys, selectedRows, this.clearSelected)
  }
  render() {
    const { selectedRowKeys, selectedRows } = this.state
    const rowSelection = {
      selectedRowKeys: selectedRowKeys,
      onChange: (selectedRowKeys, selectedRows) => {
        this.setState({ selectedRowKeys, selectedRows })
      },
      getCheckboxProps: record => ({
        name: record.name,
      }),
    }
    const { dataSource, selectedIndex, isModal = false, useExpressNumber } = this.props
    const showFooter = ('ALL' === selectedIndex || 'WAIT' === selectedIndex) && useExpressNumber
    const scroll = isModal ? { x: 990, y: 654 } : { x: 990 }
    return (
      <div className={styles[`data-grid-simple-view${isModal ? '-modal' : ''}`]}>
        <Table
          rowSelection={showFooter || isModal ? rowSelection : null}
          className="table-view"
          pagination={false}
          scroll={scroll}
          onRow={record => {
            return { onClick: () => this.handleOnRowClick(record) }
          }}
          columns={this.getColumns()}
          dataSource={dataSource}
        />
        {(showFooter || isModal) && (
          <div className={'table-footer'}>
            <Button
              category="secondary"
              disabled={!(selectedRowKeys && selectedRowKeys.length > 0)}
              onClick={() => this.handleReceiveList(selectedRowKeys, selectedRows)}>
              {i18n.get('确认收单')}
            </Button>
            <span>
              {i18n.get('已选择 {__k0}/{__k1} 张', {
                __k0: selectedRowKeys.length,
                __k1: dataSource.length,
              })}
            </span>
          </div>
        )}
      </div>
    )
  }
}
