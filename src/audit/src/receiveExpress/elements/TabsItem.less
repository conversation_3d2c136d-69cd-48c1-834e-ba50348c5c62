@keyframes badge {
  from {
    top: 10px;
    color: var(--brand-base);
  }
  to {
    top: -16px;
    color: var(--brand-base);
  }
}
.table-tabs-item-view {
  display: flex;
  padding: 5px 16px;
  flex-direction: column;
  align-items: center;
  min-width: 100px;
  cursor: pointer;
  margin-right: 8px;
  position: relative;
  .span-count {
    font-size: 20px;
    font-weight: 500;
    line-height: 1.4;
    text-align: center;
    color: rgba(0, 0, 0, 0.85);
  }
  .span-badge {
    font-size: 16px;
    line-height: 1.38;
    color: #00000000;
    right: 20px;
    position: absolute;
    animation: badge 1s;
  }
  .span-title {
    font-size: 12px;
    line-height: 1.67;
    text-align: center;
    color: rgba(0, 0, 0, 0.45);
  }
  &:hover {
    background-color: #fafafa;
  }
}

.table-tabs-item-view-selected {
  display: flex;
  padding: 5px 16px;
  flex-direction: column;
  align-items: center;
  min-width: 100px;
  cursor: pointer;
  margin-right: 8px;
  background: var(--brand-base);
  .span-count {
    font-size: 20px;
    font-weight: 500;
    line-height: 1.4;
    text-align: center;
    color: #ffffff;
  }
  .span-title {
    font-size: 12px;
    line-height: 1.67;
    text-align: center;
    color: #ffffff;
  }
}
