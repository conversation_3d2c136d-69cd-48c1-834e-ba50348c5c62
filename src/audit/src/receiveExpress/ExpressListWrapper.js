import React, { PureComponent } from 'react'

import DataGridLoader from './elements/DataGridLoader'
import { app as api } from '@ekuaibao/whispered'
import { handleActionImplementation, handleReceiveList } from '../service'

export default class ExpressListWrapper extends PureComponent {
  constructor(props) {
    super(props)
    this.state = { dataSource: props.dataSource }
  }
  handleComment = backlog => {
    api.open('@bills:BillCommentModal', { flow: backlog.flowId }).then(params => {
      const args = { params, id: backlog.flowId.id }
      api.invokeService('@bills:comment:flow', args)
    })
  }
  handleReceive = line => {
    handleActionImplementation.call(this, {
      type: 12,
      backlog: line,
      fn: result => {
        const { value } = result
        const errors = value && value.errors
        if (errors[0].resultCode === 'OK') {
          api.invokeService('@layout5:refresh:menu:data')
          const { backlogId } = errors[0]
          const { dataSource } = this.state
          this.setState({ dataSource: dataSource.filter(o => o.id !== backlogId) }, this.updateIds)
        }
      },
    })
  }
  updateIds = () => {
    const { dataSource } = this.state
    const { handleUpdateBackLogIds } = this.props
    let backlogIds = []
    for (let item of dataSource) {
      backlogIds.push(item.id)
    }
    handleUpdateBackLogIds && handleUpdateBackLogIds(backlogIds)
  }
  handleReceiveList = (keys, data) => {
    handleReceiveList(keys, data, result => {
      api.invokeService('@layout5:refresh:menu:data')
      const { value } = result
      const errors = value && value.errors
      let okKeys = []
      for (let item of errors) {
        if (item.resultCode === 'OK') {
          okKeys.push(item.backlogId)
        }
      }
      if (okKeys.length > 0) {
        const { dataSource } = this.state
        this.setState(
          { dataSource: dataSource.filter(o => okKeys.indexOf(o.id) === -1) },
          this.updateIds,
        )
      }
    })
  }

  handleShowDetail = item => {
    this.props.stackerManager.push('BillInfoView', { dataSource: item })
  }
  render() {
    const { dataSource } = this.state
    const { isModal, staffs } = this.props
    return (
      <DataGridLoader
        handleComment={this.handleComment}
        handleReceive={this.handleReceive}
        handleShowDetail={this.handleShowDetail}
        handleReceiveList={this.handleReceiveList}
        isModal={isModal}
        staffs={staffs}
        dataSource={dataSource}
      />
    )
  }
}
