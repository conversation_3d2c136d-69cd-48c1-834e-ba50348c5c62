@import '~@ekuaibao/web-theme-variables/styles/default';

.lightning-receive-view {
  display: flex;
  flex: 1;
  flex-direction: column;
  padding: 25px 32px;
  background-color: #ffffff;
  :global {
    .express-number-div {
      padding: 12px 16px;
      margin-bottom: 16px;
      background-color: #f5f5f5;
      span {
        font-size: 14px;
        &:first-child {
          color: rgba(0, 0, 0, 0.45);
          font-weight: 500;
        }
        &:last-child {
          margin-left: 8px;
          color: rgba(0, 0, 0, 0.85);
        }
      }
    }
    .input-parent-div {
      padding-top: 8px;
      width: 472px;
      display: flex;
      flex-direction: row;
      align-items: center;
      .ant-input-affix-wrapper {
        .ant-input {
          height: 40px;
          font-size: 14px;
        }
      }
    }
  }
}
