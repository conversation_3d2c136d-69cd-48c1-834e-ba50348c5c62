import React, { PureComponent } from 'react'
import styles from './LightningReceive.module.less'
import Tabs from './elements/Tabs'
import { LightningReceiveBackLog, getBackLogInfoList } from '../audit-action'
import { message } from 'antd/lib/index'
import { handleActionImplementation, handleReceiveList } from '../service'
import { app as api } from '@ekuaibao/whispered'
import { formatNum } from '../util/Utils'
import { showMessage, showModal } from '@ekuaibao/show-util'
import DataGridLoader from './elements/DataGridLoader'
import DataGridSimple from './elements/OldDataGridSimple'
const LightingMode = api.require('@elements/data-grid-v2/LightingMode')

export default class LightningReceive extends PureComponent {
  constructor(props, ...args) {
    super(props, args)
    let backlogIds = props.useExpressNumber ? props.value.backlogIds : []
    this.state = {
      flowCode: '',
      listAll: [],
      expressList: backlogIds || [],
      selectedIndex: 'ALL',
      lightingErrorFeedback: false,
    }
  }
  onPressEnter = e => {
    let { expressNumber = '', useExpressNumber } = this.props
    let { flowCode, listAll, expressList } = this.state
    const newFlowCode = flowCode.trim()
    if (newFlowCode) {
      if (this.isBillExist(newFlowCode, listAll)) {
        this.lightingSearchErrorFeedback(i18n.get('该单据已经添加'))
        this.setState({ flowCode: '' })
      } else {
        api
          .dispatch(LightningReceiveBackLog(newFlowCode, expressNumber, null, true))
          .then(res => {
            let { errors } = res.value
            let { resultCode, backlogId } = errors[0]
            if (resultCode === 'OK') {
              showMessage.success(i18n.get('收单成功'))
            } else if (resultCode === 'WAIT') {
              this.lightingSearchErrorFeedback(i18n.get('与寄送单号不符，自动收单失败，需手动确认'))
            } else if (resultCode === 'CANNOT') {
              this.lightingSearchErrorFeedback(i18n.get('自动收单失败，需手动确认'))
            }
            api
              .invokeService('@audit:get:backlog-info', {
                id: backlogId,
                hiddenMsg: true,
              })
              .then(value => {
                let newExpressList = expressList
                if (useExpressNumber && resultCode === 'OK') {
                  newExpressList = expressList.filter(o => o !== backlogId)
                }
                value.resultCode = resultCode
                value.key = value.id
                const newList = [value, ...listAll]
                this.setState({ flowCode: '', listAll: newList, expressList: newExpressList })
              })
              .catch(e => {
                const msg = e.msg || e.message || e.errorMessage
                this.lightingSearchErrorFeedback(msg, 'error')
                this.setState({ flowCode: '' })
              })
          })
          .catch(e => {
            const msg = e.msg || e.message || e.errorMessage
            this.lightingSearchErrorFeedback(msg, 'error')
            this.setState({ flowCode: '' })
          })
      }
    }
  }
  isBillExist = (flowCode, listAll) => {
    return listAll.filter(o => o.flowId.form.code === flowCode).length > 0
  }
  handleInputChange = e => {
    this.setState({ flowCode: e.target.value })
  }
  getTabsDataList = () => {
    // OK 成功  ，CANNOT 无法处理 ，WAIT 待处理
    const { useExpressNumber } = this.props
    const { listAll } = this.state
    const all = listAll.length
    const cannot = listAll.filter(o => o.resultCode === 'CANNOT').length
    const wait = listAll.filter(o => o.resultCode === 'WAIT').length
    const ok = listAll.filter(o => o.resultCode === 'OK').length
    if (useExpressNumber) {
      return [
        { type: 'ALL', count: all, title: i18n.get('全部') },
        { type: 'OK', count: ok, title: i18n.get('相符(已收单)') },
        { type: 'WAIT', count: wait, title: i18n.get('不符') },
        { type: 'CANNOT', count: cannot, title: i18n.get('无法收单') },
      ]
    } else {
      return [
        { type: 'ALL', count: all, title: i18n.get('全部') },
        { type: 'OK', count: ok, title: i18n.get('已收单') },
        { type: 'CANNOT', count: cannot, title: i18n.get('无法收单') },
      ]
    }
  }
  handleOnTabsClick = type => {
    this.setState({ selectedIndex: type })
  }
  getTableDataSource = () => {
    const { listAll, selectedIndex } = this.state
    if (selectedIndex === 'ALL') {
      return listAll
    } else {
      return listAll.filter(o => o.resultCode === selectedIndex)
    }
  }
  handleOnOtherClick = async () => {
    const { expressList } = this.state
    const { staffs } = this.props
    if (expressList.length === 0) {
      message.warning(i18n.get('无相符单据'))
      return
    }
    const action = await getBackLogInfoList(expressList)
    api.dispatch(action).then(result => {
      let list = result.items || []
      list.map(item => {
        item.key = item.id
      })
      api.open('@bills:BillStackerModal', {
        viewKey: 'ExpressListWrapper',
        staffs: staffs,
        isModal: true,
        dataSource: list,
        handleUpdateBackLogIds: this.handleUpdateBackLogIds,
      })
    })
  }
  handleUpdateBackLogIds = ids => {
    this.setState({ expressList: ids })
  }
  handleShowDetail = item => {
    api.open('@bills:BillStackerModal', {
      viewKey: 'BillInfoView',
      dataSource: item,
      scene: 'APPROVER',
    })
  }
  handleComment = backlog => {
    api.open('@bills:BillCommentModal', { flow: backlog.flowId }).then(params => {
      const args = { params, id: backlog.flowId.id }
      api.invokeService('@bills:comment:flow', args)
    })
  }
  handleReceive = line => {
    handleActionImplementation.call(this, {
      type: 12,
      backlog: line,
      fn: result => {
        const { selectedIndex, listAll, expressList } = this.state
        const { useExpressNumber } = this.props
        const { value } = result
        const errors = value && value.errors
        if (errors[0].resultCode === 'OK') {
          api.invokeService('@layout5:refresh:menu:data')
          api
            .invokeService('@audit:get:backlog-info', {
              id: line.id,
            })
            .then(value => {
              let newExpressList = expressList
              if (useExpressNumber) {
                newExpressList = expressList.filter(o => o !== line.id)
              }
              if (selectedIndex !== 'WAIT') {
                value.resultCode = listAll.filter(o => o.id === value.id)[0].resultCode
              } else {
                value.resultCode = selectedIndex
              }
              value.key = value.id
              const newList = [value, ...listAll.filter(o => o.id !== line.id)]
              this.setState({ listAll: newList, expressList: newExpressList })
            })
        }
      },
    })
  }
  handleReceiveList = (keys, data, callback) => {
    handleReceiveList(keys, data, async result => {
      api.invokeService('@layout5:refresh:menu:data')
      const { selectedIndex, listAll } = this.state
      const { value } = result
      const errors = value && value.errors
      let okKeys = []
      for (let item of errors) {
        if (item.resultCode === 'OK') {
          okKeys.push(item.backlogId)
        }
      }
      if (okKeys.length > 0) {
        const action = await getBackLogInfoList(okKeys)
        api.dispatch(action).then(result => {
          let list = result.items || []
          list.map(item => {
            item.key = item.id
            if (selectedIndex !== 'WAIT') {
              item.resultCode = listAll.filter(o => o.id === item.id)[0].resultCode
            } else {
              item.resultCode = selectedIndex
            }
          })
          this.setState({ listAll: [...list, ...listAll.filter(o => okKeys.indexOf(o.id) === -1)] })
          callback && callback()
        })
      }
    })
  }

  handleCheckChange = e => {
    this.setState({ lightingErrorFeedback: e.target.checked })
  }

  lightingSearchErrorFeedback = (msg, errType = 'warning') => {
    const { lightingErrorFeedback } = this.state
    if (lightingErrorFeedback) {
      errType === 'warning' ? showModal.warning({ title: msg }) : showModal.error({ title: msg })
    } else {
      errType === 'warning' ? message.warning(msg) : message.error(msg)
    }
  }

  render() {
    const { flowCode, expressList, selectedIndex } = this.state
    const { useExpressNumber, expressNumber, staffs } = this.props
    return (
      <div className={styles['lightning-receive-view']}>
        {useExpressNumber && (
          <div className="express-number-div">
            <span>{i18n.get('按寄送单号收单')}</span>
            <span>{formatNum(expressNumber)}</span>
          </div>
        )}
        <div className="input-parent-div" style={{ paddingBottom: 25 }}>
          <LightingMode
            flowCode={flowCode}
            hasLightingCheck={true}
            onChange={this.handleInputChange}
            onPressEnter={this.onPressEnter}
            onCheckChange={this.handleCheckChange}
          />
        </div>
        <Tabs
          onOtherClick={this.handleOnOtherClick}
          selectedIndex={selectedIndex}
          dataList={this.getTabsDataList()}
          isExpress={useExpressNumber}
          expressList={expressList}
          onClick={this.handleOnTabsClick}
        />
        {window.isNewHome ? (
          <DataGridLoader
            dataSource={this.getTableDataSource()}
            handleShowDetail={this.handleShowDetail}
            handleComment={this.handleComment}
            handleReceive={this.handleReceive}
            handleReceiveList={this.handleReceiveList}
            groupPanel={{
              visible: false,
            }}
            staffs={staffs}
            useExpressNumber={useExpressNumber}
            selectedIndex={selectedIndex}
          />
        ) : (
          <DataGridSimple
            handleShowDetail={this.handleShowDetail}
            handleComment={this.handleComment}
            handleReceive={this.handleReceive}
            handleReceiveList={this.handleReceiveList}
            staffs={staffs}
            useExpressNumber={useExpressNumber}
            selectedIndex={selectedIndex}
            dataSource={this.getTableDataSource()}
          />
        )}
      </div>
    )
  }
}
