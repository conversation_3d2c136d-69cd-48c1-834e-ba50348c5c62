import { app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
const LoaderWithLegacyData = api.require('@elements/data-grid-v2/LoaderWithLegacyData')
const { createNodeNameColumn, createNodeStaffColumn } = api.require('@elements/data-grid-v2/CreateColumn')
const { searchOptions, globalSearchOptions } = api.require('@lib/data-grid-v2/CustomSearchUtil')
import { fetchBackLogs } from '../util/fetchUtil'
import { MessageCenter } from '@ekuaibao/messagecenter'
import { Resource } from '@ekuaibao/fetch'
import * as viewUtil from '../view-util'
import { mapping } from '../util/mapping4Approve'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import { searchBackLogsCalcNoPage } from '../audit-action'
import {
  handlePrint,
  handleActionImplementation,
  handlePrintRemind,
  handleReceiveList,
  handleReceiveExceptionList,
  handleReceivePrint,
} from '../service'
const { exportExcel } = api.require('@lib/export-excel-service')
import { cloneDeep } from 'lodash'
import { fnFilterMapping, getInitScenes } from '../util/Utils'
import { createActionColumn4Receive, createRiskWarningColumn } from '../util/columnsAndSwitcherUtil'
import { EnhanceConnect } from '@ekuaibao/store'
import { externalStaffActionBlackList } from '../view-util'
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

const scenesType = 'RECEIVING'
const approve = new Resource('/api/flow/v2/filter')

const prefixColumns = { state: 'flowId', '*': 'flowId.form' }
@EnhanceConnect(state => ({
  specifications: state['@custom-specification'].specificationGroupsList,
  invoiceReviewPower: state['@common'].powers.invoiceReview,
  legalEntityCurrencyPower: state['@common'].powers.legalEntityCurrency,
  Express: state['@common'].powers.Express,
  // 涉及需求 https://hose2019.feishu.cn/wiki/wikcncXiMtXZQwNcS64OMEwSsJd#fTSuk8
  KA_GLOBAL_SEARCH_2: state['@common'].powers.KA_GLOBAL_SEARCH_2,
  showPrintBtn: state['@common'].showPrintBtn,
}))
export default class ReceiveWrapper extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
    this.state = { scenes: [] }
  }

  componentDidMount() {
    this.bus.on('buttons:click', this.handleButtonsClick)
    this.bus.on('table:row:click', this.handleTableRowClick)
    this.bus.on('table:row:action', this.handleActions)
    this.bus.on('selectAllBtn:click', this.handleSelectAllBtnClick)
    this.bus.on('initScenes:action', this.initScenes.bind(this))

    // 获取场景列表
    approve.GET('/$type', { type: scenesType }).then(async res => {
      const { value } = res
      const { specifications = [] } = this.props
      if (specifications.length === 0) {
        await api.invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned')
      }
      this.initScenes(value)
    })
  }

  componentWillUnmount() {
    this.bus.un('buttons:click', this.handleButtonsClick)
    this.bus.un('table:row:click', this.handleTableRowClick)
    this.bus.un('table:row:action', this.handleActions)
    this.bus.un('selectAllBtn:click', this.handleSelectAllBtnClick)
    this.bus.un('initScenes:action', this.initScenes.bind(this))
  }

  initScenes(data) {
    const { specifications, userInfo} = this.props
    const scenesData = getInitScenes({data,prefix:'flowId',specifications,isHasWaitInvoice:false})
    this.setState({ scenes:scenesData.scenes})
  }

  fetchPending = async (params = {}, dimensionItems = {}) => {
    const { scenes } = this.state
    params.status = { state: ['RECEIVING'] }
    const { scene } = params
    const findScene = scenes.find(s => s.sceneIndex === scene)
    if (findScene) {
      params.scene = findScene.scene !== 'waitInvoice' ? findScene.scene : ''
    }
    scene && this.setState({ scene, fetchParams: params, dimensionItems })
    const res = await fetchBackLogs(params, findScene, dimensionItems)
    this._currentDataSource = res.dataSource
    return res
  }

  handleSelectAllBtnClick = (params = {}) => {
    params.status = { state: ['RECEIVING'] }
    const { scenes, scene, fetchParams, dimensionItems } = this.state
    const findScene = scenes.find(s => s.sceneIndex === scene)
    if (findScene) {
      params.scene = findScene.scene !== 'waitInvoice' ? findScene.scene : ''
    }

    if (fetchParams) {
      if (fetchParams.filters) params.filters = fetchParams.filters
      if (fetchParams.searchText) params.searchText = fetchParams.searchText
    }

    const { legalEntityCurrencyPower, userInfo } = this.props
    return searchBackLogsCalcNoPage(params, findScene, dimensionItems, legalEntityCurrencyPower).then(resp => {
      let data = {}
      resp &&
        resp.value &&
        resp.value.flows.length > 0 &&
        resp.value.flows.forEach(flow => {
          data[flow.id] = flow
        })
      let sum = (resp && resp.value && resp.value.formMoney) || 0
      let keys = Object.keys(data)
      let buttons = [
        { name: i18n.get('确认收单'), type: 'normal', key: 'receive' },
        {
          name: i18n.get('导出'),
          type: 'normal',
          key: 'export_all',
        },
        {
          name: i18n.get('打印单据'),
          type: 'normal',
          key: 'print',
        },
        {
          name: i18n.get('打印提醒'),
          type: 'normal',
          key: 'print_remind',
        },
        {
          name: i18n.get('收到打印'),
          type: 'normal',
          key: 'recive_print',
        },
      ]
      if (this.props.showPrintBtn) {
        buttons = buttons
          .slice(0, 3)
          .concat([
            {
              name: i18n.get('打印单据和发票'),
              type: 'normal',
              key: 'printInvoice',
            },
          ])
          .concat(buttons.slice(3, buttons.length))
      }

      if (userInfo?.staff?.external) {
        buttons = buttons.filter(item => !externalStaffActionBlackList.includes(item.key))
      }

      api.open('@layout:DataGridSelectAllModal', { keys, buttons, sum }).then(name => {
        this.handleButtonsClick({ name, data, keys }, params)
      })
    })
  }

  fnGetScene() {
    const { scenes, scene } = this.state
    let cloneScenes = cloneDeep(scenes)
    let findScenes = cloneScenes.find(s => s.sceneIndex === scene)
    if (findScenes) {
      findScenes.scene = findScenes.scene !== 'waitInvoice' ? findScenes.scene : ''
    }
    if (findScenes && findScenes.scene) return scene
    return findScenes
  }

  _handleReceiveList = (keys, data) => {
    handleReceiveList(keys, data, this.reload)
  }
  _handleReceiveExceptionList = (keys, data) => {
    handleReceiveExceptionList(keys, data, this.reload)
  }
  handleButtonsClick = ({ name, data, keys }, fetchParams) => {
    keys = keys ? keys : this.bus.getSelectedRowKeys()
    data = data ? data : this.bus.getSelectedRowData()
    switch (name) {
      case 'receive':
        return this._handleReceiveList(keys, data)
      case 'receive_exception':
        return this._handleReceiveExceptionList(keys, data)
      case 'print':
        return this._handlePrintList(keys, data, this.reloadAndClearSelecteds, '0')
      case 'printInvoice':
        return this._handlePrintList(keys, data, this.reloadAndClearSelecteds, '1')
      case 'export_selected':
        return exportExcel({ exportType: 'export_selected', funcType: 'approve', data }, this.bus)
      case 'export_all': {
        let params = fetchParams || this.bus.getFilterParam()
        let { status } = params
        params.status = { ...status, state: ['RECEIVING'] }
        const scene = this.fnGetScene()
        params.scene = scene
        let exportParam = {
          exportType: 'export_all',
          funcType: 'backlog',
          data: params,
          onlyAsyncExport: true,
        }
        if (scene && scene.sceneIndex === 'waitInvoice') {
          exportParam.others = { queryString: 'waitInvoice=true' }
        }
        return exportExcel(exportParam, this.bus)
      }
      case 'print_remind':
        this._handlePrintRemindList(keys, data)
        break
      case 'recive_print':
        const flowIds = Object.values(data).map(item => item.flowId.id)
        this.handleReceivePrint(flowIds, this.reloadAndClearSelecteds)
        break
    }
  }

  reloadAndClearSelecteds = () => {
    this.bus.clearSelectedRowKeys()
    this.bus.reload().then(() => {
      setTimeout(() => {
        this.bus.emit('table:select:current:row')
      }, 500);
    })
  }

  reload = _ => {
    api.invokeService('@layout5:refresh:menu:data')
    this.bus.clearSelectedRowKeys()
    this.bus.reload().then(() => {
      setTimeout(() => {
        this.bus.emit('table:select:current:row')
      }, 500);
    })
  }

  _handlePrintRemindList = (keys, data) => {
    const backLogs = Object.values(data)
    const flowIds = backLogs.map(element => element.flowId.id)
    handlePrintRemind.call(this, flowIds, this.reload)
  }

  _handlePrintList(keys, data, fn, printInvoice) {
    handlePrint.call(this, keys, data, fn, false, printInvoice)
  }

  __billInfoPopup = backlog => {
    viewUtil.getLoanRisk(backlog).then(riskTip => {
      let title = `${i18n.get(billTypeMap()[backlog.type])}${i18n.get('详情')}`
      api.open(
        '@bills:BillInfoPopup',
        {
          title: title,
          backlog,
          riskTip: riskTip,
          isEditConfig: true,
          isShowCondition: true,
          invokeService: '@audit:get:backlog-info',
          params: {
            id: backlog.id,
            type: backlog.type,
          },
          reload: this.bus.reload,
          scene: 'APPROVER',
          needConfigButton: true,
          mask: false,
          onOpenOwnerLoanList: line => {
            this.__handleLine(9, line)
          },

          onFooterButtonsClick: (type, line) => {
            api.close()
            setTimeout(() => {
              this.__handleLine(type, line)
            }, 0)
          },
        },
        true,
      )
    })
  }

  __handleLine(type, line) {
    handleActionImplementation.call(this, { type, backlog: line, fn: this.reload })
  }

  handleReceivePrint = (key, fn) => {
    handleReceivePrint.call(this, key, fn)
  }

  handleTableRowClick = backlog => {
    if (backlog.state === 'RECEIVING') {
      startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()

      if (api.require('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
        api.open('@bills:BillInfoDrawerV2', {
          currentId: backlog.flowId.id,
          flows: this._currentDataSource?.map(v => v.flowId) || [],
          bus: this.bus,
          billDetailsProps: {
            isEditConfig: true,
            isShowCondition: true,
            onOpenOwnerLoanList: line => {
              this.__handleLine(9, line)
            },
          },
          onFooterButtonsClick: (type, line) => {
            api.close()
            setTimeout(() => {
              this.__handleLine(type, line)
            }, 0)
          },
        })
        return
      }

      api
        .invokeService('@audit:get:backlog-info', {
          id: backlog.id,
          type: backlog.type,
        })
        .then(backlog => {
          this.__billInfoPopup(backlog)
        })
    }
  }

  handleActions = (type, line) => {
    if (line.state === 'RECEIVING') {
      handleActionImplementation.call(this, { type, backlog: line, fn: this.reload })
    }
  }

  getOptionButtons = () => {
    const btns = [
      { text: i18n.get('确认收单'), name: 'receive', type: 'primary' },
      { text: i18n.get('导出选中'), name: 'export_selected' },
      { text: i18n.get('打印单据'), name: 'print' },
      { text: i18n.get('打印单据和发票'), name: 'printInvoice' },
      { text: i18n.get('打印提醒'), name: 'print_remind' },
      { text: i18n.get('收到打印'), name: 'recive_print' },
    ]
    const { showPrintBtn, userInfo } = this.props
    let showButtons = showPrintBtn ? btns : btns.filter(v => v.name !== 'printInvoice')
    if (userInfo?.staff?.external) {
      showButtons = showButtons.filter(v => !externalStaffActionBlackList.includes(v.name))
    }
    return showButtons
  }

  buttons = this.getOptionButtons()

  selectAllBtnStyles = { color: 'var(--brand-base)' }

  render() {
    const { baseDataProperties, budgetPower, staffs, size, invoiceReviewPower, Express, KA_GLOBAL_SEARCH_2 } =
      this.props
    const { scenes } = this.state

    if (Express && !this.buttons.some(item => item.name === 'receive_exception')) {
      this.buttons.splice(2, 0, { text: i18n.get('收单异常'), name: 'receive_exception' })
    }

    if (!scenes.length) {
      return null
    }

    return (
      <LoaderWithLegacyData
        newSearch={true}
        searchOptions={KA_GLOBAL_SEARCH_2 ? globalSearchOptions() : searchOptions()}
        enableGlobalSearch={KA_GLOBAL_SEARCH_2}
        lightingMode={this.state.isLightingMode}
        scenes={scenes}
        fetch={this.fetchPending}
        buttons={this.buttons}
        onButtonClick={this.handleButtonsClick}
        onSelectedAll={this.handleSelectAllBtnClick}
        prefixColumns={prefixColumns}
        bus={this.bus}
        resource={approve}
        scenesType={scenesType}
        baseDataProperties={baseDataProperties}
        createAction={createActionColumn4Receive}
        mapping={fnFilterMapping(mapping, invoiceReviewPower)}
        createRiskWarningColumn={() => createRiskWarningColumn(true)}
        showOutsideButtonCount={4}
        createNodeNameColumn={() => createNodeNameColumn({ dataIndex: 'flowId.nodeState.nodeName' })}
        createNodeStaffColumn={() => createNodeStaffColumn({ dataIndex: 'flowId.nodeState.staffName', filterType: false })}
        useNewFieldSet
      />
    )
  }
}
