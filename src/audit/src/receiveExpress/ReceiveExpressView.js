import { app } from '@ekuaibao/whispered'
import styles from './ReceiveExpressView.module.less'
import React, { PureComponent } from 'react'
import { EnhanceStackerManager } from '@ekuaibao/enhance-stacker-manager'
const EKBBreadcrumb = app.require('@ekb-components/business/breadcrumb')

@EnhanceStackerManager([
  {
    key: 'ReceiveExpressTable',
    getComponent: () => import('./ReceiveExpressTable'),
    title: i18n.get('收单')
  },
  {
    key: 'LightningReceive',
    getComponent: () => import('./LightningReceive'),
    title: i18n.get('闪电收单')
  }
])
export default class ReceiveExpressView extends PureComponent {
  componentDidMount() {
    this.props.stackerManager.push('ReceiveExpressTable', {})
  }
  handleMenuClick(line, i) {
    return this.props.stackerManager.open(i, {})
  }
  renderBreadcrumb() {
    const array = this.props.stackerManager.values()
    let items = []
    array.forEach((line, i) => {
      items.push({
        key: i,
        onClick: () => this.handleMenuClick(line, i),
        title: line.title
      })
    })
    return <EKBBreadcrumb items={items} {...this.props} />
  }

  render() {
    const length = this.props.stackerManager.keys().length
    return (
      <div className={styles['receive-express-view']}>
        {length > 1 && (
          <div className="header">
            <div className="menu">{this.renderBreadcrumb()}</div>
          </div>
        )}
        <div className="receive-express-content">{this.props.children}</div>
      </div>
    )
  }
}
