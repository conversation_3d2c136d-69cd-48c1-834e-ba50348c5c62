import { app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
const DataGridWrapper = api.require('@elements/data-grid/DataGridWrapper')
const { createNodeNameColumn, createNodeStaffColumn } = api.require('@elements/data-grid-v2/CreateColumn')
import { searchBackLogsCalcNoPage } from '../audit-action'
import { fetchBackLogs } from '../util/fetchUtil'
import { MessageCenter } from '@ekuaibao/messagecenter'
import { mapping } from '../util/mappingWSend'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import {
  handlePrint,
  handleActionImplementation,
  handlePrintRemind,
  handleSendExpressList,
  handleReceivePrint,
  fnGetFlowIds,
} from '../service'
import { createActionColumn4AuditSignature, createRiskWarningColumn } from '../util/columnsAndSwitcherUtil'
const { exportExcel } = api.require('@lib/export-excel-service')
import { cloneDeep } from 'lodash'
import { Resource } from '@ekuaibao/fetch'
import * as viewUtil from '../view-util'
import { getDefaultScenes } from '../util/Utils'
const LoaderWithLegacyData = api.require('@elements/data-grid-v2/LoaderWithLegacyData')
const { searchOptions, globalSearchOptions } = api.require('@lib/data-grid-v2/CustomSearchUtil')
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

const filterResource = new Resource('/api/flow/v2/filter')
const prefixColumns = { state: 'flowId', '*': 'flowId.form' }
import { EnhanceConnect } from '@ekuaibao/store'

@EnhanceConnect(state => ({
  legalEntityCurrencyPower: state['@common'].powers.legalEntityCurrency,
  KA_GLOBAL_SEARCH_2: state['@common'].powers.KA_GLOBAL_SEARCH_2,
  showPrintBtn: state['@common'].showPrintBtn,
}))
export default class AuditSignatureWrapper extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
    this.state = {
      scenes: [],
    }
  }

  componentDidMount() {
    this.bus.on('buttons:click', this.handleButtonsClick)
    this.bus.on('table:row:click', this.handleTableRowClick)
    this.bus.on('table:row:action', this.handleActions)
    this.bus.on('selectAllBtn:click', this.handleSelectAllBtnClick)
    this.bus.on('initScenes:action', this.initScenes.bind(this))

    this.fnInitScenes(this.props)
  }
  componentWillReceiveProps(nextProps) {
    if (nextProps.checkedKey !== this.props.checkedKey && nextProps.checkedKey === this.props.tabKey) {
      this.bus.clearSelectedRowKeys()
      this.bus.reload()
    }
  }
  fnInitScenes = props => {
    filterResource.GET('/$type', { type: this.props.tabKey }, null, { hiddenLoading: true }).then(res => {
      const { value } = res
      if (props.specifications.length) {
        this.initScenes(value)
        api
          .invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned', {}, { hiddenLoading: true })
          .then(() => this.initScenes(value))
      } else {
        api
          .invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned', {}, { hiddenLoading: true })
          .then(() => this.initScenes(value))
      }
    })
  }

  componentWillUnmount() {
    this.bus.un('buttons:click', this.handleButtonsClick)
    this.bus.un('table:row:click', this.handleTableRowClick)
    this.bus.un('table:row:action', this.handleActions)
    this.bus.un('selectAllBtn:click', this.handleSelectAllBtnClick)
    this.bus.un('initScenes:action', this.initScenes)
  }

  initScenes(data) {
    const { specifications } = this.props
    const defaultScenes = getDefaultScenes('flowId', ['expense', 'loan', 'requisition'], specifications)
    const allScenes = { text: i18n.get('全部'), scene: 'all', active: true, sceneIndex: 'all' }
    const filter = data
      ? data.filter.map(d => {
          let temp = JSON.parse(d)
          temp.pageSize = data.pageSize || 20
          return temp
        })
      : defaultScenes
    const scenes = !!~filter.findIndex(el => el.scene === 'all') ? filter : [allScenes, ...filter]
    this.setState({ scenes })
  }

  fetchPending = async (params = {}, dimensionItems = {}) => {
    const { scenes } = this.state
    const { scene } = params
    const findScene = scenes.find(s => s.sceneIndex === scene)
    if (findScene) {
      params.scene = findScene.scene
    }
    params.status = { state: [this.props.tabKey] }
    scene && this.setState({ scene, fetchParams: params, dimensionItems })
    const res = await fetchBackLogs(params, findScene, dimensionItems)
    this._currentDataSource = res.dataSource
    return res
  }

  handleSelectAllBtnClick = (params = {}) => {
    const { scenes, scene, fetchParams, dimensionItems } = this.state
    const findScene = scenes.find(s => s.sceneIndex === scene)
    findScene && (params.scene = findScene.scene || '')

    if (findScene) {
      params.scene = findScene.scene
    }
    if (fetchParams) {
      if (fetchParams.filters) params.filters = fetchParams.filters
      if (fetchParams.searchText) params.searchText = fetchParams.searchText
    }
    params.status = { state: [this.props.tabKey] }
    const { legalEntityCurrencyPower } = this.props
    return searchBackLogsCalcNoPage(params, findScene, dimensionItems, legalEntityCurrencyPower).then(resp => {
      const data = {}
      resp &&
        resp.value &&
        resp.value.flows.length > 0 &&
        resp.value.flows.forEach(flow => {
          data[flow.id] = flow
        })
      const keys = Object.keys(data)
      const buttons = [
        {
          name: i18n.get('导出'),
          type: 'normal',
          key: 'export_all',
        },
        {
          name: i18n.get('打印单据'),
          type: 'normal',
          key: 'print',
        },
      ]

      if (this.props.showPrintBtn) {
        buttons.push({
          name: i18n.get('打印单据和发票'),
          type: 'normal',
          key: 'printInvoice',
        })
      }

      api.open('@layout:DataGridSelectAllModal', { keys, buttons, isBillsTotalMoney: true }).then(name => {
        this.handleButtonsClick({ name, data, keys }, params)
      })
    })
  }

  fnGetScene() {
    const { scenes, scene } = this.state
    let cloneScenes = cloneDeep(scenes)
    let findScenes = cloneScenes.find(s => s.sceneIndex === scene)
    if (findScenes) {
      findScenes.scene = findScenes.scene !== 'waitInvoice' ? findScenes.scene : ''
    }
    if (findScenes && findScenes.scene) return scene
    return findScenes
  }

  handleButtonsClick = ({ name, data, keys }, fetchParams) => {
    keys = keys ? keys : this.bus.getSelectedRowKeys()
    data = data ? data : this.bus.getSelectedRowData()
    switch (name) {
      case 'print':
        return this._handlePrintList(keys, data, this.reloadAndClearSelecteds, '0')
      case 'printInvoice':
        return this._handlePrintList(keys, data, this.reloadAndClearSelecteds, '1')
      case 'export_selected':
        return exportExcel({ exportType: 'export_selected', funcType: 'backlog', data }, this.bus)
      case 'export_all': {
        let params = fetchParams || this.bus.getFilterParam()
        params.status = { state: [this.props.tabKey] }
        const scene = this.fnGetScene()
        params.scene = scene
        let exportParam = {
          exportType: 'export_all',
          funcType: 'backlog',
          data: params,
          onlyAsyncExport: true,
        }
        return exportExcel(exportParam, this.bus)
      }
    }
  }

  reloadAndClearSelecteds = () => {
    this.bus.clearSelectedRowKeys()
    this.bus.reload()
  }

  _handlePrintList(keys, data, fn, printInvoice) {
    handlePrint.call(this, keys, data, fn, false, printInvoice)
  }
  __billInfoPopup = backlog => {
    viewUtil.getLoanRisk(backlog).then(riskTip => {
      const {tabKey} = this.props
      let title = `${billTypeMap()[backlog.type]}${i18n.get('详情')}`
      api.open(
        '@bills:BillInfoPopup',
        {
          title: title,
          backlog,
          riskTip: riskTip,
          isEditConfig: true,
          isShowCondition: true,
          invokeService: '@audit:get:backlog-info',
          showExpressButton: true,
          reload: this.bus.reload,
          params: {
            id: backlog.id,
            type: backlog.type,
          },
          scene: 'APPROVER',
          needConfigButton: tabKey === 'WAIT_SIGN',
          onOpenOwnerLoanList: line => {
            this.__handleLine(9, line)
          },

          onFooterButtonsClick: (type, line) => {
            api.close()
            setTimeout(() => {
              this.__handleLine(type, line)
            }, 0)
          },
        },
        true,
      )
    })
  }

  _handlePrintRemindList = (keys, data) => {
    const backLogs = Object.values(data)
    const flowIds = backLogs.map(element => element.flowId.id)
    handlePrintRemind.call(this, flowIds, this.reload)
  }

  __handleLine(type, line) {
    handleActionImplementation.call(this, { type, backlog: line, fn: this.reload })
  }

  handleReceivePrint = (key, fn) => {
    handleReceivePrint.call(this, key, fn)
  }

  handleTableRowClick = backlog => {
    startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()

    if (true || api.require('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
      api.open('@bills:BillInfoDrawerV2', {
        currentId: backlog.flowId.id,
        flows: this._currentDataSource.map((item => item.flowId)),
        bus: this.bus,
        billDetailsProps: {
          isEditConfig: true,
        },
        onOpenOwnerLoanList: line => {
          this.__handleLine(9, line)
        },

        onFooterButtonsClick: (type, line) => {
          api.close()
          setTimeout(() => {
            this.__handleLine(type, line)
          }, 0)
        },
      })
      return
    }

    api
      .invokeService('@audit:get:backlog-info', {
        id: backlog.id,
        type: backlog.type,
      })
      .then(backlog => {
        this.__billInfoPopup(backlog)
      })
  }

  reload = _ => {
    api.invokeService('@layout5:refresh:menu:data')
    this.bus.clearSelectedRowKeys()
    this.bus.reload()
  }

  _handleSendExpressList = (keys, name, params) => {
    handleSendExpressList(keys, name, this.reload, params)
  }

  handleActions = (type, line) => {
    handleActionImplementation.call(this, { type, backlog: line, fn: this.reload })
  }

  getOptionButtons = () => {
    const btns = [
      { text: i18n.get('导出选中'), name: 'export_selected' },
      { text: i18n.get('打印单据'), name: 'print' },
      { text: i18n.get('打印单据和发票'), name: 'printInvoice' },
    ]
    return this.props.showPrintBtn ? btns : btns.filter(v => v.name !== 'printInvoice')
  }

  buttons = this.getOptionButtons()

  render() {
    const { baseDataProperties, KA_GLOBAL_SEARCH_2 } = this.props
    const { scenes } = this.state

    if (!scenes.length) {
      return null
    }
    return (
      <LoaderWithLegacyData
        newSearch={true}
        searchOptions={KA_GLOBAL_SEARCH_2 ? globalSearchOptions() : searchOptions()}
        enableGlobalSearch={KA_GLOBAL_SEARCH_2}
        scenes={scenes}
        fetch={this.fetchPending}
        scenesType={this.props.tabKey}
        buttons={this.buttons}
        onButtonClick={this.handleButtonsClick}
        onSelectedAll={this.handleSelectAllBtnClick}
        resource={filterResource}
        baseDataProperties={baseDataProperties}
        createAction={bus => createActionColumn4AuditSignature(bus, this.props.tabKey)}
        bus={this.bus}
        prefixColumns={prefixColumns}
        mapping={mapping}
        createRiskWarningColumn={() => createRiskWarningColumn(true)}
        showOutsideButtonCount={4}
        createNodeNameColumn={() => createNodeNameColumn({ dataIndex: 'flowId.nodeState.nodeName' })}
        createNodeStaffColumn={() => createNodeStaffColumn({ dataIndex: 'flowId.nodeState.staffName', filterType: false })}
        useNewFieldSet
      />
    )
  }
}
