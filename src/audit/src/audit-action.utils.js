import { app } from '@ekuaibao/whispered'
import { isPayeeInfos, isBaseData, isDepartments, isSpecification, isStaffs, isExpenseLink, isPaymentAccount, isEnum, isDataLink, isPaymentAccountId, isExpenseLinks, isCity } from '@ekuaibao/lib/lib/propertySetIs';
import parseSelectUsePropertySet from '@ekuaibao/lib/lib/parseSelectUsePropertySet'

const parseSelectUsePropertySetOfScene = (propertySet, options, fields) => {
  if (options === void 0) { options = {}; }

  const getRefPropertyQuerySelect = (property) => {
    const queryLimitingFields = options.queryLimitingFields;
    var name = property.name;
    var canSelect = !queryLimitingFields || (Array.isArray(queryLimitingFields) && queryLimitingFields.some(function (i) { return i.split('.').includes(name); }));
    if (!canSelect)
        return;
    if (isPaymentAccountId(property)) {
        return name + '(id,detail.name)';
    }
    if (isBaseData(property)) {
        return name + "(code,name,enName,nameSpell)";
    }
    if (isDepartments(property)) {
        return name + "(id,name,enName,nameSpell)";
    }
    if (queryLimitingFields) {
        if (isSpecification(property) || isExpenseLink(property) || isExpenseLinks(property)) {
            return name + "(id,name)"
        }
        if (isPayeeInfos(property)) {
            return name + "(name,type,icon,bank,unionBank,cardNo,branch)"
        }
        if (isDataLink(property)) {
            return name + "(id,form(...))"
        }
    }
    if (isSpecification(property)) {
        return name + "(active,id,name,enName,nameSpell,code)"
    }
    if (isStaffs(property)) {
        return name + "(id,name,enName,nickName,code,email,cellphone,note)"
    }
    if (isPayeeInfos(property)) {
        return name + "(...)"
    }
    if (isExpenseLink(property)) {
        return name + "(specificationId(active,id,name,enName,nameSpell,code),ownerId(id,name,enName,code,email,cellphone,note),...)"
    }
    if (isPaymentAccount(property)) {
        return name + "(id,name)"
    }
    if (isExpenseLink(property)) {
        return name + "(id,name)"
    }
    if (isExpenseLinks(property)) {
        return name + "(specificationId(active,id,name,enName,nameSpell,code),ownerId(id,name,enName,code,email,cellphone,note),...)"
    }
    if (isEnum(property)) {
        return name + "(id,name)"
    }
    if (isDataLink(property)) {
        return name + "(id,form(...),...)"
    }
    if (isCity(property)) {
      return 'city'
    }
    return `${name}(...)`
  }

  const groupFields = () => {
    if (fields && fields.length) {
      return fields.reduce((calc, field) => {
        // 第一项固定位 "flowId"，最外层会固定拼接该字段
        const fieldArr = field.split('.');
        if (fieldArr.length === 1) {
          calc[fieldArr[0]] = [];
        } else if (fieldArr.length > 1) {
          if (Array.isArray(calc[fieldArr[0]])) {
            calc[fieldArr[0]].push(fieldArr[1]);
          } else {
            calc[fieldArr[0]] = [fieldArr[1]];
          }
        }
        return calc;
      }, {});
    }
    return {};
  }

  // 批量审批会需要金额字段用于统计展示
  const addFieldsOfMoneyForApproving = (result = []) => {
    const moneyFieldsForApprovingCalculating = ['expenseMoney', 'loanMoney', 'requisitionMoney', 'payMoney']
    return result.concat(moneyFieldsForApprovingCalculating).filter(Boolean).join(',')
  }

  // 只取form是因为计算出来的字符串是要拼接到form字段内部的
  const formFields = groupFields()['form']?.map((field) => {
    const propertyInfo = propertySet.find(item => item.name === field)
    const DATA_ENTITY_TYPE_NOT_SUPPORT_BY_SERVER = 'connect.BlockUI.'
    if (!propertyInfo || !propertyInfo.dataType || propertyInfo.dataType.entity?.startsWith(DATA_ENTITY_TYPE_NOT_SUPPORT_BY_SERVER)) {
      return ''
    }
    if (
      propertyInfo.dataType.type === 'ref' ||
      (propertyInfo.dataType.type === 'list' && propertyInfo.dataType.elemType?.type === 'ref')
    ) {
      return getRefPropertyQuerySelect(propertyInfo)
    } else {
      return field
    }
  })

  return addFieldsOfMoneyForApproving(formFields)
}

let _ifFetchDataBySettingOfTableColumn = null

export const getIfFetchDataBySettingOfTableColumn = () => {
  if (_ifFetchDataBySettingOfTableColumn === null) {
    _ifFetchDataBySettingOfTableColumn = app.require('@lib/featbit').getIfFetchDataBySettingOfTableColumn?.()
  }
  return _ifFetchDataBySettingOfTableColumn
}

/**
 * 根据column获取Form字段查询字段
*/
export const getSelectStringOfFormFields = (propertySet, params, sceneData) => {
  if (getIfFetchDataBySettingOfTableColumn()) {
    // 待审批包含flowId前缀，保持 parseSelectUsePropertySetOfScene 函数纯粹，这里去掉该前缀
    const columns = (params.columns?.length > 0 ? params.columns.map((filed) => filed.replace('flowId.', '')) : sceneData?.defaultColumns.map(item => item.dataField)) || []
    return parseSelectUsePropertySetOfScene(propertySet, params.options, columns)
  } else {
    return parseSelectUsePropertySet(propertySet, params.options)
  }
}

export const judgmentIsUseColumnSelect = (params, sceneData) => {
  if (getIfFetchDataBySettingOfTableColumn() && (params?.columns?.length || sceneData?.defaultColumns?.length)) {
    return true
  }
  return false
}

export const getRiskingSelectContent = (column) => {
  const RISKING_WARNING_COLUMN_SYMBOL = 'calcRiskWarning.'
  if (column.some(item => item.includes(RISKING_WARNING_COLUMN_SYMBOL))) {
    return 'calcRiskWarning(...),'
  }
  return ''
}

export const getCurrentColumn = (params, scene) => {
  return params.columns?.length > 0 ? params.columns : scene?.defaultColumns.map(item => item.dataField)
}
