import { app } from '@ekuaibao/whispered'
import React, { Component } from 'react'
import FilterBar from './filter-bar'
const Money = app.require('@elements/puppet/Money')
const LineClamp = app.require('@elements/data-grid-v2/LineClamp')
import { billTypeToMoneyKey } from '@ekuaibao/lib/lib/enums'
import * as viewUtil from '../view-util'
const DataGrid = app.require('@elements/data-grid-v2/SimpleTableWithLoader')
const StaffFilter = app.require('@elements/data-grid-v2/StaffFilter')
// 此文件待废弃，目前不确认影响范围

function tableColumns() {
  return [
    {
      title: i18n.get('审批时间'),
      dataIndex: 'updateTime',
      filterType: 'date',
      width: window.isNewHome ? void 0 : 150,
      sorter: false,
      render: function (text, line) {
        return viewUtil.tdActiconTime(text, line)
      },
    },
    {
      title: i18n.get('操作类型'),
      dataIndex: 'status',
      width: window.isNewHome ? void 0 : 140,
      sorter: false,
      render: function (text, line) {
        return viewUtil.tdStatus(line)
      },
      allowReordering: false,
      allowGrouping: false,
      allowHiding: false,
    },
    {
      title: i18n.get('单据名称'),
      width: window.isNewHome ? void 0 : 100,
      sorter: false,
      filterType: 'text',
      dataIndex: 'flowId.form.title',
      render: function (text, line) {
        return (
          <LineClamp line={3} lineHeight={22}>
            {viewUtil.tdText.call(this, text, line)}
          </LineClamp>
        )
      }.bind(this),
    },
    {
      title: i18n.get('提交人'),
      width: window.isNewHome ? void 0 : 100,
      sorter: false,
      // filterType: 'text',
      dataIndex: 'flowId.form.submitterId.id',
      filterType: 'custom',
      hiddenFilterAction: true,
      filterStyles: {
        wrapperStyle: {
          border: 'none',
          backgroundColor: 'transparent'
        },
        bodyStyle: {
          padding: '0'
        }
      },
      renderFilter: (props) => <StaffFilter {...props} />,
      render: viewUtil.tdPureSubmitterText.bind(this),
    },
    {
      title: i18n.get('单号'),
      width: window.isNewHome ? void 0 : 120,
      sorter: false,
      filterType: 'text',
      dataIndex: 'flowId.form.code',
    },
    {
      title: i18n.get('单据模板'),
      width: window.isNewHome ? void 0 : 100,
      sorter: false,
      filterType: 'text',
      dataIndex: 'flowId.form.specificationId.name',
    },
    {
      title: i18n.get('单据金额'),
      sorter: false,
      width: window.isNewHome ? void 0 : 120,
      render: (value, record) => {
        record = value || record
        if (record && record.flowId) {
          let type = record.flowId.formType
          let form = record.flowId.form
          const moneyKey = billTypeToMoneyKey[type]
          const money = form[moneyKey]
          return money === undefined ? (
            '-'
          ) : (
            <Money currencySize={10} valueSize={12} color="#333333" value={money} />
          )
        }
      },
    },
  ]
}

export default class OperationTable extends Component {
  columns = tableColumns.call(this)

  constructor() {
    super()
    this.state = {
      searchText: '',
      billType: '',
      filter: '',
    }
  }

  onConditionsChange = () => {
    const { bus } = this.props
    typeof bus.clearSelectedData === 'function' && bus.clearSelectedData()
  }

  handleFilterBarChange = (filter, billType) => {
    this.setState({ filter, billType })
    const { tabFilter, searchText } = this.state
    const { pagination } = this.props
    this.onConditionsChange()
    this.props.onTableChange &&
      this.props.onTableChange(
        { current: 1, pageSize: pagination.pageSize, filter: {...tabFilter, updateTime: [filter.sdate, filter.edate]} },
        'operation',
        filter,
        billType,
        searchText,
        true
      )
  }

  handleTableChange = pagination => {
    this.setState({ tabFilter: pagination.filter })
    const { searchText, filter, billType } = this.state
    this.props.onTableChange &&
      this.props.onTableChange(pagination, 'operation', filter, billType, searchText)
  }

  render() {
    return (
      <div style={{ height: `calc(100% - 56px)` }}>
        <FilterBar
          style={{ marginBottom: '10px' }}
          sdate={this.props.sdate}
          edate={this.props.edate}
          billType={this.props.billType}
          onChange={this.handleFilterBarChange}
          queryType={this.props.queryType}
          onSearch={this.handleSearch.bind(this)}
          searchInputPlaceholder={i18n.get('搜索标题、单号或提交人')}
        />
        {this.renderTable()}
      </div>
    )
  }
  handleSearch(e) {
    const { pagination } = this.props
    this.setState({ searchText: e })
    this.onConditionsChange()
    this.props.onTableChange &&
      this.props.onTableChange({ current: 1, pageSize: pagination.pageSize }, 'operation', void 0, 'all', e, true)
  }
  renderTable() {
    return (
      <div style={{ height: '100%' }}>
        <DataGrid
          {...this.props}
          columns={this.columns}
          dataSource={this.props.dataRecords.bills}
          current={this.props.pagination.current}
          pageSize={this.props.pagination.pageSize}
          total={this.props.pagination.total}
          onPageChange={this.handleTableChange}
          isMultiSelect={true}
          groupPanelVisible={false}
          sorting={{ mode: 'single' }}
        />
      </div>
    )
    
  }
}
