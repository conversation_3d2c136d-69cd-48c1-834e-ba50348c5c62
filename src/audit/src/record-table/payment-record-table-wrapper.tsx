import React from 'react'
import * as DataGrid from '@ekuaibao/datagrid'
import { Column } from '@ekuaibao/datagrid/esm/types/column'
import { Data } from '@ekuaibao/datagrid/esm/types/dataSource'
import { Provider } from '@ekuaibao/datagrid/esm/State'
import { Language } from '@ekuaibao/datagrid/esm/i18n/Language'
import styles from './table.module.less'
import PaymentItemsTable from '../elements/PaymentItemsTable'
import { PageMode, PaginationConfig } from '@ekuaibao/datagrid/esm/types/pagination'
import { Map } from '@ekuaibao/datagrid/esm/types/utils'
import classNames from 'classnames'
import { EnhanceConnect } from '@ekuaibao/store'
import { i18nText } from '../elements/AdjustPaymentPlan/components/DoContainer/lang'

export interface PaymentRecordTableWrapperProps {
  dataSource: Data[]
  columns: Column[]
  dynamicChannelMap: any
  getInstance: (instance: any) => void
  onRowClick: (data: Data) => void
  currentPage: number
  pageSize: number
  total: number
  onTableChange: (
    pagination: {
      current: number
      pageSize: number
      total: number
      pageMode: PageMode
    },
    filter: Map<any>,
    sorter: {
      column: Column
      columnKey: string
      field: string
      order: 'ascend' | 'descend'
    },
    isSearch: boolean,
    fresh: boolean,
  ) => void
  isNeedHeight: boolean
  pageMode: PageMode
  rowKey: string
  loadingError?: boolean
}
export interface PaymentRecordTableWrapperState {
  sorters: Map<'ascend' | 'descend'>
  filters: Map<any>
}

const scrolling = {
  mode: 'virtual' as 'virtual',
}

@EnhanceConnect((state: any) => ({
  dynamicChannelMap: state['@audit'].dynamicChannelMap,
}))
export class PaymentRecordTableWrapper extends React.PureComponent<
  PaymentRecordTableWrapperProps,
  PaymentRecordTableWrapperState
> {
  state: PaymentRecordTableWrapperState = {
    sorters: {},
    filters: {},
  }
  private loaded = true

  componentDidUpdate(prevProps: PaymentRecordTableWrapperProps) {
    if (prevProps.dataSource !== this.props.dataSource) {
      this.loaded = true
    }
  }
  render() {
    const { dataSource, columns, getInstance, total = 0, currentPage, pageSize, rowKey, loadingError } = this.props
    const { filters, sorters } = this.state
    return (
      <div className={classNames(styles.container)}>
        <Provider>
          <Language texts={i18nText} />

          <div className={styles.body}>
            <DataGrid.TableWrapper
              rowKey={rowKey || 'id'}
              className={`${styles.tableWrapper} ${loadingError ? styles.tableWrapperError : ''}`}
              dataSource={dataSource}
              columns={columns}
              allowColumnReordering
              allowColumnResizing
              getInstance={getInstance}
              isMultiSelect={false}
              isSingleSelect={false}
              filters={filters}
              sorters={sorters}
              DetailTemplate={rowKey ? null : PaymentItemsTable}
              onRowClick={this.handleRowClick}
              onSorterChange={this.handleSorterChange}
              onFilterChange={this.handleFilterChange}
              onReachBottom={this.handleReachBottom}
              scrolling={scrolling}
              loadPanel={{
                enabled: true,
                showPane: false,
                text: i18n.get('加载中…'),
              }}
            />
          </div>
          <div className={styles.footer}>
            <DataGrid.Pagination
              totalLength={total}
              disabledScroll={true}
              pagination={{
                current: currentPage,
                size: pageSize,
              }}
              pageMode="pagination"
              onChange={this.handlePageChange}
            />
          </div>
        </Provider>
      </div>
    )
  }

  private handlePageChange = (pagination: PaginationConfig, pageMode: PageMode = 'pagination') => {
    if (pagination.current !== this.props.currentPage || pagination.size !== this.props.pageSize) {
      this.props.onTableChange(
        {
          current: pagination.current,
          pageSize: pagination.size,
          total: this.props.total,
          pageMode,
        },
        this.state.filters,
        this.formatSorters(this.state.sorters),
        false,
        false,
      )
    }
  }

  private handleRowClick = (data: Data) => {
    const { onRowClick } = this.props
    onRowClick(data)
  }

  private handleSorterChange = (sorters: Map<'ascend' | 'descend'>) => {
    const { pageMode } = this.props
    this.setState({
      sorters,
    })
    this.props.onTableChange(
      {
        current: pageMode === 'scroll' ? 1 : this.props.currentPage,
        pageSize: this.props.pageSize,
        total: this.props.total,
        pageMode: pageMode,
      },
      this.state.filters,
      this.formatSorters(sorters),
      false,
      true,
    )
  }

  private handleFilterChange = (filters: Map<any>) => {
    const { dynamicChannelMap = {} } = this.props
    const channelKey = 'paymentChannel'
    const active = dynamicChannelMap['CHANPAYV2'] && dynamicChannelMap['CHANPAYV2'].active
    if (filters[channelKey] && active) {
      //兼容新旧银企联数据查询
      if (filters[channelKey].includes('CHANPAYV2') && !filters[channelKey].includes('CHANPAY')) {
        filters[channelKey].push('CHANPAY')
      } else if (filters[channelKey].includes('CHANPAY')) {
        const index = filters[channelKey].indexOf('CHANPAY')
        filters[channelKey].splice(index, 1)
      }
    }
    this.setState({ filters })
    this.props.onTableChange(
      {
        current: 1,
        pageSize: this.props.pageSize,
        total: this.props.total,
        pageMode: this.props.pageMode,
      },
      filters,
      this.formatSorters(this.state.sorters),
      false,
      true,
    )
  }

  private handleReachBottom = () => {
    if (!this.loaded) {
      return
    }
    if (this.props.pageMode === 'pagination' || this.props.dataSource.length >= this.props.total) {
      return
    }
    this.props.onTableChange(
      {
        current: this.props.currentPage + 1,
        pageSize: this.props.pageSize,
        total: this.props.total,
        pageMode: this.props.pageMode,
      },
      this.state.filters,
      this.formatSorters(this.state.sorters),
      false,
      false,
    )
    this.loaded = false
  }

  private formatSorters = (sorters: Map<'ascend' | 'descend'>) => {
    const keys = Object.keys(sorters)
    return keys.reduce<{
      column: Column
      columnKey: string
      field: string
      order: 'ascend' | 'descend'
    }>((pre, cur) => {
      return {
        column: this.props.columns.find(column => column.dataIndex === cur),
        columnKey: cur,
        field: cur,
        order: sorters[cur],
      }
    }, {} as any)
  }
}

export default PaymentRecordTableWrapper
