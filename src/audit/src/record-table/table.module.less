.container {
  display: flex;
  width: 100%;
  flex: 1;
  flex-direction: column;
  margin-top: 8px;
  overflow: hidden;
}

.containerHeight {
  height: calc(100vh - 200px);
}

.payPlanContainerHeight {
  height: calc(100vh - 220px);
}

.body {
  flex: auto;
  overflow: hidden;
  height: 100%;
  position: relative;
  max-height: 960px;
  padding: 0 16px;
}

.footer {
  padding: 12px 24px;
  display: flex;
  align-items: center;
  height: 56px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 4px 24px 0px rgba(29, 43, 61, 0.2), 0px 1px 0px 0px rgba(29, 43, 61, 0.15);
}

.tableWrapper {
  :global {
    .dx-datagrid-rowsview.dx-scrollable {
      overflow: auto;
    }
  }
  border-top: 1px solid #ddd;
  height: calc(100% - 16px);
}


.tableWrapperError {
  :global {
    .dx-datagrid-nodata {
      display: none !important;
    }
  }
}
