import { app as api } from '@ekuaibao/whispered'
import React, { Component } from 'react'

import { Select, Space, Input, DatePicker, Button } from '@hose/eui'
import { EnhanceConnect } from '@ekuaibao/store'
import moment from 'moment'
const { Search } = Input
const RangePicker = DatePicker.RangePicker
const Option = Select.Option
import { getEnableRange } from '../util/Utils'
const CustomSearch = api.require('@elements/data-grid-v2/CustomSearch')

function label(type) {
  switch (type) {
    case 'record':
      return i18n.get('提交日期')
    case 'operation':
      return i18n.get('审批日期')
    case 'payment':
      return i18n.get('支付完成时间')
    case 'tobepaid': //资金管理->银行账户交易->待支付
      return i18n.get('生成日期')
    case 'paymentFailed': //资金管理->银行账户交易->支付失败
      return i18n.get('支付发起时间')
    case 'auditPaid': //待办->单据支付->已支付
      return i18n.get('支付完成时间')
  }
}

@EnhanceConnect(state => {
  return {
    BasicDocument: state['@common'].powers.BasicDocument,
  }
})
export default class FilterBar extends Component {
  constructor() {
    super()
    this.state = {
      startDay: getEnableRange().startDay,
      endDay: getEnableRange().endDay,
      paymentPickerOpen: false,
      normalPickerOpen: false,
    }
  }

  handleSelectChange = billType => {
    let { sdate, edate } = this.props
    this.props.onChange && this.props.onChange({ sdate, edate }, billType)
  }

  handlePaymentSelectChange = state => {
    this.props.onPaymentSelectChange && this.props.onPaymentSelectChange(state)
  }

  handleDateChange = (value, dateString) => {
    if (dateString[0] && dateString[1]) {
      let sdate = dateString[0] + ' 00:00:00'
      let edate = dateString[1] + ' 23:59:59'

      // 直接执行筛选
      this.props.onChange &&
        this.props.onChange(
          {
            sdate,
            edate,
          },
          this.props.queryType !== 'payment' && this.props.billType,
        )

      // 关闭面板
      this.setState({
        paymentPickerOpen: false,
        normalPickerOpen: false,
      })
    }
  }

  handleSearch = value => {
    this.props.onSearch && this.props.onSearch(value)
  }

  handleCustomSearch = value => {
    this.props.onNewSearch?.(value)
  }

  onCalendarChange = dates => {
    if (dates?.length === 1) {
      const { startDay, endDay } = getEnableRange(dates[0])
      this.setState({
        startDay,
        endDay,
      })
    }
  }
  onOpenChange = status => {
    if (status) {
      const { startDay, endDay } = getEnableRange()
      this.setState({
        startDay,
        endDay,
      })
    }
  }

  // payment类型的日期选择器开关控制
  onPaymentOpenChange = status => {
    this.setState({ paymentPickerOpen: status })
    this.onOpenChange(status)
  }

  // 普通类型的日期选择器开关控制
  onNormalOpenChange = status => {
    this.setState({ normalPickerOpen: status })
    this.onOpenChange(status)
  }

  // 处理快捷键选择
  handleQuickSelect = type => {
    const now = moment()
    let sdate, edate

    switch (type) {
      case 'today':
        sdate = now.format('YYYY-MM-DD') + ' 00:00:00'
        edate = now.format('YYYY-MM-DD') + ' 23:59:59'
        break
      case 'thisMonth':
        // 本月1号到今天
        sdate = now.clone().startOf('month').format('YYYY-MM-DD') + ' 00:00:00'
        edate = now.format('YYYY-MM-DD') + ' 23:59:59'
        break
      case 'thisYear':
        // 今年1月1号到今天
        sdate = now.clone().startOf('year').format('YYYY-MM-DD') + ' 00:00:00'
        edate = now.format('YYYY-MM-DD') + ' 23:59:59'
        break
      default:
        return
    }

    // 直接执行筛选
    this.props.onChange &&
      this.props.onChange({ sdate, edate }, this.props.queryType !== 'payment' && this.props.billType)

    // 关闭面板
    this.setState({
      paymentPickerOpen: false,
      normalPickerOpen: false,
    })
  }

  // 渲染日期快捷键footer
  renderDateFooter = () => {
    const { shouldDisabledDate } = this.props
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
        }}
      >
        <div>
          <Button
            category="text"
            size="mini"
            theme="highlight"
            onClick={() => this.handleQuickSelect('today')}
            style={{ marginRight: 8 }}
          >
            {i18n.get('今天')}
          </Button>
          <Button
            category="text"
            size="mini"
            theme="highlight"
            onClick={() => this.handleQuickSelect('thisMonth')}
            style={{ marginRight: 8 }}
          >
            {i18n.get('本月')}
          </Button>
          <Button category="text" theme="highlight" size="mini" onClick={() => this.handleQuickSelect('thisYear')}>
            {i18n.get('今年')}
          </Button>
        </div>
        {shouldDisabledDate && (
          <div style={{ fontSize: 12, color: 'var(--eui-text-title)' }}>
            {i18n.get('筛选范围最多可选择2年，如需更多请联系企业管理员')}
          </div>
        )}
      </div>
    )
  }
  renderSelecct() {
    const type = this.props.queryType
    const { BasicDocument, payResultLabel, showBillType = true, shouldDisabledDate = false } = this.props

    // 使用props中的日期
    const dateValue = [moment(this.props.sdate), moment(this.props.edate)]

    const disabledDate = current => {
      const { startDay, endDay } = this.state
      if (shouldDisabledDate) {
        return current.valueOf() > endDay || current.valueOf() < startDay
      } else {
        return false
      }
    }

    if (type === 'payment') {
      return (
        <div style={{ flex: '1', display: 'flex', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 15, marginRight: 20 }}>
            <div className="mr-10" style={{ display: 'inline-block' }}>
              {label(type)}
            </div>
            <RangePicker
              open={this.state.paymentPickerOpen}
              onOpenChange={this.onPaymentOpenChange}
              onCalendarChange={this.onCalendarChange}
              renderExtraFooter={this.renderDateFooter}
              disabledDate={disabledDate}
              allowClear={false}
              value={dateValue}
              onChange={this.handleDateChange.bind(this)}
            />
          </div>
          <div style={{ display: 'inline-block', marginBottom: 15 }}>
            <span className="mr-10">{payResultLabel ? payResultLabel : i18n.get('支付状态')}</span>
            <Select value={this.props.payState} style={{ width: 200 }} onChange={this.handlePaymentSelectChange}>
              <Option value="all">{i18n.get('全部')}</Option>
              <Option value="SUCCESS">{i18n.get('支付成功')}</Option>
              <Option value="FAILURE">{i18n.get('支付失败')}</Option>
            </Select>
          </div>
        </div>
      )
    }

    return (
      <>
        <div className="mr-10" style={{ lineHeight: '32px' }}>
          {type === 'paid' ? ( //已支付页面
            <Space>
              <span>{i18n.get('按')}</span>
              <Select
                onChange={this.props.handleSearchKeyChange}
                defaultValue={this.props.searchKey}
                options={this.props.keyOptions}
                style={{ width: 200 }}
              />
              <span>{i18n.get('筛选')}</span>
            </Space>
          ) : (
            label(type)
          )}
        </div>
        <RangePicker
          open={this.state.normalPickerOpen}
          onOpenChange={this.onNormalOpenChange}
          onCalendarChange={this.onCalendarChange}
          disabledDate={disabledDate}
          style={{ marginRight: 10 }}
          allowClear={false}
          renderExtraFooter={this.renderDateFooter}
          value={dateValue}
          onChange={this.handleDateChange.bind(this)}
        />
        {showBillType ? (
          <>
            <span className="mr-10" style={{ lineHeight: '32px' }}>
              {i18n.get('单据类型')}
            </span>
            <Select value={this.props.billType} style={{ width: 200 }} onChange={this.handleSelectChange}>
              <Option value="all">{i18n.get('全部')}</Option>
              <Option value="expense">{i18n.get('报销单')}</Option>
              <Option value="loan">{i18n.get('借款单')}</Option>
              <Option value="requisition">{i18n.get('申请单')}</Option>
              {BasicDocument && <Option value="payment">{i18n.get('付款单')}</Option>}
              {BasicDocument && <Option value="custom">{i18n.get('基础单据')}</Option>}
            </Select>
          </>
        ) : null}
      </>
    )
  }

  render() {
    const { style: prosStyle = {}, showSearch = true, newSearch, searchInputPlaceholder, searchOptions } = this.props
    const style = { paddingBottom: 0, alignItems: 'flex-start', ...prosStyle }
    const type = this.props.queryType
    return (
      <div className="filter-bar horizontal" style={style}>
        {this.renderSelecct()}
        {showSearch ? (
          newSearch ? (
            <div style={{ minWidth: 400, flexShrink: 0, marginLeft: 'auto' }}>
              <CustomSearch options={searchOptions} onSearch={this.handleCustomSearch} />
            </div>
          ) : (
            <Search
              allowClear={true}
              placeholder={searchInputPlaceholder ? searchInputPlaceholder : i18n.get('搜索单号或批次号')}
              style={{ width: 280, flexShrink: 0, marginLeft: 'auto' }}
              onSearch={this.handleSearch}
            />
          )
        ) : null}
      </div>
    )
  }
}
