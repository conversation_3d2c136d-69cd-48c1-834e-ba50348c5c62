import { app } from '@ekuaibao/whispered'
import React, { Component } from 'react'
import FilterBar from './filter-bar'
import { EnhanceConnect } from '@ekuaibao/store'
const { filterChannels } = app.require('@components/utils/fnFilterPaymentChannel')

import * as viewUtil from '../view-util'
import { createActionColumn4Paid } from '../util/columnsAndSwitcherUtil'
const withLoader = app.require('@elements/data-grid-v2/withLoader')

const PaymentRecordTableWrapper = withLoader(() => import('./payment-record-table-wrapper'))

function tableColumns(channelList) {
  const channels = filterChannels(channelList).map(item => {
    item.text = item.label
    return item
  })
  const action = createActionColumn4Paid('RECORD')
  return [
    {
      title: i18n.get('支付日期'),
      dataIndex: 'finishTime',
      width: window.isNewHome ? 136 : 120,
      sorter: true,
      render: viewUtil.tdDateTime.bind(this),
    },
    {
      title: i18n.get('单号'),
      dataIndex: 'code',
      width: window.isNewHome ? 136 : 120,
      sorter: true,
      render: (code, line) => {
        return line.state === 'FAILURE' ? viewUtil.tdToolTipFailureText(code, line.respMsg, line.channelTradeNo) : code
      },
    },
    {
      title: i18n.get('批次号'),
      width: window.isNewHome ? 198 : 150,
      dataIndex: 'channelTradeNo',
      sorter: true,
      render: viewUtil.tdText.bind(this),
    },
    {
      title: i18n.get('收款信息'),
      width: window.isNewHome ? void 0 : 130,
      dataIndex: 'paymentDigest.payee',
      render: viewUtil.tdAccountInfo.bind(this),
      sorter: false,
    },
    {
      title: i18n.get('付款账户'),
      width: window.isNewHome ? void 0 : 100,
      dataIndex: 'accountCompany.name',
      render: viewUtil.tdText.bind(this),
      sorter: false,
    },
    {
      title: i18n.get('支付方式'),
      width: window.isNewHome ? void 0 : 120,
      dataIndex: 'paymentChannel',
      render: viewUtil.tdChannel.bind(this),
      filters: channels,
      filterType: 'list',
      sorter: false,
      lookup: {
        dataSource: channels,
        displayExpr: 'label',
        valueExpr: 'value',
      },
    },
    {
      title: i18n.get('支付金额({__k0})', { __k0: window.CURRENCY_SYMBOL }),
      width: window.isNewHome ? void 0 : 120,
      sorter: true,
      dataIndex: 'paidAmount',
      render: viewUtil.tdAmount.bind(this),
    },
    action,
  ]
}

@EnhanceConnect(state => ({
  dynamicChannelMap: state['@audit'].dynamicChannelMap,
}))
export default class PaymentRecordTable extends Component {
  constructor(props) {
    super(props)
    this.state = {
      expandedRowKeys: [],
      tableColumns: props.columns ? props.columns : tableColumns.call(this, props.channelList),
      pageMode: 'pagination',
      dataSource: props.dataSource,
      controlDate: false,
    }
    this.filters = {}
    this.sorters = {}
  }

  componentDidMount() {
    app.invokeService('@audit:get:ppayment:plans:control').then(res => {
      this.setState({ controlDate: res.value })
    })
    if (window.isNewHome) {
      setTimeout(() => {
        this.instance && this.instance.resize()
      }, 500)
    }
  }

  componentDidUpdate(prevProps) {
    const { columns, channelList, dataSource, paymentRecords } = this.props
    const condition =
      prevProps.channelList !== channelList ||
      (prevProps.columns && prevProps.columns.length !== columns.length) ||
      prevProps.dataSource !== dataSource
    if (condition) {
      this.setState({
        tableColumns: columns ? columns : tableColumns.call(this, this.props.channelList),
        dataSource: dataSource ? dataSource : paymentRecords && paymentRecords.items,
      })
    }
  }

  handleFilterBarChange = date => {
    this.handleTableChange(
      {
        current: 1,
        pageSize: this.state.pageMode === 'scroll' ? 20 : this.props.pagination.pageSize,
        pageMode: this.state.pageMode,
      },
      { ...this.filters, date },
      this.sorters,
      false,
      true,
    )
  }

  handlePaymentSelectChange = state => {
    const { filterKeyName } = this.props
    const params = { ...this.filters, state }
    if (filterKeyName) {
      params[filterKeyName] = state
      delete params.state
    }
    this.handleTableChange(
      {
        current: 1,
        pageSize: this.state.pageMode === 'scroll' ? 20 : this.props.pagination.pageSize,
        pageMode: this.state.pageMode,
      },
      params,
      this.sorters,
      false,
      true,
    )
  }

  handleSearch = value => {
    this.handleTableChange(
      {
        current: 1,
        pageSize: this.state.pageMode === 'scroll' ? 20 : this.props.pagination.pageSize,
        pageMode: this.state.pageMode,
      },
      { ...this.filters, custom: value },
      this.sorters,
      true,
      true,
    )
  }

  handleTableChange = (pagination, filter, sorter, isSearch = false, fresh = false) => {
    this.setState({ pageMode: pagination.pageMode || 'pagination' })
    this.filters = filter
    this.sorters = sorter
    this.props.onTableChange && this.props.onTableChange(pagination, filter, sorter, isSearch, fresh)
  }

  handleRowClick = payments => {
    this.props.onRowClick && this.props.onRowClick(payments)
  }

  getInstance = instance => {
    this.instance = instance
    this.props?.getInstance && this.props.getInstance(instance)
  }

  render() {
    const { isNeedHeight, paymentRecords = { items: [] }, rowKey, searchInputPlaceholder, payResultLabel } = this.props
    const { dataSource } = this.state
    return (
      <>
        <FilterBar
          shouldDisabledDate={this.state.controlDate}
          onChange={this.handleFilterBarChange}
          onSearch={this.handleSearch}
          sdate={this.props.sdate}
          edate={this.props.edate}
          payState={this.props.payState}
          searchText={this.props.searchText}
          payResultLabel={payResultLabel}
          searchInputPlaceholder={searchInputPlaceholder}
          onPaymentSelectChange={this.handlePaymentSelectChange}
          queryType={this.props.queryType}
        />
        <PaymentRecordTableWrapper
          rowKey={rowKey}
          isNeedHeight={isNeedHeight}
          columns={this.state.tableColumns}
          dataSource={dataSource || paymentRecords.items}
          currentPage={this.props.pagination.current}
          pageSize={this.props.pagination.pageSize}
          total={this.props.pagination.total}
          getInstance={this.getInstance}
          onRowClick={this.handleRowClick}
          onTableChange={this.handleTableChange}
          pageMode={this.state.pageMode}
          loadingError={this.props.loadingError}
        />
      </>
    )
  }
}
