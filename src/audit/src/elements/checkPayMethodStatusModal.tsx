import style from "./checkPayMethodStatusBar.module.less";
import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Button } from '@hose/eui'
import { LoadingFill, SuccessFill, FailedFill } from './checkPayMethodStatusBar.tsx'
import { Resource, Fetch } from '@ekuaibao/fetch'
import { get } from 'lodash'

const PayMethodCheckResource = new Resource('/api/pay/v1/plugin/processor')
const EKBIcon = app.require('@elements/ekbIcon')

type Props = {
  layer?: any
  isValidating: boolean
  available: boolean
  error?: string
  showModal: boolean
  onReCheck?: () => void
  channel: string
}

type State = {
  payMethodState: any
  count: number
}

@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer',
})
export default class CheckPayMethodStatusModal extends PureComponent<Props, State> {
  private autoClose: any
  private countdown: any
  constructor(props: any) {
    super(props)
    this.state = {
      // 检测支付链路状态
      payMethodState: {
        isValidating: false,
        available: true,
        error: undefined,
      },
      count: 5,
    }
    this.autoClose = null
    this.countdown = null
  }

  componentDidMount() {
    this.checkPayMethodAvailable()
  }

  componentWillUnmount() {
    this.autoClose && clearTimeout(this.autoClose)
    this.countdown && clearInterval(this.countdown)
  }

  getResult = () => {
    const { payMethodState } = this.state
    const { isValidating, available, error } = payMethodState
    let result = !isValidating && available && !error
    return { result }
  }

  handleAutoClose = () => {
    this.autoClose = setTimeout(() => {
      this.handleModalClose()
    }, 5000)
    this.countdown = setInterval(() => {
      const { count } = this.state
      this.setState({
        count: count - 1,
      })
    }, 1000)
  }

  handleModalClose() {
    this.props.layer.emitOk()
  }

  fetchPayMethodAvailableStatus = async (channel: string) => {
    const params = {
      action: 'api/cbs/netCheck',
      body: {
        corpId: Fetch.ekbCorpId,
      },
    }
    const result = await PayMethodCheckResource.POST('/$channel', params, { channel }).catch(e => e)
    const status = get(result, 'items.success', false)
    const errorMsg = get(result, 'items.message', null)
    return { available: status, error: status ? null : errorMsg }
  }

  checkPayMethodAvailable = async () => {
    const { channel } = this.props
    const { payMethodState } = this.state
    this.setState({
      payMethodState: {
        ...payMethodState,
        isValidating: true,
        available: false,
        error: undefined,
      },
    })
    const result = await this.fetchPayMethodAvailableStatus(channel)
    // 检测完毕
    const { available, error } = result
    const newPayMethodState = {
      ...this.state.payMethodState,
      isValidating: false,
      available: available,
      error: error,
    }
    this.setState({ payMethodState: newPayMethodState }, () => {
      if (available) this.handleAutoClose()
    })
  }

  render() {
    const { payMethodState, count } = this.state
    const { isValidating, available, error } = payMethodState
    return (
      <div className={style.check_pay_method_status_Modal}>
        <div className="modal-header">
          <div className="select-payment-modal-header-title"></div>
          <EKBIcon
            className="cross-icon"
            name="#EDico-close-default"
            onClick={this.handleModalClose.bind(this)}
            data-testid="pay-checkPayMethodStatusModal-close-icon"
          />
        </div>
        <div className="modal-content">
          {isValidating && (
            <span className="loading">
              <LoadingFill />
              <span className="tip">{i18n.get('支付通道检测中，请稍后')}</span>
            </span>
          )}
          {!isValidating && available && !error && (
            <span>
              <SuccessFill />
              <span className="tip">{i18n.get('支付通道通畅，可以发起支付')}</span>
              <span className="action">
                <Button onClick={this.handleModalClose.bind(this)} data-testid="pay-checkPayMethodStatusModal-known-btn">{i18n.get('我知道了') + `（${count}）`}</Button>
              </span>
            </span>
          )}
          {!isValidating && (!available || error) && (
            <div>
              <FailedFill />
              <span className="tip">{i18n.get('支付通路连接异常，请联系管理员')}</span>
              {error && <span className="tip error">{error}</span>}
              <span className="action">
                <Button onClick={this.checkPayMethodAvailable} data-testid="pay-checkPayMethodStatusModal-retry-btn">{i18n.get('重试')}</Button>
              </span>
            </div>
          )}
        </div>
      </div>
    )
  }
}
