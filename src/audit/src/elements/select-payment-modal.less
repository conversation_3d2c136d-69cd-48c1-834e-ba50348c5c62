@import '~@ekuaibao/web-theme-variables/styles/default';
@import '~@ekuaibao/eui-styles/less/token.less';

.select-payment-modal {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin: 0 -16px;

  .select-payment-modal-footer {
    position: relative;
    flex-shrink: 0;
    padding: 16px;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
  }

  .content {
    flex: 1;
    display: block;
    padding: 0 16px;
    overflow: auto;
    max-height: calc(100vh - 200px - 56px - 64px);

    .amount-conten {
      .amount-header {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        justify-content: space-between;
        border-radius: 6px;
        background: var(--eui-bg-float-overlay, #f7f8fa);

        .left {
          display: flex;
          align-items: center;
          color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
          font: var(--eui-font-body-r1);

          .count {
            color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
            font: var(--eui-font-body-b1);
          }

          .info-icon {
            font-size: 16px;
            margin-left: 4px;
            color: var(--eui-icon-n2);
          }
        }
      }

      .wrap {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin: 16px 0;

        .left {
          color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
          font-size: 14px;
          font-style: normal;
          font-weight: 500;
          line-height: 20px;
        }

        .right {
          display: flex;
          flex-direction: column;
          align-items: flex-end;

          .amount-total {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;

            span {
              color: var(--eui-primary-pri-500, #2555ff);
              font-size: 18px;
              font-weight: 700;
              line-height: 26px;
              margin-left: 4px;
              font-family: 'DIN Alternate';
            }
          }

          .amount-convert {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 18px;

            span {
              font-size: 12px;
              font-weight: 700;
              line-height: 18px;
              margin-left: 4px;
            }
          }

          .zh-money {
            color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 18px;
          }
        }
      }
    }

    .line-wrap {
      height: 8px;
      background: var(--eui-bg-base, #f2f3f5);
      margin: 0 -16px;
    }

    .approve-signature {
      margin: 0 -16px;
    }

    .amount-wrap {
      padding: @space-6 0;
      display: flex;
      align-items: center;
      flex-direction: column;
      box-shadow: 0px 1px 8px 0px @color-line-2;
      border-radius: @radius-3;
      border: 0px solid @color-line-2;
      width: 100%;

      .amount {
        font-weight: bold;
        font-family: DINAlternate-Bold, DINAlternate, sans-serif;
        font-size: 32px;
        line-height: 1;
        color: var(--eui-text-title);
        margin-bottom: @space-4;
      }

      .count-wrap {
        .font-size-3;
        .font-weight-3;
        height: 24px;
        color: @color-black-3;

        .count {
          margin: 0 @space-2;
          color: var(--eui-text-title);
          font-weight: bold;
          font-family: DINAlternate-Bold, DINAlternate, sans-serif;
        }
      }
    }

    .search-wrap {
      width: 100%;
      margin-top: @space-9;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .search {
        flex: none;
        width: 280px;

        .ant-input {
          background-color: @color-bg-2;
          height: 32px;
          border: none;
          border-radius: @radius-2;
        }

        .ant-input-search-icon {
          color: @color-black-2;
        }
      }

      .item-setting {
        padding: @space-2;

        &:hover {
          background: @color-bg-1;
          border-radius: @radius-2;
        }
      }
    }

    .account-wrap {
      width: 100%;
      max-height: 360px;
      overflow: auto;
      margin-top: @space-6;

      .account-header {
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: @space-4;

        .account-header-title {
          color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
          font: var(--eui-font-body-b1);
        }

        .account-header-btn {
          height: 100%;
          display: inline-flex;
          align-items: center;
          cursor: pointer;
          color: @color-brand-2;
          .font-size-2;
        }
      }
    }

    .chinese-money {
      font-weight: 400;
      color: rgba(29, 43, 61, 0.5);
      margin-bottom: @space-4;
    }

    .select-payment-form {
      width: 100%;
      overflow-y: hidden;
      overflow-x: hidden;
      margin-top: 16px;

      .ant-form-item {
        margin: 0 0 16px;
        .ant-form-item-label > label {
          position: relative;
          display: inline-flex;
          align-items: flex-start;
          max-width: 100%;
          font: var(--eui-font-body-r1);
          color: var(--eui-text-title);
          &::after {
            display: none;
          }
        }
        .ant-form-item-label {
          font: var(--eui-font-body-r1);
          color: var(--eui-text-title);
          margin-bottom: 8px;
          .ant-form-item-required {
            &::after {
              display: inline-block;
              margin-left: 2px;
              color: var(--eui-function-danger-500);
              font: var(--eui-font-body-b1);
              content: '*';
            }
            &::before {
              display: none;
            }
          }
        }
        .has-error {
          .eui-input-affix-wrapper,
          .eui-select-selector,
          .eui-picker,
          .eui-input,
          .eui-input-number,
          .payee-info-wrapper,
          .receiving-currency-content,
          .payee-info-input {
            border-color: var(--eui-function-danger-500) !important;
          }
        }
        .has-error .ant-form-explain,
        .has-error .ant-form-split {
          margin-top: 4px;
          font: var(--eui-font-body-r1);
          color: var(--eui-function-danger-500);
        }
      }

      .select-payment-payRemark {
        margin-bottom: @space-4;
      }

      .select-payment-checkbox {
        margin-bottom: @space-7;
        font-size: 14px !important;
      }
    }

    .warn-text {
      top: 32px;
      left: 0;
      color: #bfbfbf;

      .warn-text-reminder {
        color: var(--eui-text-title);
      }

      .warn-text-tip {
        color: var(--eui-text-placeholder);
      }
    }

    .remark {
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      background-color: @input-bg;
      margin-bottom: 15px;

      .title {
        margin-right: 10px;
      }

      .method-line {
        width: 320px;
        margin-top: 5px;

        .ant-select-selection--single {
          height: 32px;

          .ant-select-selection__rendered {
            height: 32px;
          }
        }
      }
    }

    .info {
      font-size: 12px;
      color: #9c9c9c;
      margin-left: 10px;
    }
  }
}

.signature-container {
  display: flex;
  flex: 1;
  flex-direction: row;

  .signature-title {
    margin-top: 5px;
    margin-right: 8px;
    //width: 90px;
    height: 14px;
    font-size: 14px;
    color: #54595b;
  }
}
