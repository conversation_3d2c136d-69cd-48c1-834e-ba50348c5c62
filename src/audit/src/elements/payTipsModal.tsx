/*
 * @Author: zhang<PERSON>
 * @Date: 2021-04-27 10:42:21
 */
import React, { Component } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import {OutlinedTipsWarning} from "@hose/eui-icons"
import { Button, Checkbox, Tooltip } from '@hose/eui'
import { app } from '@ekuaibao/whispered'
import styles from './payTipsModal.module.less'
//@ts-ignore
import PAY_INFO_FILL from '../images/pay-info-fill.svg'
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')

interface Props {
  layer?: any
  failedFlows: any[]
  flowIds: string[]
}

interface State {
  checked: boolean
  failedFlows: any[]
}

@EnhanceModal({
  title: i18n.get('提示'),
  footer: [],
  className: styles['pay-tips-wrapper'],
})
//@ts-ignore
@EnhanceFormCreate()
export default class PayTipsModal extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      checked: false,
      failedFlows: props.failedFlows.map(v => v.code),
    }
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleOk = () => {
    const { checked } = this.state
    this.props.layer.emitOk({ checked })
  }

  onChange = (e: any) => {
    const checked = e.target.checked
    this.setState({ checked })
  }

  render() {
    const { failedFlows, checked } = this.state
    return (
      <div className={styles['pay-tips-modal-wrapper']}>
        <div className="pay-tips-content">
          <div className="tips-content">
            <div className="pay-tips-header">
              <img src={PAY_INFO_FILL} alt="" />
            </div>
            <div className="pay-tips-description">
              {i18n.get('已选单据中含有手动确认失败的单据(0001，0002，0003)', {
                flowCode: failedFlows.slice(0, 3).join(',') + (failedFlows.length > 3 ? '' : ')'),
              })}
              {failedFlows.length > 3 && (
                <Tooltip placement="top" title={failedFlows.slice(3).join(',')}>
                  ,...)
                </Tooltip>
              )}
              {i18n.get('，为避免重复支付造成损失，请再次确认此部分单据已在第三方平台支付失败')}
            </div>
          </div>
          {(failedFlows.length !== 1 || this.props.flowIds.length !== 1) && (
            <>
              <Checkbox
                className="tips-check-box"
                checked={checked}
                onChange={this.onChange}
                data-testid="pay-payTipsModal-filter-checkbox">
                {i18n.get('过滤曾手动确认失败的单据')}
              </Checkbox>
              <div className="check-description">
                {i18n.get('勾选后，这些单据将被留在「待支付」')}
              </div>
              {checked && failedFlows.length === this.props.flowIds.length && (
                <div className="check-warning">
                  <OutlinedTipsWarning fontSize={16} style={{ color: '#faad14', marginRight: '8px' }}/>
                  {i18n.get('请注意：所有数据均已被过滤')}
                </div>
              )}
            </>
          )}
        </div>
        <div className={styles['modal-footer']}>
          <Button className="btn-ok" onClick={this.handleOk} data-testid="pay-payTipsModal-confirm-btn">
            {i18n.get('确定')}
          </Button>
          <Button category="secondary" onClick={this.handleCancel} data-testid="pay-payTipsModal-cancel-btn">
            {i18n.get('取消')}
          </Button>
        </div>
      </div>
    )
  }
}
