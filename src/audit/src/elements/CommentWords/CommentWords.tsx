import React, { PureComponent } from 'react'
import style from './CommentWords.module.less'
import { app as api } from '@ekuaibao/whispered'
// import { Tooltip } from '@hose/eui' // '@hose/eui'
import { Button, Popover, Tooltip } from '@hose/eui'
import CommentWordsNew from './CommentWordsNew'

import { OutlinedDirectionDown, OutlinedDirectionUp, OutlinedGeneralChatNews } from '@hose/eui-icons'
import { AttachmentIF } from '@ekuaibao/ekuaibao_types'
const AttachmentComponent = api.require<any>('@elements/attachment-component/AttachmentComponent')

const ENGLISHNUM = 20 // 保留最多字母个数
const CHINESENUM = 10 // 保留最多中文个数
interface CommentWordsInterface {
  commentArea?: string
  maxCount?: number //最多可添加条数
  handleSelectWords: Function
  type: string
  bus: any
  form: any
  specialStyle?: React.CSSProperties
  suffixesPath: 'APPROVE'
  invalidSuffixesConfig?: Record<string, number>
  canSelectDP?: boolean
  useClipboard?: boolean
  showAttachment?: boolean
  onUploading?: (uploading: boolean) => void
}

export default class CommentWords extends PureComponent<CommentWordsInterface> {
  state = {
    myWords: {},
    showPanel: false,
    showPanelBtn: false,
  }

  componentDidMount() {
    this.props.bus.on('comment:action', this.handleCommentAction)
  }
  componentWillMount() {
    this.getCommentWords()
  }
  componentWillUnmount() {
    this.props.bus.un('comment:action', this.handleCommentAction)
  }
  handleCommentPanel = () => {
    this.setState(
      {
        showPanel: !this.state.showPanel,
      },
      this.setElemHeight,
    )
  }

  getCommentWords = () => {
    api.invokeService('@audit:get:commentwords', { type: this.props.type }).then((value: any) => {
      this.setState({ myWords: value || {} }, this.setCommentHeight)
    })
  }

  setCommentCallback = (data: any) => {
    this.setState({ myWords: data }, () => {
      this.handleCommentAction()
    })
  }

  renderItem = (item: any, index: number) => {
    return (
      <div
        className="comment-words-items"
        style={{ display: 'inline-block' }}
        key={index}
        onClick={() => {
          this.props.handleSelectWords(item.words)
        }}
      >
        <Words key={index} line={item} i={index} getText={this.getText} />
      </div>
    )
  }

  getText = (text: any) => {
    const isChina = /.*[\u4e00-\u9fa5]+.*$/.test(text)
    const index = isChina ? CHINESENUM : ENGLISHNUM
    if (text && text.length > index) {
      return text.substring(0, index) + '...'
    }
    return text
  }

  setElemHeight = () => {
    let commentWordsWrapper = document.querySelector('.comment-words-wrapper')
    if (commentWordsWrapper) {
      if (this.state.showPanel) {
        commentWordsWrapper.style.height = 'auto'
        commentWordsWrapper.style.overflow = 'auto'
      } else {
        commentWordsWrapper.scrollTop = 0
        commentWordsWrapper.style.height = '40px'
        commentWordsWrapper.style.overflow = 'hidden'
      }
    }
  }
  setCommentHeight = () => {
    let commentWordsWrapper = document.querySelector('.comment-words-wrapper')
    if (commentWordsWrapper) {
      let commentHeight = parseInt(window.getComputedStyle(commentWordsWrapper).height)
      if (commentHeight > 40) {
        this.setState({ showPanelBtn: true, showPanel: false }, this.setElemHeight)
      } else {
        this.setState({ showPanelBtn: false, showPanel: false }, this.setElemHeight)
      }
    }
  }
  handleCommentAction = () => {
    const { showPanel } = this.state
    let commentWordsWrapper = document.querySelector('.comment-words-wrapper')
    if (commentWordsWrapper) {
      this.setState({ showPanelBtn: true, showPanel: true }, () => {
        this.setElemHeight()
        let currentCommentHeight = parseInt(window.getComputedStyle(commentWordsWrapper).height)
        if (currentCommentHeight <= 40) {
          this.setState({ showPanelBtn: false, showPanel: false })
        } else {
          this.setState({ showPanelBtn: true, showPanel: showPanel }, () => {
            this.setElemHeight()
          })
        }
      })
    }
  }

  commentManage = () => {
    return (
      <CommentWordsNew
        bus={this.props.bus}
        type={this.props.type}
        CommentData={this.state.myWords}
        setCommentCallback={this.setCommentCallback}
      />
    )
  }

  handleAttachmentFinished = (attachments: AttachmentIF[]) => {
    const { form } = this.props
    form.setFieldsValue({ attachments })
  }

  render() {
    // @ts-ignore
    const {
      specialStyle,
      suffixesPath,
      invalidSuffixesConfig,
      canSelectDP,
      useClipboard,
      form,
      onUploading,
      showAttachment = false,
    } = this.props
    const { myWords, showPanel, showPanelBtn } = this.state
    const contents = myWords?.contents || [] // cloneDeep(()).reverse();
    return (
      <div className={style['comment-words-components']} style={specialStyle}>
        {contents.length ? (
          <div className="comment-words-wrapper" style={{ display: 'inline-block' }}>
            {contents.map((item: any, index: number) => {
              return this.renderItem(item, index)
            })}
          </div>
        ) : null}
        <div className="comment-words-btn">
          <div className="comment-group-btn">
            {showPanelBtn && (
              <div onClick={this.handleCommentPanel}>
                {showPanel ? (
                  <Button category="secondary" className="mr-10">
                    <OutlinedDirectionUp className="mr-5" />
                    {i18n.get('收起')}
                  </Button>
                ) : (
                  <Button category="secondary" className="mr-10">
                    <OutlinedDirectionDown className="mr-5" />
                    {i18n.get('展开')}
                  </Button>
                )}
              </div>
            )}
            <div className="button-row">
              <Popover
                trigger="click"
                overlayClassName={style['comment-words-popover']}
                destroyOnClose={true}
                overlayInnerStyle={{ padding: 0 }}
                destroyTooltipOnHide={true}
                zIndex={1009}
                content={this.commentManage()}
              >
                <Button category="secondary">
                  <OutlinedGeneralChatNews /> {i18n.get('常用语管理')}
                </Button>
              </Popover>
              {showAttachment
                ? form.getFieldDecorator('attachments')(
                    <AttachmentComponent
                      useClipboard={useClipboard}
                      classNameOCR={showPanelBtn ? 'comment-attachment-list-wrapper' : ''}
                      onFinshed={this.handleAttachmentFinished}
                      suffixesPath={suffixesPath}
                      invalidSuffixesConfig={invalidSuffixesConfig}
                      canSelectDP={canSelectDP}
                      onUploading={onUploading}
                    />,
                  )
                : null}
            </div>
          </div>
        </div>
      </div>
    )
  }
}

function Words(props: { line: any; i: number; getText: Function }) {
  const { line, i, getText } = props
  const words = getText(line.words)
  return (
    <div className={'comment-words-item'} key={i}>
      <div className={'comment-words-text'}>
        <Tooltip title={line.words}>
          <div>{words}</div>
        </Tooltip>
      </div>
    </div>
  )
}
