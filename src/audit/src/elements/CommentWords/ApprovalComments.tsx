import React, { PureComponent } from 'react'
import style from './CommentWords.module.less'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { Checkbox } from '@hose/eui'

import { newTrack } from '../../util/trackAudit'
import CommentWords from './CommentWords'
const NCommentComponent = api.require('@components/comment/NCommentComponent')
interface ApprovalCommentsInterface {
  form: any
  userInfo?: any
  bus: any
  required?: boolean
  styles?: any
  type: string
  showAttachment?: boolean
  suffixesPath: 'APPROVE'
  invalidSuffixesConfig?: Record<string, number>
  canSelectDP?: boolean
  useClipboard?: boolean
  onUploading?: (uploading: boolean) => void
}
@EnhanceConnect(state => ({
  userInfo: state['@common'].userinfo.data,
}))
class ApprovalComments extends PureComponent<ApprovalCommentsInterface> {
  state = {
    inputStatus: '',
    inputValue: '',
    isEditNumber: undefined,
  }
  componentDidMount() {
    const { userInfo, type } = this.props
    const session = JSON.parse(
      window.localStorage.getItem(
        `__userApproveOpinionsDialogReminder${type}${userInfo?.staff?.userId}${userInfo?.staff?.corporationId}`,
      ) || '',
    )
    this.props.form.setFieldsValue({ reminder: session?.ApproveOpinionsDialogReminder || false })
  }
  handleAddCommentWords = (words: string) => {
    const { form, userInfo } = this.props
    const comment = form.getFieldValue('comment')
    form.setFieldsValue({ comment: { value: `${comment?.value || ''}${words}`, mentions: comment?.mentions || [] } })
    // 点击使用常用语埋点
    newTrack('Click_to_use_common_words', {
      actionName: i18n.get('点击使用常用语'),
      staffId: userInfo?.staff?.userId,
      corpId: userInfo?.staff?.corporationId,
    })
  }

  reminderChange = (e: any) => {
    const { userInfo, type } = this.props
    window.localStorage.setItem(
      `__userApproveOpinionsDialogReminder${type}${userInfo?.staff?.userId}${userInfo?.staff?.corporationId}`,
      `{"ApproveOpinionsDialogReminder":${e.target.checked}}`,
    )
  }

  render() {
    const {
      form,
      bus,
      required = true,
      styles,
      type,
      suffixesPath,
      invalidSuffixesConfig,
      canSelectDP,
      useClipboard,
      onUploading,
      showAttachment,
    } = this.props

    const approveCommentEn = i18n?.currentLocale === 'en-US' ? style['approve-comments-en'] : ''

    return (
      <div style={styles} className={`${style['approve-comments']} ${approveCommentEn}`}>
        <div className="comments-title horizontal">
          <div className="fw-500 fs-14">
            {required ? <span className="color-red mr-4">*</span> : ''} {i18n.get('审批意见')}
          </div>
          <div>
            {form.getFieldDecorator('reminder', {
              initialValues: true,
              valuePropName: 'checked',
            })(
              <Checkbox className="mr-10" onChange={this.reminderChange}>
                {i18n.get('提醒提单人')}
              </Checkbox>,
            )}
            {form.getFieldDecorator('keepItSecret', {
              initialValue: false,
            })(<Checkbox>{i18n.get('仅被@的人可见')}</Checkbox>)}
          </div>
        </div>
        {/* 审批评论组件 */}
        <NCommentComponent bus={bus} required={required} form={this.props.form} type={'comment'} />

        {/* 常用语功能 */}
        <CommentWords
          type={type}
          handleSelectWords={this.handleAddCommentWords}
          commentArea={form.getFieldValue('comment')}
          key={type}
          bus={bus}
          suffixesPath={suffixesPath}
          invalidSuffixesConfig={invalidSuffixesConfig}
          canSelectDP={canSelectDP}
          useClipboard={useClipboard}
          form={form}
          onUploading={onUploading}
          showAttachment={showAttachment}
        />
      </div>
    )
  }
}

export default ApprovalComments
