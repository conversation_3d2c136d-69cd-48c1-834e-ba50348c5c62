
import { app as api } from '@ekuaibao/whispered'
import React, { Component } from 'react'
import wordStyle from './CommentWords.module.less'
import { newTrack } from '../../util/trackAudit'
import '../CommonAccountModal/style.less'

import { Button, Popconfirm ,ErrorBlock, Input, message, Tooltip} from '@hose/eui'
import {
  SortableContainer,
  SortableElement,
  SortableHandle,
  arrayMove
} from 'react-sortable-hoc';

import {
  OutlinedEditDrag,
  OutlinedEditDeleteTrash,
  OutlinedEditEdit,
  OutlinedTipsAdd
} from '@hose/eui-icons'

interface Props {
    CommentData: any
    onChange: Function
    bus:any,
    type?:string,
    setCommentCallback:(value: string | any[]) => void
}
interface ContentItem{
  words:string,
  lastTime:number
}
interface State {
    comment?: string,
    activeTime?: number,
    editingText?: string,
    addText:string,
    addActive:boolean,
    error: boolean
    contents:Array<ContentItem>,
    wordsData:{
      id:string,
      contents:Array<ContentItem>
    },
}

const DragHandle = SortableHandle(() => <OutlinedEditDrag />);

const SortableItem = SortableElement(({children}) => (
    <div className="comment-move-item-wrapper">{ children }</div>
));

const SortableContainerL =SortableContainer(({children}) => {
  return <div className="answer-wrapper">{children}</div>
});


export default class WordsEdit extends Component<Props, State> {
    state = {
        comment: '',
        activeTime: 0,
        editingText: '',
        addText:'',
        error: false,
        contents:[],
        addActive:false,
        wordsData:{
          id:null,
          contents:[]
        },
    }

    componentDidMount(){
      const { CommentData } = this.props
      this.setState({ wordsData: CommentData || {}, contents: CommentData?.contents || [] })
    }

    componentWillReceiveProps(nextProps:Props){
      if(this.state.wordsData !== nextProps.CommentData){
        this.setState({ wordsData: nextProps.CommentData || {}, contents: nextProps?.CommentData?.contents || [] })
      }
    }

    handleEdit = (activeTime: number, editingText: string) => {
        this.setState({ activeTime, editingText })
    }
    handleDelete = (index: number) => {
      const { wordsData } = this.state

      wordsData.contents.splice(index, 1)
      this.sendService(wordsData,'delete')
    }
    confirmSave = (isEdit = false) => {
        const { type } = this.props
        const { editingText, activeTime, addActive, addText, wordsData, contents} = this.state
        const { id } = wordsData
        const inputValue = isEdit ? editingText : addText; // 区分编辑和新增
        let editingKey = contents.findIndex((i:ContentItem) => { return i.lastTime === activeTime })
        const exist = contents.find((i:ContentItem) => (i.words === inputValue && i.lastTime !== activeTime))
        if (exist) {
          return message.error(i18n.get('常用语已存在'))
        }
        if(!inputValue) {
          return message.error(i18n.get('请输入常用语'))
        }
        if(contents.length >= 110 && !isEdit){
          return message.error(i18n.get('常用语最多可添加110条'))
        }

        // 保存操作
        if(addActive && !isEdit && !id){
          let newContents = [{
            words: inputValue,
            lastTime:new Date().getTime()
          }]
          api.invokeService('@audit:new:commentwords', { from: type, type, contents:newContents })
          .then((value: any) => {
            this.props.setCommentCallback && this.props.setCommentCallback(value)
            this.setState({ editingText: '',contents: value.contents })
          })
        }else{
          // 保存
          if(addActive && !isEdit){
            wordsData.contents.unshift({
              words: inputValue,
              lastTime:new Date().getTime()
            })
          }else if(activeTime >0 && isEdit){
            wordsData.contents[editingKey].words = inputValue
          }else{
            wordsData.contents.splice(editingKey, 1)
          }
          this.sendService(wordsData,isEdit ? 'edit' : 'add')
        }
    }
    // 发送后台请求
    sendService =(data:any,type = '')=>{
      const { wordsData, activeTime, addText} = this.state
      const { id } = wordsData
      const isAdd = (type =='add');
      const userInfo = api.getState()['@common'].userinfo
      let trackTitle = 'DragSort_common_words', trackText = '拖拽排序常用语';

      if(type == 'add'){
        trackTitle = 'Add_common_words'
        trackText = '编辑常用语'
      }else if(type == 'edit'){
        trackTitle = 'Edit_common_words'
        trackText = '编辑常用语'
      }else if(type == 'drag'){
        trackTitle = 'Drag_common_words'
        trackText = '编辑常用语'
      }else{
        trackTitle = 'Delete_common_words'
        trackText = '删除常用语'
      }
      api.invokeService('@audit:edit:commentwords', { from: id, ...data}).then((value: any) => {
        this.props.setCommentCallback && this.props.setCommentCallback(value)
        this.setState({ activeTime: isAdd ? activeTime : 0 , addText: isAdd ? '': addText,contents: value.contents })
         // 常用语埋点
         newTrack(trackTitle,{
          actionName: i18n.get(trackText),
          staffId: userInfo?.data?.staff?.userId,
          corpId: userInfo?.data?.staff?.corporationId
        })
      })
    }

    inputChange = (e) => {
      const value = e.target.value
      this.setState({ editingText:value})
    }
    inputAddChange = (e)=>{
      const value = e.target.value
      this.setState({ addText:value})
    }
    cancelEdit = () => {
      this.setState({ activeTime: 0, editingText: ''})
    }
    addCancelEdit = () => {
      this.setState({ addActive: false, addText: ''})
    }
    renderNormalList =  (item: ContentItem,lastTime:number, index: number) =>{
      return <>
              <div className="field-item-left"><DragHandle/>
              <Tooltip overlayStyle = {{zIndex:11111}} title={item.words}>
                  <div className="field-item-title">{item.words}</div>
                </Tooltip>
              </div>
              <div className="field-item-right">
                  <OutlinedEditEdit className = "mr-10" onClick={() => this.handleEdit(lastTime, item.words)}/>
                  <Popconfirm
                    title={'确认是否删除？'}
                    overlayStyle = {{zIndex:11111}}
                    onConfirm={() => this.handleDelete(index)}
                    okText="是"
                    cancelText="否"
                  >
                    <OutlinedEditDeleteTrash />
                  </Popconfirm>
              </div>
        </>
    }
    renderInputWrap = (words:string)=>{
      const { error, editingText, addText } = this.state
      return(
            <>
              <div className="input-warp">
               <Input
                   onChange={words ? this.inputChange : this.inputAddChange}
                    defaultValue={words || ''}
                    value = {words ? editingText : addText}
                    type="text"
                    status = {error ? 'error':''}
                    placeholder={i18n.get('请输入常用语')}
                    maxLength={200}
                    autoFocus
                />
              </div>
              <div className="btn dis-f jc-e">
                <Button className="mr-5" category="text" theme="highlight" size="small" onClick={()=>{this.confirmSave(Boolean(words))}}>{i18n.get('保存')}</Button>
                <Button category="text" size="small" onClick={Boolean(words) ? this.cancelEdit : this.addCancelEdit}>{i18n.get('取消')}</Button>
              </div>
            </>
      )
    }
    renderItem = (item: ContentItem,lastTime:number, index: number) => {
      const { activeTime } = this.state
      if (!activeTime) {
          return this.renderNormalList(item,lastTime,index)
      } else {
          if (activeTime && activeTime == lastTime) {
            return this.renderInputWrap(item.words)
          } else {
            return < >
              <div className="field-item-left">
                <DragHandle/>
                <Tooltip overlayStyle = {{zIndex:11111}} title={item.words}>
                  <div className="field-item-title">{item.words}</div>
                </Tooltip>
              </div>
            </>
          }
      }
    }

    addInput = ()=>{
      this.setState({
        addActive:true,
        addText:''
      })
    }

    onSortEnd = ({oldIndex, newIndex}:{oldIndex:number,newIndex:number}) => {
      const { contents,wordsData }  = this.state
      const newContents = arrayMove(contents, oldIndex, newIndex)
      this.setState({ contents: newContents},()=>{
        wordsData.contents = newContents
        this.sendService(wordsData)
      });
    };

    renderFooter = ()=>{
      if(this.state.addActive == true){
        return this.renderInputWrap('')
      }else{
        return <div className="add" onClick={this.addInput}><OutlinedTipsAdd className="mr-5" />{i18n.get('添加常用语')}</div>
      }
    }

    render() {
        const { contents } = this.state

        return (
          <div className={wordStyle['simple-answer-body']}>
            {
              !contents.length ? <ErrorBlock status="empty" title={i18n.get('暂无数据')} /> :
                <SortableContainerL onSortEnd={this.onSortEnd} useDragHandle>
                {contents.map((value:ContentItem, index) => (
                  <SortableItem key={`item-${index}`} index={index}>
                    {this.renderItem(value, value.lastTime,index)}
                  </SortableItem>
                ))}
                </SortableContainerL>
            }
            <div className="add-warp">
              {this.renderFooter()}
            </div>
          </div>
        )
    }
}
