@import '~@ekuaibao/eui-styles/less/token';

.comment-words-components {
  width: 100%;
  :global {
    .comment-words-btn {
      .font-size-2;
      .font-weight-2;
      display: flex;
      justify-content: space-between;
      color: var(--eui-text-title);
      .comment-words-title {
        margin-right: @space-3;
      }
      .comment-group-btn {
        display: flex;
        flex: 1;
        color: @color-brand;

        span {
          cursor: pointer;
        }
        .button-row {
          position: relative;
          flex: 1;
          .comment-attachment-list-wrapper {
            margin-left: -90px;
          }
        }
      }
    }

    .comment-words-add {
      text-align: right;
      line-height: 20px;
      background-color: @white;
      margin-right: 10px;
      color: @color-brand;
      cursor: pointer;
    }

    .comment-words-wrapper {
      max-height: 130px;
      overflow: auto;

      .comment-words-items {
        .comment-words-item {
          .font-size-2;
          .font-weight-2;
          cursor: pointer;
          background: @white;
          height: 32px;
          margin-bottom: 8px;
          line-height: 30px;
          padding: 0 @space-4;
          margin-right: 8px;
          white-space: nowrap;
          border: 1px solid #1d212933;
          box-sizing: border-box;
          border-radius: 15px;
          .comment-words-text {
            color: #6b7785;
          }
        }

        .comment-words-item:hover {
          border: 1px solid @color-brand;
        }
      }
    }
  }
}
.comment-words-popover {
  :global {
    .eui-popover-inner {
      padding: 0;
    }
  }
}

.approve-comments {
  width: 100%;
  padding: 15px 16px;
  position: relative;
  :global {
    .comments-title {
      display: flex;
      justify-content: space-between;
      .ant-checkbox-wrapper {
        font-size: 14px;
      }
    }
    .ant-checkbox + span {
      padding-right: 0;
    }

    .ekb-files-uploader-wrapper {
      position: absolute;
      top: 0;
      height: 32px;
      left: 140px;
    }
  }
}

.approve-comments-en {
  :global {
    .ekb-files-uploader-wrapper {
      left: 206px;
    }
  }
}

.simple-answer-body {
  width: 320px;
  height: 408px;
  :global {
    .eui-error-block {
      padding: 16px 0px;
      height: 350px;
    }
    .answer-wrapper {
      height: 350px;
      width: 100%;
      padding: 10px;
      overflow-y: auto;
      overflow-x: hidden;
    }
    .add-warp {
      display: flex;
      height: 48px;
      line-height: 48px;
      border-top: 1px solid var(--eui-line-divider-default, rgba(29, 33, 41, 0.1));
      margin-top: 5px;
      justify-content: space-between;
      flex-direction: row;
      padding: 0 12px;
      .input-warp {
        display: flex;
        align-items: center;
        width: 202px;
      }
      .btn {
        width: 90px;
      }
      .eui-icon {
        font-size: 16px;
      }
      .add {
        display: flex;
        align-items: center;
        color: var(--eui-primary-pri-500);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
      }
    }
  }
}
