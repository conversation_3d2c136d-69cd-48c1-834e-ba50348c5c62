import React, { Component } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Icon, Timeline } from 'antd'
import styles from './PayLogModal.module.less'
import moment from 'moment'
import { T } from '@ekuaibao/i18n'
import { showMessage } from '@ekuaibao/show-util'
import { getPayLogs, getPayLogSearchItems } from '../../audit-action'
import LogSearch from './LogSearch'
import { app as api } from '@ekuaibao/whispered'
import { Space } from '@hose/eui'

interface IProps {
  form: any
  layer: any
  formType: string
  line: any
  type?: string
  source?: string
}

interface IState {
  logList: any[]
  searchListData: any
}

@EnhanceModal({
  title: '',
  footer: [],
  className: 'respond-modal-layer',
})
// @ts-ignore
export default class PayLogModal extends Component<IProps, IState> {
  colorMap: any = { SUCCESS: 'green', FAILURE: 'red', PROCESS: 'gray' }
  dotMap: any = {
    SUCCESS: <Icon type="check-circle" style={{ fontSize: '16px' }} />,
    FAILURE: <Icon type="close-circle" style={{ fontSize: '16px' }} />,
    PROCESS: '',
  }
  state = { logList: [], searchListData: {} }
  componentDidMount() {
    const {
      line: { flowId, id, channelTradeNo, payee, channel, paymentChannel },
      type,
    } = this.props
    const payingState = type === 'payment'
    getPayLogSearchItems({
      flowId: flowId || id,
      channelType: !payingState ? null : paymentChannel || channel,
      channelTradeNo: !payingState ? null : channelTradeNo,
    }).then(result => {
      this.setState({ searchListData: result })
      const { payeeInfoList } = result
      let cardName = payee?.name
      let cardNo = payee?.cardNo
      if ((!cardName || !cardNo) && payeeInfoList && payeeInfoList.length > 0) {
        cardName = payeeInfoList[0].cardName
        cardNo = payeeInfoList[0].cardNo
      }
      this.fetchLogs({ cardName, cardNo, channelTradeNo })
    })
  }
  fetchLogs = ({ cardName, channelTradeNo, cardNo }: { cardName: string; channelTradeNo: string; cardNo: string }) => {
    const {
      line: { flowId, id, channel, paymentChannel },
      source,
    } = this.props
    getPayLogs({
      cardName,
      flowId: flowId || id,
      channelType: source === 'RECORD' ? null : paymentChannel || channel,
      channelTradeNo,
      cardNo,
    })
      .then(({ items }) => {
        this.setState({ logList: items })
      })
      .catch(e => {
        showMessage.error(e.msg || e.errorMessage)
      })
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  onSearch = (params: { channelTradeNo: string; cardNo: string; payeeInfoList: any[] }) => {
    const { channelTradeNo, cardNo, payeeInfoList } = params
    const payerCard = payeeInfoList?.find(line => line.cardNo === cardNo)
    this.fetchLogs({ channelTradeNo, cardNo, cardName: payerCard?.cardName || '' })
  }
  handleRead = (row: any) => {
    api.open('@outbound-message:OutBoundBox', {
      data: row,
    })
  }

  renderLogItem = (item: any) => {
    return (
      <div className={styles['right-item-wrap']}>
        <div className="operate-time ml-16">
          <div className="mm-dd">{moment(item.optime).format('MM-DD')}</div>
          <div className="hh-ss">{moment(item.optime).format('HH:mm:ss')}</div>
        </div>
        <div className="operate-type ml-16">
          <div className="operate-type-content-title">{item.operType}</div>
          <div className="operate-type-description">
            <div className="operate-staff mr-4">{item.creater}</div>
            <div className="operate-status mr-8">{item.content}</div>
            <Space>
              {item.msgUrl && (
                <a className="copy-btn" onClick={() => api.emit('@vendor:download', item.msgUrl)} data-testid="pay-payLogModal-download-btn">
                  <T name="下载报文" />
                </a>
              )}
              {(item.request || item.response) &&
                (item.bodyType === 'JSON' || item.bodyType === 'XML') && ( //并且是json格式或者xml格式
                  <a className="copy-btn" onClick={() => this.handleRead(item)} data-testid="pay-payLogModal-read-btn">
                    <T name="查看报文" />
                  </a>
                )}
            </Space>
          </div>
        </div>
      </div>
    )
  }
  render() {
    return (
      <div className={styles['pay-log-wrap']}>
        <div className="pay-log-header">
          <div className="flex">
            <T name="支付日志详情" />
          </div>
          <Icon className="cross-icon" type="cross" onClick={this.handleCancel} data-testid="pay-payLogModal-close-icon" />
        </div>
        <LogSearch
          params={this.props.line}
          onSearch={this.onSearch}
          type={this.props.type}
          searchListData={this.state.searchListData}
        />
        <div className="log-content">
          {!!this.state.logList.length &&
            this.state.logList.map((v: any) => {
              if (v?.payRecords && v.payRecords.length === 0) return null
              return (
                <div className="log-item-wrap" key={v.id}>
                  <div className="log-item-title">
                    <T name="批次号" />：{v.channelTradeNo}
                  </div>
                  <div className="log-item-content">
                    <Timeline>
                      {v.payRecords.map((item: any, index: number) => (
                        <Timeline.Item
                          className="timeline-item-wrap"
                          key={index}
                          dot={this.dotMap[item.state]}
                          color={this.colorMap[item.state]}
                        >
                          {this.renderLogItem(item)}
                        </Timeline.Item>
                      ))}
                    </Timeline>
                  </div>
                </div>
              )
            })}
        </div>
        <div className="log-footer" />
      </div>
    )
  }
}
