/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/5/6 下午2:17
 */
import React, { useEffect, useState } from 'react'
import { Select } from 'antd'
import { Button } from '@hose/eui'
import { showMessage } from '@ekuaibao/show-util'
const { Option } = Select
interface Props {
  params: any
  onSearch: (params: { channelTradeNo: string; cardNo: string; payeeInfoList: any[] }) => void
  searchListData: any
  type?: string
}

export default function LogSearch(props: Props) {
  const { params, onSearch, searchListData, type } = props
  const { channelTradeNo = '', payee } = params
  const { channelTradeNoList, payeeInfoList = [] } = searchListData
  let cardNo = payee?.cardNo
  const [mCardNo, setCardNo] = useState(cardNo)
  if (!cardNo && payeeInfoList?.length > 0) {
    cardNo = payeeInfoList[0].cardNo
  }
  useEffect(() => {
    setCardN<PERSON>(cardNo)
  }, [searchListData])
  const state = type === 'payment'
  const [mChannelTradeNo, setChannelTradeNo] = useState(channelTradeNo)
  return (
    <div className="pay-log-search">
      <Select
        showSearch
        allowClear
        disabled={state}
        className="select-item"
        defaultValue={mChannelTradeNo}
        onChange={(value: any) => setChannelTradeNo(value)}
        placeholder={'请选择批次号'}
        filterOption={(input: string, option: any) => {
          return option.key.indexOf(input) >= 0
        }}
        data-testid="pay-logSearch-tradeNo-select">
        {channelTradeNoList?.map((line: string) => {
          return (
            <Option key={line} value={line}>
              {line}
            </Option>
          )
        })}
      </Select>
      <Select
        showSearch
        defaultValue={cardNo}
        value={mCardNo}
        className="select-item"
        optionFilterProp="children"
        style={{flex:1}}
        placeholder={'请选择银行卡号或者名称'}
        filterOption={(input: string, option: any) => {
          return option.props.children.indexOf(input) >= 0
        }}
        onChange={(value: any) => setCardNo(value)}
        data-testid="pay-logSearch-cardNo-select">
        {payeeInfoList?.map((line: any) => {
          return (
            <Option key={line.cardNo} value={line.cardNo}>
              {`${line.cardName}(${line.cardNo})`}
            </Option>
          )
        })}
      </Select>
      <Button
        category="secondary"
        disabled={!mCardNo}
        onClick={() => {
          !mCardNo
            ? showMessage.error(i18n.get('请选择银行卡号或者名称'))
            : onSearch({ channelTradeNo: mChannelTradeNo, cardNo: mCardNo, payeeInfoList })
        }}
        data-testid="pay-logSearch-search-btn">
        {i18n.get('查询')}
      </Button>
    </div>
  )
}
