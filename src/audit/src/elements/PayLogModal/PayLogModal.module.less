@import '~@ekuaibao/eui-styles/less/token';

.pay-log-wrap {
  height: 100%;
  :global {
    .pay-log-header {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 56px;
      padding: 0 @space-7;
      .font-size-4;
      .font-weight-3;
      .cross-icon {
        cursor: pointer;
      }
    }
    .pay-log-search {
      display: flex;
      flex-direction: row;
      justify-content: center;
      overflow: hidden;
      flex: 1;
      margin: 0 16px;
      .select-item {
        width: 220px;
        margin-right: 16px;
      }
      .search-btn {
        flex-shrink: 0;
      }
    }
    .log-content {
      padding: 24px 24px 0;
      height: calc(100% - 124px);
      overflow-y: auto;
      .log-item-wrap {
        .log-item-title {
          color: #1d2b3d;
          font-size: 16px;
        }
        .log-item-content {
          margin-top: 16px;
          .timeline-item-wrap {
            .ant-timeline-item-head {
              top: 18px;
            }
            .ant-timeline-item-tail {
              top: 18px;
            }
          }
        }
      }
      .log-item-wrap:not(:first-child) {
        margin-top: 24px;
      }
    }
    .log-content:empty {
      background: url('../../images/empty.svg') no-repeat center;
      background-size: 100px;
    }
    .log-footer {
      height: 40px;
    }
  }
}

.right-item-wrap {
  display: flex;
  align-items: center;
  :global {
    .operate-time {
      .mm-dd,
      .hh-ss {
        font-size: 14px;
        height: 22px;
        line-height: 22px;
        color: #1d2b3d;
        text-align: center;
      }
      .hh-ss {
        color: rgba(20, 34, 52, 0.48);
      }
    }
    .operate-type {
      .operate-type-content-title {
        color: #1d2b3d;
        font-size: 14px;
        height: 22px;
        line-height: 22px;
        margin-bottom: 4px;
      }
      .operate-type-description {
        display: flex;
        align-items: center;
        .operate-staff {
          color: #1d2b3d;
        }
        .operate-status {
          color: rgba(29, 43, 61, 0.5);
        }
        .copy-btn:hover {
          text-decoration: none;
        }
      }
    }
  }
}
