@import '~@ekuaibao/web-theme-variables/styles/colors.less';

.check-pay-success-modal-wrapper {
  height: 230px;

  :global {
    .check-pay-success-content {
      padding: 32px;

      .check-pay-success-header {
        display: flex;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
        >img {
          margin-right: 16px;
        }
      }

      .check-pay-success-description {
        font-size: 14px;
        font-weight: 400;
        color: rgba(29,43,61,0.75);
        line-height: 22px;
        padding-left: 40px;
      }
    }

    .modal-footer {
      &.check-pay-success-footer {
        justify-content: flex-start;
        width: 100%;
        height: 56px;
        padding: 12px 72px;
        border-top: none;
        position: absolute;
        right: 0;
        bottom: 12px;
        box-shadow: none;

        button {
          font-size: 14px;
          line-height: 1.57;
        }
      }
    }
  }
}
