import './info-modal.less'
import React, { PureComponent } from 'react'
import { Button } from '@hose/eui'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'

import { app as api } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch'

@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer',
})
export default class InfoModal extends PureComponent {
  constructor(props) {
    super(props)
    this.getResult = this.getResult.bind(this)
    props.overrideGetResult(this.getResult)
  }

  handleDownLoad() {
    let url =
      'http://video.ekuaibao.com/help/%E7%8E%B0%E9%87%91%E7%BD%97%E7%9B%98%E9%A9%B1%E5%8A%A8%E5%8F%8AChrome%E6%8F%92%E4%BB%B6%E5%AE%89%E8%A3%85%E8%AF%B4%E6%98%8E.zip'
    api.emit('@vendor:open:link', url)
  }

  handleDownLoadExcel(ids, billType) {
    ids = ids.join(',')
    let ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
    let exportUrl = `${Fetch.fixOrigin(
      location.origin,
    )}/api/flow/v2/export/[${ids}]/$xlsx?corpId=${ekbCorpId}`
    api.emit('@vendor:download', exportUrl)
  }

  handleModalSave() {
    this.props.layer.emitOk({})
  }

  render() {
    let data = this.props.data
    let { exportFlowIds } = data
    let ids = exportFlowIds.map(item => item.id)
    return (
      <div className="info-modal">
        <div className="content">
          <div className="line-1">
            <div className="circle-icon">i</div>
            <div>
              {i18n.get('cash-pay-content', { tradeNo: data.channelTradeNo })}
              <a
                onClick={() => api.emit('@vendor:open:link', data.url)}
                target="_blank"
                rel="noopener">
                {i18n.get('跳转')}
              </a>
              {i18n.get('前往支付界面')}
            </div>
          </div>
          <div className="line-2">
            <a onClick={this.handleDownLoadExcel.bind(this, ids, data.billType)}>
              {i18n.get('导出Excel')}
            </a>
          </div>
          <div className="line-2">
            {i18n.get('无法正常支付？')}
            <a onClick={this.handleDownLoad.bind(this)}>{i18n.get('下载「Chrome插件」')}</a>
          </div>
        </div>
        <div className="b-content">
          <Button onClick={this.handleModalSave.bind(this)}>{i18n.get('关闭')}</Button>
        </div>
      </div>
    )
  }
}
