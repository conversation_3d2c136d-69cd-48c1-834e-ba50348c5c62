import React from 'react'
import * as DataGrid from '@ekuaibao/datagrid'
import { Column } from '@ekuaibao/datagrid/lib/types/column'
import { Data } from '@ekuaibao/datagrid/esm/types/dataSource'
import styles from './PaymentItemsTable.module.less'
import * as viewUtil from '../view-util'

export function payingTableFeeColumns(this: any, props?:any) {
  const { disableActionColumn = true } = props || {}
  let columns: Array<any> = [
    {
      title: i18n.get('编号'),
      dataIndex: 'channelTradeNo',
      render: viewUtil.tdText.bind(this)
    },
    {
      title: i18n.get('收款信息'),
      dataIndex: 'payee',
      render: viewUtil.accountInfo.bind(this)
    },
    {
      title: i18n.get('支付金额'),
      dataIndex: 'createMoneyObj',
      render: viewUtil.tdAmount.bind(this)
    },
    {
      title: i18n.get('支付状态'),
      dataIndex: 'state',
      render: (status: string, line: any) => {
        return viewUtil.renderPayStatus.call(this, status, line.respMsg)
      }
    }
  ]
  if (disableActionColumn) {
    columns.push({
      title: i18n.get('操作'),
      width: '18%',
      dataIndex: 'paymentActions',
      className: 'actions-wrapper',
      render: viewUtil.paymentAction('offLine').bind(this)
    })
  }
  return columns
}

export interface PaymentItemsTableProps {
  data: {
    id: string
    details: Data[]
    payPlanDigest?: any[]
    multiplePayeesMode?: boolean
    payPlanMode?: boolean
    payeePayPlan?: boolean
    paymentDigest?: any
  }
  detailData?: any[]
}

export interface PaymentItemsTableState {
  dataSource: any[]
}

export class PaymentItemsTable extends React.PureComponent<
  PaymentItemsTableProps,
  PaymentItemsTableState
> {
  private instance: any
  private columns: Column[]

  constructor(props: PaymentItemsTableProps) {
    super(props)
    this.columns = payingTableFeeColumns.call(this).map((c: Column) => {
      c.sorter = false
      return c
    })
    const { id } = props.data
    const { detailData = [] } = props
    const dataSource = this.initDataArray(detailData, id)
    this.state = {
      dataSource: dataSource || [],
    }
  }

  // 把flowId放到支付计划的数据里；
  // 支付计划重新支付时需要用flowId获取可见的付款账户
  initDataArray = (dataSource: any[], flowId: string) => {
    return dataSource.map((el: any) => ({ ...el, flowId }))
  }

  componentDidMount() {
    if (this.instance) {
      this.instance.on('initialized', () => {
        const scrollable = this.instance.getScrollable()
        if (scrollable) {
          scrollable.option('rowRenderingMode', 'standard')
        }
      })
    }
  }

  componentWillReceiveProps(nextProps: any) {
    if (this.props.detailData !== nextProps.detailData) {
      this.setState({ dataSource: nextProps?.detailData || [] })
    }
  }

  getClassName = (rowIndex: number) => {
    const record = this.state.dataSource?.[rowIndex]
    const repayState = record?.repayState
    const state = record?.state
    return state === 'FAILURE' && (repayState === 'PROCESSED' || repayState == 'SUCCESS')
      ? 'row-disabled'
      : ''
  }

  render() {
    const { dataSource } = this.state
    if (dataSource.length === 0) {
      return i18n.get('此支付记录没有支付明细')
    }
    return (
      <div className={styles.subContainer}>
        <div className={styles.body}>
          <DataGrid.TableWrapper
            scrolling={{
              mode: 'standard',
            }}
            standard
            rowClassName={this.getClassName}
            getInstance={this.getInstance}
            rowKey={'id'}
            className={styles.tableWrapper}
            dataSource={dataSource}
            pageIndex={1}
            pageSize={dataSource.length}
            columns={this.columns}
            allowColumnReordering={false}
            allowColumnResizing
            isMultiSelect={false}
            isSingleSelect={false}
          />
        </div>
      </div>
    )
  }

  private getInstance = (instance: any) => {
    this.instance = instance
  }
}

export default PaymentItemsTable
