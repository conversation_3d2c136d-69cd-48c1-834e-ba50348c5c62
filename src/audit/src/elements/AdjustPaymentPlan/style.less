@import '~@eku<PERSON>bao/eui-styles/less/token';

.adjust-payment-plan{
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .lock{
    height: 100%;
    width: 100%;
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(29, 43, 61, 0.15);
    z-index: 2;
  }
  .inner{
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 1;
  }
  .modal-header {
    flex-shrink: 0;
    &-title{
        flex: 1;
        .font-size-5;
        color:var(--eui-text-title);
    }
  }
  .modal-content{
    padding: 0 32px 32px;
    height: calc(100% - 60px - 56px);
    position: relative;
    .modal-warpper{
      position: absolute;
      z-index: 3;
      width: calc(100% - 64px);
      overflow-x: auto;
      height: 100%;
      .modal-warpper-c{
        height: 100%;
        .recall-plan{
          cursor: pointer;
          color: #f17b7b;
          .line-action {
            display: flex;
            .action {
              margin-right: 12px;
            }
          }
        }
      }
    }
    
    .column-chooser-box{
      position: absolute;
      top: 18px;
      right: 45px;
      z-index: 4;
      &::before{
        content: '';
        width: 1px;
        height: 16px;
        position: absolute;
        background: rgba(29, 43, 61, 0.15);
        left: -9px;
      }
    }
    .search-box{
      position: absolute;
      right: 12px;
      top: 0;
      z-index: 4;
    }
    .save-diff-box{
      position: absolute;
      top: 16px;
      right: 319px;
      z-index: 4;
      .save-diff-btn{
        color: #22b2cc;
        cursor: pointer;
        min-width: 70px;
        height: 32px;
        margin-left: 16px;
        display: inline-block;
        vertical-align: middle;
      }
    }
    
  }
  .modal-footer{
    justify-content: space-between;
    .total{
      padding: 0 5px;
      color: #22b2cc
    }
    .ant-btn-danger {
      color: #ff7c7c;
      background-color: #fff;
      border: 1px solid #ff7c7c;
      &:hover{
        color: #fff;
        background-color: #ff7c7c;
        transition: .3
      }
    }
  }
}
