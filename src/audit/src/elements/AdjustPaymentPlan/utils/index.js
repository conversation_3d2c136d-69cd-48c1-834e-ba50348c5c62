import { app as api } from '@ekuaibao/whispered'
import { parseFields } from '@ekuaibao/lib/lib/entityUtil/entityTableUtil'
const { parseColumns } = api.require('@elements/DataLinkTable/tableUtil')
import { prepareRender } from '../../../view-util'
import { getV } from '@ekuaibao/lib/lib/help'
import { cloneDeep } from 'lodash'
import { showMessage } from '@ekuaibao/show-util'
import Big from 'big.js'
import { uuid } from '@ekuaibao/helpers'
/**
 * 是否渲染dom
 * @param {*} renderDom
 * @param {*} isRender
 */
export const isRenderDom = (renderDom, isRender) => (isRender ? renderDom : null)

/**
 * 根据多重条件查询数组
 * @param {*} list
 * @param {*} query
 */
export const filterByQuery = (list, query = {}) => {
  let _sql = ''
  Object.keys(query).forEach(
    (s, i) => (_sql += `${i == 0 ? '' : ' && '}String(item.${s}).indexOf(String(${query[s] || ''})) > -1`),
  )

  return list.filter(item => eval(_sql || 'true'))
}

/**
 * 转换数组为key value对象
 * @param {Array} data [{label: "a", value: "b"}]
 * @param {String} key
 */
export const convertToKeyVal = (data, key = 'value') => {
  const newData = {}
  data.forEach(item => (newData[item[key]] = item))

  return newData
}

/**
 * 处理组装数据
 */
export const assembleData = ({
  response,
  bus,
  dynamicChannelMap,
  setColumns,
  setBaseData,
  setData,
  columnsFromScenes,
  setAllColumns,
  setOriginalListMap,
  isByDetail,
  _flowIds,
  setOriginalList,
  setOriginalData,
}) => {
  let { viewDataList, originalList } = response.value
  let { data, template, path } = viewDataList
  if (isByDetail) {
    // 明细维度进入调整支付计划时，仅已选中的支付计划可见
    const visiblePayPlanIds = _flowIds.map(el => el.id)
    data = data.filter(el => visiblePayPlanIds.includes(el.dataLink?.id))
    originalList = originalList.filter(el => visiblePayPlanIds.includes(el.id))
  }
  const dataSource = data
  const type = 'DATA_LINK'
  const dataLinkEntity = data[0] ? data[0].dataLink.entity : []
  const fields = parseFields({ res: template, type })
  const { columns } = parseColumns({
    entityInfoMap: {},
    fields,
    bus,
    path,
    platformType: type,
    dataLinkEntity,
  })

  // columnsFromScenes：保存变更后储存到后端的columns，没保存过会是null
  const defaultColumns = getDefaultColumns(columnsFromScenes, columns)

  let _columns = defaultColumns ? defaultColumns : cloneDeep(columns)
  prepareRender(_columns, dynamicChannelMap)

  return () =>
    new Promise((resolve, reject) => {
      setData(dataSource)
      setBaseData(dataSource)
      setOriginalData(dataSource)
      setColumns(_columns)
      setAllColumns(columns)
      setOriginalList(originalList)
      setOriginalListMap(convertToKeyVal(originalList, 'id'))
      resolve()
    })
}

/**
 * 列排序
 * 记录顺序的列数组
 * 原始列数组
 */
export const sortColumns = (sortArr = [], originColumns = []) => {
  //根据sortArr做排序
  let arr = []
  let cache
  sortArr.forEach(el => {
    if (!el || el === 'dataLink.id') return
    cache = originColumns.find(col => col.dataIndex === el)
    if (cache) arr.push(cache)
  })
  return arr
}

/**
 * 使用范围：调整支付计划页面的表格
 * 作用：系列化column数据，拼装filter
 */
export const columnFilterStringify = columns => {
  const filter = {
    scene: 'all',
    defaultColumns: JSON.stringify(columns),
  }
  return [JSON.stringify(filter)]
}

/**
 * 获取支付计划表格的列头数据
 * @param columnsFormScenes: 存在后端的列数据，里面没有render字段
 * @param allColumns: 支付计划表格中的可用字段
 */
export const getDefaultColumns = (columnsFormScenes, allColumns) => {
  const sortArr = columnFilterParse(columnsFormScenes)
  if (sortArr) {
    return sortColumns(sortArr, allColumns)
  } else {
    return null
  }
}

/**
 * 使用范围：调整支付计划页面的表格
 * 作用：从filter数据中解析出column数据
 * columnsFormScenes：sences数据
 */
export const columnFilterParse = columnsFormScenes => {
  const filter = getV(columnsFormScenes, 'value.filter', [])[0]
  if (filter) {
    const filterValue = JSON.parse(filter)
    const defaultColumns = JSON.parse(getV(filterValue, 'defaultColumns'))
    const columnsValue = JSON.parse(defaultColumns[0])
    const columns = JSON.parse(columnsValue.defaultColumns) || []
    return columns.map(el => el && el.dataIndex)
  } else {
    return null
  }
}

const throwError = (errorMessage = '') => {
  return {
    errorMessage: i18n.get(errorMessage),
    isError: true,
  }
}

// 合并法人实体数据
const fnMergeLegalEntity = (a, b) => {
  if (!a?.id) return b
  if (!b?.id) return a
  if (a.id.includes(b.id)) return a
  return {
    id: `${a.id},${b.id}`,
    name: `${a.name},${b.name}`,
  }
}

/**
 * 合并支付计划的操作模式
 */
const modeOfOperation = {
  id: (a, b) => `${a},${b}`,
  entityId: (a, b) => a,
  active: (a, b) => a,
  E_system_paymentPlan_code: (a, b) => `${a},${b}`,
  E_system_paymentPlan_flowId: (a, b) => `${a},${b}`,
  E_system_paymentPlan_编号: (a, b) => `${a},${b}`,
  E_system_paymentPlan_支付概要: (a, b) => `${a},${b}`,
  E_system_paymentPlan_提交人: (a, b) => {
    if (a?.id && b?.id && a.id.includes(b.id)) return a
    return {
      id: `${a.id},${b.id}`,
      name: `${a.name},${b.name}`,
      nameSpell: `${a.nameSpell},${b.nameSpell}`,
      userId: `${a.userId},${b.userId}`,
    }
  },
  E_system_paymentPlan_生成日期: () => new Date().getTime(),
  E_system_paymentPlan_支付金额: (a, b) => {
    if (a?.standardNumCode !== b?.standardNumCode) {
      return throwError('币种不相同的支付计划不能被合并')
    } else if (String(a?.standard) === '0' || String(b?.standard) == '0') {
      return throwError('金额为0的支付计划不能被合并')
    } else {
      return {
        ...a,
        standard: new Big(a?.standard || 0).plus(new Big(b?.standard || 0)).toString(),
      }
    }
  },
  E_system_paymentPlan_收款币金额: (a, b) => {
    if (a?.standardNumCode !== b?.standardNumCode) {
      return throwError('收款币币种不相同的支付计划不能被合并')
    } else if (String(a?.standard) === '0' || String(b?.standard) == '0') {
      return throwError('金额为0的支付计划不能被合并')
    } else {
      return {
        ...a,
        standard: new Big(a?.standard || 0).plus(new Big(b?.standard || 0)).toString(),
      }
    }
  },
  E_system_paymentPlan_本位币金额: (a, b) => {
    return {
      ...a,
      standard: new Big(a?.standard || 0).plus(new Big(b?.standard || 0)).toString(),
    }
  },
  E_system_paymentPlan_应付币金额: (a, b) => {
    return {
      ...a,
      standard: new Big(a?.standard || 0).plus(new Big(b?.standard || 0)).toString(),
    }
  },
  E_system_paymentPlan_原币金额: (a, b) => {
    if (a?.standardNumCode !== b?.standardNumCode) {
      return null
    } else {
      return {
        ...a,
        standard: new Big(a?.standard || 0).plus(new Big(b?.standard || 0)).toString(),
      }
    }
  },
  E_system_paymentPlan_realPayMoney: (a, b) => {
    return new Big(a || 0).plus(new Big(b || 0)).toString()
  },
  E_system_paymentPlan_币种: (a, b) => a,
  E_system_paymentPlan_收款账号类别: (a, b) => {
    if (a !== b) {
      return throwError('收款账号类别不相同的支付计划不能被合并')
    } else {
      return a
    }
  },
  E_system_paymentPlan_收款账号性质: (a, b) => {
    if (a !== b) {
      return throwError('收款账号性质不相同的支付计划不能被合并')
    } else {
      return a
    }
  },
  E_system_paymentPlan_收款信息: (a, b) => {
    if (a === null || b === null) {
      return throwError('无收款信息的支付计划不能被合并')
    } else if (a.id !== b.id) {
      return throwError('收款信息不同的支付计划不能被合并')
    } else {
      return a
    }
  },
  E_system_paymentPlan_foreignStrCode: (a, b) => {
    if (a !== b) {
      return null
    } else {
      return a
    }
  },
  E_system_paymentPlan_支付状态: () => 'READY',
  E_system_paymentPlan_legalEntity: fnMergeLegalEntity,
  E_system_paymentPlan_foreign: (a, b, prev, next) => {
    const flag = prev?.E_system_paymentPlan_foreignStrCode !== next?.E_system_paymentPlan_foreignStrCode
    if (flag || typeof a === 'undefined' || typeof b === 'undefined') {
      return undefined
    } else {
      return new Big(a || 0).plus(new Big(b || 0)).toString()
    }
  },
}
export const getNewCode = code => {
  const list = code.split('-')
  return `${list[0]}-${list[1]}-${uuid(6)}`
}
const checkoutError = result => {
  const _isError = typeof result === 'object' && result?.isError && result?.errorMessage
  return {
    isError: _isError,
    errorMessage: _isError ? result.errorMessage : '',
    response: result,
  }
}

const fnMergeAmount = (selectPlayPlans, key) => {
  return (
    selectPlayPlans.reduce((prev, item) => {
      return prev + (Number(item?.[key]?.standard) ?? 0)
    }, 0) || 0
  )
}

/**
 * 合并两条支付摘要
 * @param {*} dataMap   //全部的数据信息表
 * @param {*} needMergeList   //需要合并的数据
 * @param {*} value   //手动输入的支付摘要
 */
export const mergeRebase = (dataMap, needMergeList, value) => {
  const needMergeListFormat = needMergeList.map(i => i.split(':')[0])
  const singleIds = Array.from(new Set(needMergeListFormat))
  if (singleIds.length === 1) {
    const originalId = singleIds[0]
    const id = uuid(16)
    const newPaymentPlan = dataMap[needMergeList[0]]
    const {
      E_system_paymentPlan_支付金额,
      E_system_paymentPlan_收款币金额,
      E_system_paymentPlan_本位币金额,
      E_system_paymentPlan_应付币金额,
      E_system_paymentPlan_原币金额,
    } = newPaymentPlan
    const selectPlayPlans = needMergeList.map(it => dataMap[it])
    const totalMoney =
      selectPlayPlans.reduce((prev, item) => {
        return prev + Number(item.E_system_paymentPlan_支付金额.standard)
      }, 0) || 0
    const totalForeign =
      selectPlayPlans.reduce((prev, item) => {
        return prev + (Number(item.E_system_paymentPlan_foreign) ?? 0)
      }, 0) || 0
    const receivable_amount = fnMergeAmount(selectPlayPlans, 'E_system_paymentPlan_收款币金额')
    const standard_amount = fnMergeAmount(selectPlayPlans, 'E_system_paymentPlan_本位币金额')
    const payable_amount = fnMergeAmount(selectPlayPlans, 'E_system_paymentPlan_应付币金额')
    const original_amount = fnMergeAmount(selectPlayPlans, 'E_system_paymentPlan_原币金额')
    if (typeof value === 'string') {
      newPaymentPlan.E_system_paymentPlan_摘要 = value
    }
    const newData = {
      ...newPaymentPlan,
      id: `${originalId}:${id}`,
      E_system_paymentPlan_code: `${originalId}:${id}`,
      E_system_paymentPlan_编号: `${getNewCode(newPaymentPlan.E_system_paymentPlan_编号)}`,
      E_system_paymentPlan_支付金额: {
        ...E_system_paymentPlan_支付金额,
        standard: String(totalMoney),
      },
      E_system_paymentPlan_收款币金额: E_system_paymentPlan_收款币金额
        ? {
            ...E_system_paymentPlan_收款币金额,
            standard: String(receivable_amount),
          }
        : null,
      E_system_paymentPlan_本位币金额: E_system_paymentPlan_本位币金额
        ? {
            ...E_system_paymentPlan_本位币金额,
            standard: String(standard_amount),
          }
        : null,
      E_system_paymentPlan_应付币金额: E_system_paymentPlan_应付币金额
        ? {
            ...E_system_paymentPlan_应付币金额,
            standard: String(payable_amount),
          }
        : null,
      E_system_paymentPlan_原币金额: E_system_paymentPlan_原币金额
        ? {
            ...E_system_paymentPlan_原币金额,
            standard: String(original_amount),
          }
        : null,
      E_system_paymentPlan_realPayMoney: String(totalMoney),
    }
    if (totalForeign) {
      newData['E_system_paymentPlan_foreign'] = totalForeign
    }
    return {
      newPaymentPlan: newData,
      isError: false,
    }
  }

  let newPaymentPlan = {}
  let hasError = false
  for (let i = 0; i < needMergeList.length; i++) {
    const key = needMergeList[i]
    if (hasError)
      return {
        newPaymentPlan,
        isError: hasError,
      }
    if (i === 0) {
      newPaymentPlan = { ...dataMap[key] }
    } else {
      const _prev = newPaymentPlan
      const _next = dataMap[key]
      const keyList = Object.keys(_prev)
      for (let value of keyList) {
        if (modeOfOperation[value]) {
          const { response, isError, errorMessage } = checkoutError(
            modeOfOperation[value](_prev[value], _next[value], _prev, _next),
          )

          if (!isError && !errorMessage) {
            newPaymentPlan[value] = response
          } else {
            showMessage.error(errorMessage)
            hasError = true
            return {
              newPaymentPlan,
              isError: hasError,
            }
          }
        }
      }
    }
  }
  if (typeof value === 'string') {
    newPaymentPlan.E_system_paymentPlan_摘要 = value
  }
  newPaymentPlan.children = needMergeList.map(key => ({
    dataLink: dataMap[key],
    pId: newPaymentPlan.id,
  }))
  return {
    newPaymentPlan,
    isError: hasError,
  }
}

export const assembleTableData = ({ remarkData, isByDetail, _flowIds }) => {
  let { viewDataList, originalList } = remarkData.value
  let { data = [] } = viewDataList
  // if (isByDetail) {
  //   // 明细维度进入调整支付计划时，仅已选中的支付计划可见
  //   const visiblePayPlanIds = _flowIds.map(el => el.id)
  //   data = data.filter(el => visiblePayPlanIds.includes(el.dataLink?.id))
  //   originalList = originalList.filter(el => visiblePayPlanIds.includes(el.id))
  // }
  const _dataMap = convertToKeyVal(
    data.map(i => ({ ...i, id: i.dataLink.id })),
    'id',
  )
  const _newData = []
  const hasPNodeId = []
  data.forEach(item => {
    const ids = item.dataLink.id.split(',')
    if (ids.length > 1) {
      const _children = ids.map(id => ({
        ..._dataMap[id],
        pId: item.dataLink.id,
      }))
      _newData.push({
        ...item,
        dataLink: {
          ...item.dataLink,
          E_system_paymentPlan_提交人: _children
            .map(i => i.dataLink.E_system_paymentPlan_提交人)
            .reduce(modeOfOperation.E_system_paymentPlan_提交人),
          children: _children,
        },
      })
      hasPNodeId.push(...ids)
    } else {
      _newData.push(item)
    }
  })
  return {
    originalListMap: convertToKeyVal(originalList, 'id'),
    baseData: data,
    data: _newData.filter(i => !hasPNodeId.includes(i.dataLink.id)),
  }
}

// 获取支付计划合并方式选项
export const getConfigOptions = () => [
  {
    key: 'currency',
    label: i18n.get('币种'),
    disabled: true,
  },
  {
    key: 'payeeInfo',
    label: i18n.get('收款信息'),
    disabled: true,
  },
  {
    key: 'legalEntity',
    label: i18n.get('法人实体'),
    disabled: true,
  },
  {
    key: 'billNo',
    label: i18n.get('单号'),
  },
]
