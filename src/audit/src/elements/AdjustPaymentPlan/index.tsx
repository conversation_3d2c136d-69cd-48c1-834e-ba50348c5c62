import React, { useEffect, useState, useMemo, FC } from 'react'
import { Spin } from 'antd'
import MessageCenter from '@ekuaibao/messagecenter'
import { EnhanceConnect } from '@ekuaibao/store'
import './style.less'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, Do<PERSON>ontainer } from './components'
import { AdjustPaymentPlanProps } from './type'
import { assembleData } from './utils'
import { getPayPlanScenes } from '../../util/fetchUtil'
import { getPaymentPlan } from '../../audit-action'
const scenesType = 'ADJUST_PAY_PLAN'

const AdjustPaymentPlan: FC<AdjustPaymentPlanProps> = ({
  layer,
  dynamicChannelMap,
  adjustPaymentPlanData,
  flowIds,
  getPayRemark,
  channel,
  accountId,
  isByDetail,
}) => {
  const [loading, setLoading] = useState(true)
  const [originalData, setOriginalData] = useState([])
  const [baseData, setBaseData] = useState([])
  const [data, setData] = useState([])
  const [originalListMap, setOriginalListMap] = useState({})
  const [columns, setColumns] = useState([])
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [allColumns, setAllColumns] = useState([])
  const [originalList, setOriginalList] = useState([])

  const bus = useMemo(() => new MessageCenter(), [])
  useEffect(() => {
    const isBeAdjustment = 'data' in adjustPaymentPlanData && 'columns' in adjustPaymentPlanData

    if (isBeAdjustment) {
      const { data, baseData, columns, allColumns, originalListMap, originalData, originalList } =
        adjustPaymentPlanData
      setData(data)
      setBaseData(baseData)
      setOriginalData(originalData)
      setColumns(columns)
      setAllColumns(allColumns)
      setOriginalList(originalList)
      setOriginalListMap(originalListMap)
      setLoading(false)
    } else {
      const _flowIds = Array.isArray(flowIds) ? flowIds : [flowIds]
      const flowIdsArr = isByDetail
        ? Array.from(new Set(_flowIds.map(i => i.flowId.id)))
        : _flowIds.map(i => i.flowId.id)

      const { remark = '', dimensionItemId = '', autoSummary } = getPayRemark()
      const params = {
        flowIds: flowIdsArr,
        remark,
        dimensionItemId,
        autoSummary,
        channel,
        accountId,
      }
      Promise.all([getPayPlanScenes(scenesType), getPaymentPlan(params)])
        .then(results => {
          const [columnsFromScenes, response] = results
          //组装表格数据
          assembleData({
            bus,
            _flowIds,
            isByDetail,
            response,
            dynamicChannelMap,
            columnsFromScenes,
            setData,
            setColumns,
            setBaseData,
            setAllColumns,
            setOriginalList,
            setOriginalData,
            setOriginalListMap,
          })().then(() => {
            setLoading(false)
          })
        })
        .catch(err => {
          setLoading(false)
        })
    }
  }, [])

  const closeHandle = () => layer && layer.emitCancel()

  return (
    <div className="adjust-payment-plan">
      {loading ? (
        <div className="lock">
          <Spin />
        </div>
      ) : null}
      <div className="inner">
        <DoHeader closeHandle={closeHandle} title={i18n.get('调整支付计划')} />
        <DoContainer
          bus={bus}
          data={data}
          baseColumns={columns}
          allColumns={allColumns}
          selectedRowKeys={selectedRowKeys}
          overRideColumns={setColumns}
          setSelectedRowKeys={setSelectedRowKeys}
        />
        <DoFooter
          bus={bus}
          data={data}
          layer={layer}
          columns={columns}
          allColumns={allColumns}
          baseData={baseData}
          originalData={originalData}
          originalList={originalList}
          originalListMap={originalListMap}
          selectedRowKeys={selectedRowKeys}
          setData={setData}
          setBaseData={setBaseData}
          getPayRemark={getPayRemark}
          setSelectedRowKeys={setSelectedRowKeys}
          setOriginalListMap={setOriginalListMap}
        />
      </div>
    </div>
  )
}

export default EnhanceConnect((state: any) => ({
  dynamicChannelMap: state['@audit'].dynamicChannelMap,
}))(AdjustPaymentPlan)
