import { app } from '@ekuaibao/whispered';
import React from 'react';
import { IHeader } from '../../type';

const EKBIcon: any = app.require('@elements/ekbIcon');

const DoHeader = ({closeHandle, title}: IHeader) => {
    return <div className="modal-header">
        <div className="modal-header-title">{title}</div>
        <EKBIcon className="cross-icon" name="#EDico-close-default" onClick={closeHandle} data-testid="pay-customPaymentBranch-close-btn" />
    </div>
}

export default DoHeader;