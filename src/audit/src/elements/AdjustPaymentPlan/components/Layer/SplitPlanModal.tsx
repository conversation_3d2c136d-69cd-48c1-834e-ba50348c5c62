import { app } from '@ekuaibao/whispered'
import React, { FC, useState } from 'react'
import { Button, InputNumber, Table, TableColumnsType } from '@hose/eui'
import { uniqueId } from 'lodash'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { showMessage } from '@ekuaibao/show-util'
import styles from './SplitPlanModal.module.less'
import { OutlinedTipsAdd, OutlinedTipsClose } from '@hose/eui-icons'
const Money: any = app.require('@elements/puppet/Money')
import { MoneyIF } from '@ekuaibao/ekuaibao_types'
import { LayerDef } from '@ekuaibao/enhance-layer-manager/src/EnhanceLayerBase'
interface ISplitPlan {
  key: string | number
  rest?: boolean
  splitPaymentMoney: number | undefined
}

interface ISplitPlanModalProps {
  layer: LayerDef
  money: MoneyIF
}

const SplitPlanModal: FC<ISplitPlanModalProps> = ({ layer, money }) => {
  const [underTest, setUnderTest] = useState<boolean>(false)
  const [splitPlanData, setSplitPlanData] = useState<ISplitPlan[]>([
    { key: uniqueId(), splitPaymentMoney: undefined },
    {
      key: uniqueId(),
      rest: true,
      splitPaymentMoney: Number(money?.standard),
    },
  ])

  const restSplitPlan = splitPlanData.find(plan => plan.rest)

  const handleConfirm = async () => {
    if (splitPlanData.length <= 1) {
      layer.emitCancel()
      return
    }

    setUnderTest(true)

    const sData = splitPlanData.filter(d => !d.rest)
    if (sData.some(plan => !plan.splitPaymentMoney)) return

    if (restSplitPlan?.splitPaymentMoney! <= 0) {
      showMessage.error(i18n.get('剩余可拆分金额必须大于0'))
      return
    }

    layer.emitOk({ splitPlanData })
  }

  const handleModalClose = () => {
    layer.emitCancel()
  }

  const handleAddSplitPlan = () => {
    setUnderTest(false)
    splitPlanData.unshift({ key: uniqueId(), splitPaymentMoney: undefined })
    // @ts-ignore
    setSplitPlanData([...splitPlanData])
  }

  const handleDelSplitPlan = (index: number) => {
    splitPlanData.splice(index, 1)
    calSplitPlanData()
  }

  const calSplitPlanData = () => {
    const allSplitPaymentMoney = splitPlanData.reduce((prev: number, next: ISplitPlan) => {
      if (!next.rest) prev += next.splitPaymentMoney ?? 0
      return prev
    }, 0)
    restSplitPlan!.splitPaymentMoney = Number(
      (Number(money?.standard) - allSplitPaymentMoney).toFixed(2),
    )

    setSplitPlanData([...splitPlanData])
  }

  const onBlur = () => {
    calSplitPlanData()
  }

  const columns: TableColumnsType<ISplitPlan> = [
    {
      key: 'index',
      title: i18n.get('序号'),
      width: 150,
      dataIndex: 'index',
      render: (_text, _record, index) => index + 1,
    },
    {
      key: 'splitPaymentMoney',
      title: (
        <div className="money-title">
          <span className="money-title-require">*</span>
          {i18n.get('新支付金额')}
        </div>
      ),
      dataIndex: 'splitPaymentMoney',
      render: (splitPaymentMoney, record) => (
        <div>
          <InputNumber
            style={{ width: '70%' }}
            min={record?.rest ? undefined : 0.01}
            precision={Number(money?.standardScale) || 2}
            controls={false}
            value={splitPaymentMoney}
            disabled={record?.rest}
            placeholder={i18n.get('请输入新的金额')}
            onBlur={onBlur}
            onChange={(value: any) => (record.splitPaymentMoney = value)}
          />
          {!record.splitPaymentMoney && underTest && !record?.rest && (
            <p className="money-require">{i18n.get('此项是必填项')}</p>
          )}
        </div>
      ),
    },
    {
      key: 'options',
      title: i18n.get('操作'),
      width: 200,
      dataIndex: 'options',
      render: (_, record, index) => {
        if (record?.rest) return
        return (
          <a onClick={() => handleDelSplitPlan(index)} style={{ color: '#f17b7b' }} data-testid={`pay-customPaymentBranch-deleteSplit-${index}-btn`}>
            {i18n.get('删除')}
          </a>
        )
      },
    },
  ]

  return (
    <div className={styles['split-plan-modal']}>
      <div className="modal-header">
        <div>{i18n.get('拆分')}</div>
        <OutlinedTipsClose className="cross-icon" onClick={handleModalClose} data-testid="pay-customPaymentBranch-close-btn" />
      </div>

      <div className="model-content">
        <div className="split-money-display mb-12">
          <span className="mr-40">
            {i18n.get('可拆分总金额：')}
            <Money style={{ display: 'inline-block' }} value={money} />
          </span>
        </div>
        <Table dataSource={splitPlanData} columns={columns} pagination={false} />
        <Button
          icon={<OutlinedTipsAdd />}
          category="ghost"
          theme="highlight"
          onClick={handleAddSplitPlan}
          disabled={splitPlanData.length >= 12}
          className="mt-24"
          data-testid="pay-customPaymentBranch-addSplit-btn">
          {i18n.get('添加拆分金额')}
        </Button>
      </div>

      <div className="modal-footer">
        <Button category="secondary" className="mr-8" onClick={handleModalClose} data-testid="pay-customPaymentBranch-cancel-btn">
          {i18n.get('取消')}
        </Button>
        <Button category="primary" onClick={handleConfirm} data-testid="pay-customPaymentBranch-confirm-btn">
          {i18n.get('确定')}
        </Button>
      </div>
    </div>
  )
}

export default EnhanceModal({
  title: '',
  footer: [],
  className: 'respond-modal-layer',
})(SplitPlanModal)
