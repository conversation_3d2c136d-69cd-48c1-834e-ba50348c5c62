import { Table } from '@hose/eui'
import React, { useState, useEffect } from 'react'

const AutoMergeTable = (props: any) => {
  const { dataSource, columns } = props
  const [columnList, setColumnList] = useState([])

  useEffect(() => {
    const columnList = columns?.map((item: any) => {
      const i = item?.dataIndex?.split('.')
      return { ...item, dataIndex: i, sorter: false, filters: false }
    })
    setColumnList(columnList)
  }, [])

  return <Table columns={columnList} dataSource={dataSource?.children || []} bordered />
}

export default AutoMergeTable
