@import '~@ekuaibao/eui-styles/less/token';

.layer-container{
    .modal-header {
        flex-shrink: 0;
        border-radius: 8px;
        &-title{
            flex: 1;
            .font-size-5;
            color:var(--eui-text-title);
        }
    }
    .export-excel-container{
        height: 164px;
        padding: @space-7 @space-8 0;
        .tip{
            color: @color-black-4
        }
        .line{
            margin: @space-7 0;
            height: 1px;
            background: @mask-black;
        }
    }
    .merger-plan-container {
        padding: @space-5 @space-8 @space-5;
        .merger-plan-container-count{
            display: flex;
            justify-content: space-between;
            padding: 5px;
        }
    }  
}
.auto-merger-plan-model{
  display: flex;
  flex-direction: column;
  height: 100%;
  .modal-header {
    flex-shrink: 0;
  }
  .model-content {
    padding: @space-7;
    height: calc(100% - 240px);
    .auto-merger-plan-model-label {
      .font-size-2;
      color:var(--eui-text-title);
      margin-bottom: @space-4;
    }
    .check-all {
      margin-left: @space-4;
      margin-bottom: @space-7;
      >span{
        cursor: pointer;
        font-size: 14px;
        margin-left: @space-3;
      }
    }
    .check-item{
      overflow-y: scroll;
      height: 100%;
    }
    .payer-item {
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .ant-checkbox-wrapper {
        margin:0 @space-6 0 @space-4;
      }

      .payee-account-card {
        flex: 1;
      }
    }
    .merge-tab{
      margin-left: @space-10;
      margin-bottom: @space-7;
      .merge-show-col{
        margin-bottom: @space-4;
        >span{
          cursor: pointer;
          margin-right: @space-2;
          font-size: 16px;
          color: #999;
        }
        
      }
    }

  }

  .modal-footer {
    flex-shrink: 0;
    background-color: @color-white-1;
  }

  .select-payment-modal-header-title {
    flex: 1;
    .font-size-5;
    color:var(--eui-text-title);
  }
}