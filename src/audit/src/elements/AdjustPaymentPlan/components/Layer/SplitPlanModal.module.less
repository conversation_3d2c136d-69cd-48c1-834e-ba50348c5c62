.split-plan-modal {
  height: calc(100% - 56px);
  :global {
    .modal-header {
      justify-content: space-between;
    }

    a {
      text-decoration: none;
    }

    .model-content {
      display: flex;
      flex-direction: column;
      padding: 0 24px;
      padding-bottom: 40px;
      overflow-y: auto;
      height: calc(100% - 62px);
    }

    .money-title {
      display: flex;
      align-items: center;
      .money-title-require {
        margin-right: 5px;
        color: red;
        height: 18px;
        font-size: 15px;
      }
    }

    .money-require {
      margin: 0;
      margin-top: 8px;
      color: red;
    }

    .ant-table-wrapper {
      flex: 1;
      height: 100%;
      overflow: auto
    }

    .ant-input-number {
      width: 80%;
    }

    .split-money-display {
      > span {
        font-size: 16px;
      }
    }
  }
}