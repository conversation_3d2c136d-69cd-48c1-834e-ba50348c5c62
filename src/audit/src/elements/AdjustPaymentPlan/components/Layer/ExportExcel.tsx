import { app as api } from '@ekuaibao/whispered'
import React, { Fragment, useState } from 'react';
import { Button, Radio, Checkbox  } from '@hose/eui';
import DoHeader from '../DoHeader';
import { Fetch } from '@ekuaibao/fetch';
import { saveTempDataForExport } from '../../../../audit-action';

const ExportExcel: React.FC<any> = ({layer, data, getData}) => {

    const [ exportFullField, setExportFullField ] = useState(true);
    const [ containDetail, setcontainDetail ] = useState(false);

    const getConfig = () => {

        return {
            exportFullField,
            containDetail
        }
    }

    return <Fragment>
        <div className='export-excel-container'>
            <div className='mb-15 tip'>{i18n.get('请选择需导出的支付计划字段')}</div>
            <Radio.Group onChange={(e) => setExportFullField(e.target.value)} value={exportFullField} data-testid="pay-customPaymentBranch-exportField-radioGroup">
                <Radio value={true} data-testid="pay-customPaymentBranch-exportAllField-radio">{i18n.get('导出全部字段')}</Radio>
                <Radio value={false} data-testid="pay-customPaymentBranch-exportSelectedField-radio">{i18n.get('仅导出列选已选中的字段')}</Radio>
            </Radio.Group>
            <div className='line'></div>
            <Checkbox 
                checked={containDetail}
                onChange={(e) => setcontainDetail(e.target.checked)}
                data-testid="pay-customPaymentBranch-includeDetail-checkbox"
            >
                {i18n.get('包括合并支付计划后的子支付计划明细')}
            </Checkbox>
        </div>
        <div className="modal-footer">
            <ExportExcelFooter 
                layer={layer} 
                getConfig={getConfig} 
                data={data} 
                getData={getData}
            />
        </div>
    </Fragment>
}

export default ExportExcel;


//========================= 副作用Header && Footer ================================

const ExportExcelHeader = ({ layer }: any) => {

    const closeHandle = () => {
        layer && layer.emitCancel()
    }

    return <DoHeader closeHandle={closeHandle} title={i18n.get('请选择')}/>
}

const ExportExcelFooter = ({layer, getConfig, data, getData}: any) => {

    const closeHandle = () => {
        layer && layer.emitCancel()
    }

    const okHandle = () => {
        saveTempDataForExport(getData()).then(({value}) => {
            let ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
            let url = `${Fetch.fixOrigin(
                location.origin
            )}/api/pay/v2/paymentPlans/exportPaymentPlan?corpId=${ekbCorpId}`
            const params = {
                key: value,
                ...getConfig()
            }
            Object.keys(params).forEach(key => {
                url += `&${key}=${params[key]}`
            })
            api.emit('@vendor:download', url);
            layer && layer.emitOk();
        })    
    }

    return <Fragment>
        <Button category="secondary" onClick={closeHandle} className="mr-8" data-testid="pay-customPaymentBranch-cancel-btn">
            {i18n.get('取消')}
        </Button>
        <Button onClick={okHandle} data-testid="pay-customPaymentBranch-export-btn">
            {i18n.get('导出')}
        </Button>
    </Fragment>
}

export { 
    ExportExcelHeader as header,
    //ExportExcelFooter as footer
}