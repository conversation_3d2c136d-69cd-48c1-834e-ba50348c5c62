import React, { Fragment, useState } from 'react'
import { Button,Input } from '@hose/eui'
import DoHeader from '../DoHeader'

const MergerPlan: React.FC<any> = ({ layer, otherProps }) => {
  const [input, setInput] = useState('')

  const { remarkLimit } = otherProps

  return (
    <Fragment>
      <div className="merger-plan-container">
        <div className="mb-15">{i18n.get('请手动输入合并后的支付摘要内容')}</div>
        <Input.TextArea
          rows={4}
          placeholder={i18n.get('（选填）请填写摘要内容，不超过{__k0}个汉字', {
            __k0: remarkLimit,
          })}
          value={input}
          onChange={e => setInput(e.target.value)}
          maxLength={remarkLimit}
          data-testid="pay-customPaymentBranch-remark-input"
        />
        <div className="merger-plan-container-count">
          <div>{i18n.get('注意：以上内容将会被锁定在合并的支付计划上')}</div>
          <div>
            {input.length}/{remarkLimit}
          </div>
        </div>
      </div>
      <div className="modal-footer">
        <MergerPlanFooter layer={layer} input={input} />
      </div>
    </Fragment>
  )
}

export default MergerPlan

//========================= 副作用Header && Footer ================================

const MergerPlanHeader = ({ layer }: any) => {
  const closeHandle = () => {
    layer && layer.emitCancel()
  }

  return <DoHeader closeHandle={closeHandle} title={i18n.get('填写支付摘要')} />
}

const MergerPlanFooter = ({ layer, input }: any) => {
  const closeHandle = () => {
    layer && layer.emitCancel()
  }

  const okHandle = () => {
    layer &&
      layer.emitOk({
        value: input,
      })
  }

  return (
    <Fragment>
      <Button category="secondary" onClick={closeHandle} className="mr-8" data-testid="pay-customPaymentBranch-cancel-btn">
        {i18n.get('取消')}
      </Button>
      <Button onClick={okHandle} data-testid="pay-customPaymentBranch-confirm-btn">{i18n.get('确定')}</Button>
    </Fragment>
  )
}

export { MergerPlanHeader as header }
