import React from 'react'
import { app } from '@ekuaibao/whispered'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Icon } from 'antd'
import { Button, Checkbox, Select } from '@hose/eui'
import * as DataGrid from '@ekuaibao/datagrid'
import { convertToKeyVal, getConfigOptions, mergeRebase } from '../../utils'
import { cloneDeep, omit } from 'lodash'
const EKBIcon: any = app.require('@elements/ekbIcon')
const { TableWrapper } = DataGrid
const Option = Select.Option
import './style.less'
import { getV } from '@ekuaibao/lib/lib/help'
import { uuid } from '@ekuaibao/helpers'
import AutoMergeTable from './AutoMergeTable'

interface Props {
  layer?: any
  newData: Array<any>
  columns: any
}
interface State {
  configPage: boolean
  mergedPayPlanList: any
  dataSourceList: any
  selecterList: any
  tableList: any
  isSelectAll: boolean
  allData: Array<any>
  configOptions: any[]
  configValue: string[]
  disabledValues: any
}

@EnhanceModal({
  title: '',
  footer: [],
  className: 'respond-modal-layer',
})
export default class AutoMergerPlanModel extends React.PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      configPage: true,
      mergedPayPlanList: {},
      dataSourceList: {},
      selecterList: {},
      tableList: {},
      isSelectAll: true,
      allData: [],
      configOptions: getConfigOptions(),
      configValue: ['currency', 'payeeInfo', 'legalEntity'],
      disabledValues: {},
    }
  }

  handleModalClose = () => {
    this.props.layer.emitCancel()
  }
  handleModalSave = () => {
    const { dataSourceList, selecterList, tableList } = this.state
    if (Object.keys(selecterList).length === 0) {
      this.props.layer.emitCancel()
      return
    }
    let data = this.mergeOneGroupHandle(dataSourceList, tableList, Object.keys(selecterList))
    this.props.layer.emitOk(data)
  }
  /**
   * @name  分离已合并的子项，解决重复合并问题
   * @param {Array} data   //合并的数据
   * @returns {Array} arr //原始数据
   */
  checkChildren(data: Array<any>) {
    let arr: Array<any> = []
    data.forEach((item: any) => {
      if (item.dataLink?.children) {
        arr = arr.concat(item.dataLink.children)
        return
      }
      arr.push(item)
    })
    return arr
  }
  init(data: Array<any>) {
    const dataMap: any = convertToKeyVal(
      data.map((i: any) => ({ ...i.dataLink })),
      'id',
    )
    this.setState(this.filterListByKey(data, dataMap, 'E_system_paymentPlan_收款信息'))
  }

  getFilterId = (dataMap: any, str: string) => {
    const { configValue } = this.state
    const dataSource = dataMap[str]
    const _configValue = [...configValue, 'E_system_paymentPlan_收款币金额']
    let id = ''
    _configValue.forEach((key: string) => {
      switch (key) {
        case 'currency': {
          id += dataSource['E_system_paymentPlan_币种'] + ':'
          break
        }
        case 'payeeInfo': {
          // 开户【收款信息非必填】功能后，允许支付金额为0的单据不填写收款信息
          // 没有收款信息时，不允许合并支付计划；通过生成8位随机码让id成为唯一值
          const payeeInfoValue = dataSource['E_system_paymentPlan_收款信息']?.id || uuid(8)
          id += payeeInfoValue + ':'
          break
        }
        case 'billNo': {
          id += dataSource['E_system_paymentPlan_flowId'] + ':'
          break
        }
        case 'legalEntity': {
          const legalEntityId = dataSource['E_system_paymentPlan_legalEntity']?.id || ''
          id += legalEntityId + ':'
          break
        }
        case 'E_system_paymentPlan_收款币金额': {
          id += dataSource['E_system_paymentPlan_收款币金额']?.standardNumCode + ':'
          break
        }
      }
    })
    return id
  }
  /**
   * @name  根据收款信息分类
   * @param {Array} data   //原始数据
   * @param {Object} dataMap   //转成对象的数据
   * @param {String} key    //根据哪个字段分类
   * @returns {Object}
   *  dataSourceList:分类后的dataMap,
   *  tableList：表格显示的原始数据,
   *  mergedPayPlanList：合并后的支付计划列表,
   *  selecterList：选中状态,
   *  allData: 传入的所有数据<Array>
   */
  filterListByKey(data: any, dataMap: any, key: string) {
    let dataSourceList: any = {},
      mergedPayPlanList: any = {},
      tableList: any = {}
    for (const str in dataMap) {
      if (Object.prototype.hasOwnProperty.call(dataMap, str)) {
        const id: string = this.getFilterId(dataMap, str)
        const payPlan = data.find((i: any) => i.dataLink.id === str)
        if (id && dataSourceList[id]) {
          dataSourceList[id][str] = dataMap[str]
          tableList[id].children.push(payPlan)
        } else if (id) {
          dataSourceList[id] = {
            [str]: dataMap[str],
          }
          tableList[id] = {
            children: [payPlan],
            showCol: false,
          }
          mergedPayPlanList[id] = dataMap[str]
        }
      }
    }
    let selecterList = {}
    for (const item in dataSourceList) {
      const length = tableList?.[item]?.children?.length
      if (length <= 500) {
        selecterList = { ...selecterList, [item]: [mergedPayPlanList?.[item]] }
      }
    }
    const isSelectAll = Object.keys(mergedPayPlanList).length === Object.keys(selecterList).length
    const disabledValues = omit(dataSourceList, Object.keys(selecterList))
    return {
      dataSourceList,
      tableList,
      mergedPayPlanList,
      selecterList,
      allData: data,
      isSelectAll,
      disabledValues,
    }
  }
  /**
   * 银行单选
   * @param {Object} data   //银行信息
   * @param {String} key    //选中的银行id
   */
  selectMergedPayPlan(data: any, key: string) {
    let { selecterList, mergedPayPlanList } = this.state
    if (selecterList[key]) {
      delete selecterList[key]
    } else {
      selecterList[key] = data
    }
    const isSelectAll = Object.keys(mergedPayPlanList).length === Object.keys(selecterList).length
    this.setState({ selecterList, isSelectAll })
    this.forceUpdate()
  }
  /**
   * 全并后的支付计划列表 全选
   */
  selectAllBank() {
    let { mergedPayPlanList, isSelectAll } = this.state
    let selecterList = []
    if (!isSelectAll) {
      selecterList = cloneDeep(mergedPayPlanList)
    }
    isSelectAll = !isSelectAll
    this.setState({
      isSelectAll,
      selecterList,
    })
  }
  /**
   * 表格显示隐藏
   * @param {String} key    //选中的银行id
   * @param {Boolean} showCol //当前状态
   */
  handlerShowCol(key: string, showCol: boolean) {
    let { tableList } = this.state
    tableList[key].showCol = !showCol
    this.setState({ tableList })
    this.forceUpdate()
  }
  /**
   * 合并总数据,转换成原始数据相同的数据结构
   * @param {Object} dataSourceList    //分类后的dataMap
   * @param {Object} tableList //表格显示的原始数据
   * @param {Array} selecterList //选中的已合并支付计划id
   * @returns {Array} list //原始数据
   */
  mergeOneGroupHandle(dataSourceList: any, tableList: any, selecterList: Array<string>) {
    const baseData = Object.keys(dataSourceList)
    let list: any = []
    for (let item of baseData) {
      if (selecterList.includes(item)) {
        const keys = Object.keys(dataSourceList[item])
        if (keys.length <= 1) {
          list = list.concat(tableList[item]?.children)
          continue
        }
        list.push({
          dataLink: mergeRebase(dataSourceList[item], keys, null)?.newPaymentPlan,
        })
        continue
      }
      list = list.concat(tableList[item]?.children)
    }
    return list
  }

  renderSelect = (data: any, key: string) => {
    const { selecterList, configValue, disabledValues } = this.state
    const pathMap: { [keyName: string]: string } = {
      currency: 'dataLink.E_system_paymentPlan_币种',
      payeeInfo: 'dataLink.E_system_paymentPlan_收款信息.name',
      billNo: 'dataLink.E_system_paymentPlan_name',
      legalEntity: 'dataLink.E_system_paymentPlan_legalEntity.name',
    }
    const dataSource: any = getV(data, 'children[0]', {})
    const options = configValue.map((configkey: string) => ({
      key: configkey,
      label: getV(dataSource, pathMap[configkey], ''),
    }))

    // 没有找到tag对应的值时，将tag过滤掉
    let selectValue = configValue
    if (!getV(dataSource, 'dataLink.E_system_paymentPlan_legalEntity')) {
      selectValue = selectValue.filter(id => id !== 'legalEntity')
    }
    if (!getV(dataSource, 'dataLink.E_system_paymentPlan_收款信息')) {
      selectValue = selectValue.filter(id => id !== 'payeeInfo')
    }
    return (
      <div className="payer-item">
        <Checkbox
            key={key}
            checked={!!selecterList[key]}
            disabled={!!disabledValues[key]}
            onClick={this.selectMergedPayPlan.bind(this, data, key)}
            data-testid={`pay-customPaymentBranch-select-${key}-btn`}
          />
        <Select
          mode="multiple"
          disabled={true}
          size="large"
          onChange={this.handleConfigOnChange}
          value={selectValue}
          style={{ width: '100%', marginLeft: 8 }}
        >
          {options.map(el => (
            <Option key={el.key}>{el.label}</Option>
          ))}
        </Select>
      </div>
    )
  }

  renderBankSelecter = () => {
    const { mergedPayPlanList, tableList, isSelectAll, configPage, selecterList } = this.state
    if (configPage) return null
    const moreThan50Item = Object.values(tableList)?.find((i: any) => i?.children?.length > 500)
    const list = Object.keys(mergedPayPlanList)
    return (
      <>
        <div className="check-all">
          <Checkbox checked={isSelectAll} disabled={!isSelectAll} onClick={this.selectAllBank.bind(this)} data-testid="pay-customPaymentBranch-selectAll-btn" />
          {moreThan50Item ? (
            <span>
              {i18n.get(`全选（已选${Object.keys(selecterList)?.length}/${list?.length}笔，单笔超过500条时不可选）`)}
            </span>
          ) : (
            <span>{i18n.get(`全选（已选${Object.keys(selecterList)?.length}/${list?.length}笔）`)}</span>
          )}
        </div>
        <div className="check-item">
          {list.map((item: string) => {
            return (
              <>
                {this.renderSelect(tableList[item], item)}
                {this.renderTable(tableList[item], item)}
              </>
            )
          })}
        </div>
      </>
    )
  }

  renderTable(data: any, key: string) {
    const { columns } = this.props
    return (
      <div className="merge-tab" key={key}>
        <div className="merge-show-col" onClick={this.handlerShowCol.bind(this, key, data.showCol)} data-testid={`pay-customPaymentBranch-toggleTable-${key}-btn`}>
          <span>
            {i18n.get('合并{__pay}条计划', {
              __pay: data.children.length || 0,
            })}
          </span>
          <Icon type={data.showCol ? 'caret-down' : 'caret-right'} />
        </div>
        {data.showCol && (
          <AutoMergeTable key={key} dataSource={data} columns={columns} />
        )}
      </div>
    )
  }

  renderHeader = () => {
    return (
      <div className="modal-header">
        <div className="select-payment-modal-header-title">{i18n.get('合并确认')}</div>
        <EKBIcon className="cross-icon" name="#EDico-close-default" onClick={this.handleModalClose.bind(this)} data-testid="pay-customPaymentBranch-close-btn" />
      </div>
    )
  }

  handleInitPayPlanGroup = () => {
    this.init(this.checkChildren(this.props.newData))
    this.setState({ configPage: false })
  }

  renderConfirmBtn = () => {
    let label: string, fn: () => void
    if (this.state.configPage) {
      label = i18n.get('下一步')
      fn = this.handleInitPayPlanGroup
    } else {
      label = i18n.get('确定')
      fn = this.handleModalSave
    }
    return <Button onClick={fn} data-testid="pay-customPaymentBranch-confirm-btn">{label}</Button>
  }

  renderCancelBtn = () => {
    let label: string, fn: () => void
    if (this.state.configPage) {
      label = i18n.get('取消')
      fn = this.handleModalClose
    } else {
      label = i18n.get('上一步')
      fn = () => this.setState({ configPage: true })
    }
    return (
      <Button category="secondary" className="mr-8" onClick={fn} data-testid="pay-customPaymentBranch-cancel-btn">
          {label}
        </Button>
    )
  }

  renderFooter = () => {
    return (
      <div className="modal-footer">
        {this.renderCancelBtn()}
        {this.renderConfirmBtn()}
      </div>
    )
  }

  handleConfigOnChange = (configValue: string[]) => {
    this.setState({ configValue })
  }

  renderConfigPage = () => {
    const { configValue, configOptions, configPage } = this.state
    return (
      <div>
        <p className="auto-merger-plan-model-label">{i18n.get('合并条件（当满足以下字段值相同时）')}</p>
        <Select
          mode="multiple"
          className="mb-24"
          disabled={!configPage}
          size="large"
          onChange={this.handleConfigOnChange}
          value={configValue}
          style={{ width: '100%' }}
        >
          {configOptions.map(el => (
            <Option key={el.key} disabled={el.disabled}>
              {el.label}
            </Option>
          ))}
        </Select>
      </div>
    )
  }

  render() {
    return (
      <div className="auto-merger-plan-model">
        {this.renderHeader()}
        <div className="model-content">
          {this.renderConfigPage()}
          {this.renderBankSelecter()}
        </div>
        {this.renderFooter()}
      </div>
    )
  }
}
