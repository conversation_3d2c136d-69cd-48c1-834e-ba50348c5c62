import React, { forwardRef } from 'react';
import './style.less';
import * as ExportExcel from './ExportExcel';
import * as MergerPlan from './MergerPlan';
interface IProps {
    type: string
    layer: any
    data?: any[]
    getData?: () => any
    otherProps: any
}

const cMap: {
    [key: string]: any
} = {
    exportExcel: ExportExcel,
    mergerPlan: MergerPlan
}

const LayerContainer = ({type, layer, data=[], getData, otherProps={} }: IProps, ref: any) => {

    const { 
        default: CChildren, 
        header: CHeader, 
        footer: CFooter 
    } = cMap[type]

    return <div className='layer-container'>
        <CHeader layer={layer}/>
        <CChildren layer={layer} data={data} getData={getData} otherProps={otherProps}/>
        {
            CFooter 
            ? <div className="modal-footer">
                <CFooter layer={layer}/>
            </div>
            : null
        }
        
    </div>
}

export default forwardRef<HTMLButtonElement, IProps>(LayerContainer);