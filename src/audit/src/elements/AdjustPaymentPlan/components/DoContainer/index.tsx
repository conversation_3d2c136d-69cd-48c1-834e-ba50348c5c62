import React, { useState, useEffect, useMemo } from 'react'
import * as DataGrid from '@ekuaibao/datagrid'
import { Provider } from '@ekuaibao/datagrid/esm/State'
import { Language } from '@ekuaibao/datagrid/esm/i18n/Language'
import { i18nText } from './lang'
import { getColumns } from './columns'
import { columnFilterStringify, isRenderDom, sortColumns } from '../../utils'
import { savePayPlanScenes } from '../../../../util/fetchUtil'

const scenesType = 'ADJUST_PAY_PLAN'

interface IProps {
  data: any[]
  baseColumns: any[]
  selectedRowKeys: any[]
  setSelectedRowKeys: any
  bus: any
  overRideColumns: any
  allColumns: any[]
}

const DoContainer: React.FC<IProps> = ({
  data,
  baseColumns,
  selectedRowKeys,
  setSelectedRowKeys,
  bus,
  overRideColumns,
  allColumns,
}) => {
  const [instance, setInstance] = useState(null)
  const [columns, setColumns] = useState([])
  const [isVisibleSaveDiff, setIsVisibleSaveDiff] = useState(false)
  const [showColumnChooser, setShowColumnChooser] = useState(true)
  const [search, setSearch] = useState({
    isSearch: false,
    filterValue: '',
  })

  const _getColumns = (isShowColl: boolean) => getColumns(isShowColl, instance, baseColumns, bus)

  useEffect(() => {
    // 获取到基础数据以及表格实体后重新拼装columns
    if (Boolean(instance)) {
      setTimeout(() => {
        const _: any = _getColumns(true)
        setColumns(_)
      }, 100)
    }
  }, [instance, baseColumns])

  const handleSavePayPlanScenes = (sortArr: any[], filter: any) => {
    savePayPlanScenes({
      type: scenesType,
      filter: [JSON.stringify(filter)],
    }).then(_ => {
      setTableColumns(sortArr, false)
    })
  }

  const setTableColumns = (sortArr: any[], isVisibleSaveDiff: boolean) => {
    overRideColumns(sortArr)
    setIsVisibleSaveDiff(isVisibleSaveDiff)
    setShowColumnChooser(false)
    setTimeout(() => setShowColumnChooser(true))
  }

  /**
   * @param {boolean} saveScence 是否需要将scence保存至后端
   */
  const sortAndSaveColumns = (sortArr: any[], saveScence: boolean) => {
    //根据当前列的数据做排序
    const arr = sortColumns(sortArr, allColumns)
    const scenceData = columnFilterStringify(arr)
    const filter = {
      scene: 'all',
      defaultColumns: JSON.stringify(scenceData),
    }

    if (saveScence) {
      handleSavePayPlanScenes(arr, filter)
    } else {
      setTableColumns(arr, true)
    }
  }

  const onSaveDiffScenes = () => {
    //获取当前列的排序数据
    const currentColumns = instance.getVisibleColumns() || []
    const filterCurrentColumns = currentColumns.map((el: any) => el.dataField)

    //根据当前列的数据做排序, 保存至后端
    sortAndSaveColumns(filterCurrentColumns, true)
  }

  const filterBySearch = (data: any[], value: string) => {
    const arr = data.filter((el: any) => {
      let includeSearchValue = false
      const { E_system_paymentPlan_编号, E_system_paymentPlan_支付概要, E_system_paymentPlan_提交人 } = el.dataLink
      if (
        E_system_paymentPlan_编号.includes(value) ||
        E_system_paymentPlan_支付概要.includes(value) ||
        E_system_paymentPlan_提交人.name.includes(value)
      ) {
        includeSearchValue = true
      }
      return includeSearchValue
    }) as any

    return arr
  }

  const handleSearch = (value: string) => {
    if (value) {
      setSearch({
        isSearch: true,
        filterValue: value,
      })
    } else {
      setSearch({
        isSearch: false,
        filterValue: '',
      })
    }
  }

  //根据当前列的数据做排序, 保存至后端
  const handleVisibleColumnChange = (visibleColumns: string[]) => {
    sortAndSaveColumns(visibleColumns, false)
  }

  //按当前顺序获取已展示的列
  const columnsValue = baseColumns.filter((el: any) => el.dataIndex && el.dataIndex !== 'dataLink.id')
  const defaultVisibleColumns = columnsValue.map((col: any) => col.dataIndex)

  const RenderDetailTemplate = useMemo(() => DetailTemplate(_getColumns), [baseColumns])

  const _data = useMemo(() => {
    const { isSearch, filterValue } = search
    return (isSearch ? filterBySearch(data, filterValue) : data).map((i: any, index: any) => ({
      ...i,
      zIndex: index,
    }))
  }, [data, search])
  return (
    <div className="modal-content">
      <Provider>
        <Language texts={i18nText} />
        <div className="column-chooser-box">
          {showColumnChooser && (
            <DataGrid.ColumnChooser
              columns={allColumns}
              defaultVisibleColumns={defaultVisibleColumns}
              onChange={handleVisibleColumnChange}
            />
          )}
        </div>
        <div className="search-box">
          <DataGrid.Search
            placeholder={i18n.get('搜索编号、支付摘要或提交人')}
            onClear={() => {}}
            onSearch={handleSearch}
            className={''}
            data-testid="pay-customPaymentBranch-search-input"
          />
        </div>
        <div className="save-diff-box">
          {isRenderDom(
            <div className="save-diff-btn font-size-2" onClick={onSaveDiffScenes} data-testid="pay-customPaymentBranch-saveDiff-btn">
              {i18n.get('保存变更')}
            </div>,
            isVisibleSaveDiff,
          )}
        </div>
        <div className="modal-warpper">
          <DataGrid.TableWrapper
            dataSource={_data}
            className="modal-warpper-c"
            rowKey="dataLink.id"
            selection={{
              allowSelectAll: true,
              mode: 'multiple',
            }}
            scrolling={{
              mode: 'virtual' as 'virtual',
            }}
            pageSize={data.length}
            pageIndex={1}
            columns={columns}
            selectedRowKeys={selectedRowKeys}
            isMultiSelect
            onSelectedChange={(selectedKeys: any) => setSelectedRowKeys(selectedKeys)}
            allowColumnReordering={true}
            allowColumnResizing
            groupPanel={{ visible: true }}
            RenderDetailTemplate={RenderDetailTemplate}
            columnMinWidth={160}
            enabledDetailTemplate={false}
            getInstance={(instance: any) => setInstance(instance)}
            refreshState={setIsVisibleSaveDiff}
          />
        </div>
      </Provider>
    </div>
  )
}

export default DoContainer

const DetailTemplate =
  (_getColumns: any) =>
  ({ row, ...rest }: any) => {
    return (
      <DataGrid.TableWrapper
        rowKey="dataLink.id"
        dataSource={row.data.dataLink.children}
        pageSize={row.data.dataLink.children?.length}
        pageIndex={1}
        columns={_getColumns(false)}
        isSingleSelect={false}
        isMultiSelect={false}
        allowColumnReordering={true}
        columnMinWidth={160}
        allowColumnResizing
      />
    )
  }
