import React from 'react'
import { Icon } from 'antd'
import DevExpress from 'devextreme/bundles/dx.all'

export const getColumns = (
  isShowColl: boolean,
  instance: DevExpress.ui.dxDataGrid | null,
  baseColumns: any[],
  bus: any,
) => {
  let stack = []

  //======================== 拼装展开折叠按钮 =================================
  isShowColl &&
    stack.push({
      title: '',
      dataIndex: 'dataLink.id',
      allowReordering: false,
      allowGrouping: false,
      allowResizing: false,
      allowEditing: false,
      allowFiltering: false,
      sorter: false,
      width: 40,
      minWidth: 40,
      render: (id: string, others: any) => {
        if (!others.dataLink.children) return null
        return (
          <CollapseIcon
            cKey={id}
            isRowExpanded={instance?.isRowExpanded}
            collapseRow={instance?.collapseRow}
            expandRow={instance?.expandRow}
          />
        )
      },
    })

  isShowColl &&
    stack.push({
      title: '序号',
      dataIndex: 'zIndex',
      allowReordering: false,
      allowGrouping: false,
      allowResizing: false,
      allowEditing: false,
      allowFiltering: false,
      sorter: false,
      width: 70,
      minWidth: 40,
      render: (recond: number) => {
        return <div>{recond + 1}</div>
      },
    })

  //========================= 拼装表格字段 =====================================
  baseColumns.forEach(i => {
    stack.push({
      ...i,
      filterType: '',
      sorter: false,
      allowFiltering: false,
    })
  })

  //========================== 拼装操作按钮 =====================================
  stack.push({
    title: '操作',
    label: '操作',
    dataIndex: 'dataLink.id',
    sorter: false,
    allowReordering: false,
    allowGrouping: false,
    allowResizing: false,
    allowEditing: false,
    allowFiltering: false,
    render: (id: string, others: any) => {
      const isHasCollapse = Boolean(others.dataLink.children)
      return (
        <RecallPlan
          sId={id}
          isHasCollapse={isHasCollapse}
          isShowColl={isShowColl}
          bus={bus}
          others={others}
        />
      )
    },
  })

  return stack
}

const CollapseIcon = ({ cKey, isRowExpanded, expandRow, collapseRow }: any) => {
  return (
    <div style={{ marginLeft: '-12px' }}>
      {isRowExpanded && isRowExpanded(cKey) ? (
        <div onClick={() => collapseRow(cKey)} data-testid="pay-customPaymentBranch-collapse-btn">
          <Icon type="caret-down" style={{ transform: 'scale(.6)' }} />
        </div>
      ) : (
        <div onClick={() => expandRow(cKey)} data-testid="pay-customPaymentBranch-expand-btn">
          <Icon type="caret-right" style={{ transform: 'scale(.6)' }} />
        </div>
      )}
    </div>
  )
}

const RecallPlan = ({
  isHasCollapse,
  isShowColl,
  bus,
  sId,
  others,
}: {
  isHasCollapse: boolean
  isShowColl: boolean
  bus: any
  sId: string
  others: any
}) => {
  const withdrawPlan = () => {
    bus.emit('AdjustPaymentPlan:withdrawPlan', {
      idList: [sId],
    })
  }

  const cancelMerge = () => {
    bus.emit('AdjustPaymentPlan:cancelMerge', {
      idList: [sId],
    })
  }

  const removeHandle = () => {
    bus.emit('AdjustPaymentPlan:removeHandle', {
      id: sId,
      ...others,
    })
  }

  const splitPlan = () => {
    bus.emit('AdjustPaymentPlan:splitPlan', {
      money: others?.dataLink?.E_system_paymentPlan_支付金额,
      planValue: others,
      ...others,
    })
  }

  if (!isShowColl) {
    return (
      <div className="recall-plan" onClick={removeHandle} data-testid="pay-customPaymentBranch-remove-btn">
        {i18n.get('移除')}
      </div>
    )
  }

  return (
    <div className="recall-plan">
      {isHasCollapse ? (
        <div onClick={cancelMerge} data-testid="pay-customPaymentBranch-cancelMerge-btn">{i18n.get('取消合并')}</div>
      ) : (
        <div className="line-action">
          <div className="action" onClick={withdrawPlan} data-testid="pay-customPaymentBranch-withdraw-btn">
            {i18n.get('撤回')}
          </div>
          <div onClick={splitPlan} data-testid="pay-customPaymentBranch-split-btn">{i18n.get('拆分')}</div>
        </div>
      )}
    </div>
  )
}
