export const getEnumButtonList = (selectedRowKeys, data) => {
  return [
    {
      label: i18n.get('完成'),
      type: 'primary',
      key: 'carryOut',
    },
    {
      label: i18n.get('撤销调整'),
      type: 'danger',
      key: 'undoAdjustment',
      right: true,
    },
    {
      label: i18n.get('自动合并'),
      type: 'secondary',
      key: 'autoMergerPlan',
      disabled:
        <PERSON><PERSON>an(selectedRowKeys.length < 2) ||
        data.find(item => selectedRowKeys.includes(item.dataLink?.id) && item.source === 'MERGE'),
    },
    {
      label: i18n.get('手动合并'),
      type: 'secondary',
      key: 'mergerPlan',
      disabled: <PERSON><PERSON><PERSON>(selectedRowKeys.length < 2),
    },
    {
      label: i18n.get('撤回计划'),
      type: 'secondary',
      key: 'withdrawPlan',
      disabled: <PERSON><PERSON><PERSON>(selectedRowKeys.length < 1),
    },
    {
      label: i18n.get('导出全部'),
      type: 'secondary',
      key: 'exportExcel',
    },
  ]
}
