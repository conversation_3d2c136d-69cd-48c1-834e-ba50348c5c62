import { app } from '@ekuaibao/whispered'
import React, { useMemo, useEffect } from 'react'
import { showModal, showMessage } from '@ekuaibao/show-util'
import { Button } from '@hose/eui'
import { getEnumButtonList } from './contants'
import { cloneDeep, uniq } from 'lodash'
import { message } from '@hose/eui'
import { convertToKeyVal, getNewCode, mergeRebase } from '../../utils'
import { uuid } from '@ekuaibao/helpers'
import { MoneyMath } from '@ekuaibao/money-math'
import { LayerDef } from '@ekuaibao/enhance-layer-manager/src/EnhanceLayerBase'
import MessageCenter from '@ekuaibao/messagecenter'
import { formatFormData } from '../../../../util/Utils'
interface IProps {
  selectedRowKeys: any[]
  data: any[]
  setData: any
  setSelectedRowKeys: any
  baseData: any[]
  layer: LayerDef
  columns: any[]
  bus: MessageCenter
  allColumns: any[]
  originalListMap: any
  getPayRemark: () => any
  setOriginalListMap: any
  originalList: any
  originalData: any
  setBaseData: any
}

const DoFooter: React.FC<IProps> = ({
  selectedRowKeys,
  data,
  setData,
  setSelectedRowKeys,
  baseData,
  layer,
  columns,
  bus,
  allColumns,
  originalListMap,
  originalList,
  getPayRemark,
  setOriginalListMap,
  originalData,
  setBaseData,
}) => {
  useEffect(() => {
    bus.on('AdjustPaymentPlan:withdrawPlan', withdrawPlan)
    bus.on('AdjustPaymentPlan:cancelMerge', cancelMerge)
    bus.on('AdjustPaymentPlan:removeHandle', removeHandle)
    bus.on('AdjustPaymentPlan:splitPlan', splitPlan)

    return () => {
      bus.un('AdjustPaymentPlan:withdrawPlan')
      bus.un('AdjustPaymentPlan:cancelMerge')
      bus.un('AdjustPaymentPlan:removeHandle')
      bus.un('AdjustPaymentPlan:splitPlan')
    }
  }, [data, baseData, selectedRowKeys, originalList, originalListMap])

  const formatDataList = (data: any[]) => {
    return data.map(item => {
      return { ...item, form: formatFormData(item.form) }
    })
  }
  const getConfig = () => {
    let otherList: any = []
    const _list = data.map((i: any) => {
      if (originalListMap[i.dataLink.id]) {
        return originalListMap[i.dataLink.id]
      } else if (!i.dataLink.children) {
        const data = originalList.find((item: any) => item.id === i.dataLink.id.split(':')[0])
        const _ = cloneDeep(i)
        let _form: { [key: string]: any } = {}
        Object.keys(_.dataLink).forEach(key => {
          if (key.indexOf('E_system_paymentPlan') >= 0) {
            _form[key] = _.dataLink[key]
          }
        })
        return {
          ...data,
          id: _.dataLink.id,
          form: {
            ..._form,
            E_system_paymentPlan_srcId: _.dataLink.id,
          },
          source: 'SPLIT',
          masterId: '',
        }
      } else {
        otherList = otherList.concat(i.dataLink.children ?? [])
        const _ = cloneDeep(i)
        let _form: { [key: string]: any } = {}
        Object.keys(_.dataLink).forEach(key => {
          if (key.indexOf('E_system_paymentPlan') >= 0) {
            _form[key] = _.dataLink[key]
          }
        })
        const cId = _.dataLink.id.split(',')[0]
        return {
          ...originalListMap[cId],
          id: _.dataLink.id,
          form: {
            ..._form,
            E_system_paymentPlan_srcId: _.dataLink.id,
          },
          source: 'MERGE',
          masterId: '',
        }
      }
    })
    const _otherList = otherList.map((i: any) => {
      return {
        ...originalListMap[i.dataLink.id],
        masterId: i.pId,
      }
    })
    return formatDataList(_list.concat(_otherList))
  }

  const exportExcel = () => {
    app.open('@audit:AdjustPaymentPlanInner', {
      type: 'exportExcel',
      data: data,
      getData: getConfig,
    })
  }

  const removeHandle = (props: any) => {
    const { id, pId } = props
    const dataMap: any = convertToKeyVal(
      data.map(i => ({ ...i.dataLink })),
      'id',
    )
    const _dataMap: any = convertToKeyVal(
      baseData.map(i => ({ ...i.dataLink })),
      'id',
    )
    const parent = dataMap[pId]
    if (parent?.children) {
      if (parent.children.length <= 2) {
        cancelMerge({ idList: [pId] })
      } else {
        let removeChildren = {}
        const selectedRowKeys: any[] = []

        parent.children.forEach((ele: any) => {
          if (ele.dataLink.id !== id) {
            selectedRowKeys.push(ele.dataLink.id)
          } else {
            removeChildren = {
              dataLink: ele.dataLink,
            }
          }
        })
        const { newPaymentPlan, isError } = mergeRebase(_dataMap, selectedRowKeys, null) as any
        if (!isError) {
          const newData = [removeChildren].concat(
            data.map(i => {
              if (i.dataLink.id === pId) {
                return {
                  ...i,
                  dataLink: newPaymentPlan,
                }
              } else {
                return i
              }
            }),
          )
          setData(newData)
          setSelectedRowKeys([])
        }
      }
    }
  }

  const cancelMerge = (props: any) => {
    const { idList } = props
    let collectChildren: any[] = []
    const _data = data.filter(i => idList.includes(i.dataLink.id))

    _data.forEach(i => {
      const children = i.dataLink.children
      collectChildren = collectChildren.concat(children)
    })

    const newData = collectChildren.concat(data.filter(i => !idList.includes(i.dataLink.id)))
    setData(newData)
    setBaseData(newData)
    setSelectedRowKeys([])
  }

  const mergeHandle = (value?: string) => {
    const dataMap: any = convertToKeyVal(
      data.map(i => ({ ...i.dataLink })),
      'id',
    )
    const _dataMap: any = convertToKeyVal(
      baseData.map(i => ({ ...i.dataLink })),
      'id',
    )

    //生产新的支付计划
    let _select: string[] = []
    selectedRowKeys.forEach(key => {
      if (dataMap[key] && dataMap[key].children) {
        _select = _select.concat(dataMap[key].children.map((i: { dataLink: { id: any } }) => i.dataLink.id))
      } else {
        _select.push(key)
      }
    })
    const { newPaymentPlan, isError } = mergeRebase({ ..._dataMap, ...dataMap }, _select, value) as any
    if (!isError) {
      const index = data.findIndex(i => i.dataLink.id === selectedRowKeys[0])
      const base = data.filter(i => !selectedRowKeys.includes(i.dataLink.id))
      if (newPaymentPlan.children) {
        base.splice(index, 0, { dataLink: newPaymentPlan, source: 'MERGE' })
      } else {
        base.splice(index, 0, { dataLink: newPaymentPlan, source: 'SPLIT' })
        const originalValue: any = originalListMap[newPaymentPlan.id.split(':')[0]]
        const value = {
          ...originalValue,
          id: newPaymentPlan.id,
          form: newPaymentPlan,
          masterId: '',
          source: 'SPLIT',
        }
        setOriginalListMap({ ...originalListMap, [newPaymentPlan.id]: value })
      }
      setData(base)
      setSelectedRowKeys([])
    }
  }
  const autoMergerPlan = () => {
    const newData = data.filter(i => selectedRowKeys.includes(i.dataLink.id))
    const notSelectData = data.filter(i => !selectedRowKeys.includes(i.dataLink.id))
    app
      .open('@audit:AutoMergerPlanModel', {
        type: 'autoMergerPlan',
        newData,
        columns,
      })
      .then((newData: any) => {
        const result = newData.reduce((result: any, item: any) => {
          if (!item?.dataLink?.children?.length) {
            const originalValue: any = originalListMap[item.dataLink.id.split(':')[0]] //拆分在合并会重新生成一个新的id，其余的数据取原来的
            result[item.dataLink.id] = {
              ...originalValue,
              id: item.dataLink.id,
              form: item.dataLink,
              masterId: '',
              source: 'SPLIT',
            }
          }
          return result
        }, {})
        setData(newData.concat(notSelectData))
        setOriginalListMap({ ...originalListMap, ...result })
        setSelectedRowKeys([])
      })
  }
  const mergerPlan = () => {
    //判断大于50给出提示，不能合并
    const keys = selectedRowKeys.reduce((keyList: string[], line: string) => {
      const newKeys = line.split(',')
      const list = keyList.concat(newKeys)
      return list
    }, [])
    if (keys.length > 500) {
      message.info('支付计划不能超过500条')
      return
    }
    const selectData = data.filter(item => selectedRowKeys.includes(item?.dataLink?.id))
    let legalEntityList: any[] = []
    selectData.map(
      row =>
        row?.dataLink?.E_system_paymentPlan_legalEntity?.id &&
        legalEntityList.push(row?.dataLink?.E_system_paymentPlan_legalEntity?.id),
    )
    const lists = uniq(legalEntityList)
    if (lists?.length > 1) {
      showModal.confirm({
        content: i18n.get('当前操作合并的支付计划不在同一法人实体下，请确认是否进行合并?'),
        okText: i18n.get('确认'),
        cancelText: i18n.get('取消'),
        onOk: () => {
          mergeNeedRemark()
        },
      })
    } else {
      mergeNeedRemark()
    }
  }

  const mergeNeedRemark = () => {
    const { needRemark, remarkLimit, isAutoSummary } = getPayRemark()
    if (needRemark && isAutoSummary) {
      app
        .open('@audit:AdjustPaymentPlanInner', {
          type: 'mergerPlan',
          otherProps: {
            remarkLimit,
          },
        })
        .then((value: any) => {
          mergeHandle(value.value)
        })
    } else {
      mergeHandle()
    }
  }

  const undoAdjustment = () => {
    showModal.confirm({
      content: i18n.get('该操作将会撤销所有的支付计划调整!'),
      okText: i18n.get('确认'),
      cancelText: i18n.get('取消'),
      onOk: () => {
        setData(originalData)
        setSelectedRowKeys([])
      },
    })
  }

  const splitPlan = (props: any) => {
    const { money, planValue } = props
    app.open('@audit:SplitPlanModal', { money }).then((res: any) => {
      const planList: any = []
      const originalPlanList: any = []
      const _originalByMapList: any[] = Object.values(originalListMap)
      const originalValue = _originalByMapList?.find(
        (v: any) => v?.id?.split(':')[0] === planValue?.dataLink?.id?.split(':')[0],
      )
      const standardMoney = parseFloat(money.standard)
      res?.splitPlanData?.forEach((item: any, index: number) => {
        const id = uuid(16)
        const { dataLink = {} } = planValue
        const {
          E_system_paymentPlan_支付金额 = {},
          E_system_paymentPlan_foreign,
          E_system_paymentPlan_收款币金额,
          E_system_paymentPlan_本位币金额,
          E_system_paymentPlan_应付币金额,
          E_system_paymentPlan_原币金额,
        } = dataLink
        let _dataLink = {
          ...dataLink,
          id: `${dataLink.id}:${id}`,
          E_system_paymentPlan_code: `${dataLink.E_system_paymentPlan_code}:${id}`,
          E_system_paymentPlan_编号: `${getNewCode(dataLink.E_system_paymentPlan_编号)}`,
          E_system_paymentPlan_支付金额: {
            ...E_system_paymentPlan_支付金额,
            standard: String(item.splitPaymentMoney),
          },
          E_system_paymentPlan_本位币金额: E_system_paymentPlan_本位币金额?.standard
            ? {
                ...E_system_paymentPlan_本位币金额,
                standard: String(item.splitPaymentMoney),
              }
            : null,
          E_system_paymentPlan_应付币金额: E_system_paymentPlan_应付币金额?.standard
            ? {
                ...E_system_paymentPlan_应付币金额,
                standard: String(item.splitPaymentMoney),
              }
            : null,
          E_system_paymentPlan_realPayMoney: String(item.splitPaymentMoney),
        }
        if (E_system_paymentPlan_foreign) {
          //有原币的话拆分原币
          _dataLink = {
            ..._dataLink,
            E_system_paymentPlan_foreign:
              res.splitPlanData.length - 1 === index
                ? (
                    E_system_paymentPlan_foreign -
                    planList.reduce(
                      (sum: any, line: any) => new MoneyMath(sum).add(line.dataLink.E_system_paymentPlan_foreign).value,
                      0,
                    )
                  ).toFixed(2)
                : ((item.splitPaymentMoney / standardMoney) * E_system_paymentPlan_foreign).toFixed(2),
          }
        }
        if (E_system_paymentPlan_收款币金额) {
          //有收款币金额的话拆分收款币金额
          _dataLink = fnSplitAmount({
            data: _dataLink,
            amountMap: E_system_paymentPlan_收款币金额,
            key: 'E_system_paymentPlan_收款币金额',
            res,
            index,
            planList,
            item,
            standardMoney,
          })
        }
        if (E_system_paymentPlan_原币金额) {
          //有原币金额的话拆分原币金额
          _dataLink = fnSplitAmount({
            data: _dataLink,
            amountMap: E_system_paymentPlan_原币金额,
            key: 'E_system_paymentPlan_原币金额',
            res,
            index,
            planList,
            item,
            standardMoney,
          })
        }
        planList.push({
          ...planValue,
          source: 'SPLIT',
          dataLink: _dataLink,
        })
        originalPlanList.push({
          ...originalValue,
          id: _dataLink.id,
          source: 'SPLIT',
          form: _dataLink,
        })
      })
      const _planList = data?.filter((item: any) => item?.dataLink?.id !== planValue?.dataLink?.id)
      // const _originalPlanList = _originalByMapList?.filter((item: any) => item?.id !== planValue?.dataLink?.id)
      const _baseData = baseData?.filter((item: any) => item?.dataLink?.id !== planValue?.dataLink?.id)
      // @ts-ignore
      const _data = [..._planList, ...planList]
      const _originalList = [..._originalByMapList, ...originalPlanList]
      const newBaseData = [..._baseData, ...planList]
      setData(_data)
      setBaseData(newBaseData)
      setOriginalListMap(convertToKeyVal(_originalList, 'id'))
      setSelectedRowKeys([])
    })
  }

  const fnSplitAmount = ({ data, amountMap, key, res, index, planList, item, standardMoney }: any) => {
    return {
      ...data,
      [key]: {
        ...amountMap,
        standard:
          res.splitPlanData.length - 1 === index
            ? (
                amountMap?.standard -
                planList.reduce(
                  (sum: any, line: any) => new MoneyMath(sum).add(line?.dataLink?.[key]?.standard).value,
                  0,
                )
              ).toFixed(2)
            : ((item.splitPaymentMoney / standardMoney) * amountMap?.standard).toFixed(2),
      },
    }
  }

  const withdrawPlan = (props?: any) => {
    const idList = props?.idList || selectedRowKeys
    showModal.confirm({
      title: i18n.get('提示'),
      content: i18n.get('被撤回的支付计划，将不会包含在本次支付内。'),
      okText: i18n.get('确认'),
      cancelText: i18n.get('取消'),
      onOk: () => {
        if (data.length - idList.length < 1) {
          showMessage.error(i18n.get('请至少保留一条支付计划！'))
          return
        }
        const _map: any = convertToKeyVal(
          data.map(i => ({ ...i.dataLink })),
          'id',
        )
        const _list = idList.map((id: string) =>
          _map[id] ? String(_map[id].E_system_paymentPlan_支付金额.standard) : '',
        )
        if (_list.includes('0')) {
          showMessage.error(i18n.get('支付金额为0的支付计划不能被撤回！'))
          return
        }
        const newData = data.filter(i => !idList.includes(i.dataLink.id))
        setData(newData)
        setSelectedRowKeys(selectedRowKeys.filter(i => !idList.includes(i)))
      },
    })
  }

  const carryOut = () => {
    const _data = {
      data,
      baseData,
      originalData,
      columns,
      allColumns,
      originalList,
      originalListMap,
      config: getConfig(),
    }

    layer?.emitOk({ ..._data })
  }

  const clickMap: { [key: string]: () => void } = {
    exportExcel: exportExcel,
    mergerPlan: mergerPlan,
    undoAdjustment: undoAdjustment,
    withdrawPlan: withdrawPlan,
    carryOut: carryOut,
    autoMergerPlan: autoMergerPlan,
  }

  const clickHandle = (key: string) => {
    clickMap[key] && clickMap[key]()
  }

  const buttonList = useMemo(() => getEnumButtonList(selectedRowKeys, data), [selectedRowKeys])

  return (
    <div className="modal-footer">
      <div>
        {buttonList.map(
          (b: any) =>
            !b.right && (
              <Button
                onClick={() => clickHandle(b.key)}
                key={b.key}
                category={b.type}
                className="mr-8"
                disabled={b.disabled}
                data-testid={`pay-customPaymentBranch-${b.key}-btn`}
              >
                {b.label}
              </Button>
            ),
        )}
      </div>
      <div>
        {buttonList.map(
          (b: any) =>
            b.right && (
              <Button
                onClick={() => clickHandle(b.key)}
                key={b.key}
                theme="danger"
                className="mr-8"
                disabled={b.disabled}
                data-testid={`pay-customPaymentBranch-${b.key}-btn`}
              >
                {b.label}
              </Button>
            ),
        )}
        {i18n.get('item', { item: data.length })}
      </div>
    </div>
  )
}

export default DoFooter
