

const _d = {"items":{"path":{"entityId":"entityId","E_system_paymentPlan_生成日期":"form.E_system_paymentPlan_生成日期","E_system_paymentPlan_name":"form.E_system_paymentPlan_name","E_system_paymentPlan_code":"form.E_system_paymentPlan_code","E_system_paymentPlan_来源":"form.E_system_paymentPlan_来源","E_system_paymentPlan_flowId":"form.E_system_paymentPlan_flowId","E_system_paymentPlan_srcId":"form.E_system_paymentPlan_srcId","E_system_paymentPlan_编号":"form.E_system_paymentPlan_编号","E_system_paymentPlan_支付概要":"form.E_system_paymentPlan_支付概要","E_system_paymentPlan_提交人":"form.E_system_paymentPlan_提交人","E_system_paymentPlan_支付金额":"form.E_system_paymentPlan_支付金额","E_system_paymentPlan_币种":"form.E_system_paymentPlan_币种","E_system_paymentPlan_收款账号类别":"form.E_system_paymentPlan_收款账号类别","E_system_paymentPlan_收款账号性质":"form.E_system_paymentPlan_收款账号性质","E_system_paymentPlan_收款信息":"form.E_system_paymentPlan_收款信息","E_system_paymentPlan_付款账号类别":"form.E_system_paymentPlan_付款账号类别","E_system_paymentPlan_支付方式":"form.E_system_paymentPlan_支付方式","E_system_paymentPlan_付款信息":"form.E_system_paymentPlan_付款信息","E_system_paymentPlan_发起支付日期":"form.E_system_paymentPlan_发起支付日期","E_system_paymentPlan_支付完成日期":"form.E_system_paymentPlan_支付完成日期","E_system_paymentPlan_支付用途":"form.E_system_paymentPlan_支付用途","E_system_paymentPlan_摘要":"form.E_system_paymentPlan_摘要","E_system_paymentPlan_支付批次号":"form.E_system_paymentPlan_支付批次号","E_system_paymentPlan_渠道批次号":"form.E_system_paymentPlan_渠道批次号","E_system_paymentPlan_支付状态":"form.E_system_paymentPlan_支付状态","E_system_paymentPlan_失败原因":"form.E_system_paymentPlan_失败原因","E_system_paymentPlan_交易流水":"form.E_system_paymentPlan_交易流水","E_system_paymentPlan_凭证号":"form.E_system_paymentPlan_凭证号","E_system_paymentPlan_凭证状态":"form.E_system_paymentPlan_凭证状态","E_system_paymentPlan_凭证生成时间":"form.E_system_paymentPlan_凭证生成时间","ownerId":"ownerId","visibility":"visibility","active":"active","useCount":"useCount"},"template":{"id":"PLAN_READY","version":3,"active":true,"createTime":1585147281146,"updateTime":1585147281146,"nameSpell":"","code":"","corporationId":"","name":"","type":"TABLE","visibility":{"fullVisible":true,"staffs":null,"roles":null,"departments":null,"departmentsIncludeChildren":true},"entityId":"system_paymentPlan","content":{"type":"TABLE","selectedFields":[{"name":"E_system_paymentPlan_编号","label":"编号","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":27}},{"name":"E_system_paymentPlan_支付概要","label":"支付概要","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":26}},{"name":"E_system_paymentPlan_提交人","label":"提交人","type":"ref","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":25},"entity":"organization.Staff"},{"name":"E_system_paymentPlan_生成日期","label":"生成日期","type":"date","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":24},"withTime":true},{"name":"E_system_paymentPlan_支付金额","label":"支付金额","type":"money","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":23}},{"name":"E_system_paymentPlan_币种","label":"币种","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":22}},{"name":"E_system_paymentPlan_收款账号类别","label":"收款账号类别","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":21}},{"name":"E_system_paymentPlan_收款账号性质","label":"收款账号性质","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":20}},{"name":"E_system_paymentPlan_收款信息","label":"收款信息","type":"ref","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":19},"entity":"pay.PayeeInfo"},{"name":"E_system_paymentPlan_支付状态","label":"支付状态","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":18}}],"expansion":{"selectedFields":[{"name":"E_system_paymentPlan_编号","label":"编号","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":27}},{"name":"E_system_paymentPlan_支付概要","label":"支付概要","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":26}},{"name":"E_system_paymentPlan_提交人","label":"提交人","type":"ref","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":25},"entity":"organization.Staff"},{"name":"E_system_paymentPlan_生成日期","label":"生成日期","type":"date","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":24},"withTime":true},{"name":"E_system_paymentPlan_支付金额","label":"支付金额","type":"money","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":23}},{"name":"E_system_paymentPlan_币种","label":"币种","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":22}},{"name":"E_system_paymentPlan_收款账号类别","label":"收款账号类别","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":21}},{"name":"E_system_paymentPlan_收款账号性质","label":"收款账号性质","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":20}},{"name":"E_system_paymentPlan_收款信息","label":"收款信息","type":"ref","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":19},"entity":"pay.PayeeInfo"},{"name":"E_system_paymentPlan_支付状态","label":"支付状态","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":18}}]}}},"data":[{"dataLink":{"id":"KLsc422TqM4w00","useCount":0,"totalCount":0,"ownerId":null,"visibility":{"fullVisible":true,"staff":[],"department":[],"role":[]},"E_system_paymentPlan_code":"KLsc422TqM4w00","E_system_paymentPlan_name":"单据:tablet","E_system_paymentPlan_srcId":"I1Yc41PHM8tw00","E_system_paymentPlan_flowId":"I1Yc41PHM8tw00","E_system_paymentPlan_币种":"CNY","E_system_paymentPlan_来源":"FLOW","E_system_paymentPlan_编号":"B20000166-001","E_system_paymentPlan_提交人":{"version":1,"active":true,"createTime":*************,"updateTime":*************,"nameSpell":"ll","code":"","corporationId":"rzI9NCATtI0800","userId":"WZgbOJYkrsmc00","id":"rzI9NCATtI0800:WZgbOJYkrsmc00","name":"ll","avatar":null,"email":"","cellphone":"***********","note":null,"departments":["rzI9NCATtI0800"],"defaultDepartment":"rzI9NCATtI0800","external":false,"order":null},"E_system_paymentPlan_凭证状态":"NOTGENERATED","E_system_paymentPlan_支付概要":"单据:tablet","E_system_paymentPlan_支付状态":"READY","E_system_paymentPlan_支付金额":{"standard":23231,"standardUnit":"元","standardScale":"2","standardSymbol":"¥","standardNumCode":"156","standardStrCode":"CNY"},"E_system_paymentPlan_收款信息":{"version":1,"active":true,"createTime":1581586648764,"updateTime":1581586648764,"nameSpell":"SHOUKUAN**********","code":"","corporationId":"rzI9NCATtI0800","id":"C_MamiDDS45U00","name":"收款**********","type":"PERSONAL","owner":"CORPORATION","cardNo":"***************","logs":[{"action":"CREATE","operatorId":"rzI9NCATtI0800:sH0aeZ-joI3g00","time":*************,"attributes":null},{"action":"EDIT","operatorId":"rzI9NCATtI0800:PHMa2ILmO80400","time":*************,"attributes":{"changes":[{"field":"certificateType","newValue":"02","oldValue":null},{"field":"certificateNo","newValue":"370321198511103911","oldValue":""}]}}],"sort":"OVERSEABANK","staffId":"","visibility":{"fullVisible":true,"staffs":[],"roles":null,"departments":[],"departmentsIncludeChildren":false},"bankName":"","swiftCode":"","bankCode":"","branchCode":"","icon":"http://images.ekuaibao.com/bank/oversea.svg","bank":"海外","bankLinkNo":null,"certificateType":"02","certificateNo":"370321198511103911"},"E_system_paymentPlan_生成日期":*************,"active":true,"entityId":"system_paymentPlan","entity":{"version":2,"active":true,"createTime":*************,"updateTime":*************,"name":"system_paymentPlan","nameSpell":"system_paymentPlan","code":"","corporationId":"KPs9NCATtI0000","ledgerIds":null,"plannedIds":null,"parentId":"","scoped":false,"type":"ORDER","disableStrategy":"MANUAL_ADMIN","maxUsageCount":0,"writtenOffField":null,"fields":[{"name":"E_system_paymentPlan_生成日期","label":"生成日期","type":"date","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":28},"withTime":true},{"name":"E_system_paymentPlan_name","label":"name","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":27}},{"name":"E_system_paymentPlan_code","label":"code","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":26}},{"name":"E_system_paymentPlan_来源","label":"来源","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":25}},{"name":"E_system_paymentPlan_flowId","label":"flowId","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":24}},{"name":"E_system_paymentPlan_srcId","label":"srcId","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":23}},{"name":"E_system_paymentPlan_编号","label":"编号","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":22}},{"name":"E_system_paymentPlan_支付概要","label":"支付概要","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":21}},{"name":"E_system_paymentPlan_提交人","label":"提交人","type":"ref","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":20},"entity":"organization.Staff"},{"name":"E_system_paymentPlan_支付金额","label":"支付金额","type":"money","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":19}},{"name":"E_system_paymentPlan_币种","label":"币种","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":18}},{"name":"E_system_paymentPlan_收款账号类别","label":"收款账号类别","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":17}},{"name":"E_system_paymentPlan_收款账号性质","label":"收款账号性质","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":16}},{"name":"E_system_paymentPlan_收款信息","label":"收款信息","type":"ref","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":15},"entity":"pay.PayeeInfo"},{"name":"E_system_paymentPlan_付款账号类别","label":"付款账号类别","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":14}},{"name":"E_system_paymentPlan_支付方式","label":"支付方式","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":13}},{"name":"E_system_paymentPlan_付款信息","label":"付款信息","type":"ref","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":12},"entity":"pay.PaymentAccount"},{"name":"E_system_paymentPlan_发起支付日期","label":"发起支付日期","type":"date","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":11},"withTime":true},{"name":"E_system_paymentPlan_支付完成日期","label":"支付完成日期","type":"date","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":10},"withTime":true},{"name":"E_system_paymentPlan_支付用途","label":"支付用途","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":9}},{"name":"E_system_paymentPlan_摘要","label":"摘要","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":8}},{"name":"E_system_paymentPlan_支付批次号","label":"支付批次号","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":7}},{"name":"E_system_paymentPlan_渠道批次号","label":"渠道批次号","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":6}},{"name":"E_system_paymentPlan_支付状态","label":"支付状态","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":5}},{"name":"E_system_paymentPlan_失败原因","label":"失败原因","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":4}},{"name":"E_system_paymentPlan_交易流水","label":"交易流水","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":3}},{"name":"E_system_paymentPlan_凭证号","label":"凭证号","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":2}},{"name":"E_system_paymentPlan_凭证状态","label":"凭证状态","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":1}},{"name":"E_system_paymentPlan_凭证生成时间","label":"凭证生成时间","type":"date","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":0},"withTime":true}],"defaultMappingRuleId":null,"filterId":null,"id":"system_paymentPlan","platformId":"-WMaIZjA7MnU00","details":null,"homePageVisibleIds":null,"homeVisibility":{"fullVisible":false,"staffs":null,"roles":null,"departments":null,"departmentsIncludeChildren":true},"showOnPage":true,"importMethod":["excel","api"],"allowRecordLog":false,"logFields":[],"allowAddSubType":true},"E_system_paymentPlan_收款账号类别":"OVERSEABANK","E_system_paymentPlan_收款账号性质":"PERSONAL"},"ledger":{},"planned":{},"plans":[],"ledgers":[]},{"dataLink":{"id":"P8wc45YN-k7k00","useCount":0,"totalCount":0,"ownerId":null,"visibility":{"fullVisible":true,"staff":[],"department":[],"role":[]},"E_system_paymentPlan_code":"P8wc45YN-k7k00","E_system_paymentPlan_name":"单据:额鹅鹅鹅","E_system_paymentPlan_srcId":"IEwc45Ycb08Q00","E_system_paymentPlan_flowId":"IEwc45Ycb08Q00","E_system_paymentPlan_币种":"CNY","E_system_paymentPlan_来源":"FLOW","E_system_paymentPlan_编号":"*********-001","E_system_paymentPlan_提交人":{"version":1,"active":true,"createTime":*************,"updateTime":*************,"nameSpell":"ll","code":"","corporationId":"rzI9NCATtI0800","userId":"WZgbOJYkrsmc00","id":"rzI9NCATtI0800:WZgbOJYkrsmc00","name":"ll","avatar":null,"email":"","cellphone":"***********","note":null,"departments":["rzI9NCATtI0800"],"defaultDepartment":"rzI9NCATtI0800","external":false,"order":null},"E_system_paymentPlan_凭证状态":"NOTGENERATED","E_system_paymentPlan_支付概要":"单据:额鹅鹅鹅","E_system_paymentPlan_支付状态":"READY","E_system_paymentPlan_支付金额":{"standard":132,"standardUnit":"元","standardScale":"2","standardSymbol":"¥","standardNumCode":"156","standardStrCode":"CNY"},"E_system_paymentPlan_收款信息":{"version":1,"active":true,"createTime":1581586648764,"updateTime":1581586648764,"nameSpell":"SHOUKUAN**********","code":"","corporationId":"rzI9NCATtI0800","id":"C_MamiDDS45U00","name":"收款**********","type":"PERSONAL","owner":"CORPORATION","cardNo":"***************","logs":[{"action":"CREATE","operatorId":"rzI9NCATtI0800:sH0aeZ-joI3g00","time":*************,"attributes":null},{"action":"EDIT","operatorId":"rzI9NCATtI0800:PHMa2ILmO80400","time":*************,"attributes":{"changes":[{"field":"certificateType","newValue":"02","oldValue":null},{"field":"certificateNo","newValue":"370321198511103911","oldValue":""}]}}],"sort":"OVERSEABANK","staffId":"","visibility":{"fullVisible":true,"staffs":[],"roles":null,"departments":[],"departmentsIncludeChildren":false},"bankName":"","swiftCode":"","bankCode":"","branchCode":"","icon":"http://images.ekuaibao.com/bank/oversea.svg","bank":"海外","bankLinkNo":null,"certificateType":"02","certificateNo":"370321198511103911"},"E_system_paymentPlan_生成日期":*************,"active":true,"entityId":"system_paymentPlan","entity":{"version":2,"active":true,"createTime":*************,"updateTime":*************,"name":"system_paymentPlan","nameSpell":"system_paymentPlan","code":"","corporationId":"KPs9NCATtI0000","ledgerIds":null,"plannedIds":null,"parentId":"","scoped":false,"type":"ORDER","disableStrategy":"MANUAL_ADMIN","maxUsageCount":0,"writtenOffField":null,"fields":[{"name":"E_system_paymentPlan_生成日期","label":"生成日期","type":"date","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":28},"withTime":true},{"name":"E_system_paymentPlan_name","label":"name","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":27}},{"name":"E_system_paymentPlan_code","label":"code","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":26}},{"name":"E_system_paymentPlan_来源","label":"来源","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":25}},{"name":"E_system_paymentPlan_flowId","label":"flowId","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":24}},{"name":"E_system_paymentPlan_srcId","label":"srcId","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":23}},{"name":"E_system_paymentPlan_编号","label":"编号","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":22}},{"name":"E_system_paymentPlan_支付概要","label":"支付概要","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":21}},{"name":"E_system_paymentPlan_提交人","label":"提交人","type":"ref","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":20},"entity":"organization.Staff"},{"name":"E_system_paymentPlan_支付金额","label":"支付金额","type":"money","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":19}},{"name":"E_system_paymentPlan_币种","label":"币种","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":18}},{"name":"E_system_paymentPlan_收款账号类别","label":"收款账号类别","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":17}},{"name":"E_system_paymentPlan_收款账号性质","label":"收款账号性质","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":16}},{"name":"E_system_paymentPlan_收款信息","label":"收款信息","type":"ref","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":15},"entity":"pay.PayeeInfo"},{"name":"E_system_paymentPlan_付款账号类别","label":"付款账号类别","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":14}},{"name":"E_system_paymentPlan_支付方式","label":"支付方式","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":13}},{"name":"E_system_paymentPlan_付款信息","label":"付款信息","type":"ref","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":12},"entity":"pay.PaymentAccount"},{"name":"E_system_paymentPlan_发起支付日期","label":"发起支付日期","type":"date","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":11},"withTime":true},{"name":"E_system_paymentPlan_支付完成日期","label":"支付完成日期","type":"date","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":10},"withTime":true},{"name":"E_system_paymentPlan_支付用途","label":"支付用途","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":9}},{"name":"E_system_paymentPlan_摘要","label":"摘要","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":8}},{"name":"E_system_paymentPlan_支付批次号","label":"支付批次号","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":7}},{"name":"E_system_paymentPlan_渠道批次号","label":"渠道批次号","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":6}},{"name":"E_system_paymentPlan_支付状态","label":"支付状态","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":5}},{"name":"E_system_paymentPlan_失败原因","label":"失败原因","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":4}},{"name":"E_system_paymentPlan_交易流水","label":"交易流水","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":3}},{"name":"E_system_paymentPlan_凭证号","label":"凭证号","type":"text","source":"dataLink","optional":true,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":2}},{"name":"E_system_paymentPlan_凭证状态","label":"凭证状态","type":"text","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":1}},{"name":"E_system_paymentPlan_凭证生成时间","label":"凭证生成时间","type":"date","source":"dataLink","optional":false,"defaultValue":null,"formula":false,"calculation":{"dependencies":[],"dependenciesBy":[],"order":0},"withTime":true}],"defaultMappingRuleId":null,"filterId":null,"id":"system_paymentPlan","platformId":"-WMaIZjA7MnU00","details":null,"homePageVisibleIds":null,"homeVisibility":{"fullVisible":false,"staffs":null,"roles":null,"departments":null,"departmentsIncludeChildren":true},"showOnPage":true,"importMethod":["excel","api"],"allowRecordLog":false,"logFields":[],"allowAddSubType":true},"E_system_paymentPlan_收款账号类别":"OVERSEABANK","E_system_paymentPlan_收款账号性质":"PERSONAL"},"ledger":{},"planned":{},"plans":[],"ledgers":[]}],"total":2}}

export const getData = () => new Promise((resolve, reject) => {
    setTimeout(() => {
        resolve({
            code: 'success',
            data: _d
        })
    }, 1000)
})