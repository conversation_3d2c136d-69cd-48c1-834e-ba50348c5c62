import { ILayerProps } from '@ekuaibao/enhance-layer-manager'

export interface AdjustPaymentPlanProps extends ILayerProps {
    dynamicChannelMap: any
    adjustPaymentPlanData: any
    flowIds: any[]
    getPayRemark: () => RemarkProps
    channel: string
    accountId: string
    isByDetail: boolean // 是否从【明细维度】进入
}

interface RemarkProps {
    remarkLimit: number
    byHand: boolean
    autoSummary: boolean
    fieldLabel: string
    useSpecial: boolean
    needRemark: boolean
    isAutoSummary: boolean
    remark: string | undefined
    dimensionItemId: string | undefined
}

interface IProps {
    closeHandle: () => void
}


export interface IHeader extends IProps {
    title?: string
}