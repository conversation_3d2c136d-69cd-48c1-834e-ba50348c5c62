import { uuid } from '@ekuaibao/helpers';
/**
 *  Created by pw on 2023/6/10 10:05.
 */
import { get } from 'lodash'
import { getV } from '@ekuaibao/lib/lib/help'

export interface IParams {
  selectData: any[]
}

export interface INodeDisplay {
  configNodeId: string
  id: string
  name: string
}

export function getCurrentNodeBeforeNodes({ selectData }: IParams): { id: any; name: null }[] {
  let firstData
  if (selectData?.length === 1) {
    firstData = selectData[0]
  }
  let label = i18n.get('提交人')
  if (!!firstData) {
    let submitter =
      get(firstData, 'flowId.ownerId', undefined) ||
      get(firstData, 'backlog.flowId.ownerId', undefined)
    submitter = submitter ? submitter : get(firstData, 'ownerId', {})
    label = submitter.name ? `${label}${i18n.get('（')}${submitter.name}${i18n.get('）')}` : label
  }
  const mergeNodes: { id: null; name: null }[] = [
    {
      id: null,
      name: label,
    },
  ]
  if (!selectData?.length) {
    return mergeNodes
  }
  // 检查选择的数据是否是同一个审批流程
  if (!checkDataSameFlowPlan(selectData)) {
    return mergeNodes
  }
  // 获取所有选择的单据的之前的节点二维数组
  let allBeforeNodes: INodeDisplay[][] = selectData.map((data: any) => getNodeBeforeNodes(data))
  allBeforeNodes = allBeforeNodes.sort((a, b) => a.length - b.length)
  const [firstNodes, ...otherNodes] = allBeforeNodes
  if (!firstNodes?.length) {
    return mergeNodes
  }
  let mergeMap: any = firstNodes.reduce((map, node) => {
    const configNodeId =  node.configNodeId
    if (!configNodeId) {
      node.configNodeId = uuid()
    }
    map[node.configNodeId] = node
    return map
  }, {} as any)
  otherNodes?.forEach(currentNodes => {
    currentNodes.forEach(node => {
      if (!mergeMap[node.configNodeId]) {
        delete mergeMap[node.configNodeId]
      }
    })
  })
  const beforeNodes: any[] = Object.values(mergeMap)
  return mergeNodes.concat(beforeNodes)
}

export function getNodeBeforeNodes(data: any): INodeDisplay[] {
  const flow = data?.flowId || data?.backlog || data
  const nodes = get(flow, 'plan.nodes', [])
  const taskId = get(flow, 'plan.taskId', '')
  const taskIndex = nodes.findIndex((v: { id: any }) => v.id === taskId)
  return nodes.filter((line: any, index: number) => {
    const { type, skippedType } = line
    const isEsignature = line.config && line.config.type === 'esignature' //电子签节点
    const isNeedCashierNode = getV(line, 'config.isNeedCashierNode', true)
    return (
      isNeedCashierNode &&
      skippedType === 'NO_SKIPPED' &&
      type !== 'ebot' &&
      type !== 'carbonCopy' &&
      type !== "invoicingApplication" &&
      type !== 'recalculate' &&
      type !== 'aiApproval' &&
      !isEsignature &&
      index < taskIndex
    )
  })
}

function checkDataSameFlowPlan(data: any[] = []) {
  if (!data.length) {
    return false
  }
  const [first] = data
  const flow = first?.flowId || first?.backlog || first
  const flowPlanId = get(flow, 'plan.flowPlanConfigId')
  return (
    data.filter(item => {
      const flow = item?.flowId || item?.backlog || item
      return get(flow, 'plan.flowPlanConfigId') === flowPlanId
    }).length === data.length
  )
}

export function getNodeIdByConfigNodeId(configNodeId: string, selectData: any[] = []) {
  if (!configNodeId) {
    return null
  }
  return selectData.reduce((result, data) => {
    const flow = data?.flowId || data?.backlog || data
    const nodes = get(flow, 'plan.nodes', [])
    const rejectNode = nodes.find(
      (node: { configNodeId: string }) => node.configNodeId === configNodeId,
    )
    const backlogId = data?.flowId ? data?.id : flow?.backlogId
    if (backlogId) {
      result[backlogId] = rejectNode.id
    }
    return result
  }, {})
}
