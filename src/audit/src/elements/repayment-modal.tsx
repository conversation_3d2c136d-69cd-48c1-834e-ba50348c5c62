import { app as api } from '@ekuaibao/whispered'
import React, { Component } from 'react'
const Details = api.require<any>('@elements/puppet/details/Details')
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Button } from '@hose/eui'
import { OutlinedTipsClose } from '@hose/eui-icons'
import styles from './repayment-modal.module.less'
import classnames from 'classnames'
const PayPlayTableWrapper = api.require<any>('@elements/payPlan/PayPlayTableWrapper')
const TableStore = api.require<any>('@elements/payPlan/table/table.store').default
import { Provider } from 'mobx-react'
import { EnhanceConnect } from '@ekuaibao/store'

interface RepaymentModalProps {
  flow: StringAnyProps
  form: StringAnyProps
  layer: StringAnyProps
  formType: string
  feeTypeVisibleObjForModify: {
    flowId?: string
    showAllFeeType?: boolean
    feeTypeVisibleList: string[]
    apportionVisibleList: string[]
  }
}

interface RepaymentModalState {
  selectedData: StringAnyProps[]
  feetypes: StringAnyProps[]
  visibleDetails: StringAnyProps[]
  unvisibleCount: number
}

@EnhanceConnect(state => ({
  feeTypeVisibleObjForModify: state['@bills'].feeTypeVisibleObjForModify
}))
@EnhanceModal({
  footer: [],
  className: 'respond-modal-layer'
  // className: 'custom-modal-layer'
})
export default class RepaymentModal extends Component<RepaymentModalProps, RepaymentModalState> {
  payPlanStore: any // TableStore

  constructor(props: RepaymentModalProps) {
    super(props)
    const { form } = props
    // payPlanMode: true为按金额多收款，flase为按明细多收款
    // multiplePayeesMode: 判断是否是多收款人单据
    // payeePayPlan: 按收款信息汇总明细金额
    if (form.multiplePayeesMode && (form.payPlanMode || form.payeePayPlan)) {
      this.payPlanStore = new TableStore()
    }
  }

  state = {
    selectedData: new Array<StringAnyProps>(),
    feetypes: new Array<StringAnyProps>(),
    visibleDetails: [],
    unvisibleCount: 0
  }

  componentDidMount() {
    // payPlanMode: true为按金额多收款，flase为按明细多收款
    // multiplePayeesMode: 判断是否是多收款人单据
    // payeePayPlan: 按收款信息汇总明细金额
    const { form } = this.props
    if (!form.payPlanMode && !form.payeePayPlan) {
      api
        .dataLoader('@common.feetypes')
        .load()
        .then(({ data }: { data: any }) => {
          const { feeTypeVisibleObjForModify } = this.props
          const { feeTypeVisibleList = [], showAllFeeType } = feeTypeVisibleObjForModify
          let visibleDetails = form.details
          let unvisibleCount = 0
          if (!showAllFeeType) {
            visibleDetails = form.details.filter(el => feeTypeVisibleList.includes(el.feeTypeForm.detailId))
            unvisibleCount = form.details.length - visibleDetails.length
          }
          this.setState({ feetypes: data, visibleDetails, unvisibleCount })
        })
    } else {
      this.setState({ visibleDetails: form.details })
    }
  }

  handleSelect = (selectedData: StringAnyProps[]) => {
    this.setState({ selectedData })
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleOk = () => {
    const { selectedData } = this.state
    this.props.layer.emitOk(selectedData)
  }

  render() {
    // payPlanMode: true为按金额多收款，flase为按明细多收款
    // multiplePayeesMode: 判断是否是多收款人单据
    // payeePayPlan: 按收款信息汇总明细金额
    const { flow, form, formType, feeTypeVisibleObjForModify } = this.props
    const { showAllFeeType, apportionVisibleList } = feeTypeVisibleObjForModify
    const { selectedData, feetypes, visibleDetails, unvisibleCount } = this.state
    const headerCls = classnames('modal-header', styles['repayment-modal-header'])
    return (
      <>
        <div className={headerCls}>
          <div className="flex">{i18n.get('选择需要重新支付的条目')}</div>
          <OutlinedTipsClose className="cross-icon" onClick={this.handleCancel} data-testid="pay-repaymentModal-close-icon" />
        </div>
        {(form.payPlanMode || form.payeePayPlan) ? (
          <Provider PayPlanStore={this.payPlanStore}>
            <PayPlayTableWrapper
              className={styles['details-wrapper']}
              isModify={false}
              repay={true}
              handleSelect={this.handleSelect}
              dataSource={flow}
              payPlanValue={form.payPlan}
            />
          </Provider>
        ) : (
          <div className={styles['details-wrapper']}>
            <Details
              type={formType}
              selectedData={selectedData}
              handleChange={this.handleSelect}
              selectAble={true}
              dataSource={visibleDetails}
              unvisibleCount={unvisibleCount}
              showAllFeeType={showAllFeeType}
              apportionVisibleList={apportionVisibleList}
              isAllFeetypes={true}
              inRepaymentModal
              visibleFeeTypes={feetypes}
              specificationComponents={form.specificationId && form.specificationId.components}
            />
          </div>
        )}
        <div className="modal-footer">
          <Button category="secondary" onClick={this.handleCancel} data-testid="pay-repaymentModal-cancel-btn">{i18n.get('取消')}</Button>
          <Button className="btn-ml" disabled={!selectedData.length} onClick={this.handleOk} data-testid="pay-repaymentModal-confirm-btn">
            {i18n.get('确定')}
          </Button>
        </div>
      </>
    )
  }
}
