import React, { Component } from 'react'
import { app } from '@ekuaibao/whispered'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Form} from 'antd'
import { Input, Button } from '@hose/eui'
import { OutlinedTipsClose } from '@hose/eui-icons'
import MessageCenter from '@ekuaibao/messagecenter'
import CommentWords from './CommentWords/CommentWords'
import styles from './rejectPaymentReviewModal.module.less'
import { postPaymentManagementApproval } from '../audit-action'

const Money: any = app.require('@elements/puppet/Money')
const MoneyNzh: any = app.require('@elements/puppet/MoneyNzh')

interface Props {
  line: any
  form?: any
  layer?: any
}

interface State {}

@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer',
})
// @ts-ignore
@Form.create()
export default class RejectPaymentReviewModal extends Component<Props, State> {
  bus: any = new MessageCenter()

  handleAddCommentWords = (words: any) => {
    const remark = this.props.form.getFieldValue('remark')
    this.props.form.setFieldsValue({ remark: `${remark}${words}` })
  }

  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  handleOk = () => {
    this.props.form.validateFieldsAndScroll((errors: any, values: any) => {
      if (!!errors) {
        return
      }
      const data = {
        reviewId: this.props?.line?.id,
        isAgree: false,
        remark: values?.remark,
      }
      postPaymentManagementApproval(data).then(() => {
        this.props.layer.emitOk({})
      })
    })
  }

  render() {
    const { line, form } = this.props
    const { getFieldDecorator } = form
    return (
      <div className={styles['rejectPaymentReviewModal']}>
        <div className="modal-header">
          <div className="flex">{i18n.get('驳回')}</div>
          <OutlinedTipsClose className="cross-icon" onClick={this.handleModalClose} data-testid="pay-rejectPaymentReviewModal-close-icon" />
        </div>
        <Form className="reject-bill-wrapper">
          <Form.Item style={{ marginBottom: 0 }}>
            <MoneyView line={line?.balance} />
          </Form.Item>
          <Form.Item style={{ padding: '0 32px' }}>
            {getFieldDecorator('remark', {
              initialValue: '',
              rules: [
                { required: true, whitespace: false, message: i18n.get('驳回原因不能为空') },
                { max: 1400, message: i18n.get('驳回原因不能超过1400个字符') },
              ],
            })(
              <Input.TextArea
                autosize={{ minRows: 5, maxRows: 6 }}
                placeholder={i18n.get('请填写驳回原因（1400字）')}
              />,
            )}
          </Form.Item>
          <CommentWords
            type={'PAYMENT_REVIEW_REJECT'}
            handleSelectWords={this.handleAddCommentWords}
            commentArea={form.getFieldValue('remark')}
            key={'PAYMENT_REVIEW_REJECT'}
            bus={this.bus}
            commentWordsStyle={{ top: '-38px' }}
          />
        </Form>
        <div className="footer">
          <Button className="mr-8" onClick={() => this.handleOk()} data-testid="pay-rejectPaymentReviewModal-confirm-btn">
            {i18n.get('确定')}
          </Button>
          <Button category="secondary" onClick={() => this.handleModalClose()} data-testid="pay-rejectPaymentReviewModal-cancel-btn">
            {i18n.get('取消')}
          </Button>
        </div>
      </div>
    )
  }
}

function MoneyView(props: any) {
  const { line = {} } = props
  const { standardStrCode, standard, standardNumCode } = line
  return (
    <div className={styles['item-parent']}>
      <div className="config-item">
        <div className="config-left">
          <div className="money-type"> {standardStrCode}</div>
          <span>{i18n.get('（总计）')}</span>
        </div>
        <div className="config-right">
          <Money value={line} style={{ fontWeight: 600 }} />
        </div>
      </div>
      {'zh-CN' === i18n.currentLocale && 'CNY' === standardStrCode && (
        <div className="config-item-zh">
          <div className="config-left">
            <div className="money-type"> {standardNumCode && standardNumCode.name}</div>
          </div>
          <div className="config-right">{MoneyNzh.toMoney(standard, { outSymbol: false })}</div>
        </div>
      )}
    </div>
  )
}
