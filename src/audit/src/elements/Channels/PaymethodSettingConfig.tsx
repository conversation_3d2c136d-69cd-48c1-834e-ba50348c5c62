/*
 * @Author: zhangkai
 * @Date: 2021-11-01 18:36:46
 */
import React, { Component } from 'react'
import { Form } from 'antd'
import EnumFormItem from './components'
import OrderTypeLabel from './OrderTypeLabel'
import { getPayConfigInitData } from '../../audit-action'
import { Fetch } from '@ekuaibao/fetch'
import { isArray } from '@ekuaibao/helpers'
import { fnShowErrorModal } from './util'

const FormItem = Form.Item

interface Props {
  payeeBankNames: string[]
  paymethodConfig: any[]
  form: any
  selectedAccount: any
  channel: string
  configData: any
  loading: boolean
  setLoading: (flag: boolean) => void
}

interface State {
  configElements: any[]
  initElements: any[]
  changeDataSource: any[]
}

export default class PaymethodSettingConfig extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      configElements: props.configData?.elements || [],
      initElements: props.configData?.elements || [],
      changeDataSource: [],
    }
  }

  componentDidMount() {
    const elements = this.props?.configData?.elements || []
    const _configElements = this.fnFiterData(elements)
    this.setState({ configElements: _configElements, changeDataSource: elements }, () => {
      this.initData(elements)
    })
  }

  initData = async (elements: any) => {
    let data = elements
    let initElements = elements
    let initActionList: any[] = []
    data.map((v: any) => {
      v.initAction && initActionList.push(v.initAction)
    })
    if (!!initActionList.length) {
      const initDataValue: any = await this.fnGetInitData(initActionList)
      const initKeys = Object.keys(initDataValue) || []
      initElements = data.map((item: any) => {
        const _item = this.state?.configElements?.find(v => v.name === item.name) || item
        return {
          ..._item,
          initData: initKeys.includes(_item.name) ? initDataValue[_item.name] : _item.initData,
        }
      })
      data = data.map((item: any) => {
        const _item = this.state?.changeDataSource?.find(v => v.name === item.name) || item
        return {
          ..._item,
          initData: initKeys.includes(_item.name) ? initDataValue[_item.name] : _item.initData,
        }
      })
    }
    const _configElements = this.fnFiterData(data)
    this.setState({
      configElements: _configElements,
      changeDataSource: data,
      initElements: initElements || [],
    })
  }

  fnGetInitData = async (initActionList: any) => {
    const { form, selectedAccount, channel, payeeBankNames, configData } = this.props
    const values = form.getFieldsValue()
    const pageData = configData?.pageData || {}
    const result = await Promise.all(
      initActionList.map((action: string) => {
        return getPayConfigInitData({
          channel,
          action,
          body: {
            ...values,
            account: selectedAccount,
            accountId: selectedAccount.accountNo,
            bankName: selectedAccount.bank,
            corporationId: Fetch?.ekbCorpId,
            payeeBankNames,
            ...pageData,
          },
        })
      }),
    )
    fnShowErrorModal(result)
    const initDataValue =
      result?.reduce((prev, item) => {
        return { ...prev, ...item?.items }
      }, {}) || {}

    return initDataValue
  }

  setPaymethodConfigValue = (propsValue: any) => {
    const { type } = propsValue
    const { form } = this.props
    const { configElements, initElements = [] } = this.state
    if (type === 'initData') {
      const { initValues, eventValues } = propsValue
      const keys = Object.keys(initValues) || []
      const data = initElements?.map((item: any) => {
        const _item = configElements.find(v => v.name === item.name) || item
        const { name, initData } = _item
        if (keys.includes(name)) {
          const _data = initValues[name] || []
          const selectedData = isArray(_data) ? _data.find((v: any) => v.selected) || {} : _data
          form.setFieldsValue({ [name]: isArray(_data) ? selectedData?.code : _data })
        }
        return {
          ..._item,
          initData: keys.includes(name) ? initValues[name] : initData,
          hidden: eventValues[_item?.name] ? eventValues[_item?.name]?._hidden : _item?.hidden,
          require: eventValues[_item?.name] ? eventValues[_item?.name]?._require : _item?.require,
        }
      })
      const _configElements = this.fnFiterData(data, true)
      this.setState({ configElements: _configElements, changeDataSource: data })
    } else if (type === 'hiddenOrRequire') {
      const { values } = propsValue
      const data = initElements.map((item: any) => {
        const _item = configElements.find(v => v.name === item.name) || item
        return {
          ..._item,
          hidden: values[_item?.name] ? values[_item?.name]?._hidden : _item?.hidden,
          require: values[_item?.name] ? values[_item?.name]?._require : _item?.require,
        }
      })
      const _configElements = this.fnFiterData(data, true)
      this.setState({ configElements: _configElements, changeDataSource: data })
    }
  }

  fnFiterData = (data: any, flag?: boolean) => {
    if (flag) {
      let map: any = {}
      const hiddenValues = data.filter((item: any) => item.hidden)
      hiddenValues.forEach((e: any) => (map[e?.name] = ''))
      this.props.form.setFieldsValue({ ...map })
    }
    return data.filter((item: any) => !item.hidden)
  }

  //组装表单字段的动态规则
  validator = (rules: any, rule: any, value: any, callback: any) => {
    for (const v of rules) {
      const { regex, warn } = v
      if (value && !new RegExp(regex).test(value)) {
        callback(warn)
      }
      callback()
    }
    callback()
  }

  getRules = (require: boolean, list: any, label: string) => {
    let rules = []
    if (require) {
      rules.push({ required: require, message: `${label}为必填字段` })
    }
    if (list) {
      rules.push({ validator: this.validator.bind(this, list) })
    }
    return rules
  }

  getComponent = (type: string) => {
    return EnumFormItem[type] || 'input'
  }

  fnGetInitialValue = (item: any) => {
    const { type, defaultVal, initData = [] } = item
    if (defaultVal) {
      if (type === 'select') {
        const dValue = initData?.find((v: any) => v.code === defaultVal)
        const sValue = initData?.find((v: any) => v.selected) || {}
        return dValue ? dValue : sValue
      } else {
        return defaultVal
      }
    } else {
      return type === 'select' ? initData?.find((v: any) => v.selected) || {} : initData
    }
  }

  render() {
    const { configElements } = this.state
    const { form, selectedAccount, channel, setLoading, payeeBankNames, configData, loading } = this.props

    return configElements.map((item: any) => {
      const { desc, label, name, type, require, rules = [] } = item
      const Comp = this.getComponent(type)
      const _lable = i18n.get(label)
      const _desc = i18n.get(desc)
      const labelValue = desc ? <OrderTypeLabel label={_lable} tooltipTitle={_desc} /> : _lable
      const initialValue = this.fnGetInitialValue(item)

      return (
        <FormItem label={labelValue} key={name}>
          {form.getFieldDecorator(name, {
            initialValue: type === 'select' ? initialValue?.code : initialValue,
            rules: this.getRules(require, rules, label),
          })(
            <Comp
              payeeBankNames={payeeBankNames}
              data={item}
              form={form}
              channel={channel}
              selectedAccount={selectedAccount}
              configData={configData}
              loading={loading}
              setPaymethodConfigValue={this.setPaymethodConfigValue}
              setLoading={setLoading}
              data-testid={`pay-customPaymentBranch-${name}-input`}
            />,
          )}
        </FormItem>
      )
    })
  }
}
