import React, { useEffect, useState } from 'react'
import { Form, Select } from 'antd'
import { getPayModels, getTransactionType } from '../../audit-action'
import Label from './OrderTypeLabel'
import { showMessage } from '@ekuaibao/show-util'

const FormItem = Form.Item
const Option = Select.Option

interface Props {
  orderTypeLabel: string
  form: any
  orderTypes: any[]
  dynamicChannels: any
  selectedAccount: any
}
const CODE = 'CMBCBC'
// 招商银行云·直联
export default function CMBCBCPay(props: Props) {
  const { orderTypeLabel, form, dynamicChannels, selectedAccount } = props
  const [orderTypes, setOrderTypes] = useState([])
  const [modelTypes, setModelTypes] = useState([])
  const [defaultModelType, setDefaultModelType] = useState('')
  const [orderTypeValue, setOrderTypeValue] = useState('')
  const [transactionTypeList, setTransactionTypeList] = useState([] as any)
  const [initTransactionCode, setInitTransactionCode] = useState({ code: '' })
  const [notFoundContent, setNotFoundContent] = useState('')

  useEffect(() => {
    if (dynamicChannels && dynamicChannels.length) {
      const obj = dynamicChannels.find((line: any) => line.code === CODE)
      obj && setOrderTypes(obj.orderType)
      const orderTypeSelect = orderTypeValue || obj?.orderTypeSelect || 'onePay'
      setOrderTypeValue(orderTypeSelect)
      fnGetPayModels(orderTypeSelect)
    }
  }, [dynamicChannels])

  useEffect(() => {
    getTransactionTypeList()
  }, [selectedAccount])

  const handleChange = (value: string) => {
    setOrderTypeValue(value)
    fnGetPayModels(value)
    form.setFieldsValue({ remark: '' })
  }
  const fnGetPayModels = (value: string) => {
    getPayModels(CODE, value)
      .then((result: any) => {
        const items = result?.items
        if (items) {
          setModelTypes(items)
          const obj = items.find((line: any) => line.isChecked)
          setDefaultModelType(obj?.code)
          form.setFieldsValue({ modelType: obj?.code })
        }
      })
      .catch((error: any) => {
        showMessage.error(i18n.get(error.errorMessage))
      })
  }

  const getTransactionTypeList = () => {
    let content = i18n.get('数据请求中。。。')
    setNotFoundContent(content)
    getTransactionType(CODE, selectedAccount.accountNo)
      .then(({ items }: { items: any[] }) => {
        if (!items.length) {
          content = i18n.get('无交易类型，请确认本企业是否与银行签署代发协议')
          setNotFoundContent(content)
        }
        setTransactionTypeList(items)
        const initTransactionCode = items.find(v => v.isChecked) || items[0] || { code: '' }
        setInitTransactionCode(initTransactionCode)
      })
      .catch(() => {
        content = i18n.get('当前用户对账号23455无操作权限，请登陆ubank，添加该账户经办授权', {
          accountNo: selectedAccount.accountNo,
        })
        setNotFoundContent(content)
        setTransactionTypeList([])
        setInitTransactionCode({ code: '' })
      })
  }

  return (
    <div>
      <FormItem label={orderTypeLabel} className="select-payment-payRemark">
        {form.getFieldDecorator('orderType', {
          initialValue: orderTypeValue || '',
          rules: [{ required: true, message: i18n.get('请选择银行下单类型') }],
        })(
          <Select
            className="method-line"
            placeholder={i18n.get('请选择银行下单类型')}
            onChange={handleChange as any}
            data-testid="pay-customPaymentBranch-select">
            {orderTypes?.map((line: any) => {
              return (
                <Option key={line.code} value={line.code} data-testid={`pay-customPaymentBranch-option-${line.code}`}>
                  {i18n.get(line.name)}
                </Option>
              )
            })}
          </Select>,
        )}
      </FormItem>

      <FormItem label={i18n.get('业务模式')} className="select-payment-payRemark">
        {form.getFieldDecorator('modelType', {
          initialValue: defaultModelType,
          rules: [{ required: true, message: i18n.get('请选择业务模式') }],
        })(
          <Select className="method-line" placeholder={i18n.get('请选择业务模式')} data-testid="pay-customPaymentBranch-businessType-select">
            {modelTypes?.map((line: any) => {
              return (
                <Option key={line.code} value={line.code} data-testid={`pay-customPaymentBranch-businessType-option-${line.code}`}>
                  {i18n.get(line.name)}
                </Option>
              )
            })}
          </Select>,
        )}
      </FormItem>

      {orderTypeValue === 'batchAgentPay' && (
        <FormItem
          label={
            <Label
              label={i18n.get('交易类型名称')}
              tooltipTitle={i18n.get(
                '该字段展示的是企业与银行签约代发协议内容，如无数据，请在下拉框中查看原因',
              )}
            />
          }
          className="select-payment-payRemark">
          {form.getFieldDecorator('transactionCode', {
            initialValue: initTransactionCode.code,
            rules: [{ required: true, message: i18n.get('请选择交易类型名称') }],
          })(
            <Select
            className="method-line"
            placeholder={i18n.get('请选择交易类型名称')}
            notFoundContent={notFoundContent}
            data-testid="pay-customPaymentBranch-transactionType-select">
              {transactionTypeList?.map((line: any) => {
                return (
                <Option key={line.code} value={line.code} data-testid={`pay-customPaymentBranch-transactionType-option-${line.code}`}>
                  {i18n.get(line.name)}
                </Option>
              )
              })}
            </Select>,
          )}
        </FormItem>
      )}
    </div>
  )
}
