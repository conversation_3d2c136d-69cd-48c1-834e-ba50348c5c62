import React from 'react'
import { Modal } from '@hose/eui'
import { typeMap } from '../../util/Utils'
import { keyBy } from 'lodash'
const map: any = typeMap

const fnEventSetConfigValue = (form: any, eventsArr: any, configData: any) => {
  const values: any = {}
  eventsArr.forEach((events: any) => {
    const data = fnSetHiddenOrRequire(form, events, configData)
    values[events.name] = data
  })
  return values
}

const fnSetHiddenOrRequire = (form: any, events: any, configData: any) => {
  const hiddenList = events?.hidden?.enumList || []
  const requireList = events?.require?.enumList || []
  const obj = keyBy(configData?.elements || [], 'name')
  let _hidden: boolean = obj[events?.name]?.hidden
  let _require: boolean = obj[events?.name]?.require
  if (hiddenList?.length) {
    const linkType = events?.hidden?.linkType
    _hidden = fnGetConditions({ list: hiddenList, linkType, form, configData })
  }
  if (requireList?.length) {
    const linkType = events?.require?.linkType
    _require = fnGetConditions({ list: requireList, linkType, form, configData })
  }
  return {
    _hidden,
    _require,
  }
}

const fnGetConditions = ({ list, linkType, form, configData }: any) => {
  const values = form.getFieldsValue()
  const valueParam = { ...values, ...configData?.pageData }
  let value = ''
  const conditions = list.map((item: any) => {
    const { label, value, setType } = item
    return `"${valueParam[label]}" ${map[setType]} "${value}"`
  })

  if (list?.length === 1) {
    value = `${conditions[0]}`
  } else {
    const type = map[linkType]
    value = `${conditions.join(` ${type} `)}`
  }
  return eval(value || 'true')
}

export { fnEventSetConfigValue }

export const fnShowErrorModal = (result: any[]) => {
  const errorArr: any = result.filter(v => v?.items?.errorMessage).map(v => v.items.errorMessage)
  if (errorArr.length) {
    Modal.error({
      title: '提示',
      content: errorArr.map((item: any, index: number) => <div key={index}>{item}</div>),
    })
  }
}
