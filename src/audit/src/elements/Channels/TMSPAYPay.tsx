import React from 'react'
import { Form, Select } from 'antd'
import OrderTypeLabel from './OrderTypeLabel'
const FormItem = Form.Item
const Option = Select.Option
// 财资大管家
interface Props {
  form: any
  channelMap: any
  selectedAccount: any
}
export function TMSPAYPay(props: Props) {
  const { form, channelMap, selectedAccount } = props
  const batchPayFlagValue = form.getFieldValue('batchPayFlag')
  const showReceiptNumFlag = selectedAccount.bank === '招商银行' && batchPayFlagValue === 2
  const orderTypeLabel = (
    <OrderTypeLabel
      tooltipTitle={i18n.get(
        '银行下单类型包括批量支付（转账）、单笔支付（转账）或批量代发，具体以支付渠道为准。不同业务类型的手续费用和适用场景请咨询相应银行服务人员。',
      )}
    />
  )
  const receiptNumFlagLabel = (
    <OrderTypeLabel
      label={i18n.get('回单张数')}
      tooltipTitle={i18n.get('当付款账号是招商银行时，批量代发支持多笔单据一张回单')}
    />
  )
  return (
    <>
      <FormItem label={orderTypeLabel} className="select-payment-payRemark">
        {form.getFieldDecorator('batchPayFlag', {
          initialValue: channelMap.batchPay ? 1 : 0,
        })(
          <Select className="method-line" placeholder={i18n.get('请选择银行下单类型')} data-testid="pay-customPaymentBranch-select">
            {channelMap.batchPay && <Option value={1} data-testid="pay-customPaymentBranch-option-1">{i18n.get('批量转账')}</Option>}
            {channelMap.batchPay && <Option value={2} data-testid="pay-customPaymentBranch-option-2">{i18n.get('批量代发')}</Option>}
            {channelMap.singlePay && <Option value={0} data-testid="pay-customPaymentBranch-option-0">{i18n.get('单笔转账')}</Option>}
          </Select>,
        )}
      </FormItem>
      {showReceiptNumFlag && (
        <FormItem label={receiptNumFlagLabel} className="select-payment-payRemark">
          {form.getFieldDecorator('receiptNumFlag', {
            initialValue: channelMap.receiptNumFlag ? 0 : 1,
          })(
            <Select className="method-line" placeholder={i18n.get('请选择回单张数')} data-testid="pay-customPaymentBranch-receiptNum-select">
              <Option value={0} data-testid="pay-customPaymentBranch-receiptNum-option-0">{i18n.get('多张')}</Option>
              <Option value={1} data-testid="pay-customPaymentBranch-receiptNum-option-1">{i18n.get('一张')}</Option>
            </Select>,
          )}
        </FormItem>
      )}
    </>
  )
}
