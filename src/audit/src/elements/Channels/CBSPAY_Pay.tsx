/**************************************
 * Created By LinK On 2021/7/14 10:59.
 **************************************/
import React from 'react'
import { Form, Select } from 'antd'
import OrderTypeLabel from './OrderTypeLabel'
const FormItem = Form.Item
const Option = Select.Option

interface Props {
  form: any
  channelMap: any
}

function CBSPAY_Pay(props: Props) {
  const { form, channelMap } = props
  const orderTypeLabel = (
    <OrderTypeLabel
      tooltipTitle={i18n.get(
        '若需要使用银行代发业务，请选择「代发经办」；若使用银行直接支付业务，请选择「支付经办」。',
      )}
    />
  )
  return (
    <FormItem label={orderTypeLabel} className="select-payment-payRemark">
      {form.getFieldDecorator('batchPayFlag', {
        initialValue: 1,
      })(
        <Select className="method-line" placeholder={i18n.get('请选择银行下单类型')} data-testid="pay-customPaymentBranch-select">
          <Option value={1} data-testid="pay-customPaymentBranch-option-1">{i18n.get('支付经办')}</Option>
          {channelMap.agencyPay && <Option value={2} data-testid="pay-customPaymentBranch-option-2">{i18n.get('代发经办')}</Option>}
        </Select>,
      )}
    </FormItem>
  )
}

export default CBSPAY_Pay