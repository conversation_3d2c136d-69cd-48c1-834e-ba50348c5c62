/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/3/15 下午5:12
 */
import React, { PureComponent } from 'react'
import { getPayerInfoConfig } from '../../../audit-action'
import AbstractByHand from './AbstractByHand'
import AbstractByAutoSummary from './AbstractByAutoSummary'
import AbstractUseSpecial from './AbstractUseSpecial'
import AbstractByAutoSummarySwitch from './AbstractByAutoSummarySwitch'
import AbstractByChanPay from './AbstractByChanPay'
interface Props {
  selectedAccount: any
  dynamicChannelMap: any
  channel: string
  form: any
  isAutoSummary: boolean
  isAutoCutRemark: boolean
  onChangeAutoSummary: (value: any) => void
  getPayerInfoConfig: (config: any) => void
  onAutoCutRemarkChange: (value: any) => void
}
interface State {
  selectedAccount: any
  payerInfoConfig: any
}
export default class AbstractPay extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      selectedAccount: props.selectedAccount,
      payerInfoConfig: {},
    }
  }
  static getDerivedStateFromProps(props: any, state: any) {
    if (props.selectedAccount !== state.selectedAccount) {
      return {
        selectedAccount: props.selectedAccount,
      }
    }
    return null
  }
  componentDidUpdate(prevProps: any, prevState: any) {
    if (this.state.selectedAccount !== prevState.selectedAccount) {
      // 请求详情数据
      this._getPayerInfoConfig(this.state.selectedAccount.id)
    }
  }

  _getPayerInfoConfig(id: string) {
    getPayerInfoConfig(id).then(res => {
      // byHand: 手工填写   useSpecial: 使用特殊定段  autoSummary: 自动填写   fieldLabel: 自动填写的字段
      const { getPayerInfoConfig } = this.props
      getPayerInfoConfig && getPayerInfoConfig(res.value)
      this.setState({ payerInfoConfig: res.value })
    })
  }

  handleRemarkChange = (e: any) => {
    const { checked } = e.target
    const { onChangeAutoSummary } = this.props
    onChangeAutoSummary && onChangeAutoSummary(e)
    if (checked) {
      const { form } = this.props
      form.setFieldsValue({ remark: '' })
    }
  }
  handleRemarkValueChange = (value: any) => {
    const { form } = this.props
    form.setFieldsValue({ remark: value })
  }

  render() {
    const { dynamicChannelMap, channel, form, selectedAccount, isAutoSummary, isAutoCutRemark, onAutoCutRemarkChange } = this.props
    const channelMap = dynamicChannelMap[channel] || {}
    const { payerInfoConfig } = this.state
    return (
      <>
        <AbstractByHand
          payerInfoConfig={payerInfoConfig}
          form={form}
          channel={channel}
          selectedAccount={selectedAccount}
          dynamicChannelMap={dynamicChannelMap}
          isAutoSummary={isAutoSummary}
        />
        <AbstractUseSpecial
          form={form}
          channelMap={channelMap}
          payerInfoConfig={payerInfoConfig}
          isAutoSummary={isAutoSummary}
        />
        <AbstractByAutoSummarySwitch
          form={form}
          channel={channel}
          channelMap={channelMap}
          handleRemarkValueChange={this.handleRemarkValueChange}
          isAutoSummary={isAutoSummary}
          isAutoCutRemark={isAutoCutRemark}
          payerInfoConfig={payerInfoConfig}
          onChange={this.handleRemarkChange}
          onAutoCutRemarkChange={onAutoCutRemarkChange}
        />
        <AbstractByAutoSummary
          payerInfoConfig={payerInfoConfig}
          channel={channel}
          isAutoSummary={isAutoSummary}
          channelMap={channelMap}
          form={form}
          onChange={this.handleRemarkChange}
        />
        <AbstractByChanPay
          channel={channel}
          onChange={this.handleRemarkChange}
          isAutoSummary={isAutoSummary}
        />
      </>
    )
  }
}
