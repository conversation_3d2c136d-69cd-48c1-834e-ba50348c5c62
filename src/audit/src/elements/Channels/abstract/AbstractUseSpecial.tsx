import React, { PureComponent } from 'react'
import { Form } from 'antd'
const FormItem = Form.Item
import { app } from '@ekuaibao/whispered'
import { getDimensionList } from '../../../audit-action'
const TreeSelectSingle = app.require('@elements/puppet/TreeSelectSingle')
interface Props {
  form: any
  channelMap: any
  isAutoSummary: boolean
  payerInfoConfig: { dimensionItemId: string; useSpecial: boolean; dimensionId: string }
}

interface State {
  dimensionItemId: any
  dimensionList: any[]
  payerInfoConfig: any
}
export default class AbstractUseSpecial extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      dimensionItemId: props.payerInfoConfig.dimensionItemId,
      dimensionList: [],
      payerInfoConfig: props.payerInfoConfig,
    }
  }

  static getDerivedStateFromProps(props: any, state: any) {
    if (props.payerInfoConfig !== state.payerInfoConfig) {
      return {
        payerInfoConfig: props.payerInfoConfig,
      }
    }
    return null
  }
  componentDidUpdate(prevProps: any, prevState: any) {
    if (this.state.payerInfoConfig !== prevState.payerInfoConfig) {
      // 请求详情数据
      const { useSpecial, dimensionId } = this.state.payerInfoConfig
      if (useSpecial && dimensionId != '') {
        getDimensionList('basedata.Dimension.' + dimensionId).then(res => {
          this.setState({ dimensionList: res.items })
        })
      }
    }
  }
  onChange = (value: any = {}) => {
    this.setState({ dimensionItemId: value.id })
  }
  render() {
    const { channelMap, isAutoSummary, payerInfoConfig } = this.props
    const { useSpecial } = payerInfoConfig
    const isShow = channelMap.needRemark && useSpecial
    if (!isShow) return null
    const { dimensionItemId, dimensionList } = this.state
    const dimensionData = {
      id: dimensionItemId,
      editDept: true,
      placeholder: i18n.get('请选择自定义档案'),
      treeNodeData: dimensionList,
      disabled: isAutoSummary,
      onChange: this.onChange,
      isChangePosition: true,
    }
    const {
      form: { getFieldDecorator },
    } = this.props
    return (
      <FormItem label={i18n.get('支付摘要')} className="select-payment-payRemark">
        {getFieldDecorator('dimensionItemId', {
          initialValue: dimensionItemId,
        })(<TreeSelectSingle data={dimensionData} useEUI={true} />)}
      </FormItem>
    )
  }
}
