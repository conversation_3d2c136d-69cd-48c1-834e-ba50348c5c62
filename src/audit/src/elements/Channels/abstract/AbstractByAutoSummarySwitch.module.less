.payment-summary-wrapper {
  display: flex;
  flex-direction: column;
  max-height: 380px;
  width: 332px;
  overflow-x: hidden;
  overflow-y: auto;
  :global {
    .summary-list {
      display: flex;
      position: relative;
      flex-direction: column;
      .eui-popover-arrow{
        right: 9px;
      }
      .summary-list-item {
        display: flex;
        align-items: center;
        min-height: 32px;
        margin-bottom: 8px;
        justify-content: space-between;
        flex-direction: row;
        margin-left: 4px;
        margin-right: 4px;
        padding: 4px 8px;
        .summary-list-item-icon {
          color: var(--eui-icon-n3);
        }
        .summary-list-item-text {
          color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
          font: var(--eui-font-body-r1);
        }
        .summary-list-item-right-icon {
          display: none;
        }
      }
      .item-hover-show{
        background: var(--eui-fill-hover, rgba(29, 33, 41, 0.05));
        border-radius: 4px;
        .summary-list-item-right-icon {
          display: block;
          cursor: pointer;
          border-radius: 6px;
          align-content: center;
          color: var(--eui-icon-n2);
          width: 24px;
          height: 24px;
          text-align: center;
          &:hover {
            background: var(--eui-fill-pressed, rgba(29, 33, 41, 0.10));
          }
        }
      }
      .item-hover {
        &:hover {
          background: var(--eui-fill-hover, rgba(29, 33, 41, 0.05));
          border-radius: 4px;
          .summary-list-item-right-icon {
            display: block;
            cursor: pointer;
            border-radius: 6px;
            align-content: center;
            color: var(--eui-icon-n2);
            width: 24px;
            height: 24px;
            text-align: center;
            &:hover {
              background: var(--eui-fill-pressed, rgba(29, 33, 41, 0.1));
            }
          }
        }
      }
    }
    .summary-list-item-add {
      width: fit-content;
      margin: 0 12px;
    }
  }
  .modal-footer {
    border: none;
    padding: 16px 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
  }
}
.overlayClassName {
  min-width: 308px !important;
  padding: 12px 0px;
  border-radius: 8px;
  overflow: hidden;
  background: var(--eui-bg-float, #fff);
  box-shadow: var(--eui-shadow-down-3);
}
