import React, { useEffect, useState } from 'react'
import { Form } from 'antd'
import { Checkbox } from '@hose/eui'
const FormItem = Form.Item
interface Props {
  channel: string
  isAutoSummary: boolean
  channelMap: any
  form: any
  payerInfoConfig: {
    autoSummary: boolean
    useSpecial: boolean
    byHand: boolean
    fieldLabel: string
  }
  onChange: (value: any) => void
}

export default function AbstractByAutoSummary(props: Props) {
  const [isAutoSummary, setAutoSummary] = useState(props.isAutoSummary)
  useEffect(() => {
    setAutoSummary(props.isAutoSummary)
  })
  const { channel, channelMap, payerInfoConfig } = props
  const { autoSummary, useSpecial, byHand, fieldLabel } = payerInfoConfig
  const isShow =
    channelMap.needRemark && autoSummary && !useSpecial && !byHand && channel !== 'CHANPAY'
  if (!isShow) return null
  return (
    <FormItem label={i18n.get('支付摘要')} className="select-payment-payRemark">
      <Checkbox
        className="select-payment-checkbox"
        checked={isAutoSummary}
        onChange={(e: any) => {
          const { onChange } = props
          onChange && onChange(e)
          setAutoSummary(e.target.checked)
        }}
        data-testid="pay-customPaymentBranch-autoSummary-checkbox">
        {i18n.get('使用')}
        {`"${fieldLabel}"`}
        {i18n.get('作为支付摘要')}
      </Checkbox>
    </FormItem>
  )
}
