/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/3/15 上午11:13
 */
import React, { PureComponent } from 'react'
import { Form } from 'antd'
import { Input } from '@hose/eui'
import { ValidationRule } from 'antd/lib/form'

const FormItem = Form.Item
const { TextArea } = Input
interface Props {
  form: any
  channel: string
  selectedAccount: any
  dynamicChannelMap: any
  isAutoSummary: boolean
  payerInfoConfig: { byHand: boolean; useSpecial: boolean }
}

const requiredList = ['CBSPAY', 'SPDMTS', 'CMBCBS8', 'CCBCMS', 'HZBANKV2', 'CGBCMS', 'CIB', 'PABNEW', 'CMBCBS8OVS']
export default class AbstractByHand extends PureComponent<Props> {
  checkRemark = (rule: any, value: any, callback: (value?: string) => void) => {
    if (value) {
      const patter = /[$~@#%&^<>{}\[\]`'"+=\*()]/
      if (patter.test(value)) return callback(i18n.get('请不要输入特殊字符'))
      if (this.checkLength(value, 500)) return callback(i18n.get('不能超过{__k0}个文字', { __k0: 500 }))
    }
    callback()
  }
  checkLength = (val: any, remarkLimit: number) => {
    let valLength = 0
    for (let ii = 0; ii < val.length; ii++) {
      const word = val.split('')[ii]
      if (/[^\x20-\xff]/g.test(word)) {
        valLength += 2
      } else {
        valLength++
      }
    }
    return valLength > remarkLimit * 2
  }
  render() {
    const { payerInfoConfig, channel, selectedAccount, isAutoSummary, dynamicChannelMap } = this.props
    const channelMap = dynamicChannelMap[channel] || {}
    const { byHand, useSpecial } = payerInfoConfig
    const isShow = channelMap.needRemark && ((byHand && !useSpecial) || channel === 'CHANPAY')
    if (!isShow) return null
    const remarkLimit =
      selectedAccount && channelMap.needRemark
        ? channelMap.remarkLength[selectedAccount.bank] || channelMap.remarkLength['默认'] || 28 // @i18n-ignore
        : 28
    const {
      form: { getFieldDecorator },
    } = this.props
    const rules: ValidationRule[] = [
      { message: i18n.get('不能超过{__k0}个文字', { __k0: 500 }) },
      { validator: this.checkRemark },
    ]
    const required = requiredList.includes(channelMap?.code)
    if (required && !isAutoSummary) {
      rules.push({ required: true, whitespace: true, message: i18n.get('支付摘要是必填项') })
    }

    // CIB 兴业银企直联 摘要字数必须大于三个字
    if (channel === 'CIB' && !isAutoSummary) {
      const handleValidateMinLength = (_rule: any[], value: string, callback: any) => {
        if (value?.length <= 3) {
          return callback(i18n.get('支付摘要字数必须超过3个字'))
        }
        callback()
      }
      rules.push({ validator: handleValidateMinLength })
    }

    const placeholder = required
      ? i18n.get('请填写摘要内容，建议不超过{__k0}个汉字', { __k0: remarkLimit })
      : i18n.get('（选填）请填写摘要内容，建议不超过{__k0}个汉字', { __k0: remarkLimit })
    return (
      <FormItem label={i18n.get('支付摘要')} className="select-payment-payRemark">
        {getFieldDecorator('remark', {
          rules,
          validateFirst: true,
        })(
          <TextArea
            disabled={isAutoSummary}
            placeholder={placeholder}
            data-testid="pay-customPaymentBranch-manual-remark-input"
          />,
        )}
      </FormItem>
    )
  }
}
