import React, { useState } from 'react'
import { Checkbox } from '@hose/eui'
interface Props {
  channel: string
  onChange: (value: any) => void
  isAutoSummary: boolean
}

export default function AbstractByChanPay(props: Props) {
  const [isAutoSummary, setAutoSummary] = useState(props.isAutoSummary)
  const { channel } = props
  if (channel !== 'CHANPAY') return null
  return (
    <Checkbox
      className="select-payment-checkbox"
      checked={isAutoSummary}
      onChange={(e: any) => {
        const { onChange } = props
        onChange && onChange(e)
        setAutoSummary(e.target.checked)
      }}
      data-testid="pay-customPaymentBranch-chanpay-autoSummary-checkbox">
      {'使用单据标题作为支付摘要'}
    </Checkbox>
  )
}
