import React, { useEffect, useState } from 'react'
import { Button, Checkbox, Dropdown, Input, message, Popconfirm, Space } from '@hose/eui'
import { OutlinedEditDeleteTrash, OutlinedEditEdit, OutlinedGeneralChatNews, OutlinedTipsAdd } from '@hose/eui-icons'
import { app as api } from '@ekuaibao/whispered'
const { getBoolVariation } = api.require<{ getBoolVariation: (key: string, defaultValue?: boolean) => boolean }>(
  '@lib/featbit',
)
import {
  deletePaymentPhrase,
  getPaymentPhraseList,
  insertPaymentPhrase,
  updatePaymentPhrase,
} from '../../../audit-action'
import styles from './AbstractByAutoSummarySwitch.module.less'
interface Props {
  channel: string
  isAutoSummary: boolean
  isAutoCutRemark: boolean
  channelMap: any
  form: any
  onChange: (value: any) => void
  onAutoCutRemarkChange: (value: any) => void
  handleRemarkValueChange: (value: any) => void
  payerInfoConfig: {
    autoSummary: boolean
    useSpecial: boolean
    byHand: boolean
    fieldLabel: string
  }
}
interface DataInfo {
  content: string
  id?: string
  isCustom?: boolean
}
export default function AbstractByAutosummary(props: Props) {
  const [isAutoSummary, setIsAutoSummary] = useState(props.isAutoSummary)
  const [isAutoCutRemark, setIsAutoCutRemark] = useState(props.isAutoCutRemark)
  const [dropdownOpen, setDropdownOpen] = useState(false)
  const [editIndex, setEditIndex] = useState(-1)
  const [openIndex, setOpenIndex] = useState(-1)
  const [inputText, setInputText] = useState('')
  const [list, setList] = useState<DataInfo[]>([])
  useEffect(() => {
    setIsAutoSummary(props.isAutoSummary)
    setIsAutoCutRemark(props.isAutoCutRemark)
  }, [props.isAutoSummary, props.isAutoCutRemark])
  const updateList = () => {
    getPaymentPhraseList().then(res => {
      setList(res?.items ?? [])
      setEditIndex(-1)
      setInputText('')
    })
  }

  useEffect(() => {
    updateList()
  }, [])

  const { channel, channelMap, payerInfoConfig, onChange, onAutoCutRemarkChange } = props
  const { autoSummary, fieldLabel, useSpecial, byHand } = payerInfoConfig
  const isShow = channelMap.needRemark && autoSummary && (useSpecial || byHand) && channel !== 'CHANPAY'
  // useSpecial 必须使用特定字段，这个地方会显示下拉框
  const isByHand =
    channelMap.needRemark && byHand && channel === 'CMBCBS8' && !useSpecial && getBoolVariation('Commonpaymentterms')
  // 支付摘要开关getBoolVariation('Commonpaymentterms')

  if (!isShow && !isByHand) return null

  const handleOnChange = (e: any) => {
    setInputText(e.target.value)
  }
  const handleSaveItem = (item: DataInfo) => {
    if (!item.id) {
      insertPaymentPhrase(inputText)
        .then(res => {
          if (res?.value) {
            message.success(i18n.get('添加成功'))
            updateList()
          }
        })
        .catch(error => {
          message.error(error.errorMessage || i18n.get('添加失败'))
        })
    } else {
      updatePaymentPhrase(item.id, inputText, item.isCustom)
        .then(res => {
          if (res?.value) {
            message.success(i18n.get('修改成功'))
            updateList()
          }
        })
        .catch(error => {
          message.error(error.errorMessage || i18n.get('修改失败'))
        })
    }
  }
  const handleOnCancelEdit = (index: number) => {
    setEditIndex(-1)
    setInputText('')
    const item = list[index]
    if (!item.content) {
      const newDataList = list.slice()
      newDataList.splice(index, 1)
      setList(newDataList)
    }
  }
  const handleEditItem = (index: number) => {
    setEditIndex(index)
    setInputText(list[index].content)
  }
  const hanldeDelete = (index: number) => {
    deletePaymentPhrase(list[index])
      .then(res => {
        if (res?.value) {
          message.success(i18n.get('删除成功'))
          updateList()
        }
      })
      .catch(error => {
        message.error(error.errorMessage || i18n.get('删除失败'))
      })
  }
  const handelAdd = () => {
    const newList = list.slice()
    newList.push({
      content: '',
    })
    setList(newList)
    setEditIndex(newList.length - 1)
  }
  const handleOnItemClick = (item: DataInfo) => {
    props?.handleRemarkValueChange?.(item.content)
    setDropdownOpen(false)
  }
  const dropdownRender = () => {
    return (
      <div className={styles['payment-summary-wrapper']}>
        <div className="summary-list">
          {list.map((item, index: number) => {
            if (index === editIndex) {
              return (
                <div
                  style={{
                    width: '100%',
                    marginBottom: 8,
                    paddingLeft: 12,
                    paddingRight: 12,
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}
                >
                  <Input
                    onChange={handleOnChange}
                    showCount
                    value={inputText}
                    style={{ flex: 1, marginRight: 8 }}
                    maxLength={30}
                    placeholder={i18n.get('请输入常用语')}
                    data-testid="pay-customPaymentBranch-phrase-input"
                  />
                  <Button
                    disabled={!inputText}
                    onClick={() => handleSaveItem(item)}
                    category="text"
                    size="small"
                    style={{ marginRight: 8 }}
                    theme="highlight"
                    data-testid="pay-customPaymentBranch-savePhrase-btn"
                  >
                    {i18n.get('保存')}
                  </Button>
                  <Button onClick={() => handleOnCancelEdit(index)} size="small" category="text" data-testid="pay-customPaymentBranch-cancelEdit-btn">
                    {i18n.get('取消')}
                  </Button>
                </div>
              )
            }
            return (
              <div
                onClick={() => handleOnItemClick(item)}
                className={`summary-list-item ${editIndex < 0 ? 'item-hover' : ''} ${
                  openIndex === index ? 'item-hover-show' : ''
                }`}
              >
                <Space>
                  <OutlinedGeneralChatNews fontSize={16} className="summary-list-item-icon" />
                  <span className="summary-list-item-text">{item.content}</span>
                </Space>
                <Space>
                  <div
                      className="summary-list-item-right-icon"
                      onClick={(e: any) => {
                        e.stopPropagation()
                        e.preventDefault()
                        handleEditItem(index)
                      }}
                      data-testid={`pay-customPaymentBranch-editPhrase-${index}-btn`}
                    >
                    <OutlinedEditEdit fontSize={16} />
                  </div>

                  <Popconfirm
                    title="确定删除该常用语？"
                    arrowPointAtCenter
                    placement="topRight"
                    onOpenChange={(open: boolean) => {
                      setOpenIndex(!open ? -1 : index)
                    }}
                    zIndex={9999}
                    getPopupContainer={
                      list.length > 2 ? (triggerNode: any) => triggerNode.closest('.summary-list') : () => document.body
                    }
                    onCancel={(e: any) => {
                      e.stopPropagation()
                      e.preventDefault()
                    }}
                    onConfirm={(e: any) => {
                      e.stopPropagation()
                      e.preventDefault()
                      hanldeDelete(index)
                    }}
                    okText={i18n.get('删除')}
                    okButtonProps={{ category: 'primary', theme: 'danger' }}
                    cancelText={i18n.get('取消')}
                  >
                    <div
                      className="summary-list-item-right-icon"
                      onClick={(e: any) => {
                        e.stopPropagation()
                        e.preventDefault()
                      }}
                      data-testid={`pay-customPaymentBranch-deletePhrase-${index}-btn`}
                    >
                      <OutlinedEditDeleteTrash fontSize={16} />
                    </div>
                  </Popconfirm>
                </Space>
              </div>
            )
          })}
        </div>
        <Button
            className="summary-list-item-add"
            category="text"
            size="small"
            disabled={editIndex >= 0}
            onClick={handelAdd}
            theme="highlight"
            icon={<OutlinedTipsAdd />}
            data-testid="pay-customPaymentBranch-addPhrase-btn"
          >
          {i18n.get('添加常用语')}
        </Button>
      </div>
    )
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'space-between' }}>
      {isShow && (
        <div>
          <Checkbox
          className="select-payment-checkbox"
          checked={isAutoSummary}
          onChange={(e: any) => {
            onChange?.(e)
            setIsAutoSummary(e?.target?.checked)
          }}
          data-testid="pay-customPaymentBranch-autoSummary-checkbox"
        >
            {i18n.get('使用')}
            {`"${fieldLabel}"`}
            {i18n.get('作为支付摘要')}
          </Checkbox>
          <Checkbox
            className="select-payment-checkbox"
            data-testid="pay-customPaymentBranch-autoCutRemark-checkbox"
            checked={isAutoCutRemark}
            onChange={(e: any) => {
              onAutoCutRemarkChange?.(e)
              setIsAutoCutRemark(e?.target?.checked)
            }}
          >
            {i18n.get('摘要字数超出限制自动截取')}
          </Checkbox>
        </div>
      )}
      {isByHand && (
        <Dropdown
          trigger={['click']}
          disabled={isAutoSummary}
          overlayClassName={styles['overlayClassName']}
          open={dropdownOpen}
          onOpenChange={(open: boolean) => {
            setDropdownOpen(open)
          }}
          dropdownRender={dropdownRender}
        >
          <Button
            className="select-payment-checkbox"
            disabled={isAutoSummary}
            onClick={() => {
              setDropdownOpen(!dropdownOpen)
            }}
            size="small"
            category="text"
            theme="highlight"
            icon={<OutlinedGeneralChatNews fontSize={16} />}
            data-testid="pay-customPaymentBranch-selectPhrase-btn"
          >
            {i18n.get('选择常用语')}
          </Button>
        </Dropdown>
      )}
    </div>
  )
}
