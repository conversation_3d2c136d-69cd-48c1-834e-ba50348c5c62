import React, { useEffect, useState } from 'react'
import { Form } from 'antd'
import { DatePicker, Input, Popover, Radio, Tooltip } from '@hose/eui'
import styles from './CurrencyOffline.module.less'
import { OutlinedDirectionDown, OutlinedDirectionRefresh, OutlinedEditSearch, OutlinedTipsInfo } from '@hose/eui-icons'
import classNames from 'classnames'
import { getCurrencybyPayAccount, getCurrencyModifiable } from '../../audit-action'
import Big from 'big.js'
import moment from 'moment'

const FormItem = Form.Item

interface IProps {
  form: any
  selectedAccount?: any
  standardMap?: any
  currentAmountMap?: any
}
const CurrencyOffline: React.FC<IProps> = props => {
  const [selectCurrency, setSelectCurrency] = useState<any>({})
  const [currencyList, setCurrencyList] = useState<any>([])
  const [originalList, setOriginalList] = useState<any>([])
  const [rate, setRate] = useState<any>(null)
  const [canChangeRate, setcanChangeRate] = useState<boolean>(true)
  const [radioValue, setRadioValue] = useState('useAmount')

  const { form, selectedAccount, standardMap, currentAmountMap } = props

  const is_en_US = i18n.currentLocale === 'en-US'

  useEffect(() => {
    fnGetCurrencyModifiable()
  }, [])

  useEffect(() => {
    const date = form.getFieldValue('paidOffLineFinishedTime')
    const time = date?.valueOf()
    fnGetCurrency(time)
  }, [selectedAccount, currentAmountMap])

  const fnGetCurrencyModifiable = async () => {
    const res = await getCurrencyModifiable()
    setcanChangeRate(res?.value)
  }

  const fnGetCurrency = async (time?: any) => {
    const list: any = await fnGetCurrencyList(time)
    const item = list?.find((item: any) => item?.numCode === currentAmountMap?.numCode)
    const data = item ? item : list?.[0]
    fnCalculateAmount(data?.rate)
    setRate(data?.rate)
    setSelectCurrency(data)
    setCurrencyList(list)
    setOriginalList(list)
    form.setFieldsValue({ paidCurrencyCode: data?.numCode, paidRate: data?.rate })
  }

  const fnGetCurrencyList = async (time: any) => {
    const res = await getCurrencybyPayAccount({
      accountId: selectedAccount?.id,
      orignialId: standardMap?.numCode,
      time: time || moment().valueOf(),
    })
    return res?.items || []
  }

  const fnCalculateAmount = (value: any) => {
    if (isNaN(Number(value)) || Number(value) < 0) {
      return
    }
    if (standardMap?.standard && value > 0) {
      const amount = new Big(Number(standardMap?.standard)).div(Number(value)).toFixed(2)
      form.setFieldsValue({ paidAmount: amount })
    }
  }

  const fnSetRateAndMoney = (rate: any) => {
    fnCalculateAmount(rate)
    setRate(rate)
    form.setFieldsValue({ paidRate: rate })
  }

  const handleItemClick = (item: any) => {
    fnSetRateAndMoney(item?.rate)
    setSelectCurrency(item)
    form.setFieldsValue({ paidCurrencyCode: item?.numCode })
    if (!item?.rate) {
      form.setFieldsValue({ paidAmount: undefined })
    }
  }

  const handleRateChange = (e: any) => {
    const value = e.target.value
    const _value = value.trim()
    fnSetRateAndMoney(_value)
  }

  const handleValidatorRate = (_: any, value: any, callback: any) => {
    if (Number(value) > 0) return callback()
    callback(i18n.get('汇率不能小于0'))
  }

  const handleValidatorAmount = (_: any, value: any, callback: any) => {
    if (Number(value) > 0) return callback()
    callback(i18n.get('金额不能小于0'))
  }

  const handleSearchChange = (e: any) => {
    const searchText = e.target.value
    const _searchText = searchText.trim().toLowerCase()
    if (_searchText) {
      const list = originalList.filter(
        (el: any) =>
          (el.name && !!~el.name.toLowerCase().indexOf(_searchText)) ||
          (el.strCode && !!~el.strCode.toLowerCase().indexOf(_searchText)),
      )
      setCurrencyList(list)
    } else {
      setCurrencyList(originalList)
    }
    form.setFieldsValue({ paidCurrencyCode: selectCurrency?.numCode })
  }

  const handleDatePickerChange = async (date: any) => {
    const list = await fnGetCurrencyList(date?.valueOf())
    const item = list?.find((item: any) => item?.numCode === selectCurrency?.numCode)
    const data = item ? item : list?.[0]
    setCurrencyList(list)
    setOriginalList(list)
    setRate(data?.rate)
    setSelectCurrency(data)
    if (radioValue === 'useRate') {
      fnCalculateAmount(data?.rate)
    }
    form.setFieldsValue({ paidCurrencyCode: data?.numCode, paidRate: data?.rate })
  }

  const onRadioChange = (e: any) => {
    const value = e?.target?.value
    setRadioValue(value)
    if (value === 'useRate') {
      const date = form.getFieldValue('paidOffLineFinishedTime')
      const time = date?.valueOf()
      fnGetCurrency(time)
    }
  }

  const disabledDate = (current: moment.Moment) => {
    return current && current > moment().endOf('day')
  }

  const renderPopConten = () => {
    return (
      <div className={styles['pop-conten']}>
        <Input
              className="pop-input"
              prefix={<OutlinedEditSearch />}
              placeholder={i18n.get('搜索币种名称或代码')}
              onChange={handleSearchChange}
              data-testid="pay-customPaymentBranch-currency-search-input"
            />
        <div className="item-wrap">
          {currencyList.map((item: any) => (
            <div
              onClick={() => handleItemClick(item)}
              className={classNames('currency-item', { 'check-item': item?.numCode === selectCurrency?.numCode })}
            >
              <div className="left">
                <img className="currency-img" src={item?.icon} />
              </div>
              <div className="right">
                <div className="name">{item?.name}</div>
                <div className="code">{`${i18n.get('代码')}：${item?.strCode}（${item?.numCode}）`}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  const renderAmountLabel = () => {
    return (
      <div className={styles['amount-label']}>
        <div className="star">*</div>
        {i18n.get('实际支付金额')}
      </div>
    )
  }

  const renderRateTooltip = () => {
    return rate
      ? `${rate}=${standardMap?.strCode}: ${selectCurrency?.strCode}`
      : canChangeRate
      ? i18n.get('核算汇率不可为空')
      : i18n.get('企业未维护核算汇率，请联系管理员维护核算汇率或申请核算汇率手动编辑权限')
  }

  const cls = radioValue === 'useRate' ? 'currency-money-wrap' : 'currency-money-wrap-useAmount'

  return (
    <>
      <FormItem label="" className={styles['type-radio-group']}>
        {form.getFieldDecorator('paidCurrencyType', {
          initialValue: 'useAmount',
        })(
          <Radio.Group onChange={onRadioChange} value={radioValue} data-testid="pay-customPaymentBranch-currencyType-radio">
            <Radio value="useAmount" data-testid="pay-customPaymentBranch-useAmount-radio">
              <div className="amount-type">
                {i18n.get('实际支付金额')}
                <Tooltip
                  placement="top"
                  title={i18n.get(
                    '不使用系统内维护的汇率，将按照您选择的实付币种与金额，自动计算实付币种与本位币之间的折合汇率。',
                  )}
                >
                  <OutlinedTipsInfo />
                </Tooltip>
              </div>
            </Radio>
            <Radio value="useRate" data-testid="pay-customPaymentBranch-useRate-radio">{i18n.get('通过汇率计算实际支付金额')}</Radio>
          </Radio.Group>,
        )}
      </FormItem>

      <FormItem label={i18n.get('实际支付时间')} className={styles['offline-form-item']}>
        {form.getFieldDecorator('paidOffLineFinishedTime', {
          initialValue: moment(),
          rules: [{ required: true, message: i18n.get('实际支付时间不可为空') }],
        })(
          <DatePicker
            style={{ width: '100%' }}
            format="YYYY-MM-DD HH:mm:ss"
            showTime
            disabledDate={disabledDate}
            onChange={handleDatePickerChange}
            data-testid="pay-customPaymentBranch-datePicker"
          />,
        )}
      </FormItem>

      <Form.Item label={renderAmountLabel()} className={styles[cls]}>
        <div className="amount-left">
          <Popover
            className="currency-popo"
            overlayClassName={styles['currency-overlay']}
            placement="bottomLeft"
            title={null}
            trigger="click"
            content={renderPopConten}
          >
            {form.getFieldDecorator('paidCurrencyCode', {
              initialValue: selectCurrency?.numCode,
            })(
              <div className={classNames('paid-currency-code', { 'paid-currency-code-is-en-US': is_en_US })}>
                {`${i18n.get('实付币种')}（${selectCurrency?.strCode ?? '-'}）`}
                <OutlinedDirectionDown />
              </div>,
            )}
          </Popover>
          <Form.Item
            className={classNames({ 'paid-amount': radioValue !== 'useRate' })}
            label={null}
            style={{ marginBottom: 0 }}
          >
            {form.getFieldDecorator('paidAmount', {
              initialValue: '',
              rules: [
                { required: true, message: i18n.get('支付金额不可为空') },
                { pattern: /^(?!0\d)\d+(\.\d{1,2})?$/, message: i18n.get('请输入有效的数字，最多两位小数') },
                { validator: handleValidatorAmount },
              ],
            })(
              <Input
                  className="left-money-input"
                  disabled={radioValue === 'useRate'}
                  placeholder={i18n.get('支付金额')}
                  data-testid="pay-customPaymentBranch-amount-input"
                />,
            )}
          </Form.Item>
        </div>
        {radioValue === 'useRate' && (
          <Form.Item className="rate-form-item" label={null} style={{ marginBottom: 0 }}>
            <div
              className={classNames('amount-right', {
                'cant-change-rate': !canChangeRate,
                'amount-right-is-en-US': is_en_US,
              })}
            >
              <div className="right-wrap">
                <div className="rate-tips">
                  {i18n.get('核算汇率')}
                  <Tooltip placement="top" title={renderRateTooltip}>
                    <OutlinedTipsInfo className="tips-icon" />
                  </Tooltip>
                </div>

                {form.getFieldDecorator('paidRate', {
                  initialValue: rate,
                  rules: [
                    { required: true, message: i18n.get('核算汇率不可为空') },
                    { pattern: /^\d+(\.\d+)?$/, message: i18n.get('输入格式不正确') },
                    { max: 32, message: i18n.get('汇率的精度是0~32位') },
                    { validator: handleValidatorRate },
                  ],
                })(
                  <Input
                    className="rate-input"
                    disabled={!canChangeRate}
                    onChange={handleRateChange}
                    placeholder={i18n.get('汇率')}
                    data-testid="pay-customPaymentBranch-rate-input"
                  />,
                )}
              </div>

              {canChangeRate && (
                <Tooltip placement="top" title={i18n.get('恢复汇率初始设定')}>
                  <OutlinedDirectionRefresh
                onClick={() => fnSetRateAndMoney(selectCurrency?.rate)}
                className="reset-rate"
                data-testid="pay-customPaymentBranch-reset-rate-btn"
              />
                </Tooltip>
              )}
            </div>
          </Form.Item>
        )}
      </Form.Item>
    </>
  )
}
export default CurrencyOffline
