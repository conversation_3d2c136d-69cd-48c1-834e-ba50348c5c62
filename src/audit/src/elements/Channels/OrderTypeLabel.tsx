import React from 'react'
import { Tooltip } from '@hose/eui'
import { OutlinedTipsInfo } from '@hose/eui-icons'
interface Props {
  tooltipTitle?: string
  label?: string | Element
}
export default function OrderTypeLabel(props: Props) {
  const {
    tooltipTitle = i18n.get(
      '银行下单类型包括批量支付（转账）、单笔支付（转账）或批量代发，具体以支付渠道为准。不同业务类型的手续费用和适用场景请咨询相应银行服务人员。',
    ),
    label = i18n.get('银行下单类型'),
  } = props
  return (
    <span>
      {label}
      <Tooltip className={'ml-4 mr-4'} placement="topLeft" title={tooltipTitle}>
       <OutlinedTipsInfo fontSize={16} color="var(--eui-icon-n2)" />
      </Tooltip>
    </span>
  )
}
