.currency-overlay {
  padding-top: 0;
  width: 468px;

  :global {
    .eui-popover-content {
      .eui-popover-arrow {
        display: none;
      }

      .eui-popover-inner {
        padding: 8px;

        .eui-popover-inner-content {
          overflow: hidden;
        }
      }
    }
  }
}

.currency-input {
  cursor: default;
  width: 150px;
  border-radius: 6px 0px 0px 6px;
  border: 1px solid var(--eui-line-border-component, rgba(29, 33, 41, 0.20));
  background: var(--eui-bg-body-overlay, #F7F8FA);

  :global {
    .eui-input-disabled {
      cursor: default;
      background: var(--eui-bg-body-overlay, #F7F8FA);
      color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
    }
  }
}

.pop-conten {
  width: 100%;
  max-height: 350px;
  display: flex;
  flex-direction: column;

  :global {
    .pop-input {
      margin-bottom: 8px;
    }

    .item-wrap {
      overflow: auto;
      flex: 1;

      .currency-item {
        cursor: pointer;
        display: flex;
        align-items: center;
        margin-bottom: 2px;
        padding: 8px 16px;

        .left {
          img {
            width: 32px;
            height: 32px;
            border-radius: 100%;
            margin-right: 12px;
          }
        }

        .right {
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;

          .name {
            color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
          }

          .code {
            color: var(--eui-text-placeholder, rgba(29, 33, 41, 0.50));
          }
        }
      }

      .check-item {
        border-radius: 8px;
        background: var(--eui-fill-active, rgba(37, 85, 255, 0.10));

        .right {
          color: var(--eui-primary-pri-500, #2555FF);
        }
      }
    }
  }
}

.rate-wrap {
  display: flex;
  align-items: center;
  border-right: none !important;
  border-radius: 6px 0px 0px 6px;
  padding: 0 10px;
  color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  border: 1px solid var(--eui-line-border-component, rgba(29, 33, 41, 0.20));
  background: var(--eui-bg-body-overlay, #F7F8FA);

  :global {
    .eui-icon-OutlinedTipsInfo {
      font-size: 12px;
      margin-left: 4px;
    }
  }
}

.currency-money-wrap,
.currency-money-wrap-useAmount {
  width: 100%;
  margin-bottom: 16px;

  :global {
    .ant-form-item-label {
      line-height: normal;
      margin-bottom: 8px;
      color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
      font-size: 14px;
      font-style: normal;
      font-weight: 400;

      >label {
        display: flex;
      }
    }

    .amount-left {
      display: flex;
      align-items: flex-start;

      .paid-currency-code {
        display: flex;
        width: 152px;
        height: 32px;
        font-size: 14px;
        justify-content: space-between;
        align-items: center;
        padding: 4px 10px;
        border-radius: 6px 0px 0px 6px;
        border-right: none !important;
        border: 1px solid var(--eui-line-border-component, rgba(29, 33, 41, 0.20));
        background: var(--eui-bg-body-overlay, #F7F8FA);
      }

      .paid-currency-code-is-en-US {
        width: 270px;
      }

      .paid-amount {
        flex: 1;
      }

      .left-money-input {
        border-radius: 0 6px 6px 0;
        margin-right: 12px;
      }
    }

    .rate-form-item {
      width: 50%;

      .ant-form-item-children {
        display: inline-block;
        width: 100%;
      }

      .amount-right {
        display: flex;
        align-items: center;
        width: 129%;

        .right-wrap {
          display: flex;
          align-items: flex-start;
          width: 77%;

          .rate-tips {
            display: flex;
            align-items: center;
            width: 90px;
            height: 32px;
            padding: 8px;
            font-size: 14px;
            border-radius: 6px 0 0 6px;
            border: 1px solid var(--eui-line-border-component, rgba(29, 33, 41, 0.20));
            background: var(--eui-bg-body, #FFF);
            border-right: none;

            .eui-icon-OutlinedTipsInfo {
              font-size: 12px;
              margin-left: 4px;
              color: var(--eui-icon-n2);
            }
          }

          .rate-input {
            width: 77%;
            border-radius: 0 6px 6px 0;
          }
        }

        .reset-rate {
          width: 32px;
          height: 32px;
          padding: 7px;
          border-radius: 6px;
          margin-left: 12px;
          font-size: 16px;
          border: 1px solid var(--eui-line-border-component, rgba(29, 33, 41, 0.20));
          background: var(--eui-bg-body, #FFF);
          color: var(--eui-icon-n1);
        }
      }

      .amount-right-is-en-US {
        width: 113%;

        .rate-tips {
          width: 156px !important;
        }

        .rate-input {
          width: 49% !important;
        }
      }

      .cant-change-rate {
        width: 148%;
      }
    }
  }
}

.offline-form-item {
  margin-bottom: 16px;

  :global {
    .ant-form-item-label {
      line-height: normal;
      margin-bottom: 8px;
      color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
    }
  }
}

.type-radio-group {
  margin-bottom: 16px;

  :global {
    .ant-form-item-control {
      line-height: normal;
      color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
    }

    .amount-type {
      .eui-icon-OutlinedTipsInfo {
        font-size: 12px;
        color: var(--eui-icon-n2);
        margin: 0 8px 0 4px;
      }
    }
  }
}

.currency-money-wrap {
  :global {
    .ant-form-item-children {
      display: flex;
    }
  }
}

.amount-label {
  :global {
    .star {
      display: inline-block;
      margin-right: 4px;
      content: "*";
      font-family: SimSun;
      line-height: 1;
      font-size: 12px;
      color: #ff7c7c;
    }
  }
}