import React, { forwardRef, useEffect } from 'react'
import { DatePicker } from '@hose/eui'
import moment from 'moment'

const { MonthPicker, RangePicker } = DatePicker

export interface PropsValue {
  data: DataValue
  onChange?: (value: any) => void
  value?: any
}

interface DataValue {
  initData: any
  disabled: boolean
  attributes: any
}

export const DoDatePicker = forwardRef((props: PropsValue, ref: any) => {
  const { data, value, onChange } = props
  const { disabled, initData, ...rest } = data
  const secondType = data?.attributes?.secondType || 'DatePicker'
  const disabledDateBefore = data?.attributes?.disabledDateBefore
  const fnDisabledDateBefore = (current: any) => {
    return current && current < moment().subtract(1, 'days')
  }
  const disabledDate = disabledDateBefore ? fnDisabledDateBefore : undefined

  useEffect(() => {
    if (!initData) {
      const defaultVal = new Date().getTime()
      let val: any = defaultVal
      switch (secondType) {
        case 'DatePicker':
          val = defaultVal
          break
        case 'MonthPicker':
          val = moment(defaultVal).format('yyyy-MM')
          break
        case 'RangePicker':
          val = [defaultVal, defaultVal]
          break
        default:
          val = defaultVal
      }
      onChange?.(val)
    }
  }, [])
  const handleDatePickerChange = (e: any) => {
    const v = moment(e).valueOf()
    onChange?.(v)
  }
  const handleRangePickerChange = (e: any) => {
    const v = [moment(e?.[0]).valueOf(), moment(e?.[1]).valueOf()]
    onChange?.(v)
  }
  const handleMonthPickerChange = (e: any) => {
    const w = moment(moment(e).valueOf()).format('yyyy-MM')
    onChange?.(w)
  }
  const Picker = () => {
    switch (secondType) {
      case 'DatePicker':
        return (
          <DatePicker
            {...rest}
            ref={ref}
            disabled={disabled}
            value={isNaN(value) ? undefined : moment(value)}
            disabledDate={disabledDate}
            onChange={handleDatePickerChange}
            data-testid="pay-customPaymentBranch-datePicker"
          />
        )
      case 'MonthPicker':
        return (
          <MonthPicker
            {...rest}
            ref={ref}
            disabled={disabled}
            value={moment(value)}
            disabledDate={disabledDate}
            onChange={handleMonthPickerChange}
            data-testid="pay-customPaymentBranch-monthPicker"
          />
        )
      case 'RangePicker':
        return (
          <RangePicker
            {...rest}
            ref={ref}
            disabled={disabled}
            value={[moment(value?.[0]), moment(value?.[1])]}
            disabledDate={disabledDate}
            onChange={handleRangePickerChange}
            data-testid="pay-customPaymentBranch-rangePicker"
          />
        )
      default:
        return (
          <DatePicker
            {...rest}
            ref={ref}
            disabled={disabled}
            value={moment(value)}
            disabledDate={disabledDate}
            onChange={handleDatePickerChange}
            data-testid="pay-customPaymentBranch-datePicker"
          />
        )
    }
  }

  return <Picker />
})
