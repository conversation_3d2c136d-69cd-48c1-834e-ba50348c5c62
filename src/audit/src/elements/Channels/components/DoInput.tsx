/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-11-02 14:13:41
 */

import React, { forwardRef } from 'react'
import { Input } from '@hose/eui'

export interface PropsValue {
  data: DataValue
  onChange?: (value: any) => void
  value?: any
}

interface DataValue {
  initValue: string
  disabled: boolean
}

export const DoInput = forwardRef((props: PropsValue, ref: any) => {
  const { data, value, onChange } = props
  const { disabled, ...rest } = data
  const onInputChange = (value: any) => {
    onChange && onChange(value)
  }
  return <Input ref={ref} value={value} disabled={disabled} onChange={onInputChange} {...rest} data-testid="pay-customPaymentBranch-input" />
})
