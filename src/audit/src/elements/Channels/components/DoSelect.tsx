/*
 * @Author: zhangkai
 * @Date: 2021-11-02 14:22:28
 */

import React, { forwardRef } from 'react'
import { Select } from '@hose/eui'
import { getPayConfigInitData } from '../../../audit-action'
import { Fetch } from '@ekuaibao/fetch'
import { useEffect } from 'react'
import { fnEventSetConfigValue, fnShowErrorModal } from '../util'

const Option = Select.Option

export interface PropsValue {
  data: DataValue
  onChange?: (value: any) => void
  value?: any
  form: any
  selectedAccount: any
  setPaymethodConfigValue: (value: any) => void
  channel: string
  setLoading: (flag: boolean) => void
  payeeBankNames: string[]
  configData: any
  loading: boolean
}

interface DataValue {
  selected: any[]
  disabled: boolean
  initData: any[]
  linkAction: any[]
  eventsArr: any
  hidden: boolean
  require: boolean
}

export const DoSelect = forwardRef((props: PropsValue, ref: any) => {
  const {
    data,
    value,
    form,
    selectedAccount,
    channel,
    payeeBankNames,
    configData,
    loading,
    setPaymethodConfigValue,
    onChange,
    setLoading,
  } = props
  const { disabled, initData = [], eventsArr, linkAction, hidden, require, ...rest } = data

  useEffect(() => {
    if (eventsArr) {
      const values = fnEventSetConfigValue(form, eventsArr, configData)
      const params = {
        type: 'hiddenOrRequire',
        values: values,
      }
      setPaymethodConfigValue(params)
    }
  }, [value, hidden, require])

  const onSelectChange = async (value: any) => {
    const pageData = configData?.pageData || {}
    onChange && onChange(value)
    let params = {
      type: 'initData',
      initValues: {},
      eventValues: {},
    }
    if (linkAction) {
      setLoading(true)
      const values = form.getFieldsValue()
      const result = await Promise.all(
        linkAction.map((action: string) => {
          return getPayConfigInitData({
            channel,
            action,
            body: {
              ...values,
              account: selectedAccount,
              accountId: selectedAccount.accountNo,
              bankName: selectedAccount.bank,
              corporationId: Fetch?.ekbCorpId,
              payeeBankNames,
              ...pageData,
            },
          })
        }),
      )
      fnShowErrorModal(result)
      const initDataValue =
        result?.reduce((prev: any, item: any) => {
          return { ...prev, ...item?.items }
        }, {}) || {}
      params.initValues = initDataValue
      setLoading(false)
    }
    if (eventsArr) {
      const values = fnEventSetConfigValue(form, eventsArr, configData)
      params.eventValues = values
    }
    setPaymethodConfigValue(params)
  }
  return (
    <Select
      ref={ref}
      style={{ width: '100%' }}
      value={value}
      disabled={disabled || loading}
      onChange={onSelectChange}
      showSearch
      optionFilterProp="children"
      filterOption={(input: any, option: any) =>
        option?.props?.children?.toLowerCase().indexOf(input.toLowerCase()) >= 0
      }
      {...rest}
      data-testid="pay-customPaymentBranch-select"
    >
      {initData?.map((el: any) => (
        <Option key={el.code} value={el.code} data-testid={`pay-customPaymentBranch-option-${el.code}`}>
          {i18n.get(el.name)}
        </Option>
      ))}
    </Select>
  )
})
