import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import styles from './select-refresh-type.module.less'
import { Icon, Row, Col } from 'antd'
const CHANGJIE_LOGO = app.require('@images/changjie-logo.png')
const OTHER_LOGO = app.require('@images/other-logo.svg')

interface PaymentTypeProps {
  src: string
  label: string
  channel: string
}

@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer'
})
export default class RefreshType extends PureComponent<StringAnyProps, StringAnyProps> {
  paymentType: PaymentTypeProps[] = [
    {
      src: CHANGJIE_LOGO,
      label: i18n.get('银企联支付'),
      channel: 'CHANPAY'
    },
    {
      src: OTHER_LOGO,
      label: i18n.get('搜车支付'),
      channel: 'SOUCHE'
    }
  ]

  selectedType: PaymentTypeProps = null

  getResult = () => {
    return this.selectedType
  }

  handleSelectType = (item: PaymentTypeProps) => {
    this.selectedType = item
    this.props.layer.emitOk()
  }

  render() {
    return (
      <div className={styles['modal-wrapper']}>
        <Icon type="exclamation-circle" className="icon" />
        <div className="content">
          <span style={{ fontSize: 20 }}>{i18n.get('请选择要刷新的支付方式')}</span>
          <span style={{ fontSize: 14, color: 'rgba(0, 0, 0, 0.65)', marginTop: 8, marginBottom: 8 }}>
            {i18n.get('当前列表包含多种支付方式，请选择其中一种刷新支付结果')}
          </span>
          <Row type="flex" justify="space-between">
            {this.paymentType.map((item: PaymentTypeProps) => {
              const { src, label } = item
              return (
                <Col key={label} span={12}>
                  <div className="item" onClick={_ => this.handleSelectType(item)} data-testid={`pay-selectRefreshType-${item.channel}-option`}>
                    <img src={src} width={24} height={24} alt="" />
                    <div className="ml-5">{label}</div>
                  </div>
                </Col>
              )
            })}
          </Row>
        </div>
      </div>
    )
  }
}
