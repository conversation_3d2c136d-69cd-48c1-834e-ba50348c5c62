import React, { Component } from 'react'
import { Form } from 'antd'
import { Button, Select, Radio, Input } from '@hose/eui'
import { OutlinedTipsClose } from '@hose/eui-icons'
import { app as api } from '@ekuaibao/whispered'
const MoneyView = api.require('@elements/MoneyView/MoneyView')
const AddCreditNote = api.require('@elements/AddCreditNote')
const ExpenseFlowConfig = api.require('@elements/ekbc-business/bills/expense-flow-config')
const ApprovalAmountConfig = api.require('@elements/ApprovalAmountConfig')
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { parseAttachment } from '@ekuaibao/lib/lib/attachment/parseAttachment'
import ApprovalComments from '../elements/CommentWords/ApprovalComments'
import TO_REJECTOR_IMG from '../images/TO_REJECTOR.png'
import TO_REJECTOR_IMG_EN from '../images/<EMAIL>'
import FROM_START_IMG from '../images/FROM_START.png'
import FROM_START_IMG_EN from '../images/<EMAIL>'
import MessageCenter from '@ekuaibao/messagecenter'
import { getV } from '@ekuaibao/lib/lib/help'
import { getBacklogFlowplan } from '../audit-action'
import { showModal, showMessage } from '@ekuaibao/show-util'
import qs from 'qs'
import './reject-bill-modal.less'
import { getCurrentNodeBeforeNodes, getNodeIdByConfigNodeId } from './PlanNodeHelper'

@EnhanceModal({
  footer: [],
  className: 'respond-modal-layer',
})
@Form.create()
export default class RejectBillModal extends Component {
  constructor(props) {
    super(props)
    this.bus = props.bus || new MessageCenter()
    this.state = {
      value: undefined,
      isUpload: false,
      rejectMethod: defaultRejectMethod,
      radioValue: 'TO_REJECTOR',
      rejectTo: null,
      plan: undefined,
      rejectToDisable: false,
      creditNoteRequired: false,
      noteRequest: undefined,
      selectedData: [],
      allBeforeNodes: [],
      rejectToNode: {},
      approveDetail: {}, // 审批布局设置
    }
    props.overrideGetResult(this.getResult.bind(this))
  }

  async componentDidMount() {
    const { value } = await api.invokeService('@custom-flow:get:flow:plan:setting')
    const rejectMethod = getV(value, 'rejectSetting.rejectMethod', defaultRejectMethod)
    let { spcSetting, spcResubmitMethod, spcRejectTo, list = [] } = this.props
    let allBeforeNodes = getCurrentNodeBeforeNodes({ selectData: list })
    let plan = undefined
    if (this.props.list.length === 1) {
      const result = await getBacklogFlowplan(this.props.list[0])
      plan = result?.value
      if (plan?.nodes?.length) {
        const planMap = plan.nodes.reduce((resultMap, value) => {
          resultMap[value.id] = value
          return resultMap
        }, {})
        allBeforeNodes = allBeforeNodes.map(item => {
          if (planMap[item.id]) {
            return planMap[item.id]
          } else {
            return item
          }
        })
      }
    }
    if (list.length === 1 && spcSetting) {
      let spRejectMethod = {
        methods: [spcResubmitMethod],
      }
      let spRejectTo
      let rejectToDisable = false
      let rejectToNode = {}
      if (spcRejectTo != 'undefined' || spcRejectTo != '') {
        spRejectTo = spcRejectTo
        rejectToDisable = true
        rejectToNode = allBeforeNodes.find(node => node.id === spRejectTo)
      }
      this.setState({
        rejectMethod: spRejectMethod,
        radioValue: spcResubmitMethod,
        rejectToDisable,
        rejectToNode,
        rejectTo: spRejectTo,
        allBeforeNodes,
        plan,
      })
    } else {
      this.setState({ rejectMethod, radioValue: rejectMethod.defaultMethod, allBeforeNodes, plan })
    }
    this.getAmountConfig()
  }
  getAmountConfig = () => {
    const { list = [] } = this.props
    if (list.length > 0) {
      api.invokeService('@bills:get:approve:detail', { flowIds: list.map(it => it?.flowId?.id ?? it.id) }).then(res => {
        this.setState({ approveDetail: res?.value })
      })
    }
  }
  getResult() {
    return new Promise((resolve, reject) => {
      const { form, list } = this.props
      const { validateFieldsAndScroll, resetFields } = form
      validateFieldsAndScroll((error, value) => {
        this.bus.invoke('get:mention:content').then(mentionContent => {
          if (!!error) {
            reject()
            return
          }
          if (value.attachments) {
            value.attachments = parseAttachment(value.attachments)
          }
          const { noteRequest, rejectToNode } = this.state
          if (noteRequest) {
            value.params = {
              noteRequest: noteRequest,
            }
          }
          value.participants = value.comment?.mentions || []
          value.comment = value.comment?.value || ''
          value.rejectBacklogIdMap = getNodeIdByConfigNodeId(rejectToNode?.configNodeId, list)
          resolve({ ...value, ...mentionContent })
          resetFields()
        })
      })
    })
  }

  handleModalClose() {
    this.props.layer.emitCancel()
  }

  handleModalSave() {
    const { form } = this.props
    const comment = form.getFieldValue('comment')
    if (this.props.list.length > 1 && this.props.spcSetting) {
      showModal.info({
        content: (
          <div>
            <p>{i18n.get('部分单据已设置默认重审路径，与选择驳回路径无关')}</p>
            <p>{i18n.get('部分单据已设置了驳回路径，该驳回设置对其不生效')}</p>
          </div>
        ),
        onOk: () => {
          this.props.layer.emitOk()
          setTimeout(() => {
            this.handleGoApprovalUrl(comment)
          }, 1000)
        },
      })
    } else {
      const { creditNoteRequired, noteRequest = [], selectedData = [] } = this.state
      if (noteRequest?.length !== selectedData?.length) {
        showMessage.info(i18n.get('信用批注有未选择'))
        return
      }
      if (creditNoteRequired) {
        if (!noteRequest?.length) {
          showMessage.info(i18n.get('信用批注必须填写'))
          return
        }
      }
      this.props.layer.emitOk()
      setTimeout(() => {
        this.handleGoApprovalUrl(comment)
      }, 1000)
    }
  }

  // 临时授权接口增加【审批完成后跳转的地址】参数，仅pageType=form/backlogDetail时有效。
  handleGoApprovalUrl = comment => {
    if (!comment) return
    const urlState = qs.parse(window.location.search.slice(1))
    if (urlState && ['form', 'backlogDetail'].includes(urlState.pageType) && urlState.approvalUrl) {
      window.location.href = urlState.approvalUrl
    }
  }

  handleUpload = isUpload => {
    this.setState({ isUpload })
  }

  handleRadioChange = e => {
    this.setState({
      radioValue: e.target.value,
    })
  }

  handleSelectChange = rejectTo => {
    const { setFieldsValue } = this.props.form
    const { allBeforeNodes } = this.state
    const rejectToNode = allBeforeNodes.find(node => node.id === rejectTo)
    this.setState({ rejectTo, rejectToNode })
    setFieldsValue({ rejectTo })
  }

  addCreditNote = data => {
    console.log('[ addCreditNote data ] >', data)
    const { creditNoteRequired, noteRequest, selectedData } = data
    this.setState({ creditNoteRequired, noteRequest, selectedData })
  }
  render() {
    const { list, form, spcSetting } = this.props
    const { getFieldDecorator } = form
    const {
      isUpload,
      radioValue,
      rejectMethod,
      rejectTo,
      plan,
      rejectToDisable,
      allBeforeNodes,
      rejectToNode,
      approveDetail,
    } = this.state
    const flowId = getV(list, '0.flowId', {})
    const defualtRejectNode = !!rejectToNode?.name ? rejectToNode : allBeforeNodes?.[0]

    return (
      <div className="reject-bill-modal">
        <div className="modal-header">
          <div className="flex">{i18n.get('驳回单据')}</div>
          <OutlinedTipsClose className="cross-icon" onClick={this.handleModalClose.bind(this)} />
        </div>
        <Form className="reject-bill-wrapper">
          <Form.Item style={{ marginBottom: 0 }}>
            <MoneyView datas={list} approveDetail={approveDetail} />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }}>
            <div className="turn-down-radio">
              <div className="fs-14 fw-500 mt-16 mb-8">{i18n.get('驳回节点')}</div>
              <div className="reject-node-choose">
                {i18n.get('驳回至')}
                {getFieldDecorator('rejectTo', {
                  initialValue: rejectTo,
                })(
                  <Select
                    disabled={rejectToDisable}
                    style={{ width: '87%', marginLeft: 10, marginRight: 10 }}
                    onChange={this.handleSelectChange}
                  >
                    {allBeforeNodes.map(item => (
                      <Select.Option key={item.id} value={item.id}>
                        {i18n.currentLocale === 'en-US' && item?.enName ? item?.enName : item.name}
                      </Select.Option>
                    ))}
                  </Select>,
                )}
                {i18n.get('节点')}
              </div>
              {list.length === 1 && !spcSetting && (
                <ExpenseFlowConfig
                  doc={flowId}
                  taskId={rejectTo}
                  nodes={allBeforeNodes}
                  nodeClick={node => this.handleSelectChange(node.id)}
                />
              )}
            </div>
          </Form.Item>
          <div className="money_wrapper_line" />
          <Form.Item style={{ marginBottom: 14 }}>
            <div className="turn-down-radio">
              <div className="fs-14 fw-500 mt-16 mb-8">{i18n.get('重审路径')}</div>
              {getFieldDecorator('resubmitMethod', {
                initialValue: radioValue,
                rules: [],
              })(
                <Radio.Group onChange={this.handleRadioChange}>
                  {rejectMethod.methods.map(method => {
                    const item = rejectMethodMap(method, defualtRejectNode?.name)
                    return (
                      <Radio key={method} checked value={method} style={{ display: 'block' }}>
                        <span className="radio-title">{item.title}</span>
                        {radioValue === method && (
                          <div style={{ paddingTop: '8px', marginBottom: '-15px' }}>
                            <img
                              className="img"
                              style={{
                                width: '760px',
                              }}
                              src={item.img}
                            />
                          </div>
                        )}
                      </Radio>
                    )
                  })}
                </Radio.Group>,
              )}
            </div>
          </Form.Item>
          <div className="money_wrapper_line" />
          <ApprovalComments
            form={this.props.form}
            required={true}
            type={'REJECT'}
            bus={this.bus}
            canSelectDP={true}
            suffixesPath="APPROVE"
            useClipboard={true}
            showAttachment={true}
            onUploading={this.handleUpload}
          />
          <div className="money_wrapper_line" />
          {this.props.list.length === 1 && plan && (
            <AddCreditNote plan={plan} flowId={flowId?.id} type={'reject'} onChange={this.addCreditNote} />
          )}
        </Form>
        <div className="modal-footer modal-footer-start">
          <div>
            <Button className="danger mr-8" onClick={this.handleModalSave.bind(this)} disabled={isUpload}>
              {i18n.get('驳回')}
            </Button>
            <Button category="secondary" onClick={this.handleModalClose.bind(this)}>
              {i18n.get('取消')}
            </Button>
          </div>
          <ApprovalAmountConfig fn={this.getAmountConfig} />
        </div>
      </div>
    )
  }
}

const rejectMethodMap = (method, curReject) => {
  const map = {
    TO_REJECTOR: {
      title: i18n.get('重审时，从当前节点开始审批'),
      method: 'TO_REJECTOR',
      img: i18n?.currentLocale === 'en-US' ? TO_REJECTOR_IMG_EN : TO_REJECTOR_IMG,
    },
    FROM_START: {
      title: i18n.get('重审时，从{curReject}节点开始审批', { curReject }),
      method: 'FROM_START',
      img: i18n?.currentLocale === 'en-US' ? FROM_START_IMG_EN : FROM_START_IMG,
    },
  }
  return map[method]
}

const defaultRejectMethod = {
  methods: ['TO_REJECTOR', 'FROM_START'],
  defaultMethod: 'TO_REJECTOR',
}
