@import '~@ekuaibao/web-theme-variables/styles/default';
@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/eui-styles/less/token.less';

.agree-bill-modal {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  :global {
    .modal-header {
      border: none;
      width: 100%;
      height: 60px;
      font-size: 20px;
      .cross-icon {
        font-size: 16px;
      }
      .count {
        height: 22px;
        font-size: 16px;
        font-weight: 400;
        line-height: 22px;
        margin-left: 8px;
        color: rgba(29, 43, 61, 0.5);
      }
    }
    .agree-risk {
      width: 100%;
      padding: 0 32px 0 20px;
      background: #EDFEED;
    }
    .agree-risk-warning {
      padding-left: 16px;
      width: 100%;
      background-color: var(--eui-function-warning-50);
      display: flex;
      align-items: center;
      .bill-icon {
        margin-right: 12px;
        font-size: 16px;
        color: var(--eui-function-warning-500);
      }
    }
    .money_wrapper_line {
      width: 100%;
      height: @space-4;
      background: var(--eui-line-divider-module);
    }
    .remark {
      display: flex;
      width: 100%;
      padding: 20px 16px 0 16px;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      background-color: @input-bg;
      margin-bottom: 8px;
    }
    .attachment-style {
      width: 100%;
      padding-left: 16px;
      .ant-btn {
        border: none;
        margin-left: -15px;
        background-color: #ffffff;
      }
      &:hover {
        .ant-btn {
          color: var(--brand-base);
        }
      }
    }
    .signature-line {
      display: flex;
      flex-direction: row;
      width: 100%;
      padding: 0 32px;
      margin-top: -8px;
      .signature-text {
        margin-bottom: 24px;
        display: flex;
        align-items: center;
        margin-left: 10px;
      }
    }
    .flow-allow-modal-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // margin-top: 24px;
      width: 100%;
    }
    .flow_plan_config_checkbox {
      font-size: 14px;
      color:var(--eui-text-title);

      &:after{
        border-right: 1px solid var(--eui-line-divider-default);
        margin-right: 8px;
        margin-left: 5px;
      }
    }
    .line {
      width: 100%;
      height: 8px;
      background: rgba(29, 43, 61, 0.03);
    }
    .flow_plan_config_money {
      width: 100%;
      display: flex;
      padding: 0 20px;
      .flow_plan_config_item {
        height: 46px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .flow_plan_config_left {
          font-size: 14px;
          display: flex;
          img {
            width: 22px;
            height: 22px;
            border-radius: 11px;
          }
          div {
            margin-left: 12px;
            font-weight: 600;
          }
        }
      }
    }
    .flow_plan_config_des {
      width: 100%;
      padding-left: 18px;
      color: rgba(29, 43, 61, 0.5);
      font-size: 12px;
    }
  }
}
