import React, { PureComponent } from 'react'
import styles from './Warning-alert.module.less'
import { RiskStatus } from './AgreeBillUtil'

interface Props {
  onRetry?: Function
  status: number
  type?: string
  num?: number
  text?: any
  isRetry: boolean,
  checkRiskWarn:any
}

interface Map {
  [key: string]: string
}

const urlMap: Map = {
  [RiskStatus.Processing]: '#EDico-loading',
  [RiskStatus.Success]: '#EDico-check-circle',
  [RiskStatus.Warning]: '#EDico-plaint-circle',
  [RiskStatus.Error]: '#EDico-refresh'
}

const classMap: Map = {
  [RiskStatus.Processing]: 'agree-bill-processing',
  [RiskStatus.Success]: 'agree-bill-success',
  [RiskStatus.Warning]: 'agree-bill-warning',
  [RiskStatus.Error]: 'agree-bill-error'
}

export default class WarningAlert extends PureComponent<Props> {
  render() {
    const { status, onRetry, text, type, isRetry = false } = this.props
    const className = classMap[status]
    if(status !== RiskStatus.Warning){
      return <></>
    }
    return (
      <div className={styles['warning-alert-wrapper']}>
        <div className={`risk-content ${className}`}>
          <span className="risk-text">{text}</span>
          {isRetry && (
            <span
              className="retry"
              onClick={() => {
                onRetry(type)
              }}
            >
              {i18n.get('点击重试')}
            </span>
          )}
        </div>
      </div>
    )
  }
}



// export default class WarningAlert extends PureComponent<Props> {

//   getList = ()=>{
//     const { checkRiskWarn } = this.props
//     const riskArray = Object.keys(checkRiskWarn || [])
//     return riskArray.map((item,index)=>{
//         if(checkRiskWarn[item].status == RiskStatus.Warning){
//           return <label>
//             {
//               index == 0 ? <svg className="icon bill-icon mr-8" aria-hidden="true">
//                   <use xlinkHref='#EDico-plaint-circle' />
//               </svg>: null
//             }
//             {checkRiskWarn[item]?.text}
//             </label>
//         }
//       })
//   }
//   render(){
//     const { onRetry, checkRiskWarn, isRetry = false } = this.props
//     const riskArray = Object.keys(checkRiskWarn || [])
    
//     return(
//       <div className={styles['warning-alert-wrapper']}>
//           {
//             isRetry && (
//               <span
//                 className="retry"
//                 onClick={() => {
//                   onRetry && onRetry()
//                 }}
//               >
//                 {i18n.get('点击重试')}
//               </span>
//             )
//           }
//           {
//             riskArray.length ? <div className="lst"> {this.getList()}</div> :null
//           }
//       </div>
//     )
//   }
// }
