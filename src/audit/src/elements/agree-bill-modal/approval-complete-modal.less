@import '~@ekuaibao/eui-styles/less/token';

.approval-complete-modal {
  position: relative;
  .modal-content {
    padding: @space-10 @space-7 @space-9;
    .status-icon {
      width: 28px;
      height: 28px;
      margin: 0 auto;
      .bill-icon {
        font-size: 28px;
        color: #5ed73a;
      }
    }
    .label {
      margin-top: @space-6;
      .batch-modal-title-wrapper {
        text-align: center;
        .title {
          height: 24px;
          .font-size-3;
          .font-weight-3;
          color: @color-black-2;
        }
        .title-content {
          margin-top: @space-2;
          height: 22px;
          .font-size-2;
          color: @color-black-3;
        }
      }
    }
    .fail-items {
      margin-top: @space-7;
      .batch-modal-message-wrapper {
        box-sizing: border-box;
        overflow-x: hidden;
        overflow-y: auto;
        max-height: 120px;
        padding: @space-4 @space-6;
        border-radius: @radius-2;
        background-color: @color-bg-2;
        .batch-item {
          .font-size-2;
          color: @color-black-2;
          margin: @space-2 0;
          .mark {
            width: 0;
            height: 0;
            display: inline-block;
            vertical-align: middle;
            border: 2px @line-solid @color-line-2;
          }
        }
      }
    }
  }
  .close-icon {
    position: absolute;
    top: @space-7;
    right: @space-7;
    .font-size-2;
    .font-weight-2;
    color: rgba(29, 43, 61, 0.75);
  }
  .modal-footer {
    border: none;
    height: 56px;
    padding-right: @space-7;
    background-color: @color-white-1;
    box-shadow: 0 8px 24px 0 var(--brand-1);
  }
}
