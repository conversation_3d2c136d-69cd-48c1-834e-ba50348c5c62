import React, { PureComponent } from 'react'
import { Icon } from 'antd'
import { Button } from '@hose/eui'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import './approval-complete-modal.less'

interface Props {
  [key: string]: any
}

@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer',
})
export default class AgreeBillModal extends PureComponent<Props> {
  handleModalClose = () => {
    const { handleConfirm } = this.props
    handleConfirm && handleConfirm()
    this.props.layer.emitCancel()
  }
  render() {
    const { label, details } = this.props
    return (
      <div className="approval-complete-modal">
        <Icon className="close-icon" onClick={this.handleModalClose} type="close" />
        <div className="modal-content">
          <div className="status-icon">
            <svg className="icon bill-icon" aria-hidden="true">
              <use xlinkHref="#EDico-check-circle" />
            </svg>
          </div>
          <div className="label">{label}</div>
          <div className="fail-items">{details}</div>
        </div>
        <div className="modal-footer">
          <Button className="btn" onClick={this.handleModalClose}>
            {i18n.get('确  定')}
          </Button>
        </div>
      </div>
    )
  }
}
