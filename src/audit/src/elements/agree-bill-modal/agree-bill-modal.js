import React, { Component, Fragment } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import { app } from '@ekuaibao/whispered'
import { Form } from 'antd'
import { OutlinedTipsClose, FilledTipsWarning } from '@hose/eui-icons'
import { Input, Button, Checkbox } from '@hose/eui'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import styles from './agree-bill-modal.module.less'
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')
import { getNodeValueByPath } from '@ekuaibao/lib/lib/lib-util'
import { parseAttachment } from '@ekuaibao/lib/lib/attachment/parseAttachment'
import WarningAlert from './Warning-alert'

const FormItem = Form.Item
const { TextArea } = Input
import { checkRisk, RiskStatus } from './AgreeBillUtil'
import ApprovalProgressView from './ApprovalProgressView'
const AttachmentComponent = app.require('@elements/attachment-component/AttachmentComponent')
import { getRiskCount } from '../../services/ApproveService'
const MoneyView = app.require('@elements/MoneyView/MoneyView')
const ApproveSignature = app.require('@elements/signature/ApproveSignature')
const ApprovalAmountConfig = app.require('@elements/ApprovalAmountConfig')
import ApprovalComments from '../CommentWords/ApprovalComments'
import MessageCenter from '@ekuaibao/messagecenter'
import { get, clone } from 'lodash'
import { newTrack } from '../../util/trackAudit'

@EnhanceConnect(state => ({
  userInfo: state['@common'].userinfo.data,
  invoiceReview: state['@common'].powers.invoiceReview,
  budgetPower: state['@common'].powers.Budget,
}))
@EnhanceModal()
@EnhanceFormCreate()
export default class AgreeBillModal extends Component {
  //批量审批
  constructor(props) {
    super(props)
    let powersList = app.getState()['@common'].powers.powersList
    const hasSubmitPer = powersList.filter(v => v.powerCode == '160031' && v.state != 'expire').length ? true : false
    this.state = {
      isUpload: false,
      hasSubmitPer,
      remarkValue: '',
      isShowWaiting: false,
      checkRiskWarn: {
        ...checkRisk('budget'),
        ...checkRisk('costControl'),
        ...checkRisk('invoiceReview'),
        ...checkRisk('submit'),
      },
      approveInfo: undefined,
      isFilterRiskWarning: false,
    }
    props.overrideGetResult(this.fnGetResult)
  }

  bus = new MessageCenter()

  componentDidMount() {
    const { showLoading, isBatchAgree, invoiceReview } = this.props
    const { hasSubmitPer } = this.state
    if (isBatchAgree && !showLoading) {
      this.handleRetry('budget')
      this.handleRetry('costControl')
      if (hasSubmitPer) {
        this.handleRetry('submit')
      }
      if (invoiceReview) {
        this.handleRetry('invoiceReview')
      }
    } else if (isBatchAgree && showLoading) {
      this.fnGetResult().then(approveInfo => {
        this.setState({ isShowWaiting: true, approveInfo })
      })
    }
    this.getAmountConfig()
  }
  getAmountConfig = () => {
    const { flowIds = [] } = this.props
    if (flowIds.length > 0) {
      app.invokeService('@bills:get:approve:detail', { flowIds }).then(res => {
        this.setState({ approveDetail: res?.value })
      })
    }
  }
  componentWillReceiveProps(nextProps) {
    if (!nextProps.visible) {
      this.setState({ remarkValue: '' })
    }
  }

  fnGetResult = () => {
    let { form, userInfo } = this.props
    let { isFilterRiskWarning } = this.state

    return new Promise((resolve, reject) => {
      form.validateFieldsAndScroll((error, fieldValue) => {
        if (!!error) {
          reject()
          return
        }
        this.bus.invoke('get:approve:show:signature').then(showAutograph => {
          this.bus.invoke('get:mention:content').then(mentionContent => {
            let { comment = '' } = clone(mentionContent)
            comment = !!comment ? comment : i18n.get('同意')
            if (!fieldValue.comment) {
              delete fieldValue.comment
            }
            const data = { comment, ...fieldValue, ...mentionContent }
            data.attachments = parseAttachment(fieldValue.attachments)
            data.isFilterRiskWarning = isFilterRiskWarning

            if (showAutograph && get(userInfo, 'staff.autograph')) {
              data.autographImageId = get(userInfo, 'staff.autograph.key')
            }
            form.resetFields()
            resolve(data)
          })
        })
      })
    })
  }

  handleModalClose() {
    this.props.layer.emitCancel()
  }

  handleModalSave = () => {
    this.bus.invoke('get:approve:signature:result').then(result => {
      if (!result) return
      if (this.props.isBatchAgree) {
        this.fnGetResult().then(approveInfo => {
          this.setState({ isShowWaiting: true, approveInfo })
        })
      } else {
        this.props.layer.emitOk()
      }
    })
  }

  handleUploading = isLoading => {
    this.setState({ isLoading })
  }

  handleRetry = type => {
    const fnHandleResult = rep => {
      let { checkRiskWarn } = this.state
      checkRiskWarn = { ...checkRiskWarn, ...checkRisk(type, rep) }
      this.setState({ checkRiskWarn: Object.assign({}, checkRiskWarn) })
    }

    fnHandleResult()

    const { flowIds, fetchParams, isSelectAll } = this.props
    getRiskCount({ flowIds, fetchParams, isSelectAll, types: type })
      .then(rep => {
        fnHandleResult(rep)
      })
      .catch(error => {
        fnHandleResult({ error })
      })
  }

  handleApproveProgress = result => {
    this.setState({ isShowWaiting: false }, () => {
      const { isFilterRiskWarning } = this.state
      this.props.layer.emitOk({ ...result, isFilterRiskWarning })
    })
  }

  handleApproveFlowConfig = e => {
    this.setState({ isFilterRiskWarning: e.target.checked })
  }

  render() {
    const {
      title,
      form,
      flowIds = [],
      approveAction,
      flowPlanConfig,
      selectedRowData = {},
      invoiceReview,
      budgetPower,
      userInfo,
    } = this.props
    let { isUpload, isShowWaiting, checkRiskWarn, approveInfo, hasSubmitPer, approveDetail } = this.state
    const { budgetRisk = true, expenseStandardsRisk = true, invoiceRisk = true } = approveDetail?.riskWarningCfg ?? {}
    const { getFieldDecorator } = form
    let flowCount = flowIds.length
    const isBatch = flowCount > 1
    const approvalOpinions = get(flowPlanConfig, 'approvalOpinions')
    let commentWhenFlowHasRisk = getNodeValueByPath(flowPlanConfig, 'commentWhenFlowHasRisk')
    commentWhenFlowHasRisk = isBatch ? false : commentWhenFlowHasRisk
    const mustBeUsedSignature = get(flowPlanConfig, 'mustBeUsedSignature')
    // 限制发票复核风险显示范围，只对报销单模板校验，显示风险提示。
    const showInvoiceReview = Object.values(selectedRowData)
      .map(v => v.type)
      .some(type => type === 'expense')
    const invoiceReviewPermission =
      userInfo?.permissions && userInfo?.permissions.find(item => item === 'INVOICE_REVIEW')
    const isHasRiskWaring = Object.keys(checkRiskWarn).find(item => checkRiskWarn[item].status === 1)
    return (
      <div className={styles['agree-bill-modal']}>
        {!title && (
          <Fragment>
            <div className="modal-header">
              <div className="flex">
                {isBatch ? i18n.get('批量同意') : i18n.get('审批同意')}
                {isBatch && <span className="count">{`${flowCount}${i18n.get('张')}`}</span>}
              </div>
              <OutlinedTipsClose className="cross-icon" onClick={this.handleModalClose.bind(this)} />
            </div>
            <div className={'agree-risk-warning'}>
              {isHasRiskWaring ? <FilledTipsWarning className="bill-icon" /> : null}
              {budgetPower && !!budgetRisk && (
                <WarningAlert
                  type="budget"
                  {...checkRiskWarn.budget}
                  isRetry={checkRiskWarn.budget.status === RiskStatus.Error}
                  onRetry={this.handleRetry}
                />
              )}
              {!!expenseStandardsRisk && (
                <WarningAlert
                  type="costControl"
                  {...checkRiskWarn.costControl}
                  isRetry={checkRiskWarn.costControl.status === RiskStatus.Error}
                  onRetry={this.handleRetry}
                />
              )}
              {hasSubmitPer && checkRiskWarn.submit.status != RiskStatus.Success && (
                <WarningAlert
                  type="submit"
                  {...checkRiskWarn.submit}
                  isRetry={checkRiskWarn.submit.status === RiskStatus.Error}
                  onRetry={this.handleRetry}
                />
              )}
              {invoiceReviewPermission && invoiceReview && showInvoiceReview && !!invoiceRisk && (
                <WarningAlert
                  type="invoiceReview"
                  {...checkRiskWarn.invoiceReview}
                  isRetry={checkRiskWarn.invoiceReview.status === RiskStatus.Error}
                  onRetry={this.handleRetry}
                />
              )}
            </div>
            <MoneyView datas={Object.values(selectedRowData)} approveDetail={approveDetail} />
          </Fragment>
        )}
        <ApprovalComments
          form={this.props.form}
          required={commentWhenFlowHasRisk || approvalOpinions}
          bus={this.bus}
          type={'AGREE'}
          styles={{ marginBottom: '8px' }}
          suffixesPath="APPROVE"
          invalidSuffixesConfig={{
            png: 1,
            pptx: 2,
          }}
          useClipboard={true}
          showAttachment={true}
        />
        <div className="money_wrapper_line"></div>
        <ApproveSignature mustBeUsedSignature={mustBeUsedSignature} bus={this.bus} />
        <div className="money_wrapper_line"></div>
        <div className="modal-footer modal-footer-start flow-allow-modal-footer">
          <div>
            <Button className="mr-8" onClick={this.handleModalSave} disabled={isUpload || isShowWaiting}>
              {i18n.get('确定')}
            </Button>
            <Button category="secondary" onClick={this.handleModalClose.bind(this)}>
              {i18n.get('取消')}
            </Button>
          </div>
          <div className="fs-14">
            {isBatch && (
              <ApproveFlowConfig flowPlanConfig={flowPlanConfig} onApproveFlowConfig={this.handleApproveFlowConfig} />
            )}
            <ApprovalAmountConfig fn={this.getAmountConfig} />
          </div>
        </div>
        {isShowWaiting && (
          <ApprovalProgressView
            flowIds={flowIds}
            approveAction={approveAction}
            approveInfo={approveInfo}
            handleModalClose={this.handleModalClose.bind(this)}
            isBatch={!!isBatch}
            onCheckProgress={this.handleApproveProgress}
          />
        )}
      </div>
    )
  }
}

function ApproveFlowConfig(props) {
  const { onApproveFlowConfig } = props
  return (
    <Checkbox className="flow_plan_config_checkbox" onChange={e => onApproveFlowConfig(e)}>
      {i18n.get('过滤有风险的单据')}
    </Checkbox>
  )
}
