import { app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
const Animation = api.require('@elements/Animation/Animation')
import './ApprovalProgressView.less'
import { checkApproveProgress } from '../../audit-action'
import { showMessage } from '@ekuaibao/show-util'
import { Questionnaire } from '@hose/eui'

const { questionnaireConfig } = api.require('@components/utils/questionnaireConfig')

interface Props {
  approveInfo: any
  approveAction: Function
  onCheckProgress: Function
  isBatch: boolean
  handleModalClose: () => {}
  flowIds?: string[]
}

export default class ApprovalProgressView extends PureComponent<Props> {
  timer: NodeJS.Timeout
  componentDidMount() {
    const { approveAction, approveInfo, handleModalClose } = this.props
    if (approveAction) {
      approveAction(approveInfo)
        .then((rep: any) => {
          this.fnCheckProgress()
        })
        .catch(err => {
          showMessage.error(err.msg)
          handleModalClose && handleModalClose()
        })
    }
  }

  componentWillUnmount() {
    clearInterval(this.timer)
  }

  fnClearInterval = () => {
    clearInterval(this.timer)
    this.timer = null
  }

  fnCheckProgress = () => {
    const { onCheckProgress } = this.props
    this.timer = setInterval(() => {
      api.dispatch(checkApproveProgress()).then((rep: any) => {
        if (rep.value) {
          const { residue, success } = rep.value
          if (residue === 0) {
            this.fnClearInterval()
            if(success > 0) {
              this.initSurvey()
            }
            onCheckProgress(rep.value)
          }
        } else {
          this.fnClearInterval()
          onCheckProgress()
        }
      })
    }, 3000)
  }

  initSurvey = () => {
    Questionnaire.initSurvey({
      sid: questionnaireConfig?.approve?.sid,
      channelId: questionnaireConfig?.approve?.channelId,
      width: questionnaireConfig?.approve?.width,
      externalUserId: api.getState()['@common'].userinfo?.data?.staff?.userId,
      externalCompanyId: api.getState()['@common'].userinfo?.data?.staff?.corporationId,
      parameters: {
        flowIds: this.props?.flowIds || [] as any
      }
    })
  }

  render() {
    return (
      <div className="bill-waiting-modal">
        <div className="content">
          <Animation />
          <div className="text">
            {this.props.isBatch ? i18n.get('批量同意中，请稍后...') : i18n.get('审批同意中，请稍后...')}
          </div>
        </div>
      </div>
    )
  }
}
