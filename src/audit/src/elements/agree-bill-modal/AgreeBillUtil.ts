/**
 *  Created by <PERSON><PERSON> on 2018/12/26 7:07 PM.
 */
import { get } from 'lodash'
// @ts-ignore
import { MoneyMath } from '@ekuaibao/money-math'

interface Map {
  [key: string]: string
}

const riskWarnTextMap: any = () => ({
  budget: i18n.get('预算风险'),
  costControl: i18n.get('费标风险'),
  invoiceReview: i18n.get('发票风险'),
  submit: i18n.get('非首次提交'),
})

const noRiskTextMap: any = () => ({
  budget: i18n.get('无预算超额'),
  costControl: i18n.get('无费用标准超标'),
  invoiceReview: i18n.get('发票均已收到'),
  submit: i18n.get('首次提交'),
})

const riskWranLoading: any = {
  budget: i18n.get('正在检查预算是否超标…'),
  costControl: i18n.get('正在检查费用标准是否超标…'),
  invoiceReview: i18n.get('正在检查纸质发票是否已全部收到…'),
  submit: i18n.get('正在检查单据是否首次提交…'),
}

export enum RiskStatus {
  Error,
  Warning,
  Success,
  Processing,
}

export function checkRisk(type: string, rep: any = {}) {
  const { error, value = {} } = rep
  const { flowCount } = value
  
  let text = flowCount === 0 ? null : riskWarnTextMap()[type]
  if (error) {
    return {
      [type]: { text: i18n.get(`{__k0}加载失败`, { __k0: text }), status: RiskStatus.Error },
    }
  }
  if (flowCount) {
    return {
      [type]: {
        text: i18n.get(`有 {__k0} 张单据{__k1}`, { __k0: flowCount, __k1: text }),
        status: RiskStatus.Warning,
      },
    }
  }

  if (flowCount === 0) {
    return { [type]: { text, status: RiskStatus.Success } }
  }
  text = riskWranLoading[type]
  return { [type]: { text, status: RiskStatus.Processing } }
}

export function moneyGroupData(datas: any, inPay = false) {
  let map: Map = {},
    arr = []
  datas
    .filter((line: any) => {
      return !!getBillMoney(line, inPay)
    })
    .forEach((line: any) => {
      const money = getBillMoney(line, inPay)
      // @ts-ignore
      let { standardStrCode, standardNumCode } = money || {}
      let value = map[standardStrCode]
      if (value) {
        map[standardStrCode] = { ...new MoneyMath(value).add(money).value, standardNumCode }
      } else {
        map[standardStrCode] = { ...new MoneyMath(money).value, standardNumCode }
      }
    })
  for (let key in map) {
    arr.push(map[key])
  }
  return arr
}

function getBillMoney(line: any, inPay = false) {
  if (inPay) {
    return get(line, `flowId.form.payMoney`) || get(line, `form.payMoney`)
  }
  const type = get(line, 'flowId.formType') || get(line, 'formType')
  if (type === 'payment') {
    return get(line, `flowId.form.payMoney`) || get(line, `form.payMoney`)
  }
  return get(line, `flowId.form.${type}Money`) || get(line, `form.${type}Money`)
}
