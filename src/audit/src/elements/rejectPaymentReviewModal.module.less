@import '~@ekuaibao/eui-styles/less/token.less';
@import '~@ekuaibao/eui-styles/less/token';

.item-parent {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;
  :global {
    .config-item {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .config-left {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .money-type {
          .font-size-3;
          .font-weight-3;
          display: inline-block;
          color:var(--eui-text-title);
          padding-left: 32px;
          vertical-align: middle;
        }
        span {
          .font-size-3;
          .font-weight-3;
          color:var(--eui-text-title);
          margin-left: 32px;
          font-size: 14px;
        }
      }
      .config-right {
        float: right;
        .font-size-3;
        color:var(--eui-text-title);
        padding: 0 32px;
      }
    }
    .config-item-zh {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .config-left {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .money-type {
          .font-size-2;
          .font-weight-2;
          display: inline-block;
          color: @color-black-4;
          padding-left: 32px;
          vertical-align: middle;
        }
      }
      .config-right {
        float: right;
        .font-size-2;
        color: @color-black-4;
        padding: 0 32px;
      }
    }
  }
}

.rejectPaymentReviewModal {
  :global {
    .footer {
      display: flex;
      justify-content: flex-end;
      background: @color-white-1;
      width: 100%;
      height: 56px;
      padding: 12px 24px;
      border-top: none;
      .shadow-black-3;
      .font-size-2;
      .btn-ok {
        margin-right: @space-4;
      }
    }
  }
}
