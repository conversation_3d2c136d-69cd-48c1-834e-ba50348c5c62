import React from 'react';
import SortTransfer from './SortTransfer';

interface CommonAccountProps {
    commonList: Array<Object>,
    otherList: Array<Object>,
    updateState: Function,
    otherSearch: string
}

const DoTransfer = ({commonList, otherList, updateState, otherSearch}: CommonAccountProps) => {
 
    return <div className='modal-content'>
        <SortTransfer commonList={commonList} otherList={otherList} updateState={updateState} otherSearch={otherSearch}/>
    </div>
}


export default DoTransfer; 
