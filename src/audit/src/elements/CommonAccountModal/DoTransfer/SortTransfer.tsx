import React, { Fragment } from 'react';
import './sortTransfer.less';
import { Input, Icon, Tooltip } from 'antd';
import  DragList  from './DragList';
import { updateById } from './utils';

export const renderAccountNo = (accountNo: string, showAll?: boolean): string => {
    if (!accountNo) return ''
    const numReg = /^[0-9]+$/
    if (showAll || !accountNo.match(numReg)) return `(${accountNo})`
    return `(${accountNo.slice(-4)})`
}

interface CommonAccountProps {
    commonList: Array<any>,
    otherList: Array<any>,
    updateState: (params: {}) => void,
    otherSearch: string
}

const SortTransfer = ({commonList, otherList, updateState, otherSearch}: CommonAccountProps) => {

    const selectAllHandle = () => {
        updateState({
            otherList: otherList.filter(item => item.label.indexOf(otherSearch) < 0),
            commonList: commonList.concat(otherList.filter(item => item.label.indexOf(otherSearch) > -1).map(item => ({...item, selected: false})))
        })
    }

    const cancelSelectAllHandle = () => {
        updateState({
            commonList: [],
            otherList: otherList.concat(commonList.map(item => ({...item, selected: false}))).sort((a, b) => a.oldIndex - b.oldIndex)
        })
    }

    const selectByIdHandle = (id: string, selected: boolean) => {
        const _d = updateById(id, { selected: !selected }, otherList)
        updateState({otherList: _d})
    }

    const selectCommonByIdHandle = (id: string, selected: boolean) => {
        const _d = updateById(id, { selected: !selected }, commonList)
        updateState({commonList: _d})
    }

    const filterOtherList = (e: React.ChangeEvent<HTMLInputElement>) => {
        updateState({otherSearch: e.target.value})
    }

    const leftInnerRender = () => {
        const _list = otherList.filter(item => item.label.indexOf(otherSearch) > -1)
        return <Fragment>
             <div className='search'>
                <Input 
                    suffix={<Icon type="search" style={{fontSize: 16}}/>}
                    onChange={filterOtherList}
                />
            </div>
            <div className="list">
                {
                    _list.length
                    ? (
                        _list.map(({id, selected, label, accountNo}) => (
                            <div className="item horizontal" key={id} onClick={() => selectByIdHandle(id, selected)}>
                                <Tooltip placement="top" title={label + renderAccountNo(accountNo, true)}>
                                    <div className="drag-list-panel-flex">
                                        <span className="text-nowrap-ellipsis">{label}</span>
                                        <span>{renderAccountNo(accountNo)}</span>
                                    </div>
                                </Tooltip>
                                {selected && <Icon type="check" />}
                            </div>
                        ))
                    )
                    : <DoEmpty label={i18n.get('暂无数据')}/>
                }
            </div>
        </Fragment>
    }

    const rightInnerRender = () => {
        return <DragList 
            list={commonList} 
            emptyComp={() => <DoEmpty key={'empty'} label={i18n.get('请从左侧框中点击选择字段')}/>} 
            selectCommonByIdHandle={selectCommonByIdHandle}
            updateState={updateState}
        />
    }
    
    const addCommonList = () => {
        let _selectedList: Array<any> = [];
        let _otherList: Array<any> = [];
        otherList.forEach(item => {
            if(item.selected && item.label.indexOf(otherSearch) > -1){
                _selectedList.push(item)
            }else{
                _otherList.push(item)
            }
        })
        
        if(!_selectedList.length) return 
        updateState({
            otherList: _otherList,
            commonList: commonList.concat(_selectedList.map(item => ({...item, selected: false})))
        })

    }

    const addOtherList = () => {
        const selectedList = commonList.filter(item => item.selected).map(item => ({...item, selected: false}));
        if(!selectedList.length) return 
        updateState({
            commonList: commonList.filter(item => !item.selected),
            otherList: otherList.concat(selectedList).sort((a, b) => a.oldIndex - b.oldIndex)
        })
    }
    
    return <div className='modal-content-transfer commonAccountModal-warp-transfer'>
        <TransferItem 
            title={i18n.get('可选账户')}
            labelText={i18n.get('全部选择')}
            labelCilck={selectAllHandle}
            renderInner={leftInnerRender}
        />
        <div className="arrows">
            <span className="arrows-item" onClick={addCommonList}>
                <Icon type="right" style={{color: '#fff'}}/>
            </span>
            <span className="arrows-item" onClick={addOtherList}>
                <Icon type="left" style={{color: '#fff'}}/>
            </span>
        </div>
        <TransferItem 
            title={i18n.get('置顶常用账户（首位为默认付款账户）')}
            labelText={i18n.get('取消全部')}
            labelCilck={cancelSelectAllHandle}
            renderInner={rightInnerRender}
        />
    </div>
}

export default SortTransfer;


interface TransferItemState {
    title: string,  //标题
    labelText: string,  //头部文字
    labelCilck?: Function,   
    renderInner?: () => React.ReactNode
}

const TransferItem = ({title, labelText, labelCilck, renderInner}: TransferItemState) => {

    const labelClickHandle = () => {
        labelCilck && labelCilck()
    }

    return <div className="transfer-panel">
        <div className='title'>{title}</div>
        <div className='content'>
            <div className="header">
                <div className='label' onClick={labelClickHandle}>{labelText}</div>
            </div>
            <Fragment>
                {renderInner && renderInner()}
            </Fragment>
        </div>
    </div>
}


const DoEmpty = ({label}: any) => {

    return <div className="empty">
        <img className="empty" src={require('./images/empty.svg')} />
        <span>{label}</span>
    </div>
}