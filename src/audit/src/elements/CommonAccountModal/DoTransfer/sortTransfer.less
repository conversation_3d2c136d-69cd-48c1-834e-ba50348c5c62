@import '~@ekuaibao/eui-styles/less/token.less';

.commonAccountModal-warp-transfer{
    display: flex;
    flex-direction: row;
    align-items: stretch;
    position: relative;
    user-select: none;
    .transfer-panel {
        flex: 1;
        display: flex;
        flex-direction: column;
        //border: 0.5px solid @color-line-1;
        .title{
            color: @color-black-3;
            font-size: 14px;
            line-height: 22px;
            flex-shrink: 0;
            margin-bottom: 12px;
        }
        .content{
            border: 1px solid @color-line-1;
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: stretch;
            overflow-y: hidden;
            .header{
                background: @color-white-1;
                font-size: 14px;
                font-weight: 400;
                padding: 12px 16px;
                flex-shrink: 0;
                border-bottom: 0.5px solid @color-line-1;
                .label{
                    color: @color-brand;
                    line-height: 22px;
                    cursor: pointer;
                }
            }
            
            .search{
                padding: 12px 16px;
                flex-shrink: 0;
                .ant-input{
                    border-radius: 28px
                }
                .anticon-search{
                    font-size: @space-6;
                    color: @color-black-2;
                    font-weight: bold;
                }
            }
            .list{
                flex: 1;
                overflow-x: hidden;
                overflow-y: auto;
                background: #fff;
                .item {
                    padding: 0 16px 0 16px;
                    line-height: 38px;
                    position: relative;
                    background: #fff;
                    // cursor: pointer;
                    //background: transparent;
                    // position: relative;
                    // white-space: nowrap;
                    // overflow: hidden;
                    // text-overflow: ellipsis;
                    .drag-icon{
                        display: none;
                    }
                    &:hover {
                        background: @color-bg-2;
                        .drag-icon{
                            display: inline-block;
                        }
                    }
                }
                .drag-list-panel{
                    background: @color-bg-2;
                }

            }
            .empty {
                text-align: center;
                margin-top: 56px;
                background: #fff;
                img {
                  width: 68px;
                  height: 60px;
                  display: block;
                  margin: 0 auto 12px;
                }
      
                span {
                  font-size: 14px;
                  font-weight: 400;
                  color: @color-black-4;
                  line-height: 22px;
                }
            }
            
        }
    }

    .arrows {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0 @space-5;

        .arrows-item {
            cursor: pointer;
            margin: @space-5 0;
            width: @icon-size-7;
            height: @icon-size-7;
            background: @color-black-2;
            border-radius: @radius-2;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    

}