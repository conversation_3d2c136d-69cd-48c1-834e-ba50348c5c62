import { app } from '@ekuaibao/whispered'
import React, { Component } from 'react'
import { Icon } from 'antd'
import { Tooltip } from '@hose/eui'
import { SortableContainer, SortableElement, arrayMove, SortableHandle } from 'react-sortable-hoc'
import { renderAccountNo } from './SortTransfer'
const EKBIcon: any = app.require('@elements/ekbIcon')

interface DragListProps {
  list: Array<any>
  emptyComp: () => void
  selectCommonByIdHandle: (id: string, selected: boolean) => void
  updateState: (params: {}) => void
}

export default class DragList extends Component<DragListProps> {
  constructor(props: DragListProps) {
    super(props)
  }

  onSortEnd = ({ oldIndex, newIndex }: { oldIndex: number; newIndex: number }) => {
    const { updateState, list } = this.props
    updateState({
      commonList: arrayMove(list, oldIndex, newIndex),
    })
  }

  render() {
    const { list, emptyComp, selectCommonByIdHandle } = this.props
    const empty = [emptyComp()]
    return (
      <SortableContainerL onSortEnd={this.onSortEnd} useDragHandle>
        <div className="drag-list-panel" key={'drag-list-panel'}>
          {list.length
            ? list.map((value, index) => (
                <SortableItem
                  key={value.id}
                  index={index}
                  value={value}
                  selectCommonByIdHandle={selectCommonByIdHandle}
                />
              ))
            : empty}
        </div>
      </SortableContainerL>
    )
  }
}

const DragHandle = SortableHandle(() => (
  <span className="drag-icon">
    <EKBIcon name="#EDico-menu2" style={{ width: 16, height: 16, marginRight: 4 }} />
  </span>
))

const SortableItem = SortableElement(
  ({
    value,
    selectCommonByIdHandle,
  }: {
    value: any
    selectCommonByIdHandle: (id: string, selected: boolean) => void
  }) => (
    <div
      className="item horizontal"
      style={{ zIndex: 10000, padding: '0 16px 0 16px', background: '#fff' }}
      onClick={() => selectCommonByIdHandle(value.id, value.selected)}>
      <Tooltip placement="top" title={value.label + renderAccountNo(value.accountNo, true)}>
        <div className="drag-list-panel-flex">
          <span className="text-nowrap-ellipsis">{value.label}</span>
          <span>{renderAccountNo(value.accountNo)}</span>
        </div>
      </Tooltip>
      {value.selected && <Icon type="check" style={{ position: 'absolute', right: 46 }} />}
      <DragHandle />
    </div>
  ),
)

const SortableContainerL = SortableContainer(({ children }: any) => (
  <div className="list">{children}</div>
))
