import { app } from '@ekuaibao/whispered';

import React from 'react';
import './style.less';
import { Button } from '@hose/eui';
import DoTransfer from './DoTransfer';
import { fetchCommon, saveCommonConfig } from '../../util/fetchUtil';
import { showMessage } from '@ekuaibao/show-util'
import { cloneDeep } from 'lodash'

const EKBIcon = app.require('@elements/ekbIcon')

interface CommonAccountProps {
    layer: any
}
interface CommonAccountHeaderProps {
    onClickHandle: () => void
}

interface CommonAccountFooterProps {
    handleModalClose: () => void,
    handleModalSave: () => void
}

interface CommonGetResp{
    value: {
        commonList: Array<any>
        otherList: Array<any>
    }    
}

interface CommonAccountState{
    commonList: Array<any>,
    otherList: Array<any>,
    otherSearch: string
}
  
class CommonAccount extends React.Component<CommonAccountProps, CommonAccountState> {
    
    constructor(props: CommonAccountProps) {
        super(props)
        this.state = {
            commonList: [],
            otherList: [],
            otherSearch: ''
        }
    }

    componentDidMount(){
        fetchCommon().then(
            (data: CommonGetResp) => {
                const { commonList, otherList } = data.value;
                
                //================================================================
                // 穿梭时需要记录默认后端排序的位置，所以按照接口createTime字段先进行排序
                // oldIndex字段为默认排序字段
                const sortMap: any = {}
                const sortIndex = cloneDeep(commonList.concat(otherList)).sort((a, b) => b.createTime - a.createTime)
                sortIndex.forEach((item, index) => {
                    sortMap[item.id] = {
                        oldIndex: index,
                        ...item
                    }
                })
                //================================================================
                
                this.setState({
                    commonList: commonList.map((item: any) => ({
                        id: item.id, 
                        label: item.name || item.accountName,
                        selected: false,
                        oldIndex: sortMap[item.id]?.oldIndex,
                        accountNo: item.accountNo
                    })), 
                    otherList: otherList.map((item: any) => ({
                        id: item.id, 
                        label: item.name || item.accountName,
                        selected: false,
                        oldIndex: sortMap[item.id]?.oldIndex,
                        accountNo: item.accountNo
                    })), 
                })
            },
            err => {
                showMessage.error(err.mesage)
            }
        )
    }

    handleModalClose = () => {
        this.props.layer.emitCancel()
    }

    handleModalSave = () => {
        const _list = this.state.commonList;
        saveCommonConfig({
            accountIds: _list.map(item => item.id),
            deviceType: 'DESKTOP'   //标识应用类型
        }).then(() => {
            this.props.layer.emitOk({isOk: true})
        })

    }

    render(){
        const { commonList, otherList, otherSearch } = this.state
        return <div className='commonAccountModal-warp'>
            <DoHeader 
                onClickHandle={this.handleModalClose}
            />
            <DoTransfer 
                commonList={commonList} 
                otherList={otherList} 
                updateState={this.setState.bind(this)}
                otherSearch={otherSearch}
            />
            <DoFooter 
                handleModalClose={this.handleModalClose} 
                handleModalSave={this.handleModalSave}
            />
        </div>  
    } 
}

export default CommonAccount;


const DoHeader = ({onClickHandle}: CommonAccountHeaderProps) => {
    return <div className="modal-header">
        <div className="modal-header-title">{i18n.get('常用付款账户')}</div>
        <EKBIcon className="cross-icon" name="#EDico-close-default" onClick={onClickHandle} />
    </div>
}

const DoFooter = ({handleModalClose, handleModalSave}: CommonAccountFooterProps) => {
    return <div className="modal-footer">
        <Button className="mr-8" onClick={handleModalSave}>
            {i18n.get('确定')}
        </Button>
        <Button category="secondary" onClick={handleModalClose}>
            {i18n.get('取消')}
        </Button>
    </div>
}
