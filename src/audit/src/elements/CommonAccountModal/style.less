@import '~@ekuaibao/eui-styles/less/token';

.commonAccountModal-warp{
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  .modal-header {
    flex-shrink: 0;
    &-title{
        flex: 1;
        .font-size-5;
        color:var(--eui-text-title);
    }
  }
  .modal-content{
    padding: 0 32px 32px;
    &-transfer{
      height: 400px;
      .transfer-panel{
        width: 45%;
        .drag-list-panel-flex{
          display: flex;
          width: 100%;
        }
      }
    }
  }
  .modal-footer{
    justify-content: flex-start
  }
}

// 拖拽时，item不在窗器内，不写在这里会导致样式丢失
.comment-move-item-wrapper {
  height: 32px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  z-index: 10000;
  margin: 0px -12px;
  padding: 0 14px;
  .input-warp{
    width: 208px;
  }
    &:hover {
      border-radius: 4px;
      background: var(--eui-fill-hover);
      .field-item-right {
        display: flex;
        .eui-icon{
          cursor: pointer;
          font-size: 16px;
        }
      }
    }
  .field-item-left {
    display: flex;
    align-items: center;
    font-size: 14px;
    width: 230px;
    .eui-icon {
      cursor: move;
      font-size: 16px;
      margin-right: 8px;
      color: var(--eui-icon-n3);
    }

    .field-item-title {
      max-width: 230px;
      color: var(--eui-text-title);
      font: var(--eui-font-body-r1);
      cursor: pointer;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .field-item-right {
    display: none;    
  }
}
