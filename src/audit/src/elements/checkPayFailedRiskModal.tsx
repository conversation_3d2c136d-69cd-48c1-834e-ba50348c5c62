/*
 * @Author: zhangkai
 * @Date: 2021-04-19 17:51:24
 */

import React, { Component } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Form } from 'antd'
import { Button, Input } from '@hose/eui'
import { app } from '@ekuaibao/whispered'
import MessageCenter from '@ekuaibao/messagecenter'
import { EnhanceConnect } from '@ekuaibao/store'
import { get } from 'lodash'
import styles from './checkPayFailedRisk.module.less'

const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')
const ApproveSignature: any = app.require('@elements/signature/ApproveSignature')
const EKBIcon: any = app.require('@elements/ekbIcon')
const FormItem = Form.Item
const { TextArea } = Input

interface Props {
  layer?: any
  form?: any
  userInfo?: any
}

@EnhanceConnect((state: any) => ({
  userInfo: state['@common'].userinfo.data,
}))
@EnhanceModal({
  title: i18n.get('确认失败'),
  footer: [],
  className: styles['check-pay-failed-wrapper'],
})
//@ts-ignore
@EnhanceFormCreate()
export default class CheckPayFailedRiskModal extends Component<Props, {}> {
  bus = new MessageCenter()
  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleOk = () => {
    const { userInfo } = this.props
    this.props.form.validateFields((err: any, values: any) => {
      if (!!err) return
      this.bus.invoke('get:approve:signature:result').then(result => {
        if (result) {
          const autographImageId = get(userInfo, 'staff.autograph.key')
          this.props.layer.emitOk({ ...values, autographImageId })
        }
      })
    })
  }
  render() {
    const { getFieldDecorator } = this.props.form
    return (
      <div className={styles['pay-failed-risk-modal-wrapper']}>
        <div className="pay-failed-risk-content">
          <div className="pay-failed-risk-description">
            <EKBIcon name="#EDico-plaint-circle" className="warning" />
            <div className="pay-failed-description-content">
              {i18n.get(
                '当前正手动将单据变为待支付，为避免后续重复支付造成损失，请确认此单据已在第三方平台支付失败',
              )}
            </div>
          </div>
          <div className="pay-failed-risk-form">
            <Form>
              <FormItem>
                {getFieldDecorator('reason', {
                  rules: [
                    { max: 100, message: i18n.get('字符长度不能超过100个') },
                    { required: true, whitespace: true, message: i18n.get('请填写失败原因') },
                  ],
                })(
                  <TextArea
                    className="failed-risk-area"
                    placeholder={i18n.get('请填写失败原因，不超过100个字符')}
                  />,
                )}
              </FormItem>
            </Form>
          </div>
          <ApproveSignature mustBeUsedSignature={true} bus={this.bus} />
        </div>
        <div className={styles['modal-footer']}>
          <Button className="mr-8" onClick={this.handleOk} data-testid="pay-checkPayFailedRiskModal-confirm-btn">
            {i18n.get('确定')}
          </Button>
          <Button category= "secondary" onClick={this.handleCancel} data-testid="pay-checkPayFailedRiskModal-cancel-btn">
            {i18n.get('取消')}
          </Button>
        </div>
      </div>
    )
  }
}
