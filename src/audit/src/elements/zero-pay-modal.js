import './info-modal.less'
import React from 'react'
import { Button } from '@hose/eui'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { EnhanceConnect } from '@ekuaibao/store'
import { getBatchFlowConfig } from '../audit-action'
import { get } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import MessageCenter from '@ekuaibao/messagecenter'
const ApproveSignature = api.require('@elements/signature/ApproveSignature')

@EnhanceConnect(state => ({
  userInfo: state['@common'].userinfo.data,
}))
@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer',
})
export default class ZeroPayModal extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      mustBeUsedSignature: false,
    }
    this.getResult = this.getResult.bind(this)
    props.overrideGetResult(this.getResult)
  }

  bus = new MessageCenter()

  componentDidMount() {
    const { flowIds } = this.props
    getBatchFlowConfig({ flowIds: flowIds }).then(res => {
      const mustBeUsedSignature = get(res, 'value.mustBeUsedSignature')
      this.setState({ mustBeUsedSignature })
    })
  }

  getResult() {
    let doc = {}
    const { userInfo } = this.props
    return this.bus.invoke('get:approve:show:signature').then(showAutograph => {
      if (showAutograph) {
        doc.autographImageId = get(userInfo, 'staff.autograph.key')
      }
      return doc
    })
  }

  handleModalSave = () => {
    this.bus.invoke('get:approve:signature:result').then(result => {
      if (!result) return
      this.props.layer.emitOk()
    })
  }

  handleModalCancel() {
    this.props.layer.emitCancel()
  }

  render() {
    const { mustBeUsedSignature } = this.state
    const { standardAmount } = this.props
    return (
      <div className="info-modal">
        <div className="content">
          <div className="line-1">
            <div className="circle-icon">i</div>
            <div>
              {i18n.get('支付金额：{__k0}0.00', {
                __k0: standardAmount?.symbol || window.CURRENCY_SYMBOL,
              })}
            </div>
          </div>
          <div className="fs-12 color-gray line-3">{i18n.get('无需支付，确定即完成')}</div>
          <ApproveSignature mustBeUsedSignature={mustBeUsedSignature} bus={this.bus} />
        </div>
        <div className="b-content">
          <Button
            category="secondary"
            className="mr-10"
            onClick={this.handleModalCancel.bind(this)}
            data-testid="pay-zeroPayModal-cancel-btn">
            {i18n.get('取消')}
          </Button>
          <Button onClick={this.handleModalSave} data-testid="pay-zeroPayModal-confirm-btn">{i18n.get('确定')}</Button>
        </div>
      </div>
    )
  }
}
