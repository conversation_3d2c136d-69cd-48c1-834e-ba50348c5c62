@import '~@ekuaibao/web-theme-variables/styles/colors';
.modal-wrapper {
  display: flex;
  padding: 32px;
  :global {
    .icon {
      font-size: 28px;
      color: #fa8c16;
    }
    .content {
      margin-left: 8px;
      display: flex;
      flex-direction: column;
      flex: 1;
      .item {
        cursor: pointer;
        display: flex;
        justify-content: center;
        border: solid 1px #e4e4e4;
        height: 36px;
        border-radius: 4px;
        align-items: center;
        margin-right: 8px;
      }
      .item:hover {
        border-color: var(--brand-base);
        color: var(--brand-base);
      }
    }
  }
}
