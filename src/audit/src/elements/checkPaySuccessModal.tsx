/*
 * @Author: zhangkai
 * @Date: 2021-04-19 15:32:07
 */

import React, { Component } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Button } from '@hose/eui'
import { app } from '@ekuaibao/whispered'
import styles from './checkPaySuccess.module.less'
//@ts-ignore
import PAY_INFO_FILL from '../images/pay-info-fill.svg'
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')

interface Props {
  layer?: any
}

@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer',
})
//@ts-ignore
@EnhanceFormCreate()
export default class CheckPaySuccessModal extends Component<Props> {
  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleOk = async () => {
    this.props.layer.emitOk({})
  }
  render() {
    return (
      <div className={styles['check-pay-success-modal-wrapper']}>
        <div className="check-pay-success-content">
          <div className="check-pay-success-header">
            <img src={PAY_INFO_FILL} alt="" />
            <div className="title">{i18n.get('确认成功')}</div>
          </div>
          <div className="check-pay-success-description">
            {i18n.get('当前正手动将单据变为已支付，请确认此单据已在第三方平台支付成功')}
          </div>
        </div>
        <div className="modal-footer check-pay-success-footer">
          <Button onClick={this.handleOk} data-testid="pay-checkPaySuccessModal-confirm-btn">
            {i18n.get('确定')}
          </Button>
          <Button category="secondary" className="btn-ml" onClick={this.handleCancel} data-testid="pay-checkPaySuccessModal-cancel-btn">
            {i18n.get('取消')}
          </Button>
        </div>
      </div>
    )
  }
}
