import { app as api } from '@ekuaibao/whispered'
import './select-payment-modal.less'
import React from 'react'
import { Form } from 'antd'
import { Tooltip, Button, Dropdown, Space, message, Input, Select } from '@hose/eui'
import { OutlinedDirectionDown, OutlinedEditEdit, OutlinedTipsInfo, OutlinedTipsMaybe } from '@hose/eui-icons'
import { EnhanceConnect } from '@ekuaibao/store'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
const { getMoney } = api.require('@lib/misc')
import { uniqueId, unionBy, get, uniq, uniqBy } from 'lodash'
const { thousandBitSeparator } = api.require('@components/utils/fnThousandBitSeparator')
const EnhanceFormCreate = api.require('@elements/enhance/enhance-form-create')
const { getBoolVariation } = api.require('@lib/featbit')
import Big from 'big.js'
import {
  getAccountsReceivableInfo,
  getBatchFlowConfig,
  getBatchFlowConfigByConditions,
  getPaymentAccount,
  getValidConditionForPayPlanRepay,
  getValidCondition,
  getPaymentPlan,
  getPaymethodSettingConfig,
  postPayMoneyByPlans,
  payBackLog,
  getAccountInfoById,
  postPayeeList,
} from '../audit-action'
const getDetailCalculateMoney = api.require('@bills/util/getDetailCalculateMoney')
const AccountListItem = api.require('@elements/payee-account/account-list-item')
const PayAccountCard = api.require('@elements/PayAccountCard')
import { CHANPAY_PURPOSE } from './enums'
import SelectPayMethodModalEditView from './SelectPayMethodModalEditViewWidget'
import { fetchChannel } from '../util/fetchUtil'
import MessageCenter from '@ekuaibao/messagecenter'
const MoneyNzh = api.require('@elements/puppet/MoneyNzh')
import { showMessage } from '@ekuaibao/show-util'
const ApproveSignature = api.require('@elements/signature/ApproveSignature')
import CaptchModal from '../elements/CaptchaModal'
import { getPaymentsCaptcha, enumSmsCheck, checkCaptcha } from '../elements/CaptchaModal/actions/fetch'
import { _payFlow } from '../service'
import { Resource } from '@ekuaibao/fetch'

const FormItem = Form.Item
const Option = Select.Option
const { TextArea } = Input
import CBSPAY_Pay from './Channels/CBSPAY_Pay'
import OrderTypeLabel from './Channels/OrderTypeLabel'
import AbstractPay from './Channels/abstract/AbstractPay'
import PaymethodSettingConfig from './Channels/PaymethodSettingConfig'
import { Universal_Unique_Key } from '../index'
import { CheckPayMethodStatusBar } from './checkPayMethodStatusBar'
import { fnGetPayAmount, formatFormData } from '../util/Utils'
import { newTrack } from '../util/trackAudit'
import CurrencyOffline from './Channels/CurrencyOffline'
const needValidConditionChannelMap = {
  CHANPAY: true,
  CHANPAYV2: true,
}

const PayMethodCheckResource = new Resource('/api/pay/v2/api/corporationGrant/netCheck')
const { UniversalComponent } = api.require('@elements/UniversalComponent')

const isShouldCheckPayMethodAvailable = channel => {
  return ['TMSPAY'].includes(channel)
}

const isWx = window.__PLANTFORM__ === 'WEIXIN'

/**
 * 检测支付方式链路是否正常
 * @param channel {string}
 * @returns {Promise<{available: boolean, error?: string} | null>}
 */
async function fetchPayMethodAvailableStatus(channel) {
  switch (channel) {
    case 'TMSPAY': {
      try {
        const result = await PayMethodCheckResource.GET('/$channel', { channel })
        if (!result) return { available: false }
        return { available: result.status === 'SUCCESS' }
      } catch (error) {
        showMessage.error(error?.message)
      }
    }
  }
}

@EnhanceConnect(state => ({
  userInfo: state['@common'].userinfo.data,
  XJLPPower: state['@common'].powers.OnlinePay,
  WalletPower: state['@common'].powers.Wallet,
  K3Cloud: state['@common'].powers.K3Cloud,
  souchePower: state['@common'].powers.SOUCHE,
  fingardPower: state['@common'].powers.FINGARD,
  OpenAPI: state['@common'].powers.OpenAPI,
  standardCurrency: state['@common'].standardCurrency,
  CHANGJIEPay: state['@common'].powers.CHANGJIEPay,
  ALIPAY_switch: state['@common'].powers.ALIPAY_switch,
  ADJUSTMENT_PAYMENT_PLAN: state['@common'].powers.ADJUSTMENT_PAYMENT_PLAN,
  dynamicChannelMap: state['@audit'].dynamicChannelMap,
  channelList: state['@audit'].channelList,
  allCurrencyRates: state['@common'].allCurrencyRates,
  KA_FOREIGN_ACCOUNT: state['@common'].powers.KA_FOREIGN_ACCOUNT,
  CBS: state['@common'].powers.CBS,
  FOREIGN_CURRENCY_PAY: state['@common'].powers.FOREIGN_CURRENCY_PAY,
}))
@EnhanceModal({
  footer: null,
})
@EnhanceFormCreate()
export default class PayMethodModal extends React.PureComponent {
  constructor(props) {
    super()
    this.doc = {}
    this.copySelectedAccount = {}
    const currency = api.getState('@common').powers.Currency
    const standardMap = props?.data?.amountList?.find(v => v.key === 'standard')
    const receiveMap = props?.data?.amountList?.find(v => v.key === 'receive')
    const _amount = currency && !receiveMap?.disabled ? receiveMap : standardMap
    this.state = {
      showAutograph: true,
      supportXJLP: '',
      currentAccountChannels: [],
      payAccountActiveList: [],
      isAdvancedOptions: false,
      searchText: '',
      channel: 'OFFLINE',
      isAutoSummary: false,
      dynamicChannels: [],
      mustBeUsedSignature: false,
      adjustPaymentPlanData: {}, //支付计划临时缓存数据
      selectedCurrency: props.standardCurrency, // 选中币种的完整信息
      payPlanFilterArr: null, // 部分支付中，判断哪些支付计划可以被展示
      paymethodConfigValue: {}, // 支付渠道的中间配置
      loading: true, // 组件没有选择时，不能提交
      isShowConfig: false, // 组件接口没有返回值时，不渲染已有类型组件
      payMethodState: {
        // 检测支付链路状态
        isValidating: false,
        available: true,
        error: undefined,
      },
      amountList: props?.data?.amountList || [],
      amountMap: _amount || {},
      payeeBankNames: [], //获取支付配置时新增参数：收款银行名称
      useLegalEntityAccount: false,
      autoSummaryCacheKey: props?.userInfo?.staff?.id + 'AutoSummary',
      autoCutRemarkCacheKey: props?.userInfo?.staff?.id + 'AutoCutRemark',
      isAutoCutRemark: true,
      isCNY: props?.data?.amountList?.find(v => v.key === 'standard')?.strCode === 'CNY',
    }
    this.payerInfoConfig = {
      byHand: false,
      useSpecial: false,
      dimensionId: '',
      autoSummary: false,
      fieldLabel: '',
      dimensionField: '',
    }
    props.overrideGetResult(this.getResult.bind(this))
  }

  bus = new MessageCenter()
  modalRef = React.createRef()

  componentWillMount() {
    this.getPayeeBankNames()
    this.getMultiRepay()
    this.getPayerConfig()
  }

  componentDidUpdate(prevProps, prevState, snapshot) {
    const { channel: preChannel } = prevState
    const { channel } = this.state

    if (channel !== preChannel) {
      this.checkPayMethodAvailable()
    }
  }

  initialPayPlanFilterArr = () => {
    const { channel, accountId } = this.state
    const inPartialPayment = get(this.props, 'data._this.inPartialPayment')
    if (inPartialPayment && channel === 'HSBCPAY') {
      const flowIds = get(this.props, 'data.backlog', [])
      const _flowIds = Array.isArray(flowIds) ? flowIds : [flowIds]
      const { remark = '', dimensionItemId = '', autoSummary } = this.getPayRemark()
      const params = {
        flowIds: _flowIds.map(i => i.flowId.id),
        remark,
        dimensionItemId,
        autoSummary,
        channel,
        accountId,
      }
      return getPaymentPlan(params).then(res => {
        const arr = get(res, 'value.viewDataList.data', [])
        const payPlanIds = arr.map(el => el.dataLink.id)
        this.setState({ payPlanFilterArr: payPlanIds })
      })
    } else if (channel === 'CBSPAY') {
      return api
        .open('@audit:CheckPayMethodStatusModal', {
          channel,
        })
        .then(data => {
          if (data?.result) {
            return Promise.resolve()
          } else {
            return Promise.reject()
          }
        })
    } else {
      return Promise.resolve()
    }
  }

  componentDidMount() {
    newTrack('entrance_pay_modal')
    api.dataLoader('@common.powers').load()
    this.getPaymentAccountByFlowId()
    this.getAutoSummaryCache()
  }

  getMultiRepay = () => {
    const { data } = this.props
    const { payPlan, type } = data
    let multiRepay = false
    let multiRepayPlan
    if (type === 'payment') {
      multiRepay = true
      multiRepayPlan = payPlan?.payplanRepayIds
    }
    this.setState({ multiRepay, multiRepayPlan })
  }

  getPaymentAccountByFlowId = (flowIds, isReset = true, isResetChannel = true) => {
    this._getPaymentAccount(null, isReset, flowIds, isResetChannel)
  }

  getChannel = (selectValue, channelFlag, notGetPaymethodSetting, _channel) => {
    const channel = selectValue?.sort
    const { amountMap } = this.state
    const isCNY = amountMap?.strCode === 'CNY'
    this.setState({ isCNY })
    let value = this.state.selectedAccount
    let defaultChannel = _channel || selectValue?.defaultChannel
    fetchChannel(channel).then(
      data => {
        const { dynamicChannelMap } = this.props
        const dynamicChannels = data.items || []
        let supportForeignPayList = []
        dynamicChannels.map(v => {
          if (v?.supportForeignPay) {
            supportForeignPayList.push(v?.code)
          }
        })
        if (!isCNY) {
          let channelsArr = selectValue.channels || []
          channelsArr = channelsArr.filter(v => supportForeignPayList.includes(v))
          value = { ...selectValue, channels: channelsArr }
          defaultChannel = channelsArr?.[0] || 'OFFLINE'
        } else {
          value = this.copySelectedAccount
          defaultChannel = _channel || value?.defaultChannel || 'OFFLINE'
        }
        if (!dynamicChannelMap?.[defaultChannel]?.active) {
          defaultChannel = 'OFFLINE'
        }
        const { flowIds, paymentPlanIds } = this.fnGetIds()
        !notGetPaymethodSetting && this.getPaymethodSetting({ channel: defaultChannel, flowIds, paymentPlanIds })
        this.setState({ dynamicChannels, selectedAccount: value, channel: defaultChannel }, () => {
          channelFlag && this.onChannelChange(defaultChannel)
        })
      },
      err => {
        showMessage.error(err.mesage)
      },
    )
  }

  fnGetIds = () => {
    const { data, isPayPlanRepay } = this.props
    const { adjustPaymentPlanData } = this.state
    const { flowIds, isByDetail, type, payPlan } = data
    let backlog = data.backlog || []
    backlog = Array.isArray(backlog) ? backlog : [backlog]
    let _paymentPlanIds = []
    if (adjustPaymentPlanData?.data) {
      _paymentPlanIds = adjustPaymentPlanData?.data?.reduce((acc, v) => {
        if (v?.dataLink?.id) {
          acc.push(...v.dataLink.id.split(','))
        }
        return acc
      }, [])
    } else {
      _paymentPlanIds = isPayPlanRepay
        ? type === 'payment'
          ? payPlan?.payplanRepayIds || []
          : [payPlan?.id]
        : isByDetail
        ? backlog.map(v => v.id)
        : []
    }
    const _flowIds = !!_paymentPlanIds?.length ? [] : flowIds
    return { paymentPlanIds: _paymentPlanIds, flowIds: _flowIds }
  }

  //isResetSelectedAccount  是否重置被选择的card
  _getPaymentAccount = (cb, isResetSelectedAccountBoo, _flowIds, isResetChannel = true) => {
    const isResetSelectedAccount = isResetSelectedAccountBoo ?? true
    const { data, isPayPlanRepay } = this.props
    const { multiRepay, adjustPaymentPlanData } = this.state
    const flowIds = []
    let backlog = get(data, 'backlog', [])
    if (_flowIds) {
      flowIds.push(..._flowIds)
    } else if (isPayPlanRepay) {
      let flowId
      if (multiRepay) {
        flowId = data.payPlan.id
      } else {
        flowId = get(data, 'payPlan.flowId')
      }
      if (flowId) flowIds.push(flowId)
    } else {
      backlog = Array.isArray(backlog) ? backlog : [backlog]
      backlog.forEach(v => {
        flowIds.push(v.flowId.id)
      })
    }
    let isByPayPlans, payPlanIds
    if (adjustPaymentPlanData?.data) {
      // 调整支付计划时，按支付计划维度处理
      isByPayPlans = true
      payPlanIds = []
      adjustPaymentPlanData?.data.forEach(el => {
        if (el?.dataLink?.children) {
          const originalPayPlanIds = String(el?.dataLink?.id).split(',')
          if (originalPayPlanIds.length) {
            payPlanIds = payPlanIds.concat(originalPayPlanIds)
          }
        } else {
          payPlanIds.push(el.dataLink.id)
        }
      })
    }
    const isByDetail = get(data, 'isByDetail', false)
    const params = {
      backlogs: backlog,
      flowIds,
      isByPayPlans,
      payPlanIds,
      isByDetail: data.isByDetail,
      deviceType: 'DESKTOP', //标识应用类型
    }
    getPaymentAccount(params).then(res => {
      const { dynamicChannelMap = {} } = this.props
      const payAccountActiveList = get(res, 'items', [])
      if (payAccountActiveList.length) {
        let firstPayAccountActive = isResetSelectedAccount ? payAccountActiveList[0] : this.state.selectedAccount
        const accountId = firstPayAccountActive.id
        const defaultChannel = dynamicChannelMap[firstPayAccountActive?.defaultChannel]?.active
          ? firstPayAccountActive.defaultChannel
          : 'OFFLINE'
        if (isResetSelectedAccount) {
          this.copySelectedAccount = firstPayAccountActive
        }
        this.setState(
          {
            payAccountActiveList,
            accountId,
            radioValue: 'OFFLINE',
            selectedAccount: firstPayAccountActive,
            channel: isResetChannel ? defaultChannel : this.state.channel,
            currentAccountChannels: firstPayAccountActive.channels,
            isAutoSummary: (isResetChannel && defaultChannel === 'SOUCHEV2') || this.state.isAutoSummary, // 搜车支付的使用摘要选项默认值为选中状态
          },
          () => {
            this.getChannel(firstPayAccountActive, false, false, this.state.channel)
            const { KA_FOREIGN_ACCOUNT, FOREIGN_CURRENCY_PAY } = this.props
            const { flowIds, paymentPlanIds } = this.fnGetIds()
            !FOREIGN_CURRENCY_PAY && this.getPaymethodSetting({ channel: this.state.channel, flowIds, paymentPlanIds })
            const { channel } = this.state
            if (channel === 'HSBCPAY' && KA_FOREIGN_ACCOUNT) {
              this.handleForeignCurrency(channel)
            }
            if (needValidConditionChannelMap[defaultChannel]) {
              this.onChannelChange(isResetChannel ? defaultChannel : this.state.channel)
            }
            typeof callback === 'function' && callback(accountId)
          },
        )
        cb && cb({ list: payAccountActiveList })
      }
    })

    // 支付表格上选择全部后点支付时，这个fetchParams会有值
    const fetchParams = get(data, 'fetchParams')
    if (fetchParams) {
      getBatchFlowConfigByConditions({ fetchParams }).then(res => {
        this.setMustBeUsedSignatureFromResult(res)
      })
    } else {
      getBatchFlowConfig({ flowIds: flowIds }).then(res => {
        this.setMustBeUsedSignatureFromResult(res)
      })
    }
  }
  //后端需要收款银行列表做逻辑处理
  getPayeeBankNames = async () => {
    let result = []
    try {
      const { isPayPlanRepay, data = {} } = this.props
      const { payPlan, backlog, type } = data
      let payeeIds = []
      if (isPayPlanRepay) {
        if (type === 'payment') {
          //重新支付-批量支付
          result = payPlan?.payeeBankNames || []
        } else {
          result.push(get(payPlan, 'payee.bank'))
        }
      } else {
        if (backlog instanceof Array) {
          backlog.forEach(item => {
            const multiplePayeesMode = get(item, 'flowId.form.multiplePayeesMode')
            if (multiplePayeesMode) {
              const payPlan = get(item, 'flowId.form.payPlan')
              payPlan?.length &&
                payPlan.forEach(plan => {
                  //待支付-批量操作时 支付计划_收款信息为id  需要查询银行名称
                  const payeeInfo = get(plan, 'dataLinkForm.E_system_支付计划_收款信息')
                  if (payeeInfo && typeof payeeInfo === 'string') {
                    payeeIds.push(payeeInfo)
                  } else {
                    result.push(payeeInfo?.bank)
                  }
                })
            } else {
              result.push(get(item, 'flowId.form.payeeId.bank'))
            }
          })
        }
        if (payeeIds.length) {
          const res = await postPayeeList({ ids: payeeIds })
          if (res?.items) {
            result = result.concat(res.items.map(i => i.bank))
          }
        }
      }
      result = result.filter(i => i)
    } catch (e) {
      console.log(e)
    }
    this.setState({
      payeeBankNames: result,
    })
  }
  getPaymethodSetting = props => {
    this.setState({ loading: true, isShowConfig: false })
    const { selectedAccount, amountMap } = this.state
    const { channel, flowIds, paymentPlanIds } = props
    const params = {
      channel,
      accountId: selectedAccount?.id,
      flowIds: flowIds ?? [],
      currencyCode: amountMap?.strCode ?? 'CNY',
      paymentPlanIds: paymentPlanIds ?? [],
      bankName: selectedAccount?.bankName || '',
    }
    getPaymethodSettingConfig(params)
      .then(res => {
        const paymethodConfigValue = res?.value || {}
        this.setState({ paymethodConfigValue, loading: false, isShowConfig: true })
      })
      .catch(error => {
        showMessage.error(error?.message || error?.errorMessage)
        this.setState({ paymethodConfigValue: [], loading: false, isShowConfig: true })
      })
  }

  setMustBeUsedSignatureFromResult = res => {
    const mustBeUsedSignature = get(res, 'value.mustBeUsedSignature')
    this.setState({ mustBeUsedSignature })
  }

  getResult() {
    const { userInfo } = this.props
    return this.bus.invoke('get:approve:show:signature').then(showAutograph => {
      if (showAutograph) {
        this.doc.autographImageId = get(userInfo, 'staff.autograph.key')
      }
      return this.doc
    })
  }

  handleModalClose = () => {
    newTrack('pay_modal_cancel_btn')
    this.props.layer.emitCancel()
  }

  openHsbcModal = batch => {
    const { selectedCurrency, selectedAccount, channel, accountId, payPlanFilterArr } = this.state
    return api.open('@audit:HSBC_SupplyPaymentDrawer', {
      flowIds: get(this.props, 'data.backlog', []),
      getPayRemark: this.getPayRemark,
      channel,
      paymentAccount: selectedAccount,
      paymentCurrency: selectedCurrency,
      accountId,
      ekbTradeNo: get(batch, 'ekbTradeNo'),
      channelTradeNo: get(batch, 'channelTradeNo'),
      payPlanFilterArr,
    })
  }

  prepareSaveModal = () => {
    this.initialPayPlanFilterArr().then(() => {
      this.handleModalSave()
    })
  }

  handleModalSave = async () => {
    this.props.form.validateFields(async (err, values) => {
      if (!!err) return
      const { dynamicChannelMap } = this.props
      const {
        selectedAccount,
        channel,
        payAccountActiveList,
        autoSummaryCacheKey,
        isAutoSummary,
        autoCutRemarkCacheKey,
        isAutoCutRemark,
        amountList,
      } = this.state
      if (values?.paidCurrencyType && values?.paidCurrencyType === 'useAmount') {
        const standardMap = amountList?.find(v => v.key === 'standard') || {}
        if (standardMap?.standard && values?.paidAmount > 0) {
          const paidRate = new Big(Number(standardMap?.standard)).div(Number(values?.paidAmount)).toFixed(32)
          values.paidRate = paidRate?.replace(/(\.\d*?)0+$/, '$1')?.replace(/\.$/, '')
        }
      }
      if (values.switchLinkedPayment && values.masterAccountId) {
        let masterAccount = payAccountActiveList.find(el => el.id === values.masterAccountId)
        if (!masterAccount) {
          const result = await getAccountInfoById(values.masterAccountId)
          masterAccount = result?.items?.[0]
        }
        values.accountNo = masterAccount?.accountNo
        values.bankLinkNo = masterAccount?.bankLinkNo
        values.city = masterAccount?.city
      }
      //需要短信校验
      const isNeedSmsCheck = (dynamicChannelMap[channel] || {})?.smsCheck || []

      if (isWx) {
        session.set(autoSummaryCacheKey, isAutoSummary)
        session.set(autoCutRemarkCacheKey, isAutoCutRemark)
      } else {
        localStorage.setItem(autoSummaryCacheKey, isAutoSummary)
        localStorage.setItem(autoCutRemarkCacheKey, isAutoCutRemark)
      }

      values.isAutoCutRemark = isAutoCutRemark
      if (Array.isArray(isNeedSmsCheck) && isNeedSmsCheck.includes(enumSmsCheck.PAY)) {
        //true:已有缓存,不需要再次验证 false:缓存过期,需要再次验证
        checkCaptcha()
          .then(resp => {
            if (resp.value) {
              this.assembleConfig(values, dynamicChannelMap, {})
            } else {
              this.modalRef.current.show()
            }
          })
          .catch(err => {
            this.modalRef.current.show()
          })
        return
      }
      if (!selectedAccount) return
      this.assembleConfig(values, dynamicChannelMap, {})
    })
  }

  assembleConfig = (values, dynamicChannelMap, otherPropsData, cb) => {
    let param = {
      money: this.state?.amountMap,
      bank: this.state?.selectedAccount?.bank,
      channel: this.state?.channel,
    }
    if (values?.paidOffLineFinishedTime) {
      values.paidOffLineFinishedTime = values?.paidOffLineFinishedTime?.valueOf()
    }
    if (values?.payson) {
      param.payson = values?.payson
    }
    newTrack('pay_modal_save_btn', param)
    const otherProps = otherPropsData ?? {}
    const { selectedAccount, channel, isAutoSummary, adjustPaymentPlanData, amountMap, paymethodConfigValue } =
      this.state
    if (isAutoSummary) {
      values.dimensionItemId = ''
    }
    let isUsePaymentPlan =
      this.isUsePaymentPlan(channel, this.props.ADJUSTMENT_PAYMENT_PLAN) && adjustPaymentPlanData.config

    this.bus.invoke('get:approve:signature:result').then(result => {
      if (!result) return
      const backlog = this.props.data?.backlog || []
      const isByDetail = this.props.data?.isByDetail
      isUsePaymentPlan = isByDetail ? true : isUsePaymentPlan
      const autoSummary = dynamicChannelMap[channel] ? { autoSummary: isAutoSummary } : {}
      const hsbcCallBack = this.state.channel === 'HSBCPAY' ? this.openHsbcModal : null
      const currency = {
        symbol: amountMap?.symbol ?? '¥',
        strCode: amountMap?.strCode ?? 'CNY',
        numCode: amountMap?.numCode ?? '156',
      }
      const pageData = paymethodConfigValue?.pageData
      this.doc = {
        ...values,
        ...autoSummary,
        accountId: selectedAccount.id,
        channel,
        dynamicChannelMap,
        paymentPlans: isUsePaymentPlan ? adjustPaymentPlanData?.config || backlog : [],
        ...otherProps,
        isUsePaymentPlan: isUsePaymentPlan,
        hsbcCallBack,
        inputData: channel === 'CMBCBS8OVS' && pageData ? { ...values, ...pageData } : values,
        currency,
      }
      if (!cb) {
        this.props.layer.emitOk()
      } else {
        cb?.(this.doc)
      }
    })
  }

  //如果需要短信验证，验证成功后走此逻辑
  captchaSuccess = (code, config) => {
    const captchaId = config?.captchaId || ''
    const captchaCode = code || ''
    const { dynamicChannelMap, form, isPayPlanRepay } = this.props
    const values = form.getFieldsValue()

    const successCb = () =>
      this.props.layer.emitOk({
        isCaptchaClose: true,
      })
    const errorCb = err => {
      const _ = ['Verification code exception:', '验证码异常:']
      if (_.filter(m => err.message.indexOf(m) >= 0).length) {
        //验证码类错误，释放
      } else {
        this.props.layer.emitOk({
          isCaptchaClose: true,
        })
      }
    }

    const callback = data => {
      const { _this, callback, billType, backlog, flowIds } = this.props.data
      data.flowIds = flowIds
      if (isPayPlanRepay) {
        this.props.layer.emitOk()
      } else {
        _payFlow.call(_this, { data, callback, billType, backlog, successCb, errorCb })
      }
    }
    this.assembleConfig(values, dynamicChannelMap, { captchaId, captchaCode }, callback)
  }

  onClickEditBtn = () => {
    const { payAccountActiveList, selectedAccount } = this.state
    const { dynamicChannelMap } = this.props
    api
      .open('@audit:SelectPayAccountDrawer', {
        payAccountActiveList,
        selectedAccount,
        fnOnSelectAccount: this.onSelectAccount,
        dynamicChannelMap,
        getPaymentAccount: this._getPaymentAccount,
      })
      .then(item => {
        this.onSelectAccount(item)
      })
  }

  onSelectAccount = selectedValue => {
    const { form } = this.props
    if (this.state.selectedAccount.id !== selectedValue.id) {
      form.setFieldsValue({ remark: '' })
    }
    this.copySelectedAccount = selectedValue
    this.getChannel(selectedValue, true, true)
  }

  onChannelChange = channel => {
    const { form } = this.props
    form.setFieldsValue({ remark: '' })
    const { flowIds, paymentPlanIds } = this.fnGetIds()
    this.getPaymethodSetting({ channel, flowIds, paymentPlanIds })
    const { KA_FOREIGN_ACCOUNT } = this.props
    if (channel === 'HSBCPAY' && KA_FOREIGN_ACCOUNT) {
      this.handleForeignCurrency(channel)
    }
    // 搜车支付的使用摘要选项默认值调整为选中状态
    const isAutoSummary = channel === 'SOUCHEV2' || this.state.isAutoSummary
    const stateParams = {
      channel,
      isAdvancedOptions: false,
      isAutoSummary,
    }

    const { repay = false, dynamicChannelMap = {}, isPayPlanRepay, data } = this.props
    const { selectedAccount } = this.state
    const paymentAccountId = selectedAccount.id
    const channelMap = dynamicChannelMap[channel] || {}
    if (channelMap.validCondition) {
      const flowIds = []
      const detailIds = []
      if (isPayPlanRepay) {
        const flowId = get(data, 'payPlan.flowId')
        flowIds.push(flowId)
      } else {
        let flowList = get(this.props, 'data.backlog', [])
        flowList = Array.isArray(flowList) ? flowList : [flowList]
        flowList.length > 0 &&
          flowList.forEach(v => {
            flowIds.push(v.flowId.id)
          })
      }
      let params = { channel, flowIds, paymentAccountId }
      //重新支付
      if (repay) {
        const detailList = get(this.props, 'data.backlog[0].flowId.form.details', [])
        detailList.length > 0 &&
          detailList.forEach(v => {
            detailIds.push(v.feeTypeForm.detailId)
          })
        params = { channel, flowIds, repay, detailIds, paymentAccountId }
      }
      // 重新支付支付计划
      if (isPayPlanRepay) {
        let paymentIds = []
        const payPlan = data?.payPlan || {}
        if (data?.type === 'payment') {
          paymentIds = payPlan?.payplanRepayIds
        } else {
          paymentIds.push(payPlan?.id)
        }
        getValidConditionForPayPlanRepay({ accountId: paymentAccountId, paymentIds }).then(res =>
          this.handleCallBack(res, stateParams),
        )
      } else if (!this.state.adjustPaymentPlanData.config || channel === 'CHANPAY' || channel === 'ERP') {
        getAccountsReceivableInfo(params).then(res => this.handleCallBack(res, stateParams))
      } else {
        getValidCondition({
          paymentPlans: this.state.adjustPaymentPlanData.config,
          accountId: this.state.accountId,
        }).then(res => this.handleCallBack(res, stateParams))
      }
    } else {
      this.setState(stateParams)
    }
  }

  /**
   * 检查支付方式是否正常
   * @returns {Promise<void>}
   */
  checkPayMethodAvailable = async () => {
    const { channel } = this.state
    if (isShouldCheckPayMethodAvailable(channel)) {
      switch (channel) {
        // 宁波财资大管家
        case 'TMSPAY': {
          // reset check state
          this.setState({
            payMethodState: {
              ...this.state.payMethodState,
              isValidating: true,
              available: false,
              error: undefined,
            },
          })
          const checkPayMethodId = uniqueId()
          this.currentCheckPayMethodId = checkPayMethodId
          const result = await fetchPayMethodAvailableStatus(channel)
          // 检测完毕
          if (this.currentCheckPayMethodId !== checkPayMethodId) return
          const { available, error } = result
          this.setState({
            payMethodState: {
              ...this.state.payMethodState,
              isValidating: false,
              available: available,
              error: error,
            },
          })
        }
      }
    } else {
      this.setState({
        payMethodState: {
          // 检测支付链路状态
          isValidating: false,
          available: true,
          error: undefined,
        },
      })
    }
  }

  handleCallBack = (res, stateParams) => {
    return new Promise(r => {
      const item = get(res, 'items', [])
      const isAdvancedOptions = !!item.length
      stateParams.isAdvancedOptions = isAdvancedOptions
      this.setState(stateParams, r)
    })
  }

  handleRemarkChange = e => {
    const { checked } = e.target
    this.setState({ isAutoSummary: checked })
  }

  handleAutoCutRemarkChange = e => {
    const { checked } = e.target
    this.setState({ isAutoCutRemark: checked })
  }

  handlePayerInfoConfig = payerInfoConfig => {
    this.payerInfoConfig = payerInfoConfig
  }

  handleZeroPay = (paymentPlans, standardAmount) => {
    this.props.layer.emitCancel()
    const flowIds = paymentPlans.map(v => v.flowId)
    api.open('@audit:ZeroPayModal', { flowIds, standardAmount }).then(res => {
      const { data } = this.props
      const _paymentPlans = paymentPlans.map(v => {
        return {
          ...v,
          flowId: v.flowId?.id ?? v.flowId,
          payeeId: v.payeeId?.id ?? v.payeeId,
          instance: null,
          form: formatFormData(v?.form),
        }
      })
      const currency = {
        symbol: standardAmount?.symbol || '¥',
        strCode: standardAmount?.strCode || 'CNY',
        numCode: standardAmount?.numCode || '156',
      }
      const params = {
        ...res,
        flowIds: [],
        paymentPlans: _paymentPlans,
        channel: 'ZERO',
        isUsePaymentPlan: true,
        accountId: '', // 后端参数校验，必须要有这个字段
        currency,
      }
      api.dispatch(payBackLog(params)).then(() => {
        data?.callback?.()
        api.invokeService(`@common:get:backlog:count:payment`)
      })
    })
  }

  adjustPaymentPlan = () => {
    newTrack('audit_adjust_payment_plan_btn')
    this.props.form.validateFields(err => {
      if (!!err) return
      api
        .open('@audit:AdjustPaymentPlan', {
          adjustPaymentPlanData: this.state.adjustPaymentPlanData,
          flowIds: get(this.props, 'data.backlog', []),
          isByDetail: get(this.props, 'data.isByDetail'),
          getPayRemark: this.getPayRemark,
          channel: this.state.channel,
          accountId: this.state.accountId,
        })
        .then(async data => {
          const result = await postPayMoneyByPlans(data?.config || [])
          if (!result?.value?.standardStrCode) return
          const { amountList } = fnGetPayAmount(result.value)
          const { amountMap } = this.state
          let _amountMap = amountList?.find(v => v.key === (amountMap?.key || 'standard')) || {}
          if (_amountMap?.standard === null) {
            _amountMap = amountList?.find(v => v.key === 'standard') || {}
          }
          if (_amountMap?.standard !== null && _amountMap?.standard * 1 === 0) {
            this.props.layer.emitCancel(() => {
              this.handleZeroPay(data?.config || [], _amountMap)
            })
            return
          }
          this.setState({
            adjustPaymentPlanData: {
              ...data,
              channel: this.state.channel,
              accountId: this.state.accountId,
            },
            amountMap: _amountMap,
            amountList,
          })
          const { dynamicChannelMap = {} } = this.props

          const { validCondition = false } = dynamicChannelMap[this.state.channel] || {}
          if (validCondition) {
            getValidCondition({
              paymentPlans: data.config,
              accountId: this.state.accountId,
            }).then(res => {
              const item = get(res, 'items', [])
              const isAdvancedOptions = !!item.length
              this.setState({
                isAdvancedOptions,
              })
            })
          }

          const _flowIds = uniq(data.config.map(i => i.sourceId))
          this.getPaymentAccountByFlowId(_flowIds, false, false)
        })
    })
  }

  //是否显示合并支付计划的按钮
  isUsePaymentPlan = (channel, ADJUSTMENT_PAYMENT_PLAN) => {
    return ADJUSTMENT_PAYMENT_PLAN && !(channel === 'CHANPAY' || channel === 'ERP')
  }

  /**
   * 获取一些支付摘要的相关信息。
   */
  getPayRemark = () => {
    // byHand: 手工填写   useSpecial: 使用特殊定段  autoSummary: 自动填写   fieldLabel: 自动填写的字段
    const { dynamicChannelMap, form } = this.props
    const { byHand, fieldLabel, useSpecial } = this.payerInfoConfig
    const { channel, selectedAccount, isAutoSummary } = this.state
    const dimensionItemId = form.getFieldValue('dimensionItemId')
    const channelMap = dynamicChannelMap[channel] || {}
    const remarkLimit =
      selectedAccount && channelMap.needRemark
        ? channelMap.remarkLength[selectedAccount.bank] || channelMap.remarkLength['默认'] || 28
        : 28

    const _autoSummary = dynamicChannelMap[channel] ? { autoSummary: isAutoSummary } : {}

    return {
      remarkLimit,
      byHand, //手工填写
      ..._autoSummary, //自动填写
      fieldLabel, //自动填写的字段
      useSpecial, //使用特殊定段
      needRemark: channelMap.needRemark,
      isAutoSummary,
      remark: this.props.form.getFieldValue('remark'),
      dimensionItemId,
    }
  }

  setLoading = flag => {
    this.setState({ loading: flag })
  }

  fnGetConfigData = paymethodConfigValue => {
    const eventsArr = []
    const _elements = paymethodConfigValue?.elements || []
    _elements.forEach(item => {
      eventsArr.push({ ...item?.events, name: item?.name })
    })
    const elements = _elements.map(item => ({ ...item, eventsArr }))
    return {
      elements,
      pageData: paymethodConfigValue?.pageData,
    }
  }

  renderOrderType = (channelMap, orderTypeLabel, payeeBankNames) => {
    const { form, allCurrencyRates } = this.props
    const { getFieldDecorator } = form
    const { selectedAccount, paymethodConfigValue, channel, loading, amountList, amountMap } = this.state
    const configElements = paymethodConfigValue?.elements || []
    const configData = this.fnGetConfigData(paymethodConfigValue)
    const currency = api.getState('@common').powers.Currency
    const standardMap = amountList?.find(v => v.key === 'standard') || {}

    if (!!configElements.length) {
      return (
        <PaymethodSettingConfig
          payeeBankNames={payeeBankNames}
          form={form}
          channel={channel}
          configData={configData}
          selectedAccount={selectedAccount}
          loading={loading}
          setLoading={this.setLoading}
        />
      )
    }

    if (selectedAccount?.recevingCurrencyEnable && currency && channel === 'OFFLINE') {
      return (
        <CurrencyOffline
          form={form}
          selectedAccount={selectedAccount}
          standardMap={standardMap}
          currentAmountMap={amountMap}
        />
      )
    }

    if (channelMap.code === 'CBSPAY') {
      return <CBSPAY_Pay form={form} channelMap={channelMap} />
    }
    if (channelMap.code === 'HSBCPAY') {
      return null
    }
    if (channelMap.batchPay || channelMap.singlePay) {
      return (
        <FormItem label={orderTypeLabel} className="select-payment-payRemark">
          {getFieldDecorator('singlePay', {
            initialValue: channelMap.batchPay ? false : true,
          })(
            <Select className="method-line" placeholder={i18n.get('请选择银行下单类型')}>
              {channelMap.batchPay && <Option value={false}>{i18n.get('批量支付')}</Option>}
              {channelMap.singlePay && <Option value={true}>{i18n.get('单笔支付')}</Option>}
            </Select>,
          )}
        </FormItem>
      )
    } else {
      return null
    }
  }

  handleCurrencyChange = value => {
    const currencyList = this.getCurrency()
    const selectedCurrency = currencyList.filter(item => item.numCode === value)[0]
    this.setState({ selectedCurrency })
  }

  getCurrency = () => {
    const { allCurrencyRates, standardCurrency } = this.props
    const hasCurrency = this.state.selectedAccount?.extensions?.currency || []
    const allCurrency = unionBy([...allCurrencyRates, standardCurrency], 'numCode')
    const currencyList = allCurrency.filter(item => hasCurrency.indexOf(item.numCode) !== -1)
    return currencyList
  }

  handleForeignCurrency = channel => {
    const { accountId } = this.state
    const flowIds = get(this.props, 'data.backlog', [])
    const _flowIds = Array.isArray(flowIds) ? flowIds : [flowIds]
    const { remark = '', dimensionItemId = '', autoSummary } = this.getPayRemark()
    const params = {
      flowIds: _flowIds.map(i => i.flowId.id),
      remark,
      dimensionItemId,
      autoSummary,
      channel,
      accountId,
    }
    getPaymentPlan(params).then(res => {
      const originalList = get(res, 'value.originalList', [])
      const realPayStrCodeList = originalList
        .map(item => get(item, 'form.E_system_paymentPlan_realPayStrCode'))
        .filter(v => !!v)
      this.setState({
        hasForeignCurrency: realPayStrCodeList.length > 0 && realPayStrCodeList.some(item => item !== 'CNY'),
      })
    })
  }

  checkForeignCurrency(rule, value, callback) {
    if (value && value === '156') {
      return callback(i18n.get('请使用外币币种进行外币支付'))
    }
    callback()
  }

  handleCurrencySelectChange = ({ key }) => {
    const { amountList, selectedAccount } = this.state
    const amountMap = amountList.find(v => v?.key === key)
    this.setState({ amountMap }, () => {
      this.getChannel(selectedAccount, true, false)
    })
  }

  getPayerConfig = async _ => {
    let config = await api.invokeService('@common:get:payer:shared')
    this.setState({ useLegalEntityAccount: config?.value?.useLegalEntityAccount })
  }

  renderCurrency = channelMap => {
    const { hasForeignCurrency } = this.state
    const { KA_FOREIGN_ACCOUNT, form } = this.props
    const { getFieldDecorator } = form
    if (channelMap.code === 'HSBCPAY' && KA_FOREIGN_ACCOUNT) {
      const currencyList = this.getCurrency()
      const placeholder =
        currencyList.length > 0
          ? i18n.get('请选择{__k0}', { __k0: '币种' })
          : '当前付款账户未设置币种，请前往付款账户设置币种。'
      return (
        <FormItem label={i18n.get('付款账户币种选择')} className="select-payment-payRemark">
          {getFieldDecorator('currency', {
            initialValue: currencyList[0]?.numCode,
            rules: [
              {
                required: true,
                whitespace: true,
                message: i18n.get('请选择{__k0}', { __k0: '币种' }),
              },
              { validator: hasForeignCurrency ? this.checkForeignCurrency : null },
            ],
          })(
            <Select
              className="method-line"
              placeholder={placeholder || i18n.get('请选择付款账户币种类型')}
              onChange={this.handleCurrencyChange}
              getPopupContainer={triggerNode => triggerNode.parentNode}
              showSearch={true}
              filterOption={(input, option) => {
                return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }}
            >
              {currencyList.map(item => (
                <Option key={item.numCode} value={item.numCode}>
                  {item.name} {item.strCode}
                </Option>
              ))}
            </Select>,
          )}
        </FormItem>
      )
    } else {
      return null
    }
  }

  renderCurrencySelect = () => {
    const { amountMap = {}, amountList = [] } = this.state
    return (
      <div className="currency-select-wrap">
        <Dropdown
          menu={{ items: amountList, onClick: this.handleCurrencySelectChange }}
          overlayStyle={{ width: 210 }}
          placement="bottomRight"
        >
          <a onClick={e => e.preventDefault()} data-testid="pay-selectPaymethodModal-currencyDropdown">
            {amountMap?.label} <OutlinedDirectionDown style={{ marginLeft: 4 }} />
          </a>
        </Dropdown>
      </div>
    )
  }

  getAutoSummaryCache = () => {
    let isAutoSummary = false
    let isAutoCutRemark = true
    const { autoSummaryCacheKey, autoCutRemarkCacheKey } = this.state

    try {
      if (isWx) {
        isAutoSummary = JSON.parse(session.get(autoSummaryCacheKey))
        isAutoCutRemark = JSON.parse(session.get(autoCutRemarkCacheKey))
      } else {
        isAutoSummary = JSON.parse(localStorage.getItem(autoSummaryCacheKey))
        isAutoCutRemark = JSON.parse(localStorage.getItem(autoCutRemarkCacheKey))
      }
    } catch (_error) {}

    this.setState({
      isAutoSummary,
      isAutoCutRemark: isAutoCutRemark === null ? true : isAutoCutRemark,
    })
  }

  render() {
    const {
      form,
      data,
      visible,
      standardCurrency: { scale, symbol },
      repay = false,
      isPayPlanRepay = false,
      channelList,
      dynamicChannelMap = {},
      userInfo,
      ADJUSTMENT_PAYMENT_PLAN,
      CBS,
    } = this.props
    if (!visible) return false
    const {
      payAccountActiveList,
      isAdvancedOptions,
      selectedAccount,
      isAutoSummary,
      channel,
      dynamicChannels,
      mustBeUsedSignature,
      adjustPaymentPlanData,
      multiRepay,
      multiRepayPlan,
      payMethodState,
      loading,
      isShowConfig,
      payeeBankNames,
      amountMap = {},
      useLegalEntityAccount,
      isAutoCutRemark,
      amountList,
    } = this.state
    const { getFieldDecorator } = form
    const { backlog } = data
    const { flowId } = repay ? backlog[0] : backlog
    const isUsePaymentPlan = this.isUsePaymentPlan(channel, ADJUSTMENT_PAYMENT_PLAN)
    const channelMap = dynamicChannelMap[channel] || {}

    const amount = amountMap?.standard
    const standardMap = amountList?.find(v => v.key === 'standard') || {}
    let paymentPlanCount = 0
    if (adjustPaymentPlanData.data && adjustPaymentPlanData.data.length && isUsePaymentPlan) {
      paymentPlanCount = Array.from(new Set(adjustPaymentPlanData.config.map(i => i.sourceId))).length
    }

    const _data = uniqBy(backlog || [], 'flowId.id')
    const countText =
      repay || isPayPlanRepay
        ? i18n.getHTML(flowId.payPlan || isPayPlanRepay ? 'selected-payPlan' : 'selected-feeType', {
            count: isPayPlanRepay
              ? multiRepay
                ? multiRepayPlan.length
                : 1
              : flowId?.payPlan
              ? flowId?.payPlan?.length
              : get(flowId, 'details.length', 1),
          })
        : i18n.getHTML('selected-document', { count: paymentPlanCount || _data.length || 1 })

    const purposeList = channelMap.needPurpose ? CHANPAY_PURPOSE() : []
    const isPurposeRequired = channelMap.needPurpose || isAdvancedOptions
    const purposeText = isAdvancedOptions ? i18n.get('代发类型') : i18n.get('支付用途')
    const selectedAccountId = selectedAccount ? selectedAccount.id : ''
    const orderTypeLabel = <OrderTypeLabel />
    const phoneNumber = userInfo?.staff?.cellphone || ''
    const shouldCheckPayMethodAvailable = isShouldCheckPayMethodAvailable(channel)

    const currency = api.getState('@common').powers.Currency
    const currencyOffLine = selectedAccount?.recevingCurrencyEnable && currency && channel === 'OFFLINE'
    const isCMBCBS8OVS = channel === 'CMBCBS8OVS'
    const showPaymentPlanButton = !repay && !isPayPlanRepay && isUsePaymentPlan && !currencyOffLine && !isCMBCBS8OVS
    const isNew = getBoolVariation('select_pay_account_new')
    const AccountCard = isNew ? PayAccountCard : AccountListItem
    return (
      <div className="select-payment-modal">
        <div className="content">
          <div className="amount-conten">
            <div className="amount-header">
              <div className="left">
                {countText}
                <span className="info-icon">
                  <Tooltip
                    placement="top"
                    title={i18n.get(
                      '如果想要用原币支付，请调整支付计划，选择同币种的支付计划进行支付，其余的可以撤回等待后续支付',
                    )}
                  >
                    <OutlinedTipsInfo />
                  </Tooltip>
                </span>
              </div>
              <div className="right">{this.renderCurrencySelect()}</div>
            </div>
            <div className="wrap">
              <div className="left">{i18n.get('支付金额')}</div>
              <div className="right">
                <div className="amount-total">
                  {i18n.get('合计总额')}
                  <span>{`${amountMap?.strCode} ${amount}`}</span>
                </div>
                {amountMap?.key !== 'standard' && (
                  <div className="amount-convert">
                    {i18n.get('折合')}
                    <span>{`${standardMap?.strCode} ${standardMap?.standard}`}</span>
                  </div>
                )}
                {amountMap?.strCode === 'CNY' && (
                  <div className="zh-money">{`${MoneyNzh.toMoney(amount, { outSymbol: false })}`}</div>
                )}
              </div>
            </div>
          </div>
          <div className="line-wrap" />
          <div className="account-wrap">
            <div className="account-header">
              <span className="account-header-title">{i18n.get('付款账户')}</span>
              {selectedAccount && (
                <span className="account-header-btn" onClick={() => this.onClickEditBtn(true)} data-testid="pay-selectPaymethodModal-editAccount-btn">
              {i18n.get('修改')}
            </span>
              )}
            </div>
            {selectedAccount && (
              <AccountCard
                isCNY={this.state.isCNY}
                onChannelChange={this.onChannelChange}
                channel={channel}
                onClick={() => this.onClickEditBtn(true)}
                formChannel="paymentConfirmation"
                dynamicChannels={dynamicChannels}
                inSelectPayMethodModal
                data={selectedAccount}
                channelList={channelList}
                dynamicChannelMap={dynamicChannelMap}
                selectDisabled={loading}
              />
            )}
            {!selectedAccount && (
              <div className="warn-text">
                <p className="warn-text-reminder">
                  {i18n.get('暂无可使用的付款账户，请检查法人实体相关配置后重新发起支付')}
                </p>
                <p className="warn-text-tip">
                  {i18n.get('付款账户使用限制配置路径：法人实体-可用支付账户、付款账户可见性设置')}
                </p>
              </div>
            )}
          </div>
          <Form className="select-payment-form">
            {this.renderCurrency(channelMap)}
            {isAdvancedOptions && (
              <FormItem label={purposeText}>
                {getFieldDecorator('purpose', {
                  rules: [
                    {
                      required: isPurposeRequired,
                      message: `${purposeText}${i18n.get('不能为空')}`,
                    },
                  ],
                })(
                  <Select className="method-line" placeholder={`${i18n.get('请选择')}${purposeText}`}>
                    {purposeList.map(v => (
                      <Option key={v.key}>{v.value}</Option>
                    ))}
                  </Select>,
                )}
                {
                  <span className="warn-text">
                    {i18n.get('根据人民银行要求，当「对公账户」向「个人账户」付款超5万元时，上述“代发类型”必选。')}
                  </span>
                }
              </FormItem>
            )}
            {isShowConfig && this.renderOrderType(channelMap, orderTypeLabel, payeeBankNames)}
            <AbstractPay
              form={form}
              selectedAccount={selectedAccount}
              dynamicChannelMap={dynamicChannelMap}
              channel={channel}
              isAutoSummary={isAutoSummary}
              isAutoCutRemark={isAutoCutRemark}
              onChangeAutoSummary={this.handleRemarkChange}
              getPayerInfoConfig={this.handlePayerInfoConfig}
              onAutoCutRemarkChange={this.handleAutoCutRemarkChange}
            />
            {(repay || isPayPlanRepay) && (
              <FormItem label={i18n.get('支付原因')}>
                {getFieldDecorator('repayReason', {
                  rules: [
                    { max: 28, message: i18n.get('字符长度不能超过28个') },
                    { required: true, whitespace: true, message: i18n.get('请填写支付原因') },
                  ],
                })(<TextArea placeholder={i18n.get('请填写支付原因，不超过28个字符')} />)}
              </FormItem>
            )}
          </Form>
          <div className="line-wrap" />
          <div className="approve-signature">
            <ApproveSignature mustBeUsedSignature={mustBeUsedSignature} bus={this.bus} />
          </div>
          <div className="line-wrap" />
          {shouldCheckPayMethodAvailable && (
            <>
              <CheckPayMethodStatusBar {...payMethodState} onReCheck={this.checkPayMethodAvailable} />
              <div className="line-wrap" />
            </>
          )}
        </div>
        <div className="select-payment-modal-footer">
          <div>
            {showPaymentPlanButton && (
              <UniversalComponent uniqueKey={`${Universal_Unique_Key}.adjustPayPlan`}>
                <Button
                  category="secondary"
                  icon={<OutlinedEditEdit />}
                  onClick={this.adjustPaymentPlan}
                  disabled={!this.state.accountId}
                  data-testid="pay-selectPaymethodModal-adjustPlan-btn"
                >
                  {i18n.get('调整支付计划')}
                </Button>
              </UniversalComponent>
            )}
          </div>
          <div>
            <Button category="secondary" className="mr-8" onClick={this.handleModalClose.bind(this)} data-testid="pay-selectPaymethodModal-cancel-btn">
              {i18n.get('取消')}
            </Button>

            {!payMethodState.available && (
              <Tooltip title="支付通路连接失败">
                <Button
                  category="primary"
                  loading={loading}
                  onClick={this.prepareSaveModal}
                  disabled={!payMethodState.available}
                  data-testid="pay-selectPaymethodModal-confirm-btn"
                >
                  {i18n.get('确定')}
                </Button>
              </Tooltip>
            )}
            {payMethodState.available && (
              <Button
                category="primary"
                loading={loading}
                onClick={this.prepareSaveModal}
                disabled={!payMethodState.available}
                data-testid="pay-selectPaymethodModal-confirm-btn"
              >
                {i18n.get('确定')}
              </Button>
            )}

            <CaptchModal
              ref={this.modalRef}
              getCaptcha={getPaymentsCaptcha}
              successCb={this.captchaSuccess}
              phoneNumber={phoneNumber}
            />
          </div>
        </div>
      </div>
    )
  }
}
