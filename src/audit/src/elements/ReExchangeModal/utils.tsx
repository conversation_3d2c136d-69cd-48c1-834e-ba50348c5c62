/**************************************
 * Created By LinK On 2022/1/21 16:11.
 **************************************/
import * as viewUtil from '../../view-util'
import React from 'react'
import moment from 'moment'

export const getThirdFloorColumns = () => [
  {
    title: '回单编号',
    dataIndex: 'form.E_system_回单编号',
    sorter: false,
    width: 210
  },
  {
    title: '付款账号',
    dataIndex: 'form.E_system_本方账号',
    sorter: false,
    width: 200
  },
  {
    title: '付款账户名称',
    dataIndex: 'form.E_system_本方账户名称',
    sorter: false,
    width: 150
  },
  {
    title: '收款金额',
    dataIndex: 'form.E_system_支付金额',
    sorter: false,
    width: 150,
    render: viewUtil.tdAmount
  },
  {
    title: '交易日期',
    sorter: false,
    dataIndex: 'form.E_system_交易日期',
    width: 150,
    // eslint-disable-next-line react/display-name
    render: (text: any, record: any, index: number) => {
      return (
        <span>{moment(record.form['E_system_交易日期'])?.format('YYYY-MM-DD')}</span>
      )
    }
  },
  {
    title: '收款摘要',
    sorter: false,
    dataIndex: 'form.E_system_摘要',
    width: 200
  }
]

export const getSecondFloorDefaultColumns = () => [
  {
    title: i18n.get('批次号'),
    sorter: false,
    dataIndex: 'tradeNo',
    width: 220
  },
  {
    title: i18n.get('支付方式'),
    sorter: false,
    dataIndex: 'paymentChannel',
    width: 150,
    render: viewUtil.tdChannelType
  },
  {
    title: i18n.get('支付时间'),
    sorter: false,
    dataIndex: 'finishTime',
    width: 160
  }
]

export const getAutoMatchSecondFloorColumns = () => [
  // @ts-ignore
  ...getSecondFloorDefaultColumns(),
  {
    title: i18n.get('收款账号'),
    sorter: false,
    dataIndex: 'accountNo',
    width: 200
  },
  {
    title: i18n.get('收款人姓名'),
    sorter: false,
    dataIndex: 'accountName',
    width: 120
  }
]

export const getFirstFloorColumns = () => [
  {
    title: i18n.get('支付计划编号'),
    sorter: false,
    dataIndex: 'form.E_system_paymentPlan_编号',
    width: 210
  },
  {
    title: i18n.get('支付金额币种'),
    sorter: false,
    dataIndex: 'form.E_system_paymentPlan_币种',
    width: 140
  },
  {
    title: i18n.get('支付金额'),
    sorter: false,
    dataIndex: 'form.E_system_paymentPlan_支付金额',
    width: 120,
    render: viewUtil.tdAmount
  },
  {
    title: i18n.get('付款账号类别'),
    sorter: false,
    dataIndex: 'form.E_system_paymentPlan_付款账号类别',
    width: 140,
    render: viewUtil.tdSort
  },
  {
    title: i18n.get('支付概要'),
    sorter: false,
    dataIndex: 'form.E_system_paymentPlan_支付概要',
    render: (text: any, record: any) =>
      <span>{record.form.E_system_paymentPlan_支付概要 || '-'}</span>
  }
]