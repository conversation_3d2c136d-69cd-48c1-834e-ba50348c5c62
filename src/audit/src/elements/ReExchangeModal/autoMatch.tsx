/*
 * @Author: chenjungang
 * @Date: 2021-11-23 17:34:26
 * @Last Modified by: chenjungang
 * @Last Modified time: 2021-12-20 14:42:36
 * @desc 退汇管理弹窗
 */

import React, { Component } from 'react'
import { Input, Table } from 'antd'
import moment from 'moment'
import { showMessage, showModal } from '@ekuaibao/show-util'
import * as DataGrid from '@ekuaibao/datagrid'
import { getReceiptReexchange } from '../../audit-action'
import { bindPaymentPlan, unbindPaymentPlan } from '../../util/fetchUtil'
import { getV } from '@ekuaibao/lib/lib/help'
import { PaginationConfig } from '@ekuaibao/datagrid/esm/types/pagination'

import {
  getAutoMatchSecondFloorColumns,
  getFirstFloorColumns,
  getThirdFloorColumns
} from './utils'

const { Search } = Input

interface IProps {
  layer?: any
  byStaff?: boolean
}

interface IState {
  columnsData: any[]
  dataSource: any[]
  total: number
  searchValue: string
  pagination: {
    current: number
    size: number
  }
}

// 滚动模式下固定 pageSize
const SCROLL_PAGE_SIZE = 20
export default class AutoMatch extends Component<IProps, IState> {
  private initialPagination: PaginationConfig = { current: 1, size: 10 }

  state = {
    columnsData: getFirstFloorColumns(),
    dataSource: [],
    total: 0,
    pagination: this.initialPagination,
    searchValue: '',
  }

  componentDidMount() {
    this.initialTable()
  }

  handlePaginationChange = (pagination: PaginationConfig) => {
    this.setState({ pagination}, async () => {
      const { dataSource, total } = await this.fnFetch()
      this.setState({ dataSource, total })
    })
  }

  getActionParamsFromState = () => {
    const { pagination } = this.state
    const { byStaff } = this.props
    const current = pagination.current
    const size = pagination.size
    return {
      start: (current - 1) * size,
      count: size,
      byStaff
    }
  }

  fnFetch = async () => {
    const { searchValue } = this.state
    const params = this.getActionParamsFromState()
    const result: any = await getReceiptReexchange({ params, searchVal: searchValue })
    const data = getV(result, 'items.data', [])
    const total: number = getV(result, 'items.total', 0)
    const dataSource: any = data.map((item: any) => ({ key: item.id, ...item }))
    return { dataSource, total }
  }

  initialTable = async () => {
    const result = await this.fnFetch()
    this.setState(result)
  }

  confirmBind = (record: any, row: any) => {
    bindPaymentPlan({
      receiptId: record.id,
      paymentPlanIds: [row.paymentId],
    })
      .then(() => {
        showMessage.success('绑定成功！')
        this.initialTable()
      })
      .catch(err => {
        showModal.info({
          content: err.errorMessage,
        })
      })
  }

  cancelBind = (record: any) => {
    showModal.confirm({
      content: i18n.get('确定要解除回单与支付计划的绑定关系吗？'),
      okText: i18n.get('解绑'),
      cancelText: i18n.get('取消'),
      onOk: () => {
        unbindPaymentPlan(record.id).then(res => {
          showMessage.success('解绑成功！')
          this.initialTable()
        })
      },
    })
  }

  handleSearch = (searchValue: string) => {
    const size = getV(this.state, 'pagination.size', 10)
    this.setState({ searchValue, pagination: { current: 1, size } }, () => this.initialTable())
  }

  renderThirdFloorTable = (row: any) => {
    row = row.data
    const receiptList = getV(row, 'receiptList', [])
    if (!receiptList.length) return i18n.get('此支付计划没有退汇回单')
    const isCurrentTrde = row.currentTradeNo === row.tradeNo
    const supportCancel = row.paymentState === 'REEXCHANGE' && isCurrentTrde
    const bindedData = row.bindCode
    const dataSource: any = receiptList.map((item: any, index: number) => {
      const isBind = bindedData ? bindedData.indexOf(item.code) > -1 : false
      return {
        key: index + 1,
        isBind,
        supportCancel,
        haveBind: row.haveBind,
        ...item,
      }
    })

    const receiptRowColumns = getThirdFloorColumns()
    receiptRowColumns.push({
      title: '操作',
      sorter: false,
      fixed: 'right',
      width: 130,
      render: (_: any, record: any) => {
        const cancelDiv = record.supportCancel
          ? record.isBind
            ? <a style={{ color: 'red' }} onClick={() => this.cancelBind(record)}>{i18n.get('取消')}</a>
            : <span></span>
          : <span style={{ color: '#bfbfbf' }}>{i18n.get('取消')}</span>
        return (
          <>
            {isCurrentTrde && row.paymentState === 'SUCCESS' ? (
              <a
                style={{ marginRight: '15px', display: 'inline-block' }}
                onClick={() => this.confirmBind(record, row)}>
                {i18n.get('确认')}
              </a>
            ) : (
              <span style={{ marginRight: '15px', display: 'inline-block', color: '#bfbfbf' }}>
                {i18n.get('确认')}
              </span>
            )}
            {cancelDiv}
          </>
        )
      },
    })

    return (
      <div>
        <Table
          bordered={true}
          columns={receiptRowColumns}
          dataSource={dataSource}
          pagination={false}
          scroll={{ x: 980 }}
        />
      </div>
    )
  }

  renderSecondFloorTable = (row: any) => {
    // 数据整理
    const paymentId = getV(row, 'data.id')
    const currentTradeNo = getV(row, 'data.form.E_system_paymentPlan_支付批次号', '')
    const paymentState = getV(row, 'data.form.E_system_paymentPlan_支付状态', '')
    const receiptData = getV(row, 'data.receiptData', [])
    const bindCode = getV(row, 'data.form.E_system_paymentPlan_退汇回单编号', '')
    const arr = receiptData.map((receipt: any) => {
      const tradeNo = getV(receipt, 'channelTradeNo')
      const paymentChannel = getV(receipt, 'paymentChannel')
      const firstReceipt = getV(receipt, 'receiptList[0]', {})
      const accountNo = getV(firstReceipt, 'form.E_system_本方账号')
      const accountName = getV(firstReceipt, 'form.E_system_本方账户名称')
      const finishTime = getV(receipt, 'finishTime')
      const finishTimeMoment = moment(Number(finishTime))?.format('YYYY-MM-DD')
      const receiptList = getV(receipt, 'receiptList', [])
      let haveBind = false
      receiptList.forEach((el: any) => {
        if (!haveBind && bindCode.indexOf(el.code) > -1) {
          haveBind = true
        }
      })
      return {
        tradeNo,
        paymentId,
        finishTime: finishTimeMoment,
        bindCode,
        haveBind,
        currentTradeNo,
        paymentState,
        paymentChannel,
        accountNo,
        accountName,
        receiptList,
      }
    })
    const rowColumns: any = getAutoMatchSecondFloorColumns()
    return (
      <div>
        <DataGrid.TableWrapper
          rowKey="tradeNo"
          columns={rowColumns}
          dataSource={arr}
          isSingleSelect={false}
          isMultiSelect={false}
          columnMinWidth={120}
          allowColumnResizing
          RenderDetailTemplate={this.renderThirdFloorTable}
        />
      </div>
    )
  }

  render() {
    const { columnsData, dataSource, total, pagination } = this.state

    return (
      <div className="match-content">
        <Search
          className="match-search"
          onSearch={this.handleSearch}
          placeholder="请搜索支付计划编号、回单编号"
        />
        <div className="match-table">
          <DataGrid.TableWrapper
            className="reExchangeModal-wrapper"
            rowKey="id"
            scrolling={{
              mode: 'virtual',
            }}
            standard
            allowColumnReordering
            allowColumnResizing
            isSingleSelect={false}
            isMultiSelect={false}
            columns={columnsData}
            RenderDetailTemplate={this.renderSecondFloorTable}
            dataSource={dataSource}
            pageSize={pagination.size}
          />
        </div>
        <div className="reexchange-pagination">
          <DataGrid.Pagination
            totalLength={total}
            pagination={pagination}
            scrollPagination={{ size: SCROLL_PAGE_SIZE, current: pagination.current }}
            onChange={this.handlePaginationChange}
            pageMode="pagination"
            disabledScroll
          />
        </div>
      </div>
    )
  }
}
