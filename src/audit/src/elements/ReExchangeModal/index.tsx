import { Tabs } from 'antd'
import React, { Component } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import AutoMatch from './autoMatch'
import HandMatch from './handMatch'
import styles from './ReExchangeModal.module.less'
const { TabPane } = Tabs

interface IProps {
  layer?: any
}
@EnhanceModal({
  title: i18n.get('退汇管理'),
  footer: false,
  className: styles['reexchange-wrap']
})
export default class ReExchangeModal extends Component<IProps> {
  render() {
    return (
      <div>
        <Tabs defaultActiveKey="1">
          <TabPane tab="自动匹配退汇" key="1">
            <AutoMatch {...this.props}/>
          </TabPane>
          <TabPane tab="手动匹配退汇" key="2">
            <HandMatch {...this.props}/>
          </TabPane>
        </Tabs>
      </div>
    )
  }
}
