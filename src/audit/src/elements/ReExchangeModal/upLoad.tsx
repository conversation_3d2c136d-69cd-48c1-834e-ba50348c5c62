import React, { Component } from 'react'
import { Form } from 'antd'
import { Button } from '@hose/eui'
import { bindFile } from '../../audit-action'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import styles from './ReExchangeModal.module.less'
import { Dynamic } from '@ekuaibao/template'
import { app as api } from '@ekuaibao/whispered'
import MessageCenter from '@ekuaibao/messagecenter'
import { showMessage } from '@ekuaibao/show-util'
const { editable } = api.require('@components/index.editable')
interface IProps {
  layer?: any
  id: string
  searchHandMatch: any
}

interface IState {
  template: any[]
}

@EnhanceModal({
  title: i18n.get('上传退汇回单文件'),
  footer: false,
  className: styles['upload-modal'],
})
export default class PayPlanModal extends Component<IProps, IState> {
  private bus: any = new MessageCenter()
  constructor(props: any) {
    super(props)
    this.state = {
      template: [
        {
          name: 'attachments',
          label: i18n.get('退汇凭证'),
          type: 'attachments',
          dataType: { type: 'list', elemType: { type: 'attachment' } },
          optional: false,
          useSelfConfig: true, // 是否使用自己template中的配置
          btnText: i18n.get('上传退汇凭证'),
          tooltipmsg: i18n.get('仅支持上传「jpg,jpeg,bmp,pdf」格式文件'),
        },
        {
          name: 'remark',
          editable: true,
          label: i18n.get('备注'),
          type: 'textarea',
          dataType: { type: 'text' },
          placeholder: i18n.get('添加备注（最多50个汉字）'),
          optional: true,
          maxLength: 50,
        },
      ],
    }
  }

  handleSave = () => {
    this.bus.getValueWithValidate().then((data: any) => {
      const { attachments, remark } = data
      if (attachments && attachments.length) {
        if (attachments.length === 1) {
          const name = attachments[0].fileName
          const fileKey = attachments[0].key
          const fileType = fileKey.split('.').pop()
          const fileTypes = ['jpg', 'jpeg', 'bmp', 'pdf']
          if (fileTypes.includes(fileType)) {
            const { id, searchHandMatch } = this.props
            bindFile(id, { name, remark, fileKey })
              .then((res: any) => {
                if (res) {
                  this.handleCancel()
                  searchHandMatch()
                }
              })
              .catch(e => {
                showMessage.error(e)
              })
          } else {
            showMessage.warning(i18n.get('仅支持上传「jpg,jpeg,bmp,pdf」格式文件'))
          }
        } else {
          showMessage.warning(i18n.get('仅支持上传一个文件'))
        }
      }
    })
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  render() {
    const { template } = this.state
    return (
      <>
        <Dynamic
          bus={this.bus}
          create={T => Form.create()(T)}
          template={template}
          value={{}}
          elements={editable as any[]}
        />
        <div className="footer-wrapper">
          <Button  style={{ marginRight: '8px' }} onClick={() => this.handleSave()}>
            {i18n.get('确定')}
          </Button>
          <Button category="secondary" onClick={() => this.handleCancel()}>{i18n.get('取消')}</Button>
        </div>
      </>
    )
  }
}
