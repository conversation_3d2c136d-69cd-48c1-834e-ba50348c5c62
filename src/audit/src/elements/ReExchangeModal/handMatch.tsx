import React, { Component } from 'react'
import { Table  } from 'antd'
import { Button, Input } from '@hose/eui'
import { showModal } from '@ekuaibao/show-util'
import * as DataGrid from '@ekuaibao/datagrid'
import { getReceiptReexchange, removeManual, unbindFile } from '../../audit-action'
import { app as api } from '@ekuaibao/whispered'
import { PaginationConfig } from '@ekuaibao/datagrid/esm/types/pagination'
import { get } from 'lodash'
import {
  getFirstFloorColumns,
  getAutoMatchSecondFloorColumns,
} from './utils'
import { getV } from '@ekuaibao/lib/lib/help'
import moment from 'moment'
const { Search } = Input;

interface IProps {
  layer?: any
  byStaff?: boolean
}

interface IState {
  columnsData: any[]
  dataSource: any[]
  total: number
  pagination: {
    current: number
    size: number
  }
  searchValue: string
}

// 滚动模式下固定 pageSize
const SCROLL_PAGE_SIZE = 20
export default class HandMatch extends Component<IProps, IState> {
  private initialPagination: PaginationConfig = { current: 1, size: 10 }

  state = {
    columnsData: getFirstFloorColumns(),
    dataSource: [],
    total: 0,
    pagination: this.initialPagination,
    searchValue: '',
  }

  componentDidMount() {
    this.initialTable()
  }

  handlePaginationChange = (pagination: PaginationConfig) => {
    this.setState({ pagination}, async () => {
      const { dataSource, total } = await this.fnFetch()
      this.setState({ dataSource, total })
    })
  }

  getActionParamsFromState = () => {
    const { pagination } = this.state
    const { byStaff } = this.props
    const current = pagination.current
    const size = pagination.size
    return {
      start: (current - 1) * size,
      count: size,
      byStaff
    }
  }

  fnFetch = async () => {
    const { searchValue } = this.state
    const params = this.getActionParamsFromState()
    const result: any = await getReceiptReexchange({ params, searchVal: searchValue, manualMatch: true })
    const data = getV(result, 'items.data', [])
    const total: number = getV(result, 'items.total', 0)
    const dataSource: any = data.map((item: any) => ({ key: item.id, ...item }))
    return { dataSource, total }
  }

  initialTable = async () => {
    const result = await this.fnFetch()
    this.setState(result)
  }

  handleSearch = (searchValue: string) => {
    const size = getV(this.state, 'pagination.size', 10)
    this.setState({ searchValue, pagination: { current: 1, size } }, () => this.initialTable())
  }

  renderThirdFloorTable = (row: any) => {
    row = row.data
    const receiptList = getV(row, 'receiptList', [])
    if (!receiptList.length) return i18n.get('此支付计划没有退汇回单')
    const supportCancel = row.paymentState === 'REEXCHANGE' && row.currentTradeNo === row.tradeNo
    const bindedData = row.bindCode
    const paymentId = row.paymentId
    const dataSource: any = receiptList.map((item: any, index: number) => {
      const isBind = bindedData ? bindedData.indexOf(item.code) > -1 : false
      return {
        key: index + 1,
        isBind,
        supportCancel,
        haveBind: row.haveBind,
        ...item,
      }
    })

    const rowColumns: any = [
      {
        title: i18n.get('序号'),
        width: 60,
        render: (text: any, record: any, index: number) => <span>{index + 1}</span>
      },
      {
        title: i18n.get('文件名'),
        dataIndex: 'form.E_system_电子回单_name',
        width: 270,
      },
      { title: i18n.get('备注'),
        dataIndex: 'form.E_system_摘要',
        width: 180,
      },
      {
        title: i18n.get('操作'),
        width: 90,
        render: (text: any, record: any, index: number) => {
          return (
            <div className='action'>
              {supportCancel
                ? <a onClick={() => this.remove(record, paymentId)}>{i18n.get('删除')}</a>
                : <span>{i18n.get('删除')}</span>}
            </div>
          )
        }
      }
    ]

    return (
      <div>
        <Table
          bordered={true}
          columns={rowColumns}
          dataSource={dataSource}
          pagination={false}
          scroll={{ x: 980 }}
        />
      </div>
    )
  }

  renderSecondFloorTable = (row: any) => {
    // 数据整理
    const paymentId = getV(row, 'data.id')
    const currentTradeNo = getV(row, 'data.form.E_system_paymentPlan_支付批次号', '')
    const paymentState = getV(row, 'data.form.E_system_paymentPlan_支付状态', '')
    const receiptData = getV(row, 'data.receiptData', [])
    const bindCode = getV(row, 'data.form.E_system_paymentPlan_退汇回单编号', '')
    const arr = receiptData.map((receipt: any) => {
      const tradeNo = getV(receipt, 'channelTradeNo')
      const paymentChannel = getV(receipt, 'paymentChannel')
      const firstReceipt = getV(receipt, 'receiptList[0]', {})
      const accountNo = getV(firstReceipt, 'form.E_system_本方账号')
      const accountName = getV(firstReceipt, 'form.E_system_本方账户名称')
      const finishTime = getV(receipt, 'finishTime')
      const finishTimeMoment = moment(Number(finishTime))?.format('YYYY-MM-DD')
      const receiptList = getV(receipt, 'receiptList', [])
      let haveBind = false
      receiptList.forEach((el: any) => {
        if (!haveBind && bindCode.indexOf(el.code) > -1) {
          haveBind = true
        }
      })
      const visibleList = receiptList.filter((el: any) => getV(el, 'form.E_system_fileKey'))
      return {
        tradeNo,
        paymentId,
        finishTime: finishTimeMoment,
        bindCode,
        haveBind,
        currentTradeNo,
        paymentState,
        paymentChannel,
        accountNo,
        accountName,
        receiptList: visibleList,
      }
    })
    const rowColumns: any[] = getAutoMatchSecondFloorColumns()
    rowColumns.push({
      title: '操作',
      width: 200,
      render: (_: any, record: any) => {
        const receiptList = get(record, 'receiptList', [])
        if (receiptList.length) {
          return (<div className="action">
              <span>{i18n.get('上传退汇凭证')}</span>
              <span>{i18n.get('删除')}</span>
            </div>
          )
        }
        return (
          <div className="action">
            {record.paymentState === 'SUCCESS'
              ? <a onClick={() => this.uploadCert(record)}>{i18n.get('上传退汇凭证')}</a>
              : <span>{i18n.get('上传退汇凭证')}</span>
            }
            <a onClick={() => this.removePayPlan(record)}>{i18n.get('删除')}</a>
          </div>
        )
      }
    })
    return (
      <div>
        <DataGrid.TableWrapper
          rowKey="tradeNo"
          columns={rowColumns}
          dataSource={arr}
          isSingleSelect={false}
          isMultiSelect={false}
          columnMinWidth={120}
          allowColumnResizing
          RenderDetailTemplate={this.renderThirdFloorTable}
        />
      </div>
    )
  }

  uploadCert = (record: any) => {
    api.open('@audit:upLoadFiles', { id: record?.paymentId, searchHandMatch: this.initialTable })
  }
  removePayPlan = (record: any) => {
    const id = get(record, 'paymentId')
    if (id) {
      showModal.confirm({
        content: i18n.get('确定要删除此支付计划吗？'),
        okText: i18n.get('确定'),
        cancelText: i18n.get('取消'),
        onOk: () => {
          removeManual(id)
            .then((res: any) => {
              if (res) {
                this.initialTable()
              }
            })
            .catch(error => {})
        }
      })
    }
  }
  addPayPlan = () => {
    const { byStaff } = this.props
    api.open('@audit:PayPlanModal', { searchHandMatch: this.initialTable, byStaff })
  }
  remove = (_: any, id: string) => {
    unbindFile(id)
      .then((res: any) => {
        if (res) {
          this.initialTable()
        }
      })
      .catch(error => {})
  }

  render() {
    const { columnsData, dataSource, total, pagination } = this.state

    return (
      <div className="match-content">
        <div className='addPayPlan'>
          <Search
            className="match-search"
            onSearch={this.handleSearch}
            placeholder="请搜索支付计划编号" />
          <Button className='addbtn' onClick={this.addPayPlan}>{i18n.get('添加已退汇支付计划')}</Button>
        </div>
        <div className="match-table">
          <DataGrid.TableWrapper
            className="reExchangeModal-wrapper"
            rowKey="id"
            scrolling={{
              mode: 'virtual',
            }}
            standard
            allowColumnReordering
            allowColumnResizing
            isSingleSelect={false}
            isMultiSelect={false}
            columns={columnsData}
            RenderDetailTemplate={this.renderSecondFloorTable}
            dataSource={dataSource}
            pageSize={pagination.size}
          />
        </div>
        <div className="reexchange-pagination">
          <DataGrid.Pagination
            totalLength={total}
            pagination={pagination}
            scrollPagination={{ size: SCROLL_PAGE_SIZE, current: pagination.current }}
            onChange={this.handlePaginationChange}
            pageMode="pagination"
            disabledScroll
          />
        </div>
      </div>
    )
  }
}
