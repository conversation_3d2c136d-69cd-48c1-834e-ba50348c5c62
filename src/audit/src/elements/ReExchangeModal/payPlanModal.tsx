import React, { Component } from 'react'
import { Table } from 'antd'
import { Input, Button } from '@hose/eui'
import * as viewUtil from '../../view-util'
import { getPaymentPlans, savePaymentPlans } from '../../audit-action'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import styles from './ReExchangeModal.module.less'
import { getV } from '@ekuaibao/lib/lib/help'
const { Search } = Input
const PAGESIZE = 20
interface IProps {
  layer?: any
  byStaff?: boolean
  searchHandMatch: any
}

interface IState {
  columnsData: any[]
  dataSource: any[]
  selectedRowKeys: any[]
  total: number
}

@EnhanceModal({
  title: i18n.get('请选择支付计划'),
  footer: false,
  className: styles['payplan-modal'],
})
export default class PayPlanModal extends Component<IProps, IState> {
  state = {
    columnsData: [
      {
        title: i18n.get('支付计划编号'),
        dataIndex: 'dataLink.E_system_paymentPlan_编号',
        width: 150,
      },
      {
        title: i18n.get('批次号'),
        dataIndex: 'dataLink.E_system_paymentPlan_支付批次号',
        width: 160,
      },
      {
        title: i18n.get('收款信息'),
        dataIndex: 'dataLink.E_system_paymentPlan_收款信息',
        width: 120,
        render: viewUtil.accountInfo,
      },
      {
        title: i18n.get('支付方式'),
        dataIndex: 'dataLink.E_system_paymentPlan_支付方式',
        width: 120,
        render: viewUtil.tdChannelType,
      },
      {
        title: i18n.get('支付币种'),
        dataIndex: 'dataLink.E_system_paymentPlan_币种',
        width: 120,
      },
      {
        title: i18n.get('支付金额'),
        dataIndex: 'dataLink.E_system_paymentPlan_支付金额',
        width: 120,
        render: viewUtil.tdAmount,
      },
      {
        title: i18n.get('支付时间'),
        width: 150,
        dataIndex: 'dataLink.E_system_paymentPlan_支付完成日期',
        render: viewUtil.tdDateTime,
      },
      {
        title: i18n.get('付款账号类别'),
        dataIndex: 'dataLink.E_system_paymentPlan_付款账号类别',
        width: 120,
      },
      {
        title: i18n.get('支付概要'),
        width: 200,
        dataIndex: 'dataLink.E_system_paymentPlan_支付概要',
      },
    ],
    searchVal: '',
    dataSource: [],
    selectedRowKeys: [],
    total: 0,
  }

  componentDidMount() {
    this.initialTable()
  }

  initialTable = async (searchVal: string = '', page: number = 1) => {
    const { byStaff } = this.props
    let params = {
      limit: {
        start: (page - 1) * PAGESIZE,
        count: PAGESIZE,
      },
      byStaff,
    }
    const result: any = await getPaymentPlans(params, searchVal)
    const dataSource: any = result?.value?.viewDataList?.data || []
    const total: any = result?.value?.viewDataList?.total || 0
    this.setState({ dataSource, total, selectedRowKeys: [], searchVal })
  }

  onSelectChange = (selectedRowKeys: any) => {
    this.setState({ selectedRowKeys })
  }

  onChangePage = (page: number) => {
    this.initialTable(this.state.searchVal, page)
  }

  handleSave = () => {
    const { selectedRowKeys } = this.state
    savePaymentPlans(selectedRowKeys)
      .then((res: any) => {
        if (res) {
          this.props.layer.emitCancel()
          this.props.searchHandMatch()
        }
      })
      .catch(error => {})
  }

  render() {
    const { columnsData, dataSource, selectedRowKeys, total } = this.state
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange,
      getCheckboxProps: (record: any) => ({
        disabled: getV(record, 'dataLink.E_system_paymentPlan_手动匹配', false),
      }),
    }

    return (
      <>
        <div className="payPlanSearch">
          <div className="lineH_30">支付计划列表</div>
          <Search
            onSearch={value => this.initialTable(value)}
            style={{ width: '300px' }}
            placeholder={i18n.get('搜索编号、支付概要或提交人')}
          />
        </div>
        <Table
          pagination={{
            pageSize: PAGESIZE,
            total,
            onChange: this.onChangePage,
          }}
          rowKey={(record: any) => record.dataLink.id}
          rowSelection={rowSelection}
          columns={columnsData}
          dataSource={dataSource}
          scroll={{ x: 1000, y: 500 }}
        />
        <div className="footer-wrapper">
          <Button
            disabled={!selectedRowKeys.length}
            onClick={() => this.handleSave()}>
            {i18n.get('确定')}
          </Button>
          <div className="total">
            {i18n.get('selected-total', { selected: selectedRowKeys.length, total: total })}
          </div>
        </div>
      </>
    )
  }
}
