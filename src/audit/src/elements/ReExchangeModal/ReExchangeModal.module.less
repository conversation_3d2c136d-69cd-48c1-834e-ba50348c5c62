@import '~@ekuaibao/eui-styles/less/token.less';

.reexchange-wrap {
  padding: @space-7;
  :global {
    .ant-modal-body {
      padding: 0 24px;
      min-height: 500px;
    }
    .reexchange-pagination {
      margin-top: @space-5;
    }
    .match-content {
      height: calc(100vh - 150px);
      .match-search {
        width: 300px;
        margin: @space-5 0;
      }
      .match-table {
        height: calc(100vh - 248px);
        overflow-y: auto;
        .reExchangeModal-wrapper{
          border-top: 1px solid #ddd;
          overflow: hidden;
          height: 100%;
        }
        .action {
          a {
            text-decoration: none;
            margin-right: @space-6;
          }
          span {
            margin-right: @space-6;
            color: @color-black-4;
          }
        }
      }
      .addPayPlan {
        display: flex;
        justify-content: space-between;
        .addbtn {
          margin: @space-5 0;
        }
      }
    }
  }
}

.payplan-modal {
  :global {
    .ant-modal-body {
      padding: 0 24px;
      min-height: 500px;
    }
    .payPlanSearch {
      display: flex;
      justify-content: space-between;
      margin: 10px 0;
    }
    .footer-wrapper {
      position: absolute;
      bottom: 20px;
    }
    .total {
      display: inline-block;
      margin-left: 10px;
    }
    .lineH_30 {
      line-height: 30px;
    }
  }
}