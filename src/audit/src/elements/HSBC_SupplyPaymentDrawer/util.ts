import { getV } from '@ekuaibao/lib/lib/help';
import { getDefaultColumns } from '../AdjustPaymentPlan/utils';
import { prepareRender } from '../../view-util';
import { parseFields } from '@ekuaibao/lib/lib/entityUtil/entityTableUtil';
import { cloneDeep } from 'lodash';
import { app as api } from '@ekuaibao/whispered';
import moment from 'moment';
import { uuid } from '@ekuaibao/helpers';
import { saveLogForHSBC, saveSupplyFileForHSBC } from '../../util/fetchUtil';
// @ts-ignore
const MoneyNzh = api.require('@elements/puppet/MoneyNzh');
// @ts-ignore
const { parseColumns } = api.require('@elements/DataLinkTable/tableUtil');

// 将国家数据map为国家名称
export const initialCountries = (countries: any[]) => {
  const arr = getV(countries, 'items', [])
  return arr.map((el: any) => ({
    valueKey: el.fullCname
  }))
};

// 初始化补充文件信息
export const initialFormData =
  ({
     data = [],
     paymentAccount,
     paymentCurrency,
     planData,
     ekbTradeNo,
   }: {
    data: any[],
    paymentAccount: any,
    paymentCurrency: any,
    planData: any,
    ekbTradeNo: string
  }) => {
  let obj: any = {};
  const allCurrencyRates = api.getState('@common.allCurrencyRates')
  const standardCurrency = api.getState('@common.standardCurrency')
  const allCurencys = [...allCurrencyRates, standardCurrency];

  const {
    accountNo: remitterAccountNo = '',
    accountName: remitterAccountName = '',
    extensions:paymentAccounExtensions,
  } = paymentAccount
  const {
    accountName: remitterAccountNameEn = '',
    countryStr: remitterAccountCountry = '',
    cityStr: remitterAccountCity = '',
    town: remitterAccountTown = '',
    street: remitterAccountStreet = '',
    zipCode: remitterAccountZipCode = '',
    shortCode: remitterAccountCountryEn = '',
    cityEnStr: remitterAccountCityEn = '',
    townEn: remitterAccountTownEn = '',
    streetEn: remitterAccountStreetEn = '',
    bankCountryStr: remitterBankCountry = '',
    bankCountry_shortCode: paymentBankCountry_shortCode = '',
  }: any = paymentAccounExtensions || {}
  // 付款银行所在国家（英文）
  const remitterBankCountryEn = paymentAccount.sort === 'OVERSEABANK' ? paymentBankCountry_shortCode : 'CN';

  data.forEach(el => {
    const id = getV(el, 'dataLink.id', '');
    const payeeAccount = getV(el,'dataLink.E_system_paymentPlan_收款信息',{})
    const paymentPlanRealPayStrCode = getV(el, 'dataLink.E_system_paymentPlan_realPayStrCode');
    const amountCurrency = (paymentPlanRealPayStrCode === 'USN' || paymentPlanRealPayStrCode === 'USS')
      ? 'USD'
      : paymentPlanRealPayStrCode;
    let amountInfoMoney = getV(el,'dataLink.E_system_paymentPlan_realPayMoney')
    if (amountInfoMoney) amountInfoMoney = String(amountInfoMoney);
    const paymentPlanCurrency = allCurencys.find(el => el.strCode === paymentPlanRealPayStrCode);
    const amountInfoCurrencyCapital = getV(paymentPlanCurrency, 'name')
    const { bopType, bopSupplyType, needInstructions }: any = getBopTypeInfo(el, paymentAccount, paymentCurrency);
    // 生成id
    const billNo = `${moment().format('YYYYMMDD')}${uuid(4)}`
    const amountInfoMoneyCapital = MoneyNzh.toMoney(amountInfoMoney, { outSymbol: false })

    const {
      cardNo: beneficiaryAccountNo = '',
      name: beneficiaryAccountName = '',
      extensions: payeeAccountExtensions,
      type: payeeAccountOwner = '',
    }: any = payeeAccount;
    const beneficiaryOwner = payeeAccountOwner === 'PUBLIC' ? '0' : '1'
    const {
      accountName: beneficiaryAccountNameEn = '',
      countryStr: beneficiaryAccountCountry = '',
      shortCode: beneficiaryAccountCountryEn = '',
      cityStr: beneficiaryAccountCity = '',
      cityEnStr: beneficiaryAccountCityEn = '',
      town: beneficiaryAccountTown = '',
      townEn: beneficiaryAccountTownEn = '',
      street: beneficiaryAccountStreet = '',
      streetEn: beneficiaryAccountStreetEn = '',
      zipCode: beneficiaryAccountZipCode = '',
      bicCode: beneficiaryBankBic = '',
      bankName: beneficiaryBankNameEn = '',
      bankCountry_shortCode: payeeBankCountry_shortCode = '',
      countryStr: beneficiaryResidentCountryName = '',
      numCode: beneficiaryResidentCountryCode = '',
    }: any = payeeAccountExtensions || {}

    // 收款银行所在国家（英文）
    const beneficiaryBankCountryEn = payeeAccount.sort === 'OVERSEABANK' ? payeeBankCountry_shortCode : 'CN';
    // 收款人开户行
    const beneficiaryBankName = payeeAccount.sort === 'OVERSEABANK' ? payeeAccount.bankName : payeeAccount.branch
    // 收款人国家/地区
    const payeeAddress = String(`${beneficiaryAccountCountry || ''} ${beneficiaryAccountCity || ''}`).trim();

    if (id) {
      obj[id] = {
        id: '', // 后端要求，用于占位
        payTradeNo: ekbTradeNo,
        payPlanNo: id,
        type: bopType,
        billNo,
        instruction: bopSupplyType,
        bopData: {
          date: moment(),
          'exchangeMethod': '',
          'priority': '',
          transactionProvider:'',
          paymentPurpose:'',
          amountCurrency,
          amountInfoMoney,
          amountInfoCurrencyCapital,
          amountInfoMoneyCapital,
          // 'amountInFxMoney': 200.00,
          // 'amountInFxAccountNo': '*********',
          // 'amountOfPurchaseMoney': 200.00,
          // 'amountOfPurchaseAccountNo': '*********',
          // 'amountofOthersMoney': 200.00,
          // 'amountofOthersAccountNo': '*********',
          beneficiaryOwner,
          remitterAccountNo,
          remitterAccountName,
          remitterAccountNameEn,
          remitterAccountCountry,
          remitterAccountCountryEn,
          remitterAccountCity,
          remitterAccountCityEn,
          remitterAccountTown,
          remitterAccountTownEn,
          remitterAccountStreet,
          remitterAccountStreetEn,
          remitterAccountZipCode,
          remitterBankCountry,
          remitterBankCountryEn,
          remitterType: '0',
          // 'remitterUnitCode': '',
          // 'remitterId': '220121197501151213',
          beneficiaryAccountNo,
          beneficiaryAccountName,
          beneficiaryAccountNameEn,
          beneficiaryAccountCountry,
          beneficiaryAccountCountryEn,
          beneficiaryAccountCity: beneficiaryAccountCity || '',
          beneficiaryAccountCityEn: beneficiaryAccountCityEn || '',
          beneficiaryAccountTown,
          beneficiaryAccountTownEn,
          beneficiaryAccountStreet,
          beneficiaryAccountStreetEn,
          beneficiaryAccountZipCode,
          beneficiaryBankBic,
          beneficiaryBankName: beneficiaryBankName || '',
          beneficiaryBankNameEn,
          beneficiaryBankCountryEn,
          // 'remittanceInformation': '',
          'bearExpenses': '',
          beneficiaryResidentCountryName,
          beneficiaryResidentCountryCode,
          'paymentUnderBondedGoodsType': '',
          // 'bopTransacCode': '8520',
          'transactionRemark': '',  // 后端要求必须出现的字段
          'transactionRemarkEn': '',  // 后端要求必须出现的字段
          'paymentUnderBondedGoods': '',
          // 'invoiceNo': '*********',
          'paymentNature': '',
          // 'connectionId': '123456',
          paymentTime: moment(),
        },
        'settlementInstructions': !needInstructions? {} : {
          // 'date': '2021年8月31日',
          // 'corporationName': '北京合思',
          // 'organizationCode': '123456',
          paymentAmountTotal: amountInfoMoney, // 付款金额合计
          // 'goodsTradeAmount': 200.00,
          // 'prepayment': 200.00,
          // 'proportionAmount': 20,
          // 'expectedDays': 10,
          // 'declaredCustomsCompany': '阿里巴巴',
          // 'declaredCustomsCompanyCode': '987645',
          'declaredCustomsCurrency': '',
          // 'declaredCustomsCommonAmount': 201,
          // 'declaredCustomsFeedAmount': 202,
          // 'declaredCustomsOtherAmount': 203,
          // 'declaredCustomsBorderAmount': 204,
          // 'noEffectsDeclaredLogisticsGoodsAmount': 205,
          // 'noEffectsDeclaredOffshoreTransferAmount': 206,
          // 'noEffectsDeclaredOtherAmount': 207,
          // 'serviceTradeAmount': 300,
          // 'serviceTradePostscript': '附言1',
          // 'incomeInvestmentAmount': 301,
          // 'incomeInvestmentPostscript': '附言2',
          // 'incomeInvestmentApprovalCertificateNo': '********',
          // 'frequentTransferAmount': 302,
          // 'frequentTransferPostscript': '附言3',
          // 'capitalAccountAmount': 303,
          // 'capitalAccountPostscript': '附言4',
          // 'directInvestmentAmount': 304,
          // 'directInvestmentPostscript': '附言5',
          // 'directInvestmentApprovalCertificateNo': '7894564',
          // 'portfolioInvestmentAmount': 305,
          // 'portfolioInvestmentPostscript': '附言6',
          // 'otherInvestmentAmount': 306,
          // 'otherInvestmentPostscript': '附言7',
          payeeAddress,
          // 'field1Date': '2021-09-25',
          // 'field1Amount': '200.00.00',
          // 'field1No': '4561312',
          'field2': '',
          // 'field2No': '*********',
          // 'field3Loan': 1000,
          // 'field3LoanRate': '78%',
          // 'field3InterestDays': 100,
          // 'field3InterestBase': 10,
          // 'field3TaxAmount': 10001,
          // 'filledBy': '百里',
          // 'filledPhone': '110',
        },
      };
    }
  })
    if (planData && planData.items && planData.items.length) {
      planData.items.forEach((item: any) => {
        if (obj[item.payPlanNo]) {
          item.bopData.date = moment(item.bopData.date)
          item.bopData.paymentTime = moment(item.bopData.paymentTime)
          Object.keys(obj[item.payPlanNo].bopData).forEach(key => {
            if (item.bopData[key]) {
              obj[item.payPlanNo].bopData[key] = item.bopData[key]
            }
          })
          item.bopData = obj[item.payPlanNo].bopData
          if (item.settlementInstructions.date) {
            item.settlementInstructions.date = moment(item.settlementInstructions.date)
            item.settlementInstructions.field1Date = moment(item.settlementInstructions.field1Date)
            Object.keys(obj[item.payPlanNo].settlementInstructions).forEach(key => {
              if (item.settlementInstructions[key]) {
                obj[item.payPlanNo].settlementInstructions[key] = item.settlementInstructions[key]
              }
            })
            item.settlementInstructions = obj[item.payPlanNo].settlementInstructions
          }
          obj[item.payPlanNo] = item
        }
      })
    }
  return obj
}

/**
 * 处理组装数据
 */
export const assembleData = async({
                               response,
                               bus,
                               dynamicChannelMap,
                               setColumns,
                               setData,
                               columnsFromScenes,
                               setAllColumns,
                               setFormData,
                               paymentAccount,
                               paymentCurrency,
                               ekbTradeNo,
                               setLoading,
                               hasInstructionFile,
                               inPendingPaying,
                               payPlanFilterArr,
                               setEditArr,
                               setStepValue,
                               setSupplyFilesObj,
                             }:any) => {
  const { viewDataList } = response.value
  const { data: allPlans, path } = viewDataList;
  const data = allPlans
    .filter((payPlan: any) =>
      getV(payPlan, 'dataLink.E_system_paymentPlan_支付状态') === 'PROCESSING'
      && (!payPlanFilterArr || payPlanFilterArr.includes(getV(payPlan, 'dataLink.id')))
    )
  // 支付中页面进来时，需要获取用户存在后端的数据
  let planData = null
  if (inPendingPaying) {
    const fetchPlanParams = {
      'action': '/api/hsbc/plans/list',
      'body':
        {
          'tradeNo': ekbTradeNo,
          'planNos': data.map((el: any) => el.dataLink.id),
        },
    }
  
    try {
      planData = await saveSupplyFileForHSBC(fetchPlanParams);
    } catch (err) {
      console.log('========= err:', err);
    }
  }

  let { template } = viewDataList;
  let selectedFields: any[] = getV(template, 'content.expansion.selectedFields');
  if (selectedFields) {
    // 需要前端控制名称和过滤的两个字段：
    selectedFields = selectedFields.filter((el: any) => {
      if (el.name === 'E_system_paymentPlan_币种'){
        el.label = i18n.get('本位币币种')
      }
      return el.name !== 'E_system_paymentPlan_支付金额'
    });
    // 经过讨论，决定在前端调整的四个字段
    selectedFields.push({
      defaultValue: null,
      formula: false,
      label: "原币币种",
      name: "E_system_paymentPlan_foreignStrCode",
      optional: false,
      source: "dataLink",
      systemField: false,
      type: "text",
    }, {
      defaultValue: null,
      formula: false,
      label: "原币金额",
      name: "E_system_paymentPlan_foreign",
      optional: false,
      source: "dataLink",
      systemField: false,
      type: "text",
    }, {
      defaultValue: null,
      formula: false,
      label: "实付币种",
      name: "E_system_paymentPlan_realPayStrCode",
      optional: false,
      source: "dataLink",
      systemField: false,
      type: "text",
    },{
      defaultValue: null,
      formula: false,
      label: "实付金额",
      name: "E_system_paymentPlan_realPayMoney",
      optional: false,
      source: "dataLink",
      systemField: false,
      type: "text",
    })
    template.content.expansion.selectedFields = selectedFields
  }
  const formData = initialFormData({data, paymentAccount, paymentCurrency, ekbTradeNo, planData});
  const dataSource = data;
  const type = 'DATA_LINK';
  const dataLinkEntity = data[0] ? data[0].dataLink.entity : []
  const fields = parseFields({ res: template, type })
  const { columns } = parseColumns({
    entityInfoMap: {},
    fields,
    bus,
    path,
    platformType: type,
    dataLinkEntity
  })

  // columnsFromScenes：保存变更后储存到后端的columns，没保存过会是null
  const defaultColumns = getDefaultColumns(columnsFromScenes, columns)
  let _columns = defaultColumns ? defaultColumns : cloneDeep(columns)
  prepareRender(_columns, dynamicChannelMap);
  _columns.forEach((el: any) => el.title = i18n.get(el.title))
  columns.forEach((el: any) => el.title = i18n.get(el.title))
  const planDataItems = getV(planData, 'items', [])
  const stepValue = hasInstructionFile ? 1 : 0
  const supplyFilesObj = hasInstructionFile || null
  const editArr = planDataItems.map((item: any) => item.payPlanNo)
  dataSource.forEach((item: any) => {
    if (editArr.includes(item.dataLink.id)) {
      item.dataLink.edited = true
    }
  })
  return new Promise((resolve, reject) => {
    setColumns(_columns);
    setAllColumns(columns)
    setData(dataSource);
    setFormData(formData);
    setEditArr(editArr)
    setStepValue(stepValue);
    setSupplyFilesObj(supplyFilesObj);
  })
}

// 获取补充文件类型相关信息
export const getBopTypeInfo = (record: any, paymentAccount: any) => {

  // 实付金额币种
  const paymentPlanRealPayStrCode = getV(record, 'dataLink.E_system_paymentPlan_realPayStrCode');
  const payeeAccountSort = getV(record, 'dataLink.E_system_paymentPlan_收款账号类别');

  // 付款账户：是否是境外账户
  const payerAccountIsOversea = paymentAccount.sort === 'OVERSEABANK';
  // 收款账户：海内账户还是银行卡？
  const payeeAccountIsOversea = payeeAccountSort === 'OVERSEABANK';
  // 付款币种？ 人民币还是外币
  const paymentCurrencyIsCNY = paymentPlanRealPayStrCode === 'CNY';

  let bopType: null | 'BOP_TERRITORY' | 'BOP_ABROAD' | 'NONE', // BOP文件类型
    needInstructions: boolean,  // 是否需要【跨境人民币付款说明】
    bopSupplyType: 'PP_CNY_DOMESTIC' | 'PP_FCY' | 'PP_CNY_CROSS_BORDER', // 付款指令类型
    noCertifyFile: boolean // 证明文件

  if (!payeeAccountIsOversea && !payerAccountIsOversea && paymentCurrencyIsCNY) {
    bopType = 'NONE'
    needInstructions = false
    bopSupplyType = 'PP_CNY_DOMESTIC'
  } else {
    // 取值：收款账户是境内账户还是境外账户
    bopType = payeeAccountIsOversea ? 'BOP_ABROAD' : 'BOP_TERRITORY';
    // 取值：付款币种是人民币，且收、付款账户中有一个或都是境外账户时，需要【跨境人民币付款说明】
    needInstructions = paymentCurrencyIsCNY && payerAccountIsOversea !== payeeAccountIsOversea
    // 根据付款币种取值
    bopSupplyType = paymentCurrencyIsCNY ? 'PP_CNY_CROSS_BORDER' : 'PP_FCY';
  }
  // 不需要证明文件的情况：1、境内账户人民币交易；2、境外账户用外币付款
  noCertifyFile = bopType === 'NONE' || (payerAccountIsOversea && !paymentCurrencyIsCNY);

  return { bopType, needInstructions, bopSupplyType, noCertifyFile };
}

// HSBC: 通过url识别xml文件类型
export const getInstructionFileType = (url: string) => {

  if (url.includes('PP_FCY.xml')) {
    return 'PP_FCY';
  }
  if (url.includes('PP_CNY_DOMESTIC.xml')) {
    return 'PP_CNY_DOMESTIC';
  }
  if (url.includes('PP_CNY_CROSS_BORDER.xml')) {
    return 'PP_CNY_CROSS_BORDER';
  }
  return null
}

// 格式化日期数据
const timeFormat = (text?: string) => {
  if (!text) return text;
  return moment(text).format('YYYY-MM-DD')
}

// 将数据中的日期类数据格式化
export const formatDateValue = (data: any[]) => {
  data.forEach((el: any) => {
    el.bopData.date = timeFormat(el.bopData.date);
    el.bopData.paymentTime = timeFormat(el.bopData.paymentTime);
    el.settlementInstructions.date = timeFormat(el.settlementInstructions.date);
    el.settlementInstructions.field1Date = timeFormat(el.settlementInstructions.field1Date);
  });
}

// 发送请求，提供生成文件相关日志
export const postLog = ({ data, supplyFilesObj, ekbTradeNo, channelTradeNo }: any) => {
  // 日志数据
  const logData: any = {};
  // 是否需要发送日志请求；国内交易支付人民币时不会生成文件
  let needFetch = false
  data.forEach((el: any) => {
    const flowId = getV(el,'dataLink.E_system_paymentPlan_flowId','')
    const supportFileDataArr = getV(supplyFilesObj,'items.listResult',[])
    const payPlanId = getV(el, 'dataLink.id')
    const supportFileData: any =
      supportFileDataArr.find((fileData: any) => fileData.payPlanNo === payPlanId);
    if (!supportFileData || (!supportFileData.fileName && !supportFileData.instructionName)){
      return
    }
    if (!needFetch) needFetch = true;
    const log = {
      payPlanCode: payPlanId,
      content: String(`生成的文件是：${supportFileData.fileName || ''} ${supportFileData.instructionName || ''}`).trim()
    }
    if (logData[flowId]) {
      logData[flowId].push(log)
    } else {
      logData[flowId] = [log]
    }
  })
  // 调接口添加日志
  const logParams = {
    ekbTradeNo,
    channelTradeNo,
    channel:'HSBCPAY',
    ...logData
  }
  if (needFetch) saveLogForHSBC(logParams);
}

export const initialTabel = (ekbTradeNo: string, formData: any, id: string) => {
  const supportFormData = formData[id]
  const data = getV(supportFormData, 'bopData', {})
  const field2 = getV(supportFormData, 'settlementInstructions.field2')
  const {
    bopTransacCode,
    amountCurrency,
    amountInfoMoney,
    paymentUnderBondedGoodsType,
  }: any = data;

  let introductionText1: string = '',
    introductionText2: string = '',
    introductionText3: string = '';
  let dataSource: any = [];
  const defaultText = i18n.get('请与行方确认所需支持性文件后，将其上传至汇丰银行网银端')
  const prepareText = i18n.get('请准备以下文件并上传至汇丰网银端')

  switch (bopTransacCode){
    case '121020':
    case '121010':{
      introductionText1 = prepareText
      if (paymentUnderBondedGoodsType === '1') {
        introductionText2 = i18n.get('合同 / Purchase Order/协议/商业发票/Debit Note/进口报关单（选其一提供原件）')
        if (Number(amountInfoMoney) > 10000 && amountCurrency === 'USD') {
          dataSource = [{ name: i18n.get('报关单清单') }];
        } else {
          dataSource = null
        }
      } else if (paymentUnderBondedGoodsType === '0') {
        dataSource = [{ name: i18n.get('合同') }, { name: i18n.get('发票') }];
        if (Number(amountInfoMoney) > 10000 && amountCurrency === 'USD') {
          dataSource.push({ name: i18n.get('核验情况说明') });
        }
      } else {
        introductionText1 = defaultText;
        dataSource = null;
      }
      return { dataSource, introductionText1, introductionText2 };
    }
    case '122010':{
      if (field2 === '0') {
        introductionText1 = prepareText
        introductionText2 = i18n.get('收入时：承诺函、上下家合同；')
        introductionText3 = i18n.get('支出时：上下家合同、发票、提单或运输单据正本、收入时BOP申报单复印件或银行进帐单复印件并加盖公章或财务章')
      } else if (field2 === '1') {
        introductionText1 = prepareText
        introductionText2 = i18n.get('支出时：上下家合同、发票、提单或运输单据正本、承诺函；')
        introductionText3 = i18n.get('收入时：上下家合同、发票、提单或运输单据正本、支出时的BOP申报单复印件或银行出账单复印件并加盖公章或财务章。')
      } else {
        introductionText1 = defaultText;
      }
      return { introductionText1, introductionText2, introductionText3 };
    }
    default:{
      introductionText1 = defaultText
      return { introductionText1 };
    }
  }
}