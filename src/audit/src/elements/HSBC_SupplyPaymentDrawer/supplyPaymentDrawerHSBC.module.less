@import '~@ekuaibao/eui-styles/less/token';

.supplyPaymentDrawerHSBC-wrapper{
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  :global{
    .modal-header {
      flex-shrink: 0;
      &-title{
        flex: 1;
        .font-size-5;
        color:var(--eui-text-title);
      }
    }

    .lock{
      height: 100%;
      width: 100%;
      position: absolute;
      display: flex;
      justify-content: center;
      align-items: center;
      background: rgba(29, 43, 61, 0.15);
      z-index: 2;
    }

    .supplyPaymentDrawerHSBC-steps-wrapper {
      width: 500px;
      margin: 0 auto @space-6;
    }

    .supplyPaymentDrawerHSBC-content{
      overflow: auto;
      margin-bottom: 56px;
    }

    .hsbcActionCell {
      color: @color-brand;
      cursor: pointer;
      a {
        margin-left: @space-4;
        &:first-child {
          margin-left: 0;
        }
      }
    }


    .inner{
      height: 100%;
      width: 100%;
      position: absolute;
      z-index: 1;
    }
    .modal-content{
      padding: 0 32px 32px;
      position: relative;
      .modal-warpper{
        z-index: 3;
        overflow-x: auto;
        .modal-warpper-c{
          .recall-plan{
            cursor: pointer;
            color: #f17b7b
          }
        }
        .dx-widget.ekb-table-inner .dx-datagrid .dx-datagrid-rowsview .dx-row.dx-freespace-row{
          visibility: visible;
          background: #fff;
          td {
            border-right: 1px solid #ddd;
            &:last-child {
              border-right: none;
            }
          }
        }
      }

      .column-chooser-box{
        position: absolute;
        top: 18px;
        right: 45px;
        z-index: 4;
        &::before{
          content: '';
          width: 1px;
          height: 16px;
          position: absolute;
          background: rgba(29, 43, 61, 0.15);
          left: -9px;
        }
      }
      .search-box{
        position: absolute;
        right: 12px;
        top: 0;
        z-index: 4;
      }
      .save-diff-box{
        position: absolute;
        top: 16px;
        right: 319px;
        z-index: 4;
        .save-diff-btn{
          color: #22b2cc;
          cursor: pointer;
          min-width: 70px;
          height: 32px;
          margin-left: 16px;
          display: inline-block;
          vertical-align: middle;
        }
      }

    }
    .modal-footer{
      justify-content: space-between;
      position: absolute;
      bottom: 0;
      width: 100%;
      .total{
        padding: 0 5px;
        color: #22b2cc
      }
      .ant-btn-danger {
        color: #ff7c7c;
        background-color: #fff;
        border: 1px solid #ff7c7c;
        &:hover{
          color: #fff;
          background-color: #ff7c7c;
          transition: .3
        }
      }
      a {
        text-decoration: none;
        margin-right: @space-6;
      }
      .tooltip-inner {
        p {
          margin-bottom: 0;
          font-size: 14px;
          line-height: 22px;
          color: #FFFFFF;
        }
      }
    }
    .modal-downtable{
      padding: 0 32px 16px;
      .modal-warpper{
        .modal-warpper-c{
          border-top: 1px solid #ddd;
          .recall-plan{
            cursor: pointer;
            color: #f17b7b
          }
        }
        .dx-widget.ekb-table-inner .dx-datagrid .dx-datagrid-rowsview .dx-row.dx-freespace-row{
          visibility: visible;
          background: #fff;
          td {
            border-right: 1px solid #ddd;
            &:last-child {
              border-right: none;
            }
          }
        }
      }
      .no-warpper{
        display: none;
      }
    }
    .modal-action{
      padding: 0 32px 16px;
      height: 40px;
      text-align: right;
      .modal-title{
        font-weight: normal;
        font-size: 14px;
        line-height: 32px;
        float: left;
      }
    }
  }
}

.missInfoModal-title {
  .font-size-3;
  .font-weight-3;
}
.missInfoModal-wrapper {
  margin-top: @space-6;
  height: 200px;
  overflow: auto;
  p {
    .font-size-2;
  }
}
