import React from 'react'
import { getBopTypeInfo } from '../util'

const HSBC_ActionCell = ({ id, record, bus, paymentAccount, currentStep }: any) => {
  const { bopType, needInstructions, bopSupplyType, noCertifyFile }: any = getBopTypeInfo(
    record,
    paymentAccount,
  )

  const handleSupply = () =>
    bus.emit('HSBC_SupplyPayment:supply:file', {
      bopType,
      needInstructions,
      bopSupplyType,
      id,
    })

  const handleOpenModal = () => bus.emit('HSBC_SupplyPayment:show:introduction', { id })
  const tips = () => {
    if (currentStep === 1) {
      return i18n.get('下载付款文件')
    }
    return record?.dataLink?.edited ? i18n.get('编辑付款文件') : i18n.get('补充付款文件')
  }
  return (
    <div
      className="hsbcActionCell"
      style={{ width: 140 }}
      onClick={e => {
        e.persist()
        e.nativeEvent.stopImmediatePropagation()
        e.stopPropagation()
        e.preventDefault()
        return false
      }}>
      {!(currentStep === 1 && bopType === 'NONE') && <a onClick={handleSupply}>{tips()}</a>}
      {currentStep === 1 && !noCertifyFile && (
        <a onClick={handleOpenModal}>{i18n.get('支持性文件')}</a>
      )}
    </div>
  )
}

export default HSBC_ActionCell
