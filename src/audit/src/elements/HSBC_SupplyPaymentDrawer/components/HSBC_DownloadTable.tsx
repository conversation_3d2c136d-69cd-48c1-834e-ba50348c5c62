import React, { useState, useEffect } from 'react'
import * as DataGrid from '@ekuaibao/datagrid'
import { app as api } from '@ekuaibao/whispered'
import { getV } from '@ekuaibao/lib/lib/help';
import { getInstructionFileType } from '../util';
import { get } from 'lodash'

const HSBC_DownloadTable = ({
  commandSelection,
  setCommandSelection,
  supplyFilesObj,
  allCommands,
  setAllCommands,
  setCommandData
}: any) => {

  const [dataSource, setDataSource] = useState([]);

  const handleSelectedChange = (selectedRowKeys: string[]) => {
    setCommandSelection(selectedRowKeys)
    setAllCommands(selectedRowKeys.length === dataSource.length)
  }

  const initialTabel = () => {
    const data: any = [];
    const supportFileArr = getV(supplyFilesObj, 'items.listUrl', [])
    const payTradeNo = get(supplyFilesObj, 'items.listResult[0].payTradeNo')
    supportFileArr.forEach((urlStr: string, index: number) => {
      const fileType = getInstructionFileType(urlStr)
      data.push({
        key: index + 1,
        payTradeNo,
        type: fileType,
        url: urlStr,
      })
    })
    setDataSource(data)
    setCommandData(data)
  }

  const downcolumns = [
    {
      title: i18n.get('序号'),
      dataIndex: 'key',
      width: 80,
    },
    {
      title: i18n.get('批次号'),
      dataIndex: 'payTradeNo',
    },
    {
      title: i18n.get('文件类型'),
      dataIndex: 'type',
    },
    {
      title: i18n.get('操作'),
      dataIndex: 'url',
      width: 220,
      fixed: 'right',
      render: (url: string) => {
        return (<a className='hsbcSupportFileModal-table-action'
          onClick={() => {
            // @ts-ignore
            api.emit('@vendor:download', url)
          }}>
          {i18n.get('下载')}
        </a>)
      },
    }
  ]
  useEffect(() => {
    initialTabel()
  }, [supplyFilesObj])
  useEffect(() => {
    if (allCommands) {
      const selectedKeys: string[] = []
      dataSource.forEach((item: any) => {
        selectedKeys.push(item.key)
      })
      setCommandSelection(selectedKeys)
    } else {
      setCommandSelection(commandSelection)
    }
  }, [allCommands])
  return (
    <div className="modal-downtable">
      <div className="modal-warpper">
        <DataGrid.TableWrapper
          columns={downcolumns}
          className="modal-warpper-c"
          rowKey="key"
          dataSource={dataSource}
          isMultiSelect={true}
          selectedRowKeys={commandSelection}
          onSelectedChange={handleSelectedChange}
          columnMinWidth={160}
        />
      </div>
    </div>
  )
}

export default HSBC_DownloadTable
