import React, { useState, useEffect, useMemo } from 'react'
import * as DataGrid from '@ekuaibao/datagrid'
import { savePayPlanScenes } from '../../../util/fetchUtil'
import { getColumns } from './columns_HSBC_SupplyPayment'
import { columnFilterStringify, isRenderDom, sortColumns } from '../../AdjustPaymentPlan/utils'
import { app as api } from '@ekuaibao/whispered'
import { get } from 'lodash'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

const scenesType = 'SUPPLY_PAYMENT'

const HSBC_SupplyPaymentContent = ({
                                     bus,
                                     baseColumns,
                                     allColumns,
                                     overRideColumns,
                                     paymentAccount,
                                     formData,
                                     setFormData,
                                     currentStep,
                                     supplyFilesObj,
                                     data,
                                     ekbTradeNo,
                                     payPlanSelection,
                                     setPayPlanSelection,
                                     allPayPlan,
                                     setAllPayPlan,
                                     setPayPlanData,
                                     editArr,
                                     setEditArr,
                                     setData
                                   }:any) => {

  const [showColumnChooser, setShowColumnChooser] = useState(true);
  const [ isVisibleSaveDiff, setIsVisibleSaveDiff ] = useState(false)
  const [ instance, setInstance ] = useState(null)
  const [ columns, setColumns ] = useState([])
  const [search, setSearch] = useState({
    isSearch: false,
    filterValue: '',
  })

  const handleSupplyFile = ({ bopType, needInstructions, bopSupplyType, id }: any) => {
    if (currentStep === 0) {
      const dataSource = []
      if (currentStep === 0){
        dataSource.push({
          key: bopSupplyType,
          type: bopSupplyType,
          name: i18n.get('付款指令'),
        });
      }
      if (bopType && bopType !== 'NONE') {
        dataSource.push({
          key: bopType,
          type: bopType,
          name: 'BOP',
        })
      }
      if (needInstructions) {
        dataSource.push({
          key: 'CROSS_BORDER_RMB',
          type: 'CROSS_BORDER_RMB',
          name: i18n.get('跨境人民币付款说明'),
        })
      }
      api.open('@audit:HSBC_TableFillingModal', { dataSource, formData, id, setFormData, title: '付款文件', setData, data, editArr, setEditArr, width: '850px' })
    } else {
      // @ts-ignore
      api.open('@audit:HSBC_SupportFileModal', {
        bopType,
        needInstructions,
        bopSupplyType,
        currentStep,
        supplyFilesObj,
        formData,
        id,
        title: i18n.get('下载付款文件'),
        footer: [],
        className: 'hsbcSupportFileModal-wrap',
      })
    }
  }

  const handleShowIntroduction = ({ id }: any) => {
    // @ts-ignore
    api.open('@audit:HSBC_SupportFileIntroductionsModal', {
      ekbTradeNo,
      formData,
      id,
    })
  }

  const _getColumns = () =>
    getColumns(instance, baseColumns, bus, paymentAccount, currentStep )

  useEffect(() => {
    // 获取到基础数据以及表格实体后重新拼装columns
    if (Boolean(instance)) {
      const _: any = _getColumns()
      setColumns(_)
    }

    bus.on('HSBC_SupplyPayment:supply:file', handleSupplyFile)
    bus.on('HSBC_SupplyPayment:show:introduction', handleShowIntroduction)

    return () => {
      bus.un('HSBC_SupplyPayment:supply:file')
      bus.un('HSBC_SupplyPayment:show:introduction')
    }
  }, [
    data,
    formData,
    instance,
    baseColumns,
    currentStep,
    supplyFilesObj
  ])

  const handleSelectedChange = (selectedRowKeys: string[]) => {
    setPayPlanSelection(selectedRowKeys)
    setAllPayPlan(selectedRowKeys.length === _data.length)
  }

  useEffect(() => {
    if (allPayPlan) {
      const selectedKeys: string[] = []
      _data.forEach((item: any) => {
        selectedKeys.push(item.dataLink.id)
      })
      setPayPlanSelection(selectedKeys)
    } else {
      setPayPlanSelection(payPlanSelection)
    }
  }, [allPayPlan])

  //按当前顺序获取已展示的列
  const columnsValue = baseColumns.filter((el: any) => el.dataIndex && el.dataIndex !== 'dataLink.id')
  const defaultVisibleColumns = columnsValue.map((col: any) => col.dataIndex)

  //根据当前列的数据做排序, 保存至后端
  const handleVisibleColumnChange = (visibleColumns: string[]) => {
    sortAndSaveColumns(visibleColumns, false)
  }

  const handleSavePayPlanScenes = (sortArr: any[], filter: any) => {
    savePayPlanScenes({
      type: scenesType,
      filter: [JSON.stringify(filter)],
    }).then(_ => {
      setTableColumns(sortArr, false)
    })
  }

  const setTableColumns = (sortArr: any[], isVisibleSaveDiff: boolean) => {
    overRideColumns(sortArr)
    setIsVisibleSaveDiff(isVisibleSaveDiff)
    setShowColumnChooser(false)
    setTimeout(() => setShowColumnChooser(true))
  }

  /**
   * @param {boolean} saveScence 是否需要将scence保存至后端
   */
  const sortAndSaveColumns = (sortArr: any[], saveScence: boolean) => {
    //根据当前列的数据做排序
    const arr = sortColumns(sortArr, allColumns)
    const scenceData = columnFilterStringify(arr)
    const filter = {
      scene: 'all',
      defaultColumns: JSON.stringify(scenceData),
    }

    if (saveScence) {
      handleSavePayPlanScenes(arr, filter)
    } else {
      setTableColumns(arr, true)
    }
  }

  const onSaveDiffScenes = () => {
    //获取当前列的排序数据
    const currentColumns = instance.getVisibleColumns() || []
    const filterCurrentColumns = currentColumns.map((el: any) => el.dataField)
    //根据当前列的数据做排序, 保存至后端
    sortAndSaveColumns(filterCurrentColumns, true)
  }

  const filterBySearch = (data: any[], value: string) => {
    const arr = data.filter((el: any) => {
      let includeSearchValue = false
      const {
        E_system_paymentPlan_编号,
        E_system_paymentPlan_支付概要,
        E_system_paymentPlan_提交人,
      } = el.dataLink
      if (
        E_system_paymentPlan_编号.includes(value) ||
        E_system_paymentPlan_支付概要.includes(value) ||
        E_system_paymentPlan_提交人.name.includes(value)
      ) {
        includeSearchValue = true
      }
      return includeSearchValue
    }) as any

    return arr
  }

  const handleSearch = (value: string) => {
    if (value) {
      setSearch({
        isSearch: true,
        filterValue: value,
      })
    } else {
      setSearch({
        isSearch: false,
        filterValue: '',
      })
    }
  }

  const _data = useMemo(() => {
    const { isSearch, filterValue } = search
    const newData = (isSearch ? filterBySearch(data, filterValue) : data).map((i: any, index: any) => ({
      ...i,
      zIndex: index,
    }))
    setPayPlanData(newData)
    return newData
  }, [data, search, editArr])

  const handleTableRowClick = (record: any) => {
    startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()

    if (api.require<any>('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
      api.open('@bills:BillInfoDrawerV2', {
        flows: [],
        currentId: get(record, 'dataLink.E_system_paymentPlan_flowId'),
        bus,
      })
      return
    }

    api.invokeService('@bills:get:flow-info',
      { id: get(record, 'dataLink.E_system_paymentPlan_flowId') })
      .then((resp: any) => {
        const flow = resp.value
        const paymentPlantId = get(record, 'dataLink.id')
        const ekbTradeNoStr = ekbTradeNo ? ekbTradeNo + '-' : ''
        const paymentPlantIdStr = paymentPlantId ? paymentPlantId + '-' : ''
        const fileList = get(flow, 'form.attachments')
        const detailList = get(flow, 'form.details')
        const logsList = get(flow, 'logs')
        const fileName = ekbTradeNoStr + paymentPlantIdStr + i18n.get('支持性文件')
        if (fileList) {
          fileList.forEach((item: any) => {
            const suffix = item.fileName.split('.').reverse()[0]
            item.fileName = fileName + '.' + suffix
          })
        }
        if (detailList) {
          detailList.forEach((item: any) => {
            const itemlList = get(item, 'feeTypeForm.attachments')
            itemlList && itemlList.forEach((el: any) => {
              const suffix = el.fileName.split('.').reverse()[0]
              el.fileName = fileName + '.' + suffix
            })
          })
        }
        if (logsList) {
          logsList.forEach((item: any) => {
            const itemlList = get(item, 'attachments')
            itemlList && itemlList.forEach((el: any) => {
              const suffix = el.fileName.split('.').reverse()[0]
              el.fileName = fileName + '.' + suffix
            })
          })
        }
        const title = `${i18n.get(billTypeMap()[flow.formType])}${i18n.get('详情')}`
        const params = {
          title,
          invokeService: '@bills:get:flow-info',
          params: { id: flow.id },
          backlog: { id: -1, flowId: flow },
        }
        // @ts-ignore
        api.open('@bills:BillInfoPopup', params)
      })
  }

  const configProps = {
    columns: columns,
    className: "modal-warpper-c",
    rowKey: "dataLink.id",
    dataSource: _data,
    allowColumnReordering: true,
    allowColumnResizing: true,
    groupPanel: { visible: true },
    scrolling: {
      mode: 'virtual' as 'virtual',
    },
    onRowClick: (record: any) => handleTableRowClick(record),
    pageSize: data.length,
    pageIndex: 1,
    columnMinWidth: 160,
    getInstance: (instance: any) => setInstance(instance),
    refreshState: setIsVisibleSaveDiff,
    selectedRowKeys: payPlanSelection,
    onSelectedChange: handleSelectedChange,
  }
  return (
    <>
      {
        currentStep === 1 && <div className='modal-action'>
          <span className='modal-title'>付款文件、支持性文件下载</span>
        </div>
      }
      <div className="modal-content">
        <div className="column-chooser-box">
          {showColumnChooser && (
            <DataGrid.ColumnChooser
              columns={allColumns}
              defaultVisibleColumns={defaultVisibleColumns}
              onChange={handleVisibleColumnChange}
            />
          )}
        </div>

        <div className="search-box">
          <DataGrid.Search
            placeholder={i18n.get('搜索编号、支付摘要或提交人')}
            onClear={() => { }}
            onSearch={handleSearch}
            className={''}
          />
        </div>
        <div className="save-diff-box">
          {isRenderDom(
            <div className="save-diff-btn font-size-2" onClick={onSaveDiffScenes}>
              {i18n.get('保存变更')}
            </div>,
            isVisibleSaveDiff,
          )}
        </div>
        <div className='modal-warpper' style={{display: currentStep === 1 ? 'none' : 'block'}}>
          <DataGrid.TableWrapper {...configProps} isMultiSelect={false}/>
        </div>
        <div className='modal-warpper' style={{display: currentStep === 1 ? 'block' : 'none'}}>
          <DataGrid.TableWrapper {...configProps} isMultiSelect={true} />
        </div>
      </div>
    </>
  )
}

export default HSBC_SupplyPaymentContent
