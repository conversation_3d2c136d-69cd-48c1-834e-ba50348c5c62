import React from 'react';
import { <PERSON><PERSON>, Tooltip } from '@hose/eui';
import { get } from 'lodash'
import Big from 'big.js'

const HSBC_SupplyPaymentFooter = ({
                                    data,
                                    stepValue,
                                    confirmHandle,
                                    fnHandleClickStepBtn,
                                  }: any) => {

  const btnText = stepValue === 0 ? i18n.get('下一步') : i18n.get('上一步');
  const infoText = stepValue === 0
    ? i18n.get('请确认所需付款文件均补充完整后再进行下一步操作')
    : i18n.get('请确认所需付款文件均下载完成后再点击完成');
  const showConfirmBtn = stepValue === 1;
  const tooltipinner = {
    marginBottom: 0,
    fontSize: '14px',
    lineHeight: '22px',
    color: '#FFFFFF'
  }
  const summaryofPaymentAmount:any = {}
  data.forEach((item: any) => {
    const standardSymbol = get(item, 'dataLink.E_system_paymentPlan_realPayStrCode')
    const standard = get(item, 'dataLink.E_system_paymentPlan_realPayMoney')
    if (standardSymbol && standard) {
      if (Object.keys(summaryofPaymentAmount).includes(standardSymbol)) {
        summaryofPaymentAmount[standardSymbol] = new Big(summaryofPaymentAmount[standardSymbol]).plus(new Big(standard)).toFixed(2)
      } else {
        summaryofPaymentAmount[standardSymbol] = new Big(standard).toFixed(2)
      }
    }
  })
  return (<div className="modal-footer">
    <div>
      <Button onClick={fnHandleClickStepBtn} className="mr-8">
        {btnText}
      </Button>
      {showConfirmBtn
        ? (<Button onClick={confirmHandle} className="mr-8">
          {i18n.get('完成')}
        </Button>)
        : null}
      {!!infoText && (<span>{infoText}</span>)}
    </div>
    <div>
      <Tooltip
        title={
          <div>
            <p style={tooltipinner}>支付金额共计：</p>
            {
              Object.keys(summaryofPaymentAmount).map((item: any) => <p style={tooltipinner}>{item} {summaryofPaymentAmount[item]}</p>)
            }
          </div>
        } placement="top">
        <a>支付金额查询</a>
      </Tooltip>
      {i18n.get('item', { item: data.length })}
    </div>
  </div>);
};

export default HSBC_SupplyPaymentFooter;