/**************************************
 * Created By LinK On 2021/8/26 14:25.
 **************************************/
import React from 'react';
import DevExpress from 'devextreme/bundles/dx.all';
import HSBC_ActionCell from './HSBC_ActionCell';

export const getColumns = (instance: DevExpress.ui.dxDataGrid | null,
                           baseColumns: any[],
                           bus: any,
                           paymentAccount: any,
                           currentStep: number,
) => {

  const stack: any = [];

  stack.push({
    title: i18n.get('HSBC序号'),
    dataIndex: 'zIndex',
    allowReordering: false,
    allowGrouping: false,
    allowResizing: false,
    allowEditing: false,
    allowFiltering: false,
    sorter: false,
    width: 70,
    minWidth: 40,
    render: (index: number) => {
      return <div>{index + 1}</div>;
    },
  });

  //========================= 拼装表格字段 =====================================
  baseColumns.forEach((i) => {
    stack.push({
      ...i,
      filterType: '',
      sorter: false,
      allowFiltering: false,
    });
  });

  //========================== 拼装操作按钮 =====================================
  stack.push({
    title: i18n.get('操作'),
    dataIndex: 'dataLink.id',
    allowReordering: false,
    allowGrouping: false,
    allowResizing: false,
    allowEditing: false,
    allowFiltering: false,
    sorter: false,
    width: 220,
    fixed: 'right',
    render: (id: string, record: any) =>
      (<HSBC_ActionCell id={id}
                        record={record}
                        bus={bus}
                        paymentAccount={paymentAccount}
                        currentStep={currentStep}/>),
  });

  return stack;
};

