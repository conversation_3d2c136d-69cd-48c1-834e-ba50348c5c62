import { ILayerProps } from '@ekuaibao/enhance-layer-manager';

export interface HSBC_SupplyPaymentDrawerProps extends ILayerProps {
  dynamicChannelMap: any
  adjustPaymentPlanData: any
  flowIds: any[]
  getPayRemark: () => RemarkProps
  channel: string
  accountId: string
  paymentCurrency: any
  paymentAccount: any
  ekbTradeNo: string
  channelTradeNo: string
  inPendingPaying: boolean
  payPlanFilterArr?: any[]
}

export interface RemarkProps {
  remarkLimit: number
  byHand: boolean
  autoSummary: boolean
  fieldLabel: string
  useSpecial: boolean
  needRemark: boolean
  isAutoSummary: boolean
  remark: string | undefined
  dimensionItemId: string | undefined
}

export interface IProps {
  closeHandle: () => void
}

export interface IHeader extends IProps {
  title?: string
}