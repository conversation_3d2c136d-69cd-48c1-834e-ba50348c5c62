import './hsbcSupportFileModal.less'
import React, { Component } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Table } from 'antd'
import { Button } from '@hose/eui'
import { initialTabel } from '../util'

export interface Props {
  layer?: any
  supplyFilesObj: any
  ekbTradeNo: string | number
}

export interface State {
  columns?: any
  dataSource?: any
  introductionText1?: string
  introductionText2?: string
  introductionText3?: string
}

@EnhanceModal({
  title: i18n.get('支持性文件'),
  footer: [],
  className: 'hsbcSupportFileModal-wrap',
})
export default class HSBC_SupportFileIntroductionsModal extends Component<Props, State> {
  state = {
    columns: [{ title: i18n.get('文件类型'), dataIndex: 'name' }],
    dataSource: null,
    introductionText1: '',
    introductionText2: '',
    introductionText3: '',
  }

  componentDidMount() {
    const { ekbTradeNo, formData, id }: any = this.props
    const tableData = initialTabel(ekbTradeNo, formData, id)
    this.setState(tableData)
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  renderText = (text: string | undefined) => {
    if (!text) return null
    return <p>{text}</p>
  }

  renderContent = () => {
    const {
      columns,
      dataSource,
      introductionText1,
      introductionText2,
      introductionText3,
    } = this.state
    return (
      <div className="hsbcSupportFileModal-content">
        {this.renderText(introductionText1)}
        {this.renderText(introductionText2)}
        {this.renderText(introductionText3)}
        {dataSource && (
          <Table
            className="hsbcSupportFileModal-table"
            columns={columns}
            dataSource={dataSource}
            pagination={false}
          />
        )}
      </div>
    )
  }

  render() {
    return (
      <>
        {this.renderContent()}
        <div className="hsbcSupportFileModal-footer">
          <Button onClick={this.handleCancel}>
            {i18n.get('确定')}
          </Button>
        </div>
      </>
    )
  }
}
