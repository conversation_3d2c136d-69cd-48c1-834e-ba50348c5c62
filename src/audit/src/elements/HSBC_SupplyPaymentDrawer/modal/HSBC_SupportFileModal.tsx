/**************************************
 * Created By LinK On 2021/8/27 17:21.
 **************************************/
import './hsbcSupportFileModal.less';
import React, { Component } from 'react';
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager';
import { Table } from 'antd'
import { Button } from '@hose/eui'
import HSBC_SupportFileModal_ActionCell from './HSBC_SupportFileModal_ActionCell'

export interface Props {
  layer?: any
  flowIds: number[]
}

export interface State {
  columns: any[]
  dataSource: any[]
}

@EnhanceModal((props: any) => ({
  title: props.title,
  footer: props.footer,
  className: props.className,
}))
export default class HSBC_SupportFileModal extends Component<Props, State> {

  state = {
    columns: [],
    dataSource: [],
  };

  componentDidMount(){
    const tableData = this.initialTabel();
    this.setState(tableData)
  }

  initialTabel = () => {
    const {
      bopType,
      needInstructions,
      bopSupplyType,
      id,
      currentStep,
      supplyFilesObj,
    }: any = this.props;

    const dataSource = []
    if (bopType && bopType !== 'NONE') {
      dataSource.push({
        key: bopType,
        type: bopType,
        name: 'BOP',
      })
    }
    if (needInstructions) {
      dataSource.push({
        key: 'CROSS_BORDER_RMB',
        type: 'CROSS_BORDER_RMB',
        name: i18n.get('跨境人民币付款说明'),
      })
    }
    if (currentStep === 0){
      dataSource.push({
        key: bopSupplyType,
        type: bopSupplyType,
        name: i18n.get('付款指令'),
      });
    }

    const columns = [
      {
        title: i18n.get('类型'),
        dataIndex: 'name',
      },
      {
        title: i18n.get('操作'),
        dataIndex: 'type',
        render: (type: string, record: any) => (<HSBC_SupportFileModal_ActionCell type={type}
                                                                     id={id}
                                                                     currentStep={currentStep}
                                                                     supplyFilesObj={supplyFilesObj}
        />),
      },
    ]
    return { columns, dataSource }
  }

  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  renderContent = () => {
    const { columns, dataSource } = this.state
    return (
      <div className='hsbcSupportFileModal-content'>
        <Table
          className="hsbcSupportFileModal-table"
          columns={columns}
          dataSource={dataSource}
          pagination={false}
        />
      </div>
    )
  }

  render() {
    return (
      <>
        {this.renderContent()}
        <div className='hsbcSupportFileModal-footer'>
          <Button onClick={this.handleModalClose}>
            {i18n.get('确定')}
          </Button>
        </div>
      </>
    )
  }
}
