/**************************************
 * Created By LinK On 2021/9/13 11:04.
 **************************************/

import React from 'react';
import { app as api } from '@ekuaibao/whispered'
import { getV } from '@ekuaibao/lib/lib/help';

const HSBC_SupportFileModal_ActionCell = ({
                                            type,
                                            id,
                                            currentStep,
                                            supplyFilesObj,
                                          }: any) => {
  if (currentStep === 0) {  // 第一步
    return null
  } else {  // 第二步

    const supportFileDataArr = getV(supplyFilesObj,'items.listResult',[])
    const supportFileData = supportFileDataArr.find((el: any) => el.payPlanNo === id);

    if (!supportFileData) return null;

    const url = getUrlByType(supportFileData, type)

    if (!url) return null;

    return (<a className='hsbcSupportFileModal-table-action'
               onClick={() => {
                 // @ts-ignore
                 api.emit('@vendor:download', url);
               }}>
      {i18n.get('下载')}
    </a>);
  }
};

export default HSBC_SupportFileModal_ActionCell


const getUrlByType = (supportFileData: any, type: string) => {
  if(type === 'CROSS_BORDER_RMB'){
    return supportFileData['instructionUrl']
  }else{
    return supportFileData['url']
  }
}