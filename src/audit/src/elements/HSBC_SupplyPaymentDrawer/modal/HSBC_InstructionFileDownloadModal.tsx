/**************************************
 * Created By LinK On 2021/9/13 15:25.
 **************************************/
import './hsbcSupportFileModal.less';
import React, { Component } from 'react';
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager';
import { Table } from 'antd';
import { Button } from '@hose/eui';
import { app as api } from '@ekuaibao/whispered';
import { getV } from '@ekuaibao/lib/lib/help';
import { getInstructionFileType } from '../util';

export interface Props {
  layer?: any
  supplyFilesObj: any
}

export interface State {
  columns: any[]
  dataSource: any[]
}

@EnhanceModal({
  title: i18n.get('付款指令下载'),
  footer: false,
  className: 'hsbcSupportFileModal-wrap'
})
export default class HSBC_InstructionFileDownloadModal extends Component<Props, State> {

  state = {
    columns: [],
    dataSource: [],
  };

  componentDidMount(){
    const tableData = this.initialTabel();
    this.setState(tableData)
  }

  initialTabel = () => {
    const {
      supplyFilesObj
    }: any = this.props;

    const dataSource:any = [];

    const supportFileArr = getV(supplyFilesObj,'items.listUrl',[])

    supportFileArr.forEach((urlStr: string) => {
      const fileType = getInstructionFileType(urlStr)
      dataSource.push({
        key: fileType,
        type: fileType,
        url: urlStr,
      })
    })

    const columns = [
      {
        title: i18n.get('HSBC类型'),
        dataIndex: 'type',
      },
      {
        title: i18n.get('操作'),
        dataIndex: 'url',
        render: (url: string) => {
          return (<a className='hsbcSupportFileModal-table-action'
                        onClick={() => {
                          // @ts-ignore
                          api.emit('@vendor:download', url)
                        }}>
            {i18n.get('下载')}
            </a>)
        },
      },
    ];
    return { columns, dataSource };
  };

  handleCancel = () => {
    this.props.layer.emitCancel();
  };

  renderContent = () => {
    const { columns, dataSource } = this.state;
    return (
      <div className='hsbcSupportFileModal-content'>
        <Table className='hsbcSupportFileModal-table' columns={columns} dataSource={dataSource} pagination={false}/>
      </div>
    );
  };

  render() {
    return (
      <>
        {this.renderContent()}
        <div className='hsbcSupportFileModal-footer'>
          <Button onClick={this.handleCancel}>
            {i18n.get('确定')}
          </Button>
        </div>
      </>
    );
  }
}
