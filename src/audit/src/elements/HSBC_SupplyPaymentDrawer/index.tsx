import React, { useEffect, useMemo, useState } from 'react';
import { Spin, Steps } from 'antd';
import { Button } from '@hose/eui';
import { HSBC_SupplyPaymentDrawerProps } from './type';
import HSBC_SupplyPaymentHeader from '../AdjustPaymentPlan/components/DoHeader';
import HSBC_SupplyPaymentContent from './components/HSBC_SupplyPaymentContent';
import HSBC_SupplyPaymentFooter from './components/HSBC_SupplyPaymentFooter';
import HSBC_DownloadTable from './components/HSBC_DownloadTable';
import styles from './supplyPaymentDrawerHSBC.module.less';
import { getPaymentPlan } from '../../audit-action';
import { getPayPlanScenes, saveSupplyFileForHSBC } from '../../util/fetchUtil';
import { assembleData, formatDateValue, postLog, getBopTypeInfo, initialTabel } from './util';
import MessageCenter from '@ekuaibao/messagecenter';
import { EnhanceConnect } from '@ekuaibao/store';
import { app as api } from '@ekuaibao/whispered';
import { Fetch } from '@ekuaibao/fetch';
import { showModal, showMessage } from '@ekuaibao/show-util'
import { cloneDeep } from 'lodash'
import { getV } from '@ekuaibao/lib/lib/help';

const { Step } = Steps;
const scenesType = 'SUPPLY_PAYMENT';

const HSBC_SupplyPaymentDrawer = ({
                                    layer,
                                    flowIds,
                                    getPayRemark,
                                    dynamicChannelMap,
                                    channel,
                                    paymentCurrency,
                                    paymentAccount,
                                    accountId,
                                    ekbTradeNo,
                                    channelTradeNo,
                                    inPendingPaying,
                                    payPlanFilterArr
                                  }: HSBC_SupplyPaymentDrawerProps) => {

  // 控制loading效果
  const [loading, setLoading] = useState(true);
  // 表格列-过滤后
  const [columns, setColumns] = useState([]);
  // 表格列
  const [allColumns, setAllColumns] = useState([]);
  const [data, setData] = useState([]);

  // 控制步进条
  const [stepValue, setStepValue] = useState(0);
  // 后端返回的补充文件相关链接
  const [supplyFilesObj, setSupplyFilesObj] = useState();

  const bus = useMemo(() => new MessageCenter(), []);

  const [formData, setFormData] = useState({});

  // 付款指令选中
  const [commandSelection, setCommandSelection] = useState([]);
  // 支付计划选中
  const [payPlanSelection, setPayPlanSelection] = useState([]);
  // 全选
  const [allSelected, setAllSelected] = useState(false);
  // 付款指令下载全选
  const [allCommands, setAllCommands] = useState(false);
  // 付款文件、支持性文件下载全选
  const [allPayPlan, setAllPayPlan] = useState(false);
  // 付款指令表格数据
  const [commandData, setCommandData] = useState([]);
  // 付款文件、支持性文件表格数据
  const [payPlanData, setPayPlanData] = useState([]);
  // 付款文件弹框编辑过的支付计划ID
  const [editArr, setEditArr] = useState([])

  useEffect(() => {
    setAllSelected(allCommands && allPayPlan)
  }, [allCommands, allPayPlan]);

  const init = async()=>{
    const _flowIds = Array.isArray(flowIds) ? flowIds : [flowIds];
    const { remark = '', dimensionItemId = '', autoSummary } = getPayRemark();
    const ids = inPendingPaying ? _flowIds : _flowIds.map(i => i.flowId.id);
    const params = {
      flowIds: ids,
      remark,
      dimensionItemId,
      autoSummary,
      channel,
      containsProcessing: true,
      accountId,
    };

    let hasInstructionFile = false
    if (inPendingPaying) {
      const fetchUrlParams = {
        'action': '/api/hsbc/allUrl',
        'body': {
          'tradeNo': ekbTradeNo,
        },
      }
      try {
        hasInstructionFile = await saveSupplyFileForHSBC(fetchUrlParams);
        console.log('========= hasInstructionFile:', hasInstructionFile);
      } catch (e) {
        console.log('========= e:', e);
      }
    }

    let results = null
    try{
      results = await Promise.all([
        getPayPlanScenes(scenesType),
        getPaymentPlan(params),
      ])
    }catch (e) {
      console.log('========= e:', e);
    }
    if (results) {
      setLoading(false);
    }

    const [columnsFromScenes, resp] = results;
    //组装表格数据
    assembleData({
      response: resp,
      bus,
      dynamicChannelMap,
      setColumns,
      setAllColumns,
      setData,
      setFormData,
      paymentAccount,
      paymentCurrency,
      ekbTradeNo,
      payPlanFilterArr,
      setLoading,
      hasInstructionFile,
      inPendingPaying,
      columnsFromScenes,
      setEditArr,
      setStepValue,
      setSupplyFilesObj,
    }).then(() => setLoading(false))
  }

  useEffect( () => {
    init()
  }, []);

  const closeHandle = () => layer && layer.emitCancel();
  const confirmHandle = () => {
    showModal.info({
      content: i18n.get('请登陆汇丰网银端完成相应操作'),
      onOk: () => {
        postLog({ data, supplyFilesObj, ekbTradeNo, channelTradeNo })
        layer && layer.emitCancel();
      },
    })
  };

  const fnHandleClickStepBtn = () => {
    if (stepValue === 0) {
      showModal.confirm({
        content: i18n.get('请确认所需付款文件均补充完整后再进行下一步操作'),
        onOk() {
          setCommandSelection([])
          setPayPlanSelection([])
          setAllSelected(false)
          setAllCommands(false)
          setAllPayPlan(false)
          getSupplyFile()
        },
      })
    } else {
      setStepValue(0);
      setSupplyFilesObj(null);
    }
  }

  // 将生成文件的数据发送至后端，获取对应文件
  const getSupplyFile = () => {
    const formDataArr = Object.values(cloneDeep(formData))
    formatDateValue(formDataArr)

    const params = {
      'action': '/api/hsbc/save',
      'body': {
        'cropId': Fetch.ekbCorpId,
        'lists': formDataArr,
      },
    };
    saveSupplyFileForHSBC(params).then(result => {
      const supportFileDataArr = getV(result,'items.listResult')
      if (supportFileDataArr) {// 返回结果正常
        setStepValue(1);
        setSupplyFilesObj(result);
        // @ts-ignore
        // api.open('@audit:HSBC_InstructionFileDownloadModal', { supplyFilesObj: result });
      } else { // 后端校验失败
        const errorArr = getV(result, 'items', []);
        const warningInfoArr: any[] = [];
        errorArr.forEach((err: any) => {
          if (err.error && err.error.length > 0) {
            const payPlanIndex = data.findIndex((el: any) => err.payPlanNo === getV(el, 'dataLink.id'));
            warningInfoArr.push((<p>{`第${payPlanIndex+1}条：`}</p>))
            err.error.forEach((msg: string) => warningInfoArr.push(<p>{msg}</p>));
          }
        });
        showModal.error({
          title: <span className={styles['missInfoModal-title']}>{i18n.get('信息缺失')}</span>,
          content: <div className={styles['missInfoModal-wrapper']}>{warningInfoArr}</div>,
        });
      }
    }).catch(err => {
      if (err.errorMessage){
        return showMessage.error(err.errorMessage)
      }
    })
  };

  const fnHandleOpenXML = () =>
    // @ts-ignore
    api.open('@audit:HSBC_InstructionFileDownloadModal', { supplyFilesObj });

  const handleAllSelected = () => {
    setAllSelected(!allSelected)
    setAllCommands(!allSelected)
    setAllPayPlan(!allSelected)
    if (allSelected) {
      setCommandSelection([])
      setPayPlanSelection([])
    }
  }

  const handleBatchDownload = () => {
    const tradeNo = getV(supplyFilesObj, 'items.listResult[0].payTradeNo')
    const instructions: string[] = []
    const planFiles: any[] = []
    commandData
      .filter((el: any) => commandSelection.includes(el?.key))
      .forEach((item: any) => {
        instructions.push(item.type)
      })
    payPlanData
      .filter((el: any) => payPlanSelection.includes(el?.dataLink?.id))
      .forEach((item: any) => {
        const { noCertifyFile } = getBopTypeInfo(item, paymentAccount)
        let supplementStr = ''
        const supplementMap = {
          introductionText1: null,
          introductionText2: null,
          introductionText3: null,
          dataSource: null,
        }
        if (!noCertifyFile) {
          supplementMap = {...supplementMap, ...initialTabel(ekbTradeNo, formData, item.dataLink.id)}
        }
        Object.keys(supplementMap).forEach((el: string) => {
          if (supplementMap[el]) {
            if (el === 'dataSource') {
              let fileNameStr = supplementMap[el].map((v: any) => v.name).join('、')
              supplementStr = supplementStr + fileNameStr + '\r\n'
            } else {
              supplementStr = supplementStr + supplementMap[el] + '\r\n'
            }
          }
        })
        planFiles.push(
          {
            planNo: item.dataLink.id,
            supplement: supplementStr
          }
        )
      })
    const params = {
      action: "/api/hsbc/batchDownload",
      body: { tradeNo, instructions, planFiles },
    }
    if (tradeNo) {
      saveSupplyFileForHSBC(params).then(result => {
        api.emit('@vendor:download', result.items, null, 'zip')
      }).catch(err => {
        if (err.errorMessage){
          return showMessage.error(err.errorMessage)
        }
      })
    }
  }

  return (<div className={styles['supplyPaymentDrawerHSBC-wrapper']}>
    {
      loading ? <div className='lock'><Spin/></div> : null
    }
    <HSBC_SupplyPaymentHeader closeHandle={closeHandle} title={i18n.get('支付计划文件处理')} />
    <Steps className='supplyPaymentDrawerHSBC-steps-wrapper' current={stepValue}>
      <Step title={i18n.get('确认支付币种及补充付款文件')} />
      <Step title={i18n.get('下载付款指令及付款文件')} />
    </Steps>
    <div className='supplyPaymentDrawerHSBC-content'>
      {
        stepValue === 1 && <>
          <div className='modal-action'>
            <span className='modal-title'>付款指令下载</span>
            <Button category="secondary" className="mr-8" onClick={handleAllSelected}>{allSelected ? '取消全选' : '全选'}</Button>
            <Button disabled={commandSelection.length === 0 && payPlanSelection.length === 0} onClick={handleBatchDownload}>批量下载</Button>
          </div>
          <HSBC_DownloadTable
            commandSelection={commandSelection}
            setCommandSelection={setCommandSelection}
            allCommands={allCommands}
            setAllCommands={setAllCommands}
            supplyFilesObj={supplyFilesObj}
            setCommandData={setCommandData}
          />
        </>
      }
      <HSBC_SupplyPaymentContent baseColumns={columns}
        allColumns={allColumns}
        overRideColumns={setColumns}
        bus={bus}
        paymentAccount={paymentAccount}
        paymentCurrency={paymentCurrency}
        data={data}
        setData={setData}
        formData={formData}
        setFormData={setFormData}
        currentStep={stepValue}
        supplyFilesObj={supplyFilesObj}
        flowIds={flowIds}
        ekbTradeNo={ekbTradeNo}
        payPlanSelection={payPlanSelection}
        setPayPlanSelection={setPayPlanSelection}
        allPayPlan={allPayPlan}
        setAllPayPlan={setAllPayPlan}
        setPayPlanData={setPayPlanData}
        editArr={editArr}
        setEditArr={setEditArr}
        inPendingPaying={inPendingPaying}
      />
    </div>
    <HSBC_SupplyPaymentFooter data={data}
      stepValue={stepValue}
      fnHandleClickStepBtn={fnHandleClickStepBtn}
      fnHandleOpenXML={fnHandleOpenXML}
      confirmHandle={confirmHandle} />
  </div>);
};


export default EnhanceConnect((state: any) => ({
  dynamicChannelMap: state['@audit'].dynamicChannelMap,
}))(HSBC_SupplyPaymentDrawer);

