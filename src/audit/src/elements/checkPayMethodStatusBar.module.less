@import '~@ekuaibao/eui-styles/less/token.less';

.check_pay_method_status {
  &_bar {
    font-size: 14px;
    padding: 16px 0;

    :global {
      svg {
        width: 1em;
        height: 1em;
        display: inline-block;
        vertical-align: middle;
        font-size: 26px;
        margin-left: 8px;
      }

      .loading {
        svg {
          display: inline-block;
          animation: loadingCircle 3s infinite linear;
        }
      }
    }
  }

  &_action {
    float: right;
  }

  &_button {
    color: @brand-color;
    cursor: pointer;
  }
}
.check_pay_method_status_Modal {
  min-height: 204px;

  :global {
    .modal-header {
      flex-shrink: 0;
    }
    .select-payment-modal-header-title {
      flex: 1;
      .font-size-5;
      color: var(--eui-text-title);
    }

    .modal-content {
      overflow: hidden;
      svg {
        width: 100%;
        height: 50px;
        display: inline-block;
        vertical-align: middle;
        font-size: 26px;
      }

      .tip {
        margin-top: @space-7;
        display: block;
        width: 100%;
        text-align: center;
        font-weight: 500;
        .font-size-2;
      }

      .error {
        .font-size-1;
        color: rgba(39, 46, 59, 0.48);
      }

      .action {
        display: block;
        width: 100%;
        text-align: center;
        padding: @space-7;
      }

      .loading {
        svg {
          display: inline-block;
          animation: loadingCircle 3s infinite linear;
        }
      }
    }
  }
}
