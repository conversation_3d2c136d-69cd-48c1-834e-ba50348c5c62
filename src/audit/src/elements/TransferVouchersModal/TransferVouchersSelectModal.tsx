import React, { Component } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Button } from '@hose/eui'
import { OutlinedTipsClose } from '@hose/eui-icons'
import styles from './TransferVouchersSelectModal.module.less'
import { EnhanceConnect } from '@ekuaibao/store'
import { Fetch } from '@ekuaibao/fetch'
const ComplexSelect: any = api.require('@elements/select/ComplexSelect')

interface RepaymentModalProps {
  transferFileTemplates: any[]
  layer: StringAnyProps
  batchId: string
}

interface TransferVouchersSelectModalState {
  selectValue: string
}

@EnhanceConnect(state => ({
  transferFileTemplates: state['@audit'].transferFileTemplates,
}))
@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer custom-modal-layer-for-select-modal',
})
export default class TransferVouchersSelectModal extends Component<
  RepaymentModalProps,
  TransferVouchersSelectModalState
> {
  state = {
    selectValue: undefined,
  }

  constructor(props) {
    super(props)
    api.invokeService('@audit:get:transferfile:template')
  }

  handleAdd = (selectValue: string) => {
    if (selectValue === 'add') {
      api.open('@audit:TransferVouchersEditModal').then((res: any) => {
        this.setState({ selectValue: res })
      })
    } else {
      this.setState({ selectValue })
    }
  }

  handleEdit = (tempData: StringAnyProps) => {
    api.open('@audit:TransferVouchersEditModal', { tempData }).then((res: any) => {
      if (res.isDelete) {
        if (res.id === this.state.selectValue) this.setState({ selectValue: undefined })
      } else {
        this.setState({ selectValue: res.id })
      }
    })
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleOk = () => {
    const { batchId } = this.props
    const { selectValue } = this.state
    const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
    const url = `${Fetch.fixOrigin(
      location.origin,
    )}/api/pay/v2/transferfile/export/excel/batchId/$${batchId}/templateId/$${selectValue}?corpId=${ekbCorpId}`
    console.log('url:', url)
    api.emit('@vendor:download', url)
    this.handleCancel()
  }

  render() {
    const { transferFileTemplates = [] } = this.props
    const { selectValue } = this.state
    const selectData = transferFileTemplates.find(v => v.id === selectValue) || {}
    const { diffBank = false, new: newExcel = true } = selectData
    const showTips = diffBank || !newExcel
    return (
      <div className={styles['transferVouchersSelectModal-wrap']}>
        <div className="transferVouchersSelectModal-header">
          <div className="flex">{i18n.get('请选择转账文件模板')}</div>
          <OutlinedTipsClose className="cross-icon" onClick={this.handleCancel} />
        </div>
        <ComplexSelect
          key="ComplexSelect"
          classNames="transferVouchersSelectModal-select"
          style={{ display: 'flex' }}
          value={selectValue}
          list={transferFileTemplates}
          handleAdd={this.handleAdd}
          handleEdit={this.handleEdit}
          emptyTitle={i18n.get('目前没有任何文件模板')}
          title={i18n.get('自定义转账模板')}
          labelInValue={false}
        />
        {showTips && (
          <div className="tipContent">
            <div>{i18n.get('温馨提示：')}</div>
            <div className="message">
              {!newExcel && <span>{i18n.get('当前模板为旧模板，请重新上传并编辑')}</span>}
              {diffBank && (
                <span>
                  {i18n.get(
                    '若支付数据中收付款账户开户行信息缺失或不正确，可能导致模板中跨行标识字段缺失',
                  )}
                </span>
              )}
            </div>
          </div>
        )}
        <div className="transferVouchersSelectModal-action">
          <Button disabled={!selectValue} className="mr-8" onClick={this.handleOk}>
            {i18n.get('确定')}
          </Button>
          <Button category="secondary" onClick={this.handleCancel}>{i18n.get('取消')}</Button>
        </div>
      </div>
    )
  }
}
