/**************************************
 * Created By LinK On 2020/6/9 17:21.
 **************************************/

import React, { Component } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Select, Button, Icon, Form, Input, Table } from 'antd'
import styles from './TransferVouchersEditModal.module.less'
import { get, set, debounce, keyBy } from 'lodash'
import {
  createTransferfileTemplate,
  deleteTransferfileTemplate,
  putTransferfileTemplate,
  getTransferfileFields,
} from '../../util/fetchUtil'
import { showModal } from '@ekuaibao/show-util'
import TransferVouchersEditModalForm from './TransferVouchersEditModalForm'

const { Option } = Select
const FormItem = Form.Item

interface TransferVouchersEditModalProps {
  layer: any
  formType: string
  tempData: any
}

interface TransferVouchersEditModalState {
  fileName: string
  dataSource: any[]
  errorText: string
  fileKey: string
  fieldMap: any
  headRowNum: number
  disabled: boolean
}

@EnhanceModal({
  footer: [],
  className: 'respond-modal-layer',
})
export default class TransferVouchersEditModal extends Component<
  TransferVouchersEditModalProps,
  TransferVouchersEditModalState
> {
  columns: any[]
  constructor(props: TransferVouchersEditModalProps) {
    super(props)
    this.columns = this.fnGetColumns()
    this.state = {
      dataSource: [],
      fileName: '',
      errorText: '',
      fileKey: '',
      fieldMap: {},
      headRowNum: 1,
      disabled: false
    }
  }

  formRef: any = React.createRef()

  async componentDidMount() {
    const res = await getTransferfileFields()
    const list = res?.items || []
    const map = keyBy(list, 'key')
    this.setState({ fieldMap: map }, () => {
      const initState = this.initStateValue()
      this.setState(initState)
    })
  }

  initStateValue = () => {
    const { tempData } = this.props
    if (tempData) {
      const { mappings, excelName, headRowNum, fileKey, new: newExcel } = tempData
      return {
        dataSource: this.fnFormatDataArrFormSelectModal(mappings),
        fileName: excelName,
        headRowNum: headRowNum || 1,
        fileKey: fileKey || '',
        disabled: !newExcel
      }
    } else {
      return {
        dataSource: [],
        fileName: '',
        headRowNum: 1,
        fileKey: '',
        disabled: true
      }
    }
  }

  onInputChange = debounce((data: any) => {
    const { value, index, type, record } = data
    const { dataSource } = this.state
    const flag = value.length > 10
    const _value = flag ? value.substring(0, 10) + '...' : value
    set(dataSource[index], 'demo', _value)
    if (record?.field === 'custom_defined_text') {
      set(dataSource[index], `settings`, null)
    }
    set(dataSource[index], `settings.${type}`, value)
    this.setState({ dataSource })
  }, 500)

  renderConfigurationColumn = (value: any, record: any, index: number) => {
    switch (record?.field) {
      case 'cross_bank':
        return (
          <>
            {i18n.get('跨行：')}
            <Input
              key="different"
              defaultValue={value?.different}
              onChange={e =>
                this.onInputChange({ value: e?.target?.value, index, type: 'different', record })
              }
              placeholder="请输入"
            />
            {i18n.get('同行：')}
            <Input
              key="same"
              defaultValue={value?.same}
              onChange={e =>
                this.onInputChange({ value: e?.target?.value, index, type: 'same', record })
              }
              placeholder="请输入"
            />
          </>
        )
      case 'custom_defined_text':
        return (
          <Input
            key="customText"
            defaultValue={value?.customText}
            onChange={e =>
              this.onInputChange({ value: e?.target?.value, index, type: 'customText', record })
            }
            placeholder="请输入"
          />
        )
      default: {
        return ''
      }
    }
  }

  fnGetColumns = () => {
    return [
      {
        title: i18n.get('上传文件表头'),
        dataIndex: 'name',
        width: 200,
      },
      {
        title: i18n.get('对应字段'),
        dataIndex: 'field',
        width: 150,
        render: (value: any, _: any, index: number) => {
          const { fieldMap } = this.state
          return (
            <Select
              value={value}
              showSearch
              key={index}
              getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
              onChange={selectValue => this.handleCellSelectChange(selectValue, index)}>
              <Option key="-" value="">
                {i18n.get('-')}
              </Option>
              {Object.keys(fieldMap).map((key, idx) => {
                return (
                  <Option key={idx} value={key}>
                    {i18n.get(fieldMap[key].label)}
                  </Option>
                )
              })}
            </Select>
          )
        },
      },
      {
        title: i18n.get('配置项'),
        dataIndex: 'settings',
        width: 150,
        render: (value: any, record: any, index: number) => {
          if (record?.field === 'cross_bank' || record?.field === 'custom_defined_text') {
            return this.renderConfigurationColumn(value, record, index)
          } else {
            return ''
          }
        },
      },
      {
        title: i18n.get('数据预览'),
        dataIndex: 'demo',
        width: 100,
      },
    ]
  }

  handleCellSelectChange = (selectValue: any, index: number) => {
    const { dataSource, fieldMap } = this.state
    const data = fieldMap[selectValue]

    set(dataSource[index], 'field', selectValue)
    set(dataSource[index], 'demo', get(data, 'demo', ''))
    this.setState({ dataSource })
  }

  fnFormatDataArrFormSelectModal = (arr = []) => {
    const { fieldMap } = this.state
    return arr.map((el: any, idx: number) => {
      let demo = ''
      if (el.toField === 'cross_bank') {
        demo = el?.settings?.same || el?.settings?.different
      } else if (el.toField === 'custom_defined_text') {
        demo = el?.settings?.customText
      } else {
        demo = get(fieldMap?.[el.toField], 'demo', '')
      }
      return {
        key: idx,
        name: el.originalField,
        field: el.toField,
        demo,
        settings: el.settings,
      }
    })
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleOk = () => {
    const { tempData, layer } = this.props
    const { form } = this.formRef.current
    form?.validateFieldsAndScroll((errors: any, values: any) => {
      if (!!errors) return
      const { dataSource } = this.state
      if (!dataSource.length) return
      const { excelName, exportType, tempName, headRowNum } = values
      const { fileKey } = this.state
      const mappings = dataSource.map(el => {
        let settings
        if (el.field === 'cross_bank') {
          settings = {
            same: el?.settings?.same || '',
            different: el?.settings?.different || '',
          }
        } else if (el.field === 'custom_defined_text') {
          settings = {
            customText: el?.settings?.customText || '',
          }
        } else {
          settings = null
        }
        return {
          originalField: el.name,
          toField: el.field,
          settings,
        }
      })
      const params = {
        name: tempName,
        excelName,
        exportType,
        headRowNum,
        mappings,
        fileKey,
      }
      if (tempData) {
        putTransferfileTemplate(tempData.id, params, () => layer.emitOk({ id: tempData.id }))
      } else {
        createTransferfileTemplate(params, (res: any) => layer.emitOk(res.value))
      }
    })
  }

  fnSetState = (state: any) => {
    this.setState(state)
  }

  renderContent = () => {
    const { tempData = {} } = this.props
    return (
      <div className="transferVouchersEditModal-content">
        <TransferVouchersEditModalForm
          {...this.state}
          fnSetState={this.fnSetState}
          tempData={tempData}
          wrappedComponentRef={this.formRef}
        />
        {this.renderTable()}
      </div>
    )
  }

  renderTable = () => {
    const { dataSource, fileName } = this.state
    if (!fileName) return null
    return (
      <>
        <FormItem label={i18n.get('表头字段对应规则')} style={{ marginBottom: 0 }} />
        <Table
          columns={this.columns}
          dataSource={dataSource}
          locale={{ emptyText: this.renderErrorText() }}
          bordered={true}
          pagination={false}
        />
      </>
    )
  }

  renderErrorText = () => {
    const { errorText } = this.state
    if (errorText === i18n.get('文件解析中')) return errorText
    return (
      <div className="transferVouchersEditModal-table-errorText">
        <svg className="icon warning-icon" aria-hidden="true">
          <use xlinkHref="#EDico-plaint-circle" />
        </svg>
        {errorText || i18n.get('文件内容解析失败，请检查表头所在行数是否正确或重新上传')}
      </div>
    )
  }

  handleDelete = () => {
    const { tempData, layer } = this.props
    const fn = () => layer.emitOk({ id: tempData.id, isDelete: true })
    showModal.confirm({
      title: i18n.get('是否确认删除'),
      okText: i18n.get('确认'),
      cancelText: i18n.get('取消'),
      onOk() {
        deleteTransferfileTemplate(tempData.id).then(() => {
          fn()
        })
      },
    })
  }

  renderFooter = () => {
    const { tempData } = this.props
    return (
      <div className="transferVouchersEditModal-action">
        <div className="active-wrap">
          <Button type="primary" className="mr-8" onClick={this.handleOk}>
            {i18n.get('保存')}
          </Button>
          <Button onClick={this.handleCancel}>{i18n.get('取消')}</Button>
        </div>
        {tempData && (
          <div className="active-wrap delete-btn" onClick={this.handleDelete}>
            {i18n.get('删除')}
          </div>
        )}
      </div>
    )
  }

  render() {
    return (
      <div className={styles['transferVouchersEditModal-wrap']}>
        <div className="transferVouchersEditModal-header">
          <div className="flex">{i18n.get('自定义转账模板')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleCancel} />
        </div>
        {this.renderContent()}
        {this.renderFooter()}
      </div>
    )
  }
}
