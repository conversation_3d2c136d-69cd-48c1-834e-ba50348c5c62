@import '~@ekuaibao/eui-styles/less/token';

.transferVouchersEditModal-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  :global {
    .transferVouchersEditModal-header {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 56px;
      padding: 0 @space-7;
      .font-size-4;
      .font-weight-3;
      .cross-icon {
        cursor: pointer;
      }
    }
    .transferVouchersEditModal-content {
      flex: 1;
      overflow-y: auto;
      padding: @space-7;
      .ekb-files-input {
        width: 100%;
      }
      .transferVouchersEditModal-table-errorText {
        display: flex;
        align-items: center;
        justify-content: center;
        .warning-icon {
          color: @color-warning;
          margin-right: @space-2;
          .font-size-2;
        }
      }
      //覆盖样式
      .ant-select {
        width: 100%;
      }
      .header-line {
        display: flex;
        >span {
          margin-top: 6px;
        }
      }
    }
    .transferVouchersEditModal-action {
      flex-shrink: 0;
      text-align: center;
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 56px;
      padding: @space-5 @space-7;
      justify-content: space-between;
      .shadow-black-3;
      .active-wrap {
        .font-size-2;
        .font-weight-2;
        color: @color-inform-2;
        &.delete-btn {
          .font-size-2;
          cursor: pointer;
          color: @color-error-2;
        }
      }
    }
  }
}