import React, { forwardRef, useImperativeHandle } from 'react'
import { Icon, Form, Input, InputNumber, Radio, Popover } from 'antd'
import { debounce } from 'lodash'
import { Fetch } from '@ekuaibao/fetch'
import { FilesUploader } from '@ekuaibao/uploader'
import { getTransferfileHeaders } from '../../util/fetchUtil'
//@ts-ignore
import XLSXIMG from '../../images/xlsxImg.jpg'
//@ts-ignore
import CSVIMG from '../../images/csvImg.jpg'
//@ts-ignore
import HEADER_EXCEL from '../../images/headerImg.jpg'

//@ts-ignore
const FormItem = Form.Item
const RadioGroup = Radio.Group
const layout: any = 'vertical'

interface FormProps {
  fnSetState: (value: any) => void
  fileKey: string
  form: any
  headRowNum: number
  tempData: any
  disabled: boolean
}

const TransferVouchersEditModalForm = (
  { fnSetState, fileKey, form, headRowNum, tempData, disabled }: FormProps,
  ref: any,
) => {
  const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
  const uploadUrl = `${Fetch.fixOrigin(
    location.origin,
  )}/api/pay/v2/transferfile/file/upload?corpId=${ekbCorpId}`

  useImperativeHandle(ref, () => ({
    form,
  }))

  const fnFormatDataArrFormImport = (arr = []) => {
    return arr.map((el, idx) => ({ key: idx, name: el, field: '', demo: '', settings: null }))
  }

  const handleOnStart = () => {
    const state = { dataSource: [], errorText: i18n.get('文件解析中') }
    fnSetState(state)
  }

  const handleChange = async (info: any) => {
    let file = info[0]
    if (file.status === 'done') {
      let { xhr } = file
      let type = xhr && xhr.getResponseHeader('content-type')
      const { response } = file
      if (type === 'application/json') {
        const utf8 = Array.from(new Uint8Array(response))
          .map(function (item) {
            return String.fromCharCode(item)
          })
          .join('')
        let str = decodeURIComponent(escape(utf8))
        try {
          const obj = JSON.parse(str)
          // 导入出错时，兼容服务器报错
          if (!obj.errorCode) {
            const fileKey = obj?.value || ''
            const data = { fileKey, headRowNum }
            const headerList = await getTransferfileHeaders(data)
            const arr = headerList?.items || []
            const state = {
              dataSource: fnFormatDataArrFormImport(arr),
              errorText: '',
              fileKey,
              disabled: false,
            }
            fnSetState(state)
          }
        } catch (err) {
          fnSetState({ errorText: i18n.get('文件解析错误') })
        }
      }
    } else if (file.status === 'error') {
      fnSetState({ dataSource: [], errorText: '' })
    }
  }

  const fnBuildData = (file: any) => {
    form?.setFieldsValue({ excelName: file.name })
    fnSetState({ fileName: file.name })
    return {
      name: file.name,
    }
  }

  const handleInputNumberChange = debounce(async (value: any) => {
    if (value) {
      const data = { fileKey, headRowNum: value }
      const headerList = await getTransferfileHeaders(data)
      const arr = headerList?.items || []
      const state = {
        dataSource: fnFormatDataArrFormImport(arr),
        errorText: '',
        headRowNum: value,
      }
      fnSetState(state)
    }
  }, 800)

  const renderContent = () => {
    const { getFieldDecorator } = form
    const { name, excelName, exportType, new: newExcel } = tempData
    const show = typeof newExcel === 'boolean' && !newExcel
    const tempNamePlaceholder = i18n.get('请输入转账模板的名称')
    const uploadPlaceholder = i18n.get('请导入xls或xlsx格式的银行转账文件模板')

    const uploaderProps = {
      action: uploadUrl,
      onChange: handleChange,
      onStart: handleOnStart,
      multiple: false,
      data: fnBuildData,
      accept: '.xlsx,.xls',
      responseType: 'arraybuffer',
    }

    return (
      <>
        <FormItem label={i18n.get('模板名称')} {...layout}>
          {getFieldDecorator('tempName', {
            initialValue: name,
            rules: [
              { required: true, whitespace: true, message: tempNamePlaceholder },
              { max: 40, message: i18n.get('模板名称不能超过40个字') },
            ],
          })(<Input placeholder={tempNamePlaceholder} />)}
        </FormItem>
        <FormItem label={i18n.get('文件模板')} {...layout}>
          <FilesUploader {...(uploaderProps as any)}>
            {getFieldDecorator('excelName', {
              initialValue: excelName,
              rules: [{ required: true, whitespace: true, message: uploadPlaceholder }],
            })(<Input placeholder={uploadPlaceholder} />)}
          </FilesUploader>
        </FormItem>
        <div className="header-line">
          <span className="mr-4">{i18n.get('表头为第')}</span>
          <FormItem {...layout}>
            {getFieldDecorator('headRowNum', {
              initialValue: headRowNum || 1,
            })(
              <InputNumber
                min={1}
                precision={0}
                onChange={handleInputNumberChange}
                disabled={disabled}
              />,
            )}
          </FormItem>
          <span className="ml-4">
            {i18n.get('行')}
            <Popover
              placement="bottomRight"
              content={<img style={{ width: 600 }} src={HEADER_EXCEL} />}>
              <Icon type="question-circle-o" style={{ marginLeft: 4 }} />
            </Popover>
          </span>
        </div>
        <FormItem label={i18n.get('导出格式')} {...layout}>
          {getFieldDecorator('exportType', {
            initialValue: exportType || 'xlsx',
          })(
            <RadioGroup value={'xlsx'}>
              <Radio value={'xlsx'}>
                .xlsx
                <Popover placement="bottomRight" content={<img src={XLSXIMG} />}>
                  <Icon type="question-circle-o" style={{ marginLeft: 4 }} />
                </Popover>
              </Radio>
              {!show && (
                <Radio value={'csv'}>
                  .csv
                  <Popover placement="bottomRight" content={<img src={CSVIMG} />}>
                    <Icon type="question-circle-o" style={{ marginLeft: 4 }} />
                  </Popover>
                </Radio>
              )}
            </RadioGroup>,
          )}
        </FormItem>
      </>
    )
  }

  return renderContent()
}

export default Form.create()(forwardRef(TransferVouchersEditModalForm))
