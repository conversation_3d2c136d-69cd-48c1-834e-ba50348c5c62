@import '~@ekuaibao/web-theme-variables/styles/default';
@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/eui-styles/less/token.less';

.reject-bill-modal {
  display: flex;
  height: 100%;
  flex: 1;
  flex-direction: column;
  
  .reject-bill-wrapper {
    flex: 1;
    overflow-x: hidden;
    flex-direction: column;
    width: 100%;
    overflow-y: auto;
    margin-bottom: 50px;
  
    .flow-config-con {
      padding: 0 0 @space-6 @space-6;
    }
    .money_wrapper_line {
      height: @space-4;
      background: var(--eui-line-divider-module);
    }
    .reject-node-choose {
      .font-size-2;
      color: var(--eui-text-title);
      padding-bottom: @space-6;
    }
    .turn-down-radio {
      margin: 0px @space-6;
      .radio-title {
        .font-size-2;
        color: var(--eui-text-title);
      }
    }
    .content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 35px 0px;
      .amount {
        font-size: 16px;
        font-weight: 500;
        color: var(--brand-base);
        margin-bottom: 5px;
      }
      .remark {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        background-color: @input-bg;
        margin-bottom: 15px;
        .title {
          margin-right: 10px;
        }
        .method-line {
          width: 320px;
          margin-top: 5px;
        }
      }
      .info {
        font-size: 12px;
        color: #9c9c9c;
        margin-left: 10px;
      }
    }
    .attachment-wrapper {
      width: 100%;
      padding-left: 16px;
      .ant-btn {
        border: none;
        margin-left: -15px;
        background-color: #ffffff;
      }
      &:hover {
        .ant-btn {
          color: var(--brand-base);
        }
      }
    }
    .flow-allow-modal-footer {
      display: flex;
      justify-content: flex-end;
      width: 100%;
      height: 52px;
      padding: 10px 16px 0 16px;
      .signature-container {
        display: flex;
        flex: 1;
        .signature-title {
          margin-top: 5px;
          margin-right: 8px;
          height: 14px;
          font-size: 14px;
          line-height: 1;
          color: #54595b;
        }
      }
      .modal-footer-button {
        display: flex;
        flex-direction: row;
        .btn-ml {
          margin-left: 17px;
        }
      }
    }
    .justify-content-between {
      justify-content: space-between;
    }
    .justify-content-end {
      justify-content: flex-end;
    }
  }
  .modal-footer-start{
    .danger{
      background-color: var(--eui-function-danger-500);
    }
    justify-content: space-between;
  }
}
