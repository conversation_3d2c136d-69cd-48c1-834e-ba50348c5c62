@import '~@ekuaibao/web-theme-variables/styles/colors.less';

.pay-tips-modal-wrapper {
  :global {
    .pay-tips-content {
      padding: 14px 36px 80px;

      .tips-content {
        display: flex;
        background: rgba(0,107,224,0.10);
        border: 1px solid rgba(0,107,224,0.10);
        border-radius: 5px;
        padding: 8px 24px 8px 6px;
        margin-bottom: 24px;

        .pay-tips-header {
          >img {
            margin-right: 8px;
            width: 16px;
            height: 16px;
          }
        }

        .pay-tips-description {
          font-size: 14px;
          font-weight: 400;
          color: rgba(29,43,61,0.75);
          line-height: 22px;
        }
      }

      .tips-check-box {
        font-size: 14px;
        margin-bottom: 6px;
      }

      .check-description {
        font-size: 14px;
        font-weight: 400;
        color: rgba(29,43,61,0.50);
        line-height: 22px;
      }
      .check-warning {
        font-size: 14px;
        color: #faad14;
      }
    }
  }
}

.modal-footer {
  width: 100%;
  position: absolute;
  left: -8px;
  bottom: 0;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 56px;
  padding: 0 32px;
  :global{
    .btn-ok {
      margin-right: 8px;
    }
  }
}

.pay-tips-wrapper {
  :global{
    .ant-modal-body {
      padding: 0;
    }
    .ant-modal-footer {
      border: none;
      padding: 0;
    }
  }
}
