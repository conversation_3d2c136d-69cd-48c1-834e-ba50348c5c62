@import '~@ekuaibao/eui-styles/less/token';

.hsbcTableFillingModal-wrap {
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  margin-bottom: 56px;
  .hsbcTableFillingModal-menu{
    width: 200px;
    height: 100%;
    position: absolute;
    left: 0;
    top: 60px;
    border-right: 1px solid #e6e6e6;
    a {
      display: block;
    }
    .linkdiv {
      width: 100%;
      height: 46px;
      position: relative;
    }
    .linkmask {
      width: 100%;
      height: 46px;
      position: absolute;
      z-index: 10;
      cursor:pointer;
    }
    .ant-anchor-wrapper {
      margin-left: 0;
      .ant-anchor-ink {
        display: none;
      }
      .ant-anchor-link {
        padding: 0 0 0 18px;
        height: 46px;
        font-size: 16px;
        font-weight: bold;
        line-height: 46px;
        position: absolute;
        z-index: 5;
        .ant-anchor-link-title {
          text-decoration: none;
        }
      }
    }
  }
  .hsbcTableFillingModal-title {
    font-size: 16px;
    font-weight: bold;
    line-height: 24px;
    padding: @space-6 @space-7;
    margin-bottom: 0;
    margin-left: 200px;
  }
  .hsbcTableFillingModal-content {
    padding: 0 @space-7;
    margin-left: 200px;
    .hsbcTableFillingModal-inputInline {
      align-items: center;
      display: flex;
      justify-content: flex-start;
      margin-bottom: @space-7;
      .font-size-2;

      &.dis-if {
        display: inline-flex;
      }

      .ant-form-item {
        display: inline-block;
        margin: 0;
        input {
          width: 100px;
          margin: 0 @space-4;
        }
      }

      &.smallInput {
        .ant-form-item {
          input {
            width: 56px;
          }
        }
      }
    }
    .hsbcTableFillingModal-info {
      .font-size-2;
    }
    .ant-form-item-label {
      text-align: left;
      white-space: normal;
    }
  }
  .hsbcTableFillingModal-footer {
    background: @color-white-1;
    width: 100%;
    height: 56px;
    padding: 12px 24px;
    border-top: none;
    position: absolute;
    left: 0;
    bottom: 0;
    .shadow-black-3;
    .font-size-2;
    .btn-ok {
      margin-right: @space-4;
    }
  }
}

.missInfoModal-title {
  .font-size-3;
  .font-weight-3;
}
.missInfoModal-wrapper {
  margin-top: @space-6;
  height: 200px;
  overflow: auto;
  p {
    .font-size-2;
  }
}