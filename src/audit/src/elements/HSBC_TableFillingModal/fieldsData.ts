export interface SelectField {
  label: string
  name: string
  type: string
  required?: boolean
  options: {
    label?: string
    valueKey: number | string
  }[]
}

export const getHsbcFormItemsByType = (type: string) => {
  switch (type) {
    case 'BOP_TERRITORY': {
      return getBopTerritory()
    }
    case 'BOP_ABROAD': {
      return getBopAbroad()
    }
    case 'CROSS_BORDER_RMB': {
      return getCrossBorderRmb()
    }
    case 'PP_CNY_DOMESTIC': {
      return getPP_CNY_Domestic()
    }
    case 'PP_FCY': {
      return getPP_FCY()
    }
    case 'PP_CNY_CROSS_BORDER': {
      return getPP_CNY_Crossborder()
    }

    default: {
      return []
    }
  }
}

// 获取KA-外币账户打开后需要展示的字段
export const getBopBaseFormItems = () => [
  {
    label: '创建日期',
    name: 'date',
    type: 'date',
    disabled: true,
  },
  {
    label: '汇款方式',
    name: 'exchangeMethod',
    type: 'select',
    options: [
      { label: '电汇 T/T', valueKey: '0' },
      { label: '票汇 D/D', valueKey: '1' },
      { label: '信汇 M/T', valueKey: '2' },
    ],
  },

  {
    label: '发电等级',
    name: 'priority',
    type: 'select',
    options: [
      { label: '普通 Normal', valueKey: '0' },
      { label: '加急 Urgent', valueKey: '1' },
    ],
  },

  {
    label: '汇款币种',
    name: 'amountCurrency',
    type: 'input',
    disabled: true,
  },

  {
    label: '汇款金额',
    name: 'amountInfoMoney',
    type: 'inputNumber',
    disabled: true,
  },

  {
    label: '汇款币种大写',
    name: 'amountInfoCurrencyCapital',
    type: 'input',
    disabled: true,
  },

  {
    label: '汇款金额大写',
    name: 'amountInfoMoneyCapital',
    type: 'input',
    disabled: true,
  },

  {
    label: '现汇金额 Amount in FX',
    name: 'amountInFxMoney',
    type: 'input',
    max: 140,
  },
  {
    label: '现汇帐号 Account No./Credit Card No. for FX',
    name: 'amountInFxAccountNo',
    type: 'input',
    max: 140,
  },
  {
    label: '购汇金额 Amount of Purchase',
    name: 'amountOfPurchaseMoney',
    type: 'input',
    max: 140,
  },
  {
    label: '购汇帐号 Account No./Credit Card No. for Purchase',
    name: 'amountOfPurchaseAccountNo',
    type: 'input',
    max: 140,
  },
  {
    label: '其他金额 Amount of Others',
    name: 'amountofOthersMoney',
    type: 'input',
    max: 140,
  },
  {
    label: '其他金额对应帐号 Account No./Credit Card No. for Others',
    name: 'amountofOthersAccountNo',
    type: 'input',
    max: 140,
  },

  {
    label: "汇款人帐号 Remitter's Account No.",
    name: 'remitterAccountNo',
    type: 'input',
    max: 140,
    disabled: true,
  },

  {
    label: "汇款人帐号名称 Remitter's Account Name.",
    name: 'remitterAccountName',
    type: 'input',
    disabled: true,
  },

  {
    label: "汇款人帐号英文名称 Remitter's Account Name in English",
    name: 'remitterAccountNameEn',
    type: 'input',
    disabled: true,
  },

  {
    label: "汇款人地址-国家 Remitter's Address - Country",
    name: 'remitterAccountCountry',
    type: 'input',
    disabled: true,
  },

  {
    label: "汇款人地址-国家（英文） Remitter's Address - Country in English",
    name: 'remitterAccountCountryEn',
    type: 'input',
    disabled: true,
  },

  {
    label: "汇款人地址-城市 Remitter's Address - City",
    name: 'remitterAccountCity',
    type: 'input',
    disabled: true,
  },

  {
    label: "汇款人地址-城市（英文） Remitter's Address - City in English",
    name: 'remitterAccountCityEn',
    type: 'input',
    disabled: true,
  },

  {
    label: "汇款人地址-镇/区 Remitter's Address - Town/District",
    name: 'remitterAccountTown',
    type: 'input',
    disabled: true,
  },

  {
    label: "汇款人地址-镇/区（英文） Remitter's Address - Town/District in English",
    name: 'remitterAccountTownEn',
    type: 'input',
    disabled: true,
  },

  {
    label: "汇款人地址-街道 Remitter's Address - Street",
    name: 'remitterAccountStreet',
    type: 'input',
    disabled: true,
  },

  {
    label: "汇款人地址-街道（英文） Remitter's Address - Street in English",
    name: 'remitterAccountStreetEn',
    type: 'input',
    disabled: true,
  },

  {
    label: "汇款人邮编 Remitter's Address - Zip Code",
    name: 'remitterAccountZipCode',
    type: 'input',
    disabled: true,
  },

  {
    label: '汇款银行所在国家',
    name: 'remitterBankCountry',
    type: 'input',
    disabled: true,
  },

  {
    label: '汇款银行所在国家（英文）',
    name: 'remitterBankCountryEn',
    type: 'input',
    disabled: true,
  },

  {
    label: '汇款类型',
    name: 'remitterType',
    type: 'select',
    disabled: true,
    options: [
      { label: '中国非居民个人 Non-Resident Individual', valueKey: '0' },
      { label: '中国居民个人 Resident Individual', valueKey: '1' },
    ],
  },

  {
    label: '组织机构代码 Unit Code',
    name: 'remitterUnitCode',
    type: 'input',
  },

  {
    label: '个人身份证号码 Individual ID NO.',
    name: 'remitterId',
    type: 'input',
  },

  {
    label: "收款人帐号 Bene's A/C No.",
    name: 'beneficiaryAccountNo',
    type: 'input',
    disabled: true,
  },

  {
    label: "收款人帐号名称 Bene's Account Name",
    name: 'beneficiaryAccountName',
    type: 'input',
    disabled: true,
  },

  {
    label: "收款人帐号英文名称 Bene's Account Name in English",
    name: 'beneficiaryAccountNameEn',
    type: 'input',
    disabled: true,
  },

  {
    label: "收款人地址-国家 Bene's Address - Country",
    name: 'beneficiaryAccountCountry',
    type: 'input',
    disabled: true,
  },

  {
    label: "收款人地址-国家（英文） Bene's Address - Country in English",
    name: 'beneficiaryAccountCountryEn',
    type: 'input',
    disabled: true,
  },

  {
    label: "收款人地址-城市 Bene's Address - City",
    name: 'beneficiaryAccountCity',
    type: 'input',
    disabled: true,
  },

  {
    label: "收款人地址-城市（英文） Bene's Address - City in English",
    name: 'beneficiaryAccountCityEn',
    type: 'input',
    disabled: true,
  },

  {
    label: "收款人地址-镇/区 Bene's Address - Town/District",
    name: 'beneficiaryAccountTown',
    type: 'input',
    disabled: true,
  },

  {
    label: "收款人地址-镇/区（英文） Bene's Address - Town/District in English",
    name: 'beneficiaryAccountTownEn',
    type: 'input',
    disabled: true,
  },

  {
    label: "收款人地址-街道 Bene's Address - Street",
    name: 'beneficiaryAccountStreet',
    type: 'input',
    disabled: true,
  },

  {
    label: "收款人地址-街道（英文） Bene's Address - Street in English",
    name: 'beneficiaryAccountStreetEn',
    type: 'input',
    disabled: true,
  },

  {
    label: "收款人邮编 Bene's Address - Zip Code",
    name: 'beneficiaryAccountZipCode',
    type: 'input',
    disabled: true,
  },

  {
    label: '收款人开户行',
    name: 'beneficiaryBankName',
    type: 'input',
    disabled: true,
  },

  {
    label: '收款人开户行（英文）',
    name: 'beneficiaryBankNameEn',
    type: 'input',
    disabled: true,
  },

  {
    label: '收款银行所在国家（英文）',
    name: 'beneficiaryBankCountryEn',
    type: 'input',
    disabled: true,
  },

  {
    label: '汇款附言 Remittance Information',
    name: 'remittanceInformation',
    type: 'input',
    max: 140,
  },

  {
    label: "国内外费用承担All Bank's Charges If Any Are To Be Borne By",
    name: 'bearExpenses',
    type: 'select',
    options: [
      { label: '汇款人 OUR', valueKey: '0' },
      { label: '收款人 BEN', valueKey: '1' },
      { label: '共同 SHA', valueKey: '2' },
    ],
  },

  {
    label: '收款人常驻国家（地区）名称 Resident Country/Region Name',
    name: 'beneficiaryResidentCountryName',
    type: 'input',
    disabled: true,
  },

  {
    label: '收款人常驻国家（地区）代码 Resident Country/Region  Code',
    name: 'beneficiaryResidentCountryCode',
    type: 'input',
    disabled: true,
  }
]

// 【BOP境内】相关字段
const getBopTerritory = () => {
  const baseArr: any[] = getBopBaseFormItems()
  return baseArr.concat([
    {
      label: '本笔款项是否为保税货物项下付款',
      name: 'paymentUnderBondedGoods',
      type: 'select',
      options: [
        { label: '否', valueKey: '0' },
        { label: '是', valueKey: '1' },
      ],
    },
    paymentUnderBondedGoodsType,
    bopTransacCode,
    {
      label: '付汇性质',
      name: 'paymentNature',
      type: 'select',
      options: [
        { label: '保税区', valueKey: '0' },
        { label: '出口加工区', valueKey: '1' },
        { label: '钻石交易所', valueKey: '2' },
        { label: '其他特殊经济区域', valueKey: '3' },
        { label: '深加工结转', valueKey: '4' },
        { label: '其他', valueKey: '5' },
      ],
    },
    {
      label: '合同号',
      name: 'contractNo',
      type: 'input',
    },
    {
      label: '发票号',
      name: 'invoiceNo',
      type: 'input',
    },
  ])
}

// 【BOP境外】相关字段
const getBopAbroad = () => {
  const baseArr: any[] = getBopBaseFormItems()
  return baseArr.concat([
    paymentUnderBondedGoodsType,
    bopTransacCode,
    {
      label: '交易附言',
      name: 'transactionRemark',
      type: 'input',
      max: 140,
    }
  ])
}

// 【跨境人民币付款说明】相关字段
const getCrossBorderRmb = () => [
  {
    label: '付款日期',
    name: 'date',
    type: 'date',
    disabledDateBefore: true
  },

  {
    label: '付款企业名称',
    name: 'corporationName',
    type: 'input',
    max: 100,
  },

  {
    label: '组织机构代码（身份证件号）',
    name: 'organizationCode',
    type: 'input',
    max: 100,
  },

  {
    labelFront: '付款金额合计',
    labelAfter: '元',
    name: 'paymentAmountTotal',
    type: 'input_inline',
    disabled: true,
  },

  {
    labelFront: '货物贸易金额',
    labelAfter: '元',
    name: 'goodsTradeAmount',
    type: 'input_inline',
    max: 30,
  },

  {
    labelFront: '预付货款项下',
    labelAfter: '元，',
    name: 'prepayment',
    className:'dis-if',
    type: 'input_inline',
    max: 30,
  },

  {
    labelFront: '占合同金额比例',
    labelAfter: '%，',
    name: 'proportionAmount',
    type: 'input_inline',
    className:'dis-if smallInput',
    max: 30,
  },

  {
    labelFront: '预计',
    labelAfter: '天后报关（结账期）',
    name: 'expectedDays',
    type: 'input_inline',
    className:'dis-if smallInput',
    max: 5,
  },

  {
    label: '已报关：',
    type: 'info',
  },

  {
    label: '报关经营单位名称',
    name: 'declaredCustomsCompany',
    type: 'input',
    className:'ml-24',
    max: 100,
  },

  {
    label: '组织机构代码',
    name: 'declaredCustomsCompanyCode',
    type: 'input',
    className:'ml-24',
    max: 100,
  },

  {
    label: '报关类型',
    name: 'declaredCustomsCurrency',
    type: 'select',
    className:'ml-24',
    options: [
      { label: '人民币报关', valueKey: '0' },
      { label: '外币报关', valueKey: '1' },
    ],
  },

  {
    labelFront: '一般贸易',
    labelAfter: '元',
    name: 'declaredCustomsCommonAmount',
    className:'ml-24',
    type: 'input_inline',
    max: 30,
  },

  {
    labelFront: '其他贸易',
    labelAfter: '元',
    name: 'declaredCustomsOtherAmount',
    className:'ml-24',
    type: 'input_inline',
    max: 30,
  },

  {
    labelFront: '进料加工',
    labelAfter: '元',
    name: 'declaredCustomsFeedAmount',
    className:'ml-24',
    type: 'input_inline',
    max: 30,
  },

  {
    labelFront: '边境贸易',
    labelAfter: '元',
    name: 'declaredCustomsBorderAmount',
    className:'ml-24',
    type: 'input_inline',
    max: 30,
  },

  {
    label: '无货物报关：',
    type: 'info',
  },

  {
    labelFront: '海关特殊监管区域及保税监管场所进出境物流货物',
    labelAfter: '元',
    name: 'noEffectsDeclaredLogisticsGoodsAmount',
    type: 'input_inline',
    className:'ml-24',
    max: 30,
  },
  {
    labelFront: '离岸转手买卖',
    labelAfter: '元',
    name: 'noEffectsDeclaredOffshoreTransferAmount',
    type: 'input_inline',
    className:'ml-24',
    max: 30,
  },

  {
    labelFront: '其他',
    labelAfter: '元',
    name: 'noEffectsDeclaredOtherAmount',
    type: 'input_inline',
    className:'ml-24',
    max: 30,
  },

  {
    label: '服务贸易：',
    type: 'info',
  },

  {
    labelFront: '金额',
    labelAfter: '元',
    name: 'serviceTradeAmount',
    type: 'input_inline',
    className:'ml-24',
    max: 30,
  },

  {
    label: '国际收支编码及交易附言',
    name: 'serviceTradePostscript',
    type: 'input',
    className:'ml-24',
    max: 140,
  },

  {
    label: '投资收益：',
    type: 'info',
  },

  {
    labelFront: '金额',
    labelAfter: '元',
    name: 'incomeInvestmentAmount',
    type: 'input_inline',
    className:'ml-24',
    max: 30,
  },

  {
    label: '国际收支编码及交易附言',
    name: 'incomeInvestmentPostscript',
    type: 'input',
    className:'ml-24',
    max: 140,
  },

  {
    label: '批准证书号（仅汇出直投收益时填报）',
    name: 'incomeInvestmentApprovalCertificateNo',
    type: 'input',
    className:'ml-24',
    max: 100,
  },

  {
    label: '经常转移：',
    type: 'info',
  },

  {
    labelFront: '金额',
    labelAfter: '元',
    name: 'frequentTransferAmount',
    type: 'input_inline',
    className:'ml-24',
    max: 30,
  },

  {
    label: '国际收支编码及交易附言',
    name: 'frequentTransferPostscript',
    type: 'input',
    className:'ml-24',
    max: 140,
  },

  {
    label: '资本账户：',
    type: 'info',
  },

  {
    labelFront: '金额',
    labelAfter: '元',
    name: 'capitalAccountAmount',
    type: 'input_inline',
    className:'ml-24',
    max: 30,
  },

  {
    label: '国际收支编码及交易附言',
    name: 'capitalAccountPostscript',
    type: 'input',
    className:'ml-24',
    max: 140,
  },

  {
    label: '直接投资：',
    type: 'info',
  },

  {
    labelFront: '金额',
    labelAfter: '元',
    name: 'directInvestmentAmount',
    type: 'input_inline',
    className:'ml-24',
    max: 30,
  },

  {
    label: '国际收支编码及交易附言',
    name: 'directInvestmentPostscript',
    type: 'input',
    className:'ml-24',
    max: 140,
  },

  {
    label: '批准证书号',
    name: 'directInvestmentApprovalCertificateNo',
    type: 'input',
    className:'ml-24',
    max: 100,
  },

  {
    label: '证券投资：',
    type: 'info',
  },

  {
    labelFront: '金额',
    labelAfter: '元',
    name: 'portfolioInvestmentAmount',
    type: 'input_inline',
    className:'ml-24',
    max: 30,
  },

  {
    label: '国际收支编码及交易附言',
    name: 'portfolioInvestmentPostscript',
    type: 'input',
    className:'ml-24',
    max: 140,
  },

  {
    label: '其他投资：',
    type: 'info',
  },

  {
    labelFront: '金额：',
    labelAfter: '元',
    name: 'otherInvestmentAmount',
    type: 'input_inline',
    className:'ml-24',
    max: 30,
  },

  {
    label: '国际收支编码及交易附言',
    name: 'otherInvestmentPostscript',
    type: 'input',
    className:'ml-24',
    max: 140,
  },

  {
    label: '备注：',
    type: 'info',
  },

  {
    label: '收款人国家/地区',
    name: 'payeeAddress',
    type: 'input',
    className:'ml-24',
    max: 140,
  },

  {
    label: '原交易日期（仅适用于退汇，必填）',
    name: 'field1Date',
    className:'ml-24',
    type: 'date',
  },

  {
    label: '原交易金额（仅适用于退汇，必填）',
    name: 'field1Amount',
    type: 'input',
    className:'ml-24',
    max: 30,
  },

  {
    label: '原交易申报号码（仅适用于退汇，必填）',
    name: 'field1No',
    type: 'input',
    className:'ml-24',
    max: 30,
  },

  {
    label: '转口贸易项下(应在同一家银行办理，且不能交叉币种结算)，请提供如下信息（必填）',
    name: 'field2',
    type: 'select',
    className:'ml-24',
    options: [
      { label: '先收后支', valueKey: '0' },
      { label: '先支后收', valueKey: '1' },
    ],
  },

  {
    label: '前一手收入对应的国际收支申报号码',
    name: 'field2No',
    type: 'input',
    className:'ml-24',
    max: 30,
  },

  // 偿还境外借款利息，请提供如下信息（必填）：

  {
    label: '偿还境外借款利息，请提供如下信息（必填）',
    type: 'info',
  },
  {
    label: '借款本金',
    name: 'field3Loan',
    type: 'input',
    className:'ml-24',
    max: 30,
  },

  {
    label: '借款利率',
    name: 'field3LoanRate',
    type: 'input',
    className:'ml-24',
    max: 10,
  },

  {
    label: '利息天数',
    name: 'field3InterestDays',
    type: 'input',
    className:'ml-24',
    max: 10,
  },

  {
    label: '计息基数',
    name: 'field3InterestBase',
    type: 'input',
    className:'ml-24',
    max: 30,
  },

  {
    label: '税费（境内代缴）金额',
    name: 'field3TaxAmount',
    type: 'input',
    className:'ml-24',
    max: 30,
  },

  {
    label: '填报人',
    name: 'filledBy',
    type: 'input',
    className:'ml-24',
    max: 30,
  },

  {
    label: '联系电话',
    name: 'filledPhone',
    type: 'input',
    className:'ml-24',
    max: 20,
  },
]

// BOP文件补充字段：PP_CNY_Domestic
const getPP_CNY_Domestic = () => [paymentTime]

// BOP文件补充字段：PP_CNY_Crossborder
const getPP_CNY_Crossborder = () => [
  paymentTime,
  transactionProvider,
  paymentPurpose,
  transactionRemarkEn,
]

// BOP文件补充字段：PP_FCY
const getPP_FCY = () => [paymentTime, transactionProvider, transactionRemarkEn]

// 支付时间
const paymentTime = {
  label: '支付时间（仅可选周一-周五的时间且非法定节假日，最长不超过付款指令生成时间45天）',
  name: 'paymentTime',
  type: 'date',
  required: true,
  disabledDateBefore: true
}

// 交易附言（英文）
const transactionRemarkEn = {
  label: '交易附言（英文）',
  name: 'transactionRemarkEn',
  type: 'input',
  max: 140,
  required: true,
  onlyEn: true,
}

// 支付用途
const paymentPurpose: SelectField = {
  label: '支付用途',
  name: 'paymentPurpose',
  type: 'select',
  required: true,
  options: [
    { valueKey: '/BUSINESS/CAPITAL TRF' },
    { valueKey: '/BUSINESS/CHARITY DONATION' },
    { valueKey: '/BUSINESS/CURRENT ACC TXN' },
    { valueKey: '/BUSINESS/GOODS TRADE' },
    { valueKey: '/BUSINESS/SERVICE TRADE' },
  ],
}

// 交易手续费提供方
const transactionProvider: SelectField = {
  label: '交易手续费提供方',
  name: 'transactionProvider',
  type: 'select',
  required: true,
  options: [
    {
      label: '汇款人 OUR',
      valueKey: 'DEBT',
    },
    {
      label: '收款人 BEN',
      valueKey: 'CRED',
    },
    {
      label: '共同 SHA',
      valueKey: 'SHAR',
    },
  ],
}

// 涉外收支交易码
const tradeCodes: { valueKey: string; label: string }[] = [
  { valueKey: '121010', label: '121010-一般贸易' },
  { valueKey: '121020', label: '121020-进料加工贸易' },
  { valueKey: '121030', label: '121030-海关特殊监管区域及保税监管场所进出境物流货物' },
  { valueKey: '121040', label: '121040-非货币黄金进出口' },
  { valueKey: '121050', label: '121050-金融性租赁贸易' },
  { valueKey: '121060', label: '121060-提供国家间、国际组织无偿援助和赠送的物资' },
  { valueKey: '121070', label: '121070-国内机构或个人提供无偿援助和赠送的物资' },
  { valueKey: '121080', label: '121080-边境小额贸易' },
  { valueKey: '121100', label: '121100-外商投资企业作为投资进口设备、物品的支出' },
  { valueKey: '121110', label: '121110-加工贸易进口设备' },
  { valueKey: '121990', label: '121990-其他纳入海关统计的货物贸易' },
  { valueKey: '122010', label: '122010-离岸转手买卖' },
  { valueKey: '122020', label: '122020-未纳入海关统计的非货币黄金' },
  { valueKey: '122030', label: '122030-未纳入海关统计的网络购物' },
  { valueKey: '122990', label: '122990-其他未纳入海关统计的货物贸易' },
  { valueKey: '221000', label: '221000-出料加工工缴费支出' },
  { valueKey: '222011', label: '222011-涉及我国出口的海洋货运服务' },
  { valueKey: '222012', label: '222012-涉及我国进口的海洋货运服务' },
  { valueKey: '222013', label: '222013-不涉及我国进出口的海洋货运服务' },
  { valueKey: '222014', label: '222014-海运客运' },
  { valueKey: '222019', label: '222019-其他海运服务' },
  { valueKey: '222021', label: '222021-涉及我国出口的空中货运服务' },
  { valueKey: '222022', label: '222022-涉及我国进口的空中货运服务' },
  { valueKey: '222023', label: '222023-不涉及我国进出口的空中货运服务' },
  { valueKey: '222024', label: '222024-空运客运' },
  { valueKey: '222029', label: '222029-其他空运服务' },
  { valueKey: '222031', label: '222031-涉及我国出口的其他运输方式货运服务' },
  { valueKey: '222032', label: '222032-涉及我国进口的其他运输方式货运服务' },
  { valueKey: '222033', label: '222033-不涉及我国进出口的其他运输方式货运服务' },
  { valueKey: '222034', label: '222034-其他运输方式客运' },
  { valueKey: '222039', label: '222039-其他运输方式的其他服务' },
  { valueKey: '222040', label: '222040-邮政及寄递服务' },
  { valueKey: '223010', label: '223010-公务及商务旅行' },
  { valueKey: '223021', label: '223021-就医及健康相关旅行' },
  { valueKey: '223022', label: '223022-留学及教育相关旅行（一年以上）' },
  { valueKey: '223023', label: '223023-留学及教育相关旅行（一年及一年以下）' },
  { valueKey: '223029', label: '223029-其他私人旅行' },
  { valueKey: '224010', label: '224010-境外建设' },
  { valueKey: '224020', label: '224020-境内建设' },
  { valueKey: '225010', label: '225010-寿险' },
  { valueKey: '225021', label: '225021-为我国出口提供的保险' },
  { valueKey: '225022', label: '225022-为我国进口提供的保险' },
  { valueKey: '225029', label: '225029-其他非寿险' },
  { valueKey: '225030', label: '225030-再保险' },
  { valueKey: '225040', label: '225040-标准化担保服务' },
  { valueKey: '225050', label: '225050-保险辅助服务' },
  { valueKey: '226000', label: '226000-金融服务' },
  { valueKey: '227010', label: '227010-电信服务' },
  { valueKey: '227020', label: '227020-计算机服务' },
  { valueKey: '227030', label: '227030-信息服务' },
  { valueKey: '228010', label: '228010-研发成果转让费及委托研发' },
  { valueKey: '228021', label: '228021-法律服务' },
  { valueKey: '228022', label: '228022-会计服务' },
  { valueKey: '228023', label: '228023-管理咨询和公共关系服务' },
  { valueKey: '228024', label: '228024-广告服务' },
  { valueKey: '228025', label: '228025-展会服务' },
  { valueKey: '228026', label: '228026-市场调查、民意测验服务' },
  { valueKey: '228031', label: '228031-建筑、工程技术服务' },
  { valueKey: '228032', label: '228032-废物处理和防止污染服务' },
  { valueKey: '228033', label: '228033-农业和采矿服务' },
  { valueKey: '228039', label: '228039-其他技术服务' },
  { valueKey: '228040', label: '228040-经营性租赁服务' },
  { valueKey: '228050', label: '228050-货物或服务交易佣金及相关服务' },
  { valueKey: '228060', label: '228060-办事处、代表处等办公经费' },
  { valueKey: '228990', label: '228990-上述未提及的其他商业服务' },
  { valueKey: '229010', label: '229010-视听和相关服务' },
  { valueKey: '229020', label: '229020-教育服务' },
  { valueKey: '229030', label: '229030-医疗服务' },
  { valueKey: '229990', label: '229990-其他文化和娱乐服务' },
  { valueKey: '230000', label: '230000-别处未涵盖的维护和维修服务' },
  { valueKey: '231010', label: '231010-特许和商标使用费' },
  { valueKey: '231020', label: '231020-研发成果使用费' },
  { valueKey: '231030', label: '231030-复制或分销计算机软件许可费' },
  { valueKey: '231040', label: '231040-复制或分销视听及相关产品许可费' },
  { valueKey: '231990', label: '231990-其他知识产权使用费' },
  { valueKey: '232000', label: '232000-别处未涵盖的政府货物和服务' },
  { valueKey: '321000', label: '321000-职工报酬（工资、薪金和福利）' },
  { valueKey: '322011', label: '322011-向境外母公司支付的股息、红利或利润' },
  { valueKey: '322012', label: '322012-向境外子公司等支付的股息、红利（持有本机构10%以下股份）' },
  { valueKey: '322013', label: '322013-向境外联属公司支付的股息、红利（持股10%以下的关联机构）' },
  { valueKey: '322014', label: '322014-向境内建筑物的非居民所有者支付的租金' },
  { valueKey: '322021', label: '322021-向境外母公司支付的利息' },
  { valueKey: '322022', label: '322022-向境外子公司等支付的利息（持有本机构10%以下股权）' },
  { valueKey: '322023', label: '322023-向境外联属公司支付的利息（持股10%以下的关联机构）' },
  { valueKey: '322031', label: '322031-股票投资的股息、红利' },
  { valueKey: '322032', label: '322032-投资基金份额的股息、红利' },
  { valueKey: '322033', label: '322033-短期债券利息' },
  { valueKey: '322034', label: '322034-中长期债券利息' },
  { valueKey: '322041', label: '322041-存贷款利息' },
  { valueKey: '322042', label: '322042-向保单持有者支付的红利和利息' },
  {
    valueKey: '322043',
    label: '322043-向准公司（持股10%以下）和国际组织份额持有者（因份额投资）支付的红利、收益',
  },
  { valueKey: '322049', label: '322049-其他的其他投资收益' },
  { valueKey: '323010', label: '323010-使用自然资源的租金' },
  { valueKey: '323020', label: '323020-产品和生产的税收及补贴' },
  { valueKey: '421010', label: '421010-个人间捐赠及无偿援助' },
  { valueKey: '421020', label: '421020-政府、国际组织间捐赠及无偿援助（与固定资产无关）' },
  { valueKey: '421990', label: '421990-其他捐赠及无偿援助（与固定资产无关）' },
  { valueKey: '422000', label: '422000-非寿险保险赔偿' },
  { valueKey: '423010', label: '423010-社保缴款' },
  { valueKey: '423020', label: '423020-社保返还的福利' },
  { valueKey: '424000', label: '424000-其他二次收入（经常转移）' },
  { valueKey: '521010', label: '521010-债务减免' },
  { valueKey: '521020', label: '521020-与固定资产有关的捐赠及无偿援助' },
  { valueKey: '521030', label: '521030-移民转移' },
  { valueKey: '521990', label: '521990-其他资本转移' },
  { valueKey: '522000', label: '522000-品牌、商标、契约和许可所有权等非生产非金融资产转让' },
  { valueKey: '621011', label: '621011-新设境外子公司资本金汇出' },
  { valueKey: '621012', label: '621012-筹备资金汇出' },
  { valueKey: '621013', label: '621013-对境外子公司增资' },
  { valueKey: '621014', label: '621014-购买转让的境外企业股权' },
  { valueKey: '621015', label: '621015-境外子公司撤回对境内母公司的股权投资（逆向股权投资的撤回）' },
  { valueKey: '621016', label: '621016-非法人投资款汇出' },
  { valueKey: '621021', label: '621021-对境外子公司的贷款及其他往来' },
  { valueKey: '621022', label: '621022-偿还境外子公司的贷款及其他往来（逆向投资的撤回）' },
  { valueKey: '621030', label: '621030-购买境外不动产的支出' },
  { valueKey: '621040', label: '621040-收益再投资支出' },
  { valueKey: '621050', label: '621050-用境内股权交换境外股权' },
  { valueKey: '621060', label: '621060-实物投资' },
  { valueKey: '621070', label: '621070-无形资产投资' },
  { valueKey: '622011', label: '622011-因外商投资企业清算、终止等撤资' },
  { valueKey: '622012', label: '622012-筹备资金撤出' },
  { valueKey: '622013', label: '622013-外商投资企业减资' },
  { valueKey: '622014', label: '622014-购买转让的外商投资企业股权' },
  { valueKey: '622015', label: '622015-外商投资企业对境外母公司的股权投资（逆向股权投资）' },
  { valueKey: '622016', label: '622016-非法人投资款撤出' },
  { valueKey: '622021', label: '622021-偿还境外母公司的贷款及其他往来' },
  { valueKey: '622022', label: '622022-对境外母公司的贷款及其他往来（逆向贷款投资）' },
  { valueKey: '622030', label: '622030-从非居民购买境内不动产支出' },
  { valueKey: '622050', label: '622050-用境外股权交换境内股权' },
  { valueKey: '622060', label: '622060-实物投资' },
  { valueKey: '622070', label: '622070-无形资产投资' },
  { valueKey: '623011', label: '623011-对境外联属企业的股权投资' },
  { valueKey: '623012', label: '623012-境外联属企业撤回对境内的股权投资' },
  { valueKey: '623021', label: '623021-向境外联属企业提供贷款及其他债权' },
  { valueKey: '623022', label: '623022-偿还境外联属企业贷款及其他债务' },
  { valueKey: '721010', label: '721010-投资境外机构境外发行的股票或股权' },
  { valueKey: '721020', label: '721020-投资境内机构在境外发行的股票或股权' },
  { valueKey: '721030', label: '721030-境内公司回购境外发行的股票或股权' },
  { valueKey: '721040', label: '721040-非居民卖出境内股票或股权' },
  { valueKey: '721050', label: '721050-非居民发行境内股票或股权' },
  { valueKey: '722010', label: '722010-申购境外投资基金' },
  { valueKey: '722020', label: '722020-清算境外投资基金' },
  { valueKey: '722030', label: '722030-非居民赎回境内投资基金' },
  { valueKey: '722040', label: '722040-非居民境内募集投资基金' },
  { valueKey: '723011', label: '723011-买入境外短期债券' },
  { valueKey: '723012', label: '723012-偿付境外短期债券' },
  { valueKey: '723013', label: '723013-非居民卖出境内短期债券' },
  { valueKey: '723014', label: '723014-非居民发行境内短期债券' },
  { valueKey: '723021', label: '723021-买入境外中长期债券' },
  { valueKey: '723022', label: '723022-偿付境外中长期债券' },
  { valueKey: '723023', label: '723023-非居民卖出境内中长期债券' },
  { valueKey: '723024', label: '723024-非居民发行境内中长期债券' },
  { valueKey: '724000', label: '724000-因金融衍生工具交易引起的支出' },
  { valueKey: '821010', label: '821010-资产-人寿保险和年金权益' },
  { valueKey: '821020', label: '821020-向境外提供贷款' },
  { valueKey: '821030', label: '821030-存放境外存款' },
  { valueKey: '821041', label: '821041-出口延期应收款' },
  { valueKey: '821042', label: '821042-进口预付货款' },
  { valueKey: '821990', label: '821990-其他债权' },
  { valueKey: '822010', label: '822010-负债-人寿保险和年金权益' },
  { valueKey: '822020', label: '822020-偿还境外贷款' },
  { valueKey: '822030', label: '822030-境外存入款项调出' },
  { valueKey: '822041', label: '822041-进口延期应付款' },
  { valueKey: '822042', label: '822042-出口预收货款' },
  { valueKey: '822050', label: '822050-实物外债' },
  { valueKey: '822990', label: '822990-偿还其他债务' },
  { valueKey: '921010', label: '921010-偿还出口押汇' },
  { valueKey: '921020', label: '921020-偿还进口押汇' },
  { valueKey: '921030', label: '921030-代理进出口支出' },
  { valueKey: '922090', label: '922090-其他' },
  { valueKey: '923010', label: '923010-代外国投资者划出投资款' },
  { valueKey: '923020', label: '923020-境内投资者投资款划出' },
  { valueKey: '923090', label: '923090-其他境内投资支出' },
  { valueKey: '924010', label: '924010-偿还国内银行及其他金融机构外汇贷款本金' },
  { valueKey: '924020', label: '924020-偿还委托贷款本金' },
  { valueKey: '924030', label: '924030-划出委托贷款' },
  { valueKey: '924090', label: '924090-偿还其他贷款' },
  { valueKey: '925010', label: '925010-个人直系亲属账户资金转出' },
  { valueKey: '925020', label: '925020-个人经营性资金转出' },
  { valueKey: '929010', label: '929010-同名账户资金转出' },
  { valueKey: '929030', label: '929030-提取外币现钞' },
  { valueKey: '929040', label: '929040-划出保证金' },
  { valueKey: '929050', label: '929050-总分公司之间划转的外汇支出' },
  { valueKey: '929060', label: '929060-企业集团公司与境内其他成员企业之间经常项目外汇资金集中管理项下支出' },
  { valueKey: '929070', label: '929070-结汇待支付账户（视同外汇账户管理）资金划出' },
  { valueKey: '929080', label: '929080-因跨境电子商务引起支付机构境内外汇支出' },
  { valueKey: '929090', label: '929090-其他' },
  { valueKey: '999998', label: '999998-无实际资金收付的轧差结算' },
  { valueKey: '999999', label: '999999-有实际资金收付的集中或轧差结算' },
]

// 字段：保税货物付款类型
const paymentUnderBondedGoodsType: SelectField = {
  label: '保税货物付款类型',
  name: 'paymentUnderBondedGoodsType',
  type: 'select',
  options: [
    { label: '预付货款 Advance Payment', valueKey: '0'},
    { label: '货到付款 Payment Against Deliver', valueKey: '1'},
    { label: '退款 Refund', valueKey: '2'},
    { label: '其它 Others', valueKey: '3'},
  ],
}

// 字段：交易编码
const bopTransacCode: SelectField = {
  label: '交易编码 BOP Transac. Code',
  name: 'bopTransacCode',
  type: 'select',
  options: tradeCodes,
}
