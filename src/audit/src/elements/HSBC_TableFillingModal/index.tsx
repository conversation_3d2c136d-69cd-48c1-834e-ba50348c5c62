/**************************************
 * Created By LinK On 2021/8/30 15:37.
 **************************************/
import './styles.less';
import React, { Component } from 'react';
import { Anchor, Form  } from 'antd';
import { Button } from '@hose/eui';
import { app } from '@ekuaibao/whispered';
import { getHsbcFormItemsByType } from './fieldsData';
import { renderForm } from './components/hsbcFields';
import { saveSupplyFileForHSBC } from '../../util/fetchUtil';
import { showModal, showMessage } from '@ekuaibao/show-util';
import { get } from 'lodash'
//@ts-ignore
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create');
import { EnhanceDrawer } from '@ekuaibao/enhance-layer-manager';

const { Link } = Anchor;
export interface Props {
  layer?: any
  flowIds: string[]
  form: any
  supportFormData: any
  setFormData: (formData: any) => void
  formData: any
  id: string
  type: string
  setData: any
  data: any
  editArr: any
  setEditArr: any
}

@EnhanceDrawer((props: any) => ({
  closable: true,
  title: props.title,
  class: {
    left: 'drag-hover',
  },
  width: props.width,
}))
//@ts-ignore
@EnhanceFormCreate()
export default class HSBC_TableFillingModal extends Component<Props> {
  state = {
    dataSource: [],
  }

  componentDidMount() {
    const { formData, id, dataSource }: any = this.props;
    dataSource.forEach((item: any) => {
      const formItemsArrHSBC = getHsbcFormItemsByType(item.type);
      const supportFormDataKey: string = item.type === 'CROSS_BORDER_RMB'
        ? 'settlementInstructions'
        : 'bopData';
      const supportFormData = formData[id]
      const data = supportFormData[supportFormDataKey] || {}
      item.formItemsArrHSBC = formItemsArrHSBC
      item.supportFormDataKey = supportFormDataKey
      item.supportFormData = supportFormData
      item.data = data
    })
    this.setState({
      dataSource,
    })
  }

  handleOk = (conplate: boolean) => {
    const { setFormData, formData, id, form, layer, setData, data, editArr, setEditArr } = this.props;
    const { dataSource } = this.state;
    form.validateFieldsAndScroll((err: any, values: any) => {
      if (!!err) return;
      dataSource.forEach((el: any) => {
        const originData = formData[id][el.supportFormDataKey]
        formData[id][el.supportFormDataKey] = { ...originData, ...values[el.type] };
      })
      saveSupplyFileForHSBC({
        action: "/api/hsbc/plans/save",
        body: formData[id]
      }).then((result) => {
        if (result?.items?.id) {
          showMessage.success(i18n.get('保存成功'))
          setFormData(formData) ``
          data.forEach((item: any) => {
            if (item.dataLink.id === id) {
              item.dataLink.edited = true
            }
          })
          setData(data)
          if (!editArr.includes(result?.items?.id)) {
            setEditArr(editArr.push(result?.items?.id))
          }
          if (conplate) {
            layer.emitCancel()
          }
        } else {
          const errorArr = get(result, 'items', []);
          const warningInfoArr: any[] = [];
          errorArr.forEach((err: any) => {
            if (err?.error?.length > 0) {
              err.error.forEach((msg: string) => warningInfoArr.push(<p>{msg}</p>));
            }
          });
          showModal.error({
            title: <span className='missInfoModal-title'>{i18n.get('信息缺失')}</span>,
            content: <div className='missInfoModal-wrapper'>{warningInfoArr}</div>,
          });
        }
      }).catch(err => {
        if (err.errorMessage) {
          return showMessage.error(err.errorMessage)
        }
      })
    })
  };

  handleClickFun = (e: any, link: any) => {
    // e.preventDefault()
    if (link) {
      const element = document.querySelector(`#${link}`);
      element && element.scrollIntoView({ behavior: 'smooth' });
    }
  }

  renderAnchor = () => {
    const { dataSource } = this.state;
    return (
      <Anchor
        getContainer = {()=> document.querySelector('#hsbcTableFillingModal-wrap')}
      >
        {
          dataSource.map((el: any) => {
            return (
              <div className='linkdiv'>
                <div className='linkmask' onClick={(e: any) => this.handleClickFun(e, el.name)}></div>
                <Link href={`#${el.name}`} title={el.name} />
              </div>
            )
          })
        }
      </Anchor>
    )
  };

  renderContent = () => {
    const { form }: any = this.props;
    const { getFieldDecorator } = form;
    const { dataSource } = this.state;

    return <>
      {
        dataSource.map((el: any ) => {
          console.log('el', el)
          return (
            <>
              <p className='hsbcTableFillingModal-title' id={el.name}>{el.name}</p>
              <Form 
                className='hsbcTableFillingModal-content' 
                key={el.type}>
                {renderForm({
                  formComponents: el.formItemsArrHSBC,
                  initialValue: el.data,
                  getFieldDecorator,
                  type: el.type,
                })}
              </Form>
            </>
          )
        })
      }
    </>
  };

  renderFooter = () => {
    return (<div className='hsbcTableFillingModal-footer'>
      <Button className="btn-ok"  onClick={() => this.handleOk(true)}>
        {i18n.get('完成')}
      </Button>
      <Button category="secondary" onClick={() => this.handleOk(false)}>
        {i18n.get('保存')}
      </Button>
    </div>);
  };

  render() {
    return (
      <div className='hsbcTableFillingModal-wrap' id='hsbcTableFillingModal-wrap'>
        <div className='hsbcTableFillingModal-menu'>
          {this.renderAnchor()}
        </div>
        <div>
          {this.renderContent()}
          {this.renderFooter()}
        </div>
      </div>
    );
  }
}
