/**************************************
 * Created By LinK On 2021/8/31 14:27.
 **************************************/

import React from 'react'
import { DatePicker, Input, Select, InputNumber, Form } from 'antd'
import moment from 'moment'

const Option = Select.Option
const FormItem = Form.Item;

export const renderForm = ({
                             formComponents,
                             initialValue,
                             getFieldDecorator,
                             type,
                           }: any) => {

  return formComponents.map((el: any) => {
    if (el.disabled && type !== 'CROSS_BORDER_RMB') return null;
    switch (el.type) {
      case 'info':{
        return renderByType(el)
      }
      case 'input_inline': {
        const {
          labelFront: labelFrontValue,
          labelAfter: labelAfterValue,
        } = el;
        const labelFront = i18n.get(labelFrontValue);
        const labelAfter = i18n.get(labelAfterValue);
        const classNameV = 'hsbcTableFillingModal-inputInline ' + el.className;
        return (
          <div className={classNameV}>
            {labelFront}
            <FormItem key={el.name} label={el.label}>
              {getFieldDecorator(type + '.' + el.name, {
                initialValue: initialValue[el.name] || undefined,
                rules: getRulesByType(el),
              })(renderByType(el))}
            </FormItem>
            {labelAfter}
          </div>
        );
      }
      default: {
        return (<FormItem key={el.name} label={el.label} className={el.className}>
            {getFieldDecorator(type + '.' + el.name, {
              initialValue: initialValue[el.name] || undefined,
              rules: getRulesByType(el),
            })(renderByType(el))}
          </FormItem>
        );
      }
    }
  })
}

// Can not select days before today
const fnDisabledDateBefore = (current:any) => {
  //  汇丰要求付款日期仅当日起的45天内可选
  return current && (current < moment().subtract(1, 'days')
    || current > moment().add(44, 'days')
  );
}

export const renderByType = (field: any) => {
  const {
    type,
    disabled,
    label: labelValue,
    options = [],
    disabledDateBefore
  } = field;
  const label = i18n.get(labelValue);
  switch (type) {
    case 'date': {
      const disabledDate = disabledDateBefore ? fnDisabledDateBefore : undefined;
      return (
        <DatePicker
          disabledDate={disabledDate}
          disabled={disabled}
          size="large"
        />
      )
    }
    case 'input_inline':
    case 'input': {
      return <Input disabled={disabled} placeholder={i18n.get(`请输入{__k0}`, { __k0: label })} />
    }
    case 'inputNumber': {
      return (
        <InputNumber
          style={{ width: '100%' }}
          disabled={disabled}
          placeholder={i18n.get(`请输入{__k0}`, { __k0: label })}
        />
      )
    }
    case 'select': {
      return (
        <Select disabled={disabled} placeholder={i18n.get(`请选择{__k0}`, { __k0: label })}>
          {options.map((el: { valueKey: string | number; label: string }) => (
            <Option value={el.valueKey}>{i18n.get(el.label || el.valueKey)}</Option>
          ))}
        </Select>
      )
    }
    case "info":{
      return (
        <p className='hsbcTableFillingModal-info'>{label}</p>
      )
    }
    default: {
      return null
    }
  }
}

/**
 * 根据组件类型返回校验规则
 */
export const getRulesByType = (field: any) => {
  const { type, max, label, onlyEn, required } = field
  const rules = [
    { required, message: label + i18n.get('为必填项') },
    { validator: onlyEn ? checkEn : null }
  ]
  switch (type) {
    case 'input': {
      rules.push( { max, message: label + i18n.get('不能超过{max}个字', { max: max }) })
      return rules
    }
    case 'select':{
      return rules
    }
    case 'date': {
      return [{ type: 'object', required, message: label + i18n.get('为必填项') }];
    }
    default: {
      return []
    }
  }
}

/**
 * 校验字符串是否为英文
 */
const checkEn = (rule: any, value: string, callback: any) => {
  if (value) {
    if (value.match(/[\u4e00-\u9fa5]/)) return callback(i18n.get('Please enter in English'))
  }
  callback()
}
