/**************************************
 * Created By LinK On 2019/12/27 17:08.
 **************************************/
//@i18n-ignore-all

export const XJLP_PURPOSE = () => [
  {
    key: 'EXPENSE',
    value: i18n.get('员工报销')
  },
  {
    key: 'TO_BUSINESS',
    value: i18n.get('对企业付款')
  },
  {
    key: 'TO_PERSON',
    value: i18n.get('对个人付款')
  }
]

export const CHANPAY_PURPOSE = () => [
  {
    key: 'BYFD',
    value: i18n.get('代发信托返还资金')
  },
  {
    key: 'BYXI',
    value: i18n.get('代发递延奖金')
  },
  {
    key: 'BYXH',
    value: i18n.get('代发通讯费')
  },
  {
    key: 'BYXG',
    value: i18n.get('代发交通费')
  },
  {
    key: 'BYXF',
    value: i18n.get('代发差旅费')
  },
  {
    key: 'BYXE',
    value: i18n.get('代发分红款')
  },
  {
    key: 'BYXD',
    value: i18n.get('代发车贴')
  },
  {
    key: 'BYXC',
    value: i18n.get('代发烤火费')
  },
  {
    key: 'BYXB',
    value: i18n.get('代发住房公积金')
  },
  {
    key: 'BYXA',
    value: i18n.get('代发改制费')
  },
  {
    key: 'BYXJ',
    value: i18n.get('代发报刊费')
  },
  {
    key: 'BYXK',
    value: i18n.get('代发餐费')
  },
  {
    key: 'BYXL',
    value: i18n.get('代发医药费')
  },
  {
    key: 'BYXM',
    value: i18n.get('代发会议费')
  },
  {
    key: 'BYXN',
    value: i18n.get('代发招待费')
  },
  {
    key: 'BYBK',
    value: i18n.get('代发其他')
  },
  {
    key: 'BYBJ',
    value: i18n.get('代发农副品销售')
  },
  {
    key: 'BYBI',
    value: i18n.get('代发纳税退还')
  },
  {
    key: 'BYBH',
    value: i18n.get('代发继承赠与款')
  },
  {
    key: 'BYBG',
    value: i18n.get('代发证券期货款')
  },
  {
    key: 'BYBF',
    value: i18n.get('代发个人贷款')
  },
  {
    key: 'BYBE',
    value: i18n.get('代发债产权转让')
  },
  {
    key: 'BYBD',
    value: i18n.get('代发投资本益')
  },
  {
    key: 'BYBC',
    value: i18n.get('代发劳务收入')
  },
  {
    key: 'BYWK',
    value: i18n.get('代发加班费')
  },
  {
    key: 'BYTF',
    value: i18n.get('代发报销款')
  },
  {
    key: 'BYWF',
    value: i18n.get('代发福利费')
  }
]
