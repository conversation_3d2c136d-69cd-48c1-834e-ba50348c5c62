@import '~@eku<PERSON>bao/eui-styles/less/token.less';
.drawer-base-wrap {
  :global {
    .eui-drawer-body {
      padding: 0 !important;
      overflow: hidden;
    }
  }
}
.selectPaymethodModalEditView-wrap {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 0px;
  :global {
    .paymethodModalEditView-search {
      display: flex;
      margin: 16px 16px 12px;
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 12px;

      .paymethodModalEditView-search-setting {
        min-width: 0;
        padding: 3px 8px;
      }
    }

    .paymethodModalEditView-list {
      overflow-y: auto;
      flex: 1;
      padding: 0 16px 16px;

      .paymethodModalEditView-content {
        position: relative;
        display: flex;
        flex-direction: column;
        gap: 12px;
      }
    }

    .paymethodModalEditView-footer {
      padding: 16px;
      box-shadow: var(--eui-shadow-up-2);
      display: flex;
      flex-direction: row;
      gap: 12px;
    }
  }
}
