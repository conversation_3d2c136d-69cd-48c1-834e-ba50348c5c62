import '../audit.less'
import React, { Component } from 'react'
import { <PERSON><PERSON>, message } from '@hose/eui'
import { app as api } from '@ekuaibao/whispered'
const ExpenseExport = api.require('@elements/expense-export')
import { EnhanceConnect } from '@ekuaibao/store'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Fetch } from '@ekuaibao/fetch'
import { OutlinedTipsClose } from '@hose/eui-icons'

@EnhanceConnect(state => ({
  expenseTemplates: state['@common'].expenseTemplates,
}))
@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer',
})
export default class ExportModal extends Component {
  constructor(props) {
    super(props)
    let defaultMethod = 'expense'
    this.state = {
      url: this.getExportHref(defaultMethod, props.data.ids),
      radioValue: defaultMethod,
      selectValue: '',
    }
    this.handleModalClose = this.handleModalClose.bind(this)
    this.handleModalSave = this.handleModalSave.bind(this)
    this.handleRadioChange = this.handleRadioChange.bind(this)
  }

  getResult() {
    let head = 'by-'
    let value = this.state.radioValue
    let selectValue = this.state.selectValue
    if (value === 'templates') {
      head = ''
      if (selectValue) {
        let templates = head + selectValue
        return { templates }
      } else {
        message.error(i18n.get('请先在下拉菜单中选择自定义模板'))
        return false
      }
    } else {
      let templates = head + value
      return { templates }
    }
  }

  getExportHref(methodVal, ids) {
    let method = 'by-' + methodVal
    return `${Fetch.fixOrigin(
      location.origin,
    )}/api/v1/export/bills/[${ids}]?method=${method}&corpId=${Fetch.ekbCorpId}`
  }

  handleRadioChange(radioValue) {
    this.setState({
      url: this.getExportHref(radioValue, this.props.data.ids),
      radioValue: radioValue,
    })
  }

  handleModalClose() {
    this.props.layer.emitCancel()
  }

  handleModalSave() {
    api.emit('@vendor:download', this.state.url)
    this.props.layer.emitOk()
  }

  render() {
    return (
      <div className="reject-bill-modal">
        <div className="modal-header">
          <div className="flex" />
          <OutlinedTipsClose className="cross-icon" onClick={this.handleModalClose} />
        </div>
        <div className="content center">
          <ExpenseExport
            onRadioChange={this.handleRadioChange.bind(this)}
            expenseTemplates={this.props.expenseTemplates}
          />
        </div>
        <div className="modal-footer">
          <Button category="secondary" className="btn-ml" onClick={this.handleModalClose}>
            {i18n.get('取消')}
          </Button>
          <Button className="btn-ml" onClick={this.handleModalSave.bind(this)}>
            {i18n.get('确定')}
          </Button>
        </div>
      </div>
    )
  }
}
