.bind-phone-modal-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 41px 0;
  .txt {
    font-weight: 500;
    margin-bottom: 20px;
    margin-left: 40px;
  }
  .form-bind-phone {
    width: 400px;
  }
  .ant-input-group {
    margin-top: 24px;
    width: 320px;
  }
  .ant-input-group-addon {
    background-color: #ffffff;
    border: 0 !important;
    color: #9c9c9c;
  }
  .ant-input-group > .ant-input-preSuffix-wrapper:not(:first-child) .ant-input {
    border-radius: 4px;
  }
  .ant-input-group > .ant-input:last-child {
    border-bottom-left-radius: 4px;
    border-top-left-radius: 4px;
  }
  .code {
    width: 308px;
  }
  .ant-input-suffix {
    margin-left: 14px;
    background-color: #eda85a;
    border-radius: 4px;
    right: -93px;
    .captcha-code {
      height: 32px;
      line-height: 32px;
      padding-left: 10px;
      padding-right: 10px;
      font-size: 12px;
      color: #ffffff;
      border-radius: 4px;
    }
  }
}

.ant-modal-footer button + button {
  margin-left: 18px;
}

.ant-modal-title {
  font-size: 13px;
  color: #9c9c9c;
}

.container {
  background: #fff;
  padding: 20px;
  margin: 20px;
  width: 400px;
}

.ln {
  padding: 5px 0;

  .nc-container {
    .nc_wrapper {
      width: 100% !important;
    }
    .nc_scale {
      height: 32px;

      .nc_bg {
        background: var(--brand-base);
      }

      .btn_ok {
        color: var(--brand-base);
      }
    }
  }
}

.ln .h {
  display: inline-block;
  width: 4em;
}

.ln input {
  border: solid 1px #999;
  padding: 5px 8px;
}
