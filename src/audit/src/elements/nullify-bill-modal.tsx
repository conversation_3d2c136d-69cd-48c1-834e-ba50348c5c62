import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Button, Checkbox } from '@hose/eui'
import styles from './nulllify-bill-modal.module.less'
import { OutlinedTipsClose } from '@hose/eui-icons'

type IProps = {
  layer: any
  flowId: string
  lockInvoiceWhenNullify: boolean
}
type IState = {
  lockInvoiceWhenNullify: boolean
  
}

@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer'
})
export default class NulllifyBillModal extends PureComponent<IProps, IState> {

  state = {
    lockInvoiceWhenNullify: this.props.lockInvoiceWhenNullify || false
  }
  handleModalClose() {
    this.props.layer.emitCancel()
  }


  handleModalSave() {
    this.props.layer.emitOk({
      lockInvoiceWhenNullify: this.state.lockInvoiceWhenNullify
    })
  }

  handleChange = (e: any) => {
    const checked = e.target.checked
    this.setState({
      lockInvoiceWhenNullify: checked
    })
  }
  render() {
    return (
      <div className={styles['nulllify_bill_modal']}>
        <div className="modal-header">
          <div className="flex">{i18n.get('确认作废单据？')}</div>
          <OutlinedTipsClose className="cross-icon" onClick={this.handleModalClose.bind(this)} />
        </div>
        <div className="modal-content">
          <p>单据将会被审批为作废状态</p>
          <div>
            <Checkbox
              className="fs-14"
              checked={this.state.lockInvoiceWhenNullify}
              disabled={this.props?.lockInvoiceWhenNullify}
              onChange={this.handleChange}
            >
              {i18n.get('发票将会被锁定，无法被继续关联')}
            </Checkbox>
          </div>
        </div>
        <div className="modal-footer modal-footer-start">
          <Button
            className="btn"
            style={{ marginRight: '8px' }}
            onClick={this.handleModalSave.bind(this)}
          >
            {i18n.get('确定')}
          </Button>
          <Button category="secondary" className="btn mr-10" onClick={this.handleModalClose.bind(this)}>
            {i18n.get('取消')}
          </Button>
        </div>
      </div>
    )
 }
}