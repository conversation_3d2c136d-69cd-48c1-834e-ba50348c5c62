import { app } from '@ekuaibao/whispered'
/**************************************
 * Created By LinK On 2019/12/30 15:40.
 **************************************/
import React, { PureComponent } from 'react'
import styles from './SelectPayMethodModalEditViewWidget.module.less'
import { Button } from '@hose/eui'
import { OutlinedGeneralSetting } from '@hose/eui-icons'
const SearchInput = app.require<any>('@elements/search-input')
const AccountListItem = app.require<any>('@elements/payee-account/account-list-item')
const { getBoolVariation } = app.require('@lib/featbit') as any
const PayAccountCard = app.require('@elements/PayAccountCard')
const EmptyBody = app.require('@bills/elements/EmptyBody') as any
interface Props {
  payAccountActiveList: any[]
  dynamicChannelMap?: any
  layer?: any
  selectedAccount?: any
  getPaymentAccount: (fn: (f: any) => void, f: boolean) => void
}

interface State {
  list: any[]
  selectedItem: any
}

export default class SelectPayMethodModalEditView extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { list: props.payAccountActiveList, selectedItem: props.selectedAccount }
  }

  handleSearch = (e: any) => {
    const searchText = e.target.value
    const { payAccountActiveList } = this.props
    const _searchText = searchText.trim().toLowerCase()
    if (_searchText) {
      const list = payAccountActiveList.filter(
        el =>
          (el.name && !!~el.name.toLowerCase().indexOf(_searchText)) ||
          (el.accountName && !!~el.accountName.toLowerCase().indexOf(_searchText)) ||
          (el.code && !!~el.code.toLowerCase().indexOf(_searchText)) ||
          (el.remark && !!~el.remark.toLowerCase().indexOf(_searchText)) ||
          (el.accountNo && !!~el.accountNo.toLowerCase().indexOf(_searchText)),
      )
      this.setState({ list })
    } else {
      this.setState({ list: payAccountActiveList })
    }
  }

  setCommonAccount = () => {
    app.open('@audit:CommonAccountModal').then(() => {
      this.props.getPaymentAccount(this.setState.bind(this), false)
    })
  }

  renderSearch = () => {
    return (
      <div className="paymethodModalEditView-search">
        <SearchInput data-testid="pay-selectPayMethodModalEditView-search-input" placeholder={i18n.get('搜索开户名、备注名称、编码、备注')} onChange={this.handleSearch} />
        <Button data-testid="pay-selectPayMethodModalEditView-setting-btn" className="paymethodModalEditView-search-setting" onClick={this.setCommonAccount} category="secondary">
          <OutlinedGeneralSetting fontSize={16} />
        </Button>
      </div>
    )
  }

  renderList = () => {
    const { dynamicChannelMap = {} } = this.props
    const { list, selectedItem } = this.state
    const isNew = getBoolVariation('select_pay_account_new')
    const AccountCard = isNew ? PayAccountCard : AccountListItem
    return (
      <div className="paymethodModalEditView-list">
        {list.length ? (
          <div className="paymethodModalEditView-content" id="paymethodModalEditView-content">
            {list.map((item: any, idx: number) => {
              return (
                <AccountCard
                  key={item.id}
                  isSelected={item.id === selectedItem?.id}
                  configDisabled={true}
                  parentID="paymethodModalEditView-content"
                  data={item}
                  inSelectPayMethodModal
                  onClick={() => this.setState({ selectedItem: item })}
                  formChannel="pay"
                  dynamicChannelMap={dynamicChannelMap}
                  data-testid={`pay-selectPayMethodModalEditView-account-item-${item.id}`}
                />
              )
            })}
          </div>
        ) : (
          <EmptyBody label={i18n.get('没有可见的“支付账户”，请联系管理员配置可见性')} />
        )}
      </div>
    )
  }
  handleOk = () => {
    const { selectedItem } = this.state
    this.props.layer?.emitOk(selectedItem)
  }
  handleCancel = () => {
    this.props.layer?.emitCancel()
  }
  render() {
    return (
      <div className={styles['selectPaymethodModalEditView-wrap']}>
        {this.renderSearch()}
        {this.renderList()}
        <div className="paymethodModalEditView-footer">
          <Button onClick={this.handleOk}>{i18n.get('确定')}</Button>
          <Button category="secondary" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </Button>
        </div>
      </div>
    )
  }
}
