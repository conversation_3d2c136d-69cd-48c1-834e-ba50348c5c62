@import '~@ekuaibao/web-theme-variables/styles/colors.less';

.pay-failed-risk-modal-wrapper {
  :global {
    .pay-failed-risk-content {
      padding: 12px 0 64px;

      .pay-failed-risk-description {
        display: flex;
        margin: 0 24px;
        padding: 8px 12px 8px 16px;
        background-color:rgba(250, 150, 42, 0.1);
        line-height: 22px;
        color: rgba(29,43,61,0.75);
        font-size: 14px;
        margin-bottom: 16px;
        border: 1px solid rgba(250,150,42,0.10);
        border-radius: 5px;

        .warning {
          font-size: 20px;
          color: #fa8c16;
          margin-right: 8px;
        }
      }

      .pay-failed-risk-form {
        padding: 0 24px;

        .failed-risk-area {
          height: 80px;
        }
      }
    }
  }
}

.modal-footer {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 56px;
  padding: 0 32px;
  :global{
    .btn-ok {
      background-color: #32b4c4;
      border-color: #32b4c4;
      color:#fff;
      margin-right: 8px;
    }
  }
}

.check-pay-failed-wrapper {
  :global{
    .ant-modal-body {
      padding: 0;
    }
    .ant-modal-footer {
      border: none;
      padding: 0;
    }
  }
}
