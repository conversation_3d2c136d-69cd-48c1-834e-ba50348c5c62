import { app as api } from '@ekuaibao/whispered'
import './paying-bind-phone.less'
import React from 'react'
import { Form, Icon } from 'antd'
import { Input, Button, Alert } from '@hose/eui'
import { showNotification } from '@ekuaibao/show-util'
import { getNcpcToken } from '@ekuaibao/lib/lib/addNcpcCode'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
const EnhanceFormCreate = api.require('@elements/enhance/enhance-form-create')
import { Fetch } from '@ekuaibao/fetch'
import { getPayCaptha, getThirdCaptcha } from '../audit-action'

const FormItem = Form.Item
const verifyCode = i18n.get('输入验证码')
const phoneNumber = i18n.get('请输入手机号')

@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer',
})
@EnhanceFormCreate()
export default class PayingBindPhone extends React.PureComponent {
  constructor(props) {
    super(props)
    props.overridePropsFnByMap({
      getResult: this.getResult.bind(this),
      willAfterClose: this.willAfterClose.bind(this),
    })
    this.identityId = ''
    this.ncpcData = null

    this.state = {
      captcha: i18n.get('获取验证码'),
      disabled: false,
      id: '',
    }
  }

  componentDidMount() {
    const lang = Fetch.staffSetting ? Fetch.staffSetting.language : Fetch.defaultLanguage
    getNcpcToken(this.setNcpcData, lang)
  }

  setNcpcData = data => {
    this.setState({ disabled: true })
    delete data.value
    data.scene = 'message'
    data.platform = 3
    this.ncpcData = data
  }

  getResult() {
    const { source } = this.props.data
    const identityId = this.identityId
    return new Promise((resolve, reject) => {
      this.props.form.validateFieldsAndScroll((errors, values) => {
        if (!!errors) {
          return reject()
        }
        let data = {
          identityId: identityId,
          identityCode: values.code,
          enable: true,
        }
        this.identityId = ''
        if (source && source === 'aliTrip') {
          delete data.enable
          data.cellPhone = values.phone
        }
        resolve(data)
      })
    }).then(data => {
      return data
    })
  }

  willAfterClose() {
    this.identityId = ''
    this.props.form.resetFields()
  }

  handleModalClose() {
    this.identityId = ''
    this.props.layer.emitCancel()
  }

  handleModalSave() {
    this.props.layer.emitOk()
  }

  handleCaptchaCode(phone) {
    const { source } = this.props.data
    if (phone) {
      let wait = 60
      this.time(wait)
      const thisAction = source && source === 'aliTrip' ? getThirdCaptcha : getPayCaptha

      api.dispatch(thisAction(phone, this.ncpcData)).then(data => {
        this.identityId = data.id || data.value.captchaId
      })
    } else {
      showNotification.error(i18n.get('请输入手机号'))
    }
  }

  time(wait) {
    let _this = this
    if (wait === 0) {
      this.setState({
        captcha: i18n.get('获取验证码'),
        disabled: true,
      })
      wait = 60
    } else {
      let time = i18n.get('retry-after', { wait })
      this.setState({
        captcha: time,
        disabled: false,
      })
      wait--
      setTimeout(function () {
        _this.time(wait)
      }, 1000)
    }
  }

  render() {
    let { data } = this.props
    const { getFieldDecorator } = { ...this.props.form }
    const warnMsg = data.cellphone
      ? i18n.get(`{__k0}：{__k1}`, { __k0: i18n.get('您当前绑定的手机号'), __k1: data.cellphone })
      : i18n.get('手机验证通过后将默认开通短信提醒')
    return (
      <div>
        <div className="modal-header">
          <div className="flex">
            {data.cellphone ? i18n.get('修改绑定手机') : i18n.get('短信通知服务')}
          </div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose.bind(this)} data-testid="pay-payingBindPhone-close-icon" />
        </div>
        <div className="bind-phone-modal-wrapper">
          <Alert
            message={warnMsg}
            type="warning"
            className="mb-20"
            style={{ width: 340 + 'px', marginLeft: -10 + 'px' }}
          />
          <Form className="form-bind-phone" layout="vertical">
            <FormItem label={`${i18n.get('输入手机号')}:`} className="mb-20">
              {getFieldDecorator('phone', {
                rules: [{ required: true, whitespace: true, message: phoneNumber }],
              })(<Input placeholder={phoneNumber} />)}
            </FormItem>
            <FormItem label={`${i18n.get('滑动验证码')}:`} className="mb-20">
              <div className="ln">
                <div id="ncpcdom" />
              </div>
            </FormItem>
            <FormItem label={`${verifyCode}:`} className="mb-20">
              {getFieldDecorator('code', {
                initialValue: '',
                rules: [{ required: true, whitespace: true, message: i18n.get('请输入验证码') }],
              })(
                <Input
                  className="code"
                  placeholder={i18n.get('请输入验证码')}
                  suffix={
                    <div
                      style={
                        this.state.disabled
                          ? { backgroundColor: ' #eda85a' }
                          : { backgroundColor: ' #b6b6b6' }
                      }
                      onClick={
                        this.state.disabled &&
                        this.handleCaptchaCode.bind(this, this.props.form.getFieldValue('phone'))
                      }
                      className="captcha-code"
                      data-testid="pay-payingBindPhone-captcha-btn">
                      {this.state.captcha}
                    </div>
                  }
                />,
              )}
            </FormItem>
          </Form>
        </div>
        {/*短信防控滑动验证服务必须引入*/}
        <div id="_umfp" />
        <div className="modal-footer">
          <Button
            category="secondary"
            className="btn-ml"
            onClick={this.handleModalClose.bind(this)}
            data-testid="pay-payingBindPhone-cancel-btn">
            {i18n.get('取消')}
          </Button>
          <Button className="btn-ml" onClick={this.handleModalSave.bind(this)} data-testid="pay-payingBindPhone-confirm-btn">
            {i18n.get('确定')}
          </Button>
        </div>
      </div>
    )
  }
}
