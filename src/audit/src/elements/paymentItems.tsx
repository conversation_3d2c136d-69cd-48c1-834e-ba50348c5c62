/**************************************************
 * Created by nanyuantingfeng on 20/09/2017 14:22.
 **************************************************/
import styles from './paymentItems.module.less'
import React, { Component } from 'react'
import { Table } from 'antd'
import * as viewUtil from '../view-util'
import classnames from 'classnames'

export function feeTypeTableColumns() {
  const { disableActionColumn = true } = this.props
  let columns: Array<any> = [
    {
      title: i18n.get('费用类型'),
      dataIndex: 'feeType',
      render: viewUtil.feetypeInfo
    },
    {
      title: i18n.get('收款信息'),
      dataIndex: 'paymentDigest.payee',
      render: viewUtil.accountInfo
    },
    {
      title: i18n.get('支付金额'),
      dataIndex: 'paymentDigest.balance',
      render: viewUtil.tdAmount
    },
    {
      title: i18n.get('支付状态'),
      dataIndex: 'paymentDigest.state',
      render: (status: string, line: any) => {
        return viewUtil.renderPayStatus(status, line.paymentDigest.respMsg)
      }
    }
  ]
  if (disableActionColumn) {
    columns.push({
      title: i18n.get('操作'),
      width: '18%',
      dataIndex: 'paymentDigest.actions',
      className: 'actions-wrapper',
      render: viewUtil.paymentAction('offLine')
    })
  }
  return columns
}

export function payPlanTableColumns() {
  const { disableActionColumn = true } = this.props
  let columns: Array<any> = [
    {
      title: i18n.get('收款信息'),
      dataIndex: 'paymentDigest.payee',
      render: viewUtil.accountInfo
    },
    {
      title: i18n.get('支付金额'),
      dataIndex: 'paymentDigest.balance',
      render: viewUtil.tdAmount
    },
    {
      title: i18n.get('支付状态'),
      dataIndex: 'paymentDigest.state',
      render: (status: string, line: any) => {
        return viewUtil.renderPayStatus(status, line.paymentDigest.respMsg)
      }
    }
  ]
  if (disableActionColumn) {
    columns.push({
      title: i18n.get('操作'),
      width: '18%',
      dataIndex: 'paymentDigest.actions',
      className: 'actions-wrapper',
      render: viewUtil.paymentAction('offLine')
    })
  }
  return columns
}

interface Feetype {
  id: string
  name: string
  code: string
  icon: string
  color: string
  fullname: string
}

interface Payee {
  name: string
  type: string
  bank: string
  unionBank: string
  accountNo: string
  branch: string
  icon: string
  province: string
  city: string
}

interface PaymentDigest {
  actions: string[]
  paymentChannel: string
  state: string
  payee: Payee
}

interface Item {
  amount: StringAnyProps
  detailId: string
  feeType: Feetype
  paymentDigest: PaymentDigest
}

interface PayPlanItem {
  dataLinkId: string
  payeeId: string
  paymentDigest: PaymentDigest
}

interface PaymentItemsProps {
  payPlanMode: boolean
  className: string
  dataSource: Item[]
}

export default class PaymentItems extends Component<PaymentItemsProps> {
  static defaultProps = {
    dataSource: new Array<Item>()
  }

  render() {
    const { dataSource, className, payPlanMode } = this.props
    const rowKey = payPlanMode ? 'dataLinkId' : 'detailId'
    const fnGetColumns = payPlanMode ? payPlanTableColumns : feeTypeTableColumns

    if (!dataSource.length) return i18n.get('此支付记录没有支付明细')
    const cls = classnames(styles['payment-item-ant-wrapper'], styles['paymentItems-wrapper'], className)
    return (
      <div className={cls}>
        <Table
          className="payment-item"
          pagination={false}
          bordered={true}
          columns={fnGetColumns.call(this)}
          rowKey={rowKey}
          dataSource={dataSource}
        />
      </div>
    )
  }
}
