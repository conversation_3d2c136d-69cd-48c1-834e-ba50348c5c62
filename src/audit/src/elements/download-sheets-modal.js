import { app } from '@ekuaibao/whispered'
import { I<PERSON>, Button, DatePicker } from 'antd'
const SelectPaymentMethod = app.require('@elements/payment-form-field/select-payment-method')
import React, { Component } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
const RangePicker = DatePicker.RangePicker
import styles from './download-sheet-modal.module.less'

@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer'
})
export default class DownloadSheetsModal extends Component {
  constructor(props) {
    super(props)
    this.state = {
      remarkValue: '',
      showAutograph: true,
      imageVisible: false,
      imgIndex: 0,
      imageList: [],
      uploaderFileList: [],
      value: undefined,
      isUpload: false
    }
    props.overrideGetResult(this.getResult)
  }

  componentWillReceiveProps(nextProps) {
    if (!nextProps.visible) {
      this.setState({ remarkValue: '' })
    }
  }

  getResult = () => {
    let { form, userInfo } = this.props
    let { value } = this.state

    return new Promise((resolve, reject) => {
      form.validateFieldsAndScroll((error, fieldValue) => {
        if (!!error) {
          reject()
          return
        }
        let { showAutograph, comment } = fieldValue
        let data = { comment }
        data.attachments = value

        if (showAutograph && userInfo && userInfo.staff && userInfo.staff.autograph) {
          data.autographImageId = userInfo.staff.autograph.key
        }
        form.resetFields()
        resolve(data)
      })
    })
  }

  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  handleModalSave() {
    this.props.layer.emitOk()
  }

  handleChange = uploaderFileList => {
    this.setState({ uploaderFileList })
  }

  render() {
    return (
      <div className={styles['download-sheets-modal']}>
        <div className="modal-header">
          <div className="flex title">{i18n.get('对账单下载')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose} data-testid="pay-downloadSheetsModal-close-icon" />
        </div>
        <div className="content">
          <div className="label">{i18n.get('请选择日期：')}</div>
          <RangePicker style={{ width: '100%' }} onChange={this.handleChange} format="YYYY-MM-DD" size="large" data-testid="pay-downloadSheetsModal-dateRange-picker" />
          <div className="label">{i18n.get('可下载类型：')}</div>
          <div className="method">
            <SelectPaymentMethod
              isSingle
              channels={[
                {
                  channel: 'CHANPAY'
                }
              ]}
              radioValue="CHANPAY"
            />
          </div>
        </div>
        <div className="modal-footer">
          <Button type="ghost" className="mr-10" onClick={this.handleModalClose} data-testid="pay-downloadSheetsModal-cancel-btn">
            {i18n.get('取消')}
          </Button>
          <Button type="primary" onClick={this.handleModalSave} data-testid="pay-downloadSheetsModal-confirm-btn">
            {i18n.get('确定')}
          </Button>
        </div>
      </div>
    )
  }
}
