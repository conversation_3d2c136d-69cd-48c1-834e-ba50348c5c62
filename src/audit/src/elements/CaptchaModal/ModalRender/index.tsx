import styles from '../captcha-modal.module.less';

import React, { useState, useRef } from 'react';
import { Button } from '@hose/eui';
import {OutlinedTipsClose} from "@hose/eui-icons"
import InnerRender from './InnerRender';
interface IProps {
    onCancelHandle: () => void
    getCaptcha: Function
    getConfigData: Function
    successCb: Function
    phoneNumber?: string
}

const ModalRender = ({ onCancelHandle, getCaptcha, getConfigData, successCb, phoneNumber }: IProps) => {

    const [ disabled, setDisabled ] = useState(true)

    const formRef: any = useRef(null)

    const onOkHandle = () => {
        const { _form = {}, getConfig = () => ({}) } = formRef?.current || {}
        const { validateFieldsAndScroll } = _form
        validateFieldsAndScroll && validateFieldsAndScroll((err: any, values: any) => {
            if(!err){
                const _otherConfig = getConfig()
                successCb && successCb(values.code, _otherConfig)
            }
        })
        
    }

    return <div className={styles['do-captch-modal-wrapper']}>
        <div className="do-captch-modal-header modal-header">
            <div className="flex"></div>
            <OutlinedTipsClose className="cross-icon" onClick={onCancelHandle} />
        </div>
        <InnerRender 
            getCaptcha={getCaptcha} 
            setAllowSubmit={() => setDisabled(false)} 
            getConfigData={getConfigData}
            wrappedComponentRef={formRef}
            phoneNumber={phoneNumber}
        />
        <div className={`do-captch-modal-footer modal-footer`}>
            <Button category="secondary" onClick={onCancelHandle}>{i18n.get('取消')}</Button>
            <Button disabled={disabled} onClick={onOkHandle}>
                {i18n.get('确定')}
            </Button>
        </div>
    </div>
}

export default ModalRender;