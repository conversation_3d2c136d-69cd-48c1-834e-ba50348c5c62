import React, {
  useEffect,
  useState,
  useImperativeHandle,
  forwardRef,
  useRef,
  Fragment,
} from 'react'
import { Form, Input } from 'antd'
import { getNcpcToken } from '@ekuaibao/lib/lib/addNcpcCode'
import { Fetch } from '@ekuaibao/fetch'
import { showMessage } from '@ekuaibao/show-util'

const FormItem = Form.Item
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
}

interface IProps {
  form: any
  getCaptcha: Function
  setAllowSubmit?: () => void
  getConfigData: Function
  phoneNumber?: string
}

const InnerRender = (
  { form, getCaptcha, setAllowSubmit, getConfigData, phoneNumber = '' }: IProps,
  ref: any,
) => {
  const _captchaTip = i18n.get('获取验证码')
  const [captchaTip, setCaptchaTip] = useState(_captchaTip)
  const [disabled, setDisabled] = useState(true)
  const [ncpcData, setNcpcData] = useState({})
  const [captchaData, setCaptchaData] = useState({})

  useImperativeHandle(ref, () => ({
    _form: form,
    getConfig,
  }))
  let timer: any = useRef(null)

  useEffect(() => {
    const lang = Fetch.staffSetting ? Fetch.staffSetting.language : Fetch.defaultLanguage
    getNcpcToken(setNcpcDataCb, lang)

    return () => {
      timer.current && clearTimeout(timer.current) //清除定时器
    }
  }, [])

  const getConfig = () => Object.assign({}, captchaData, ncpcData)

  const setNcpcDataCb = (data: any) => {
    setDisabled(false)
    const { csessionid, sig, token } = data
    const config = {
      csessionid,
      sig,
      token,
      scene: 'message',
      platform: 3,
    }
    getConfigData && getConfigData(config)
    setNcpcData(config)
  }

  const { getFieldDecorator } = form

  const checkPhone = (rule: any, value: string, callback: (mag?: string) => void) => {
    const regs = /^\d{1,4}?\-?\d+$/
    if (!regs.test(value)) {
      return callback(i18n.get('手机格式不正确'))
    }
    callback()
  }

  const time = (wait: number) => {
    if (wait === 0) {
      setCaptchaTip(i18n.get('获取验证码'))
      setDisabled(false)
      wait = 60
    } else {
      let _time = i18n.get('retry-after', { wait })
      setCaptchaTip(_time)
      setDisabled(true)
      wait--
      timer.current = setTimeout(function () {
        time(wait)
      }, 1000)
    }
  }

  const getCaptchaCode = () => {
    if (!disabled) {
      const _key = 'phone'
      form.validateFieldsAndScroll([_key], (err: any, values: any) => {
        if (err) return
        const cellphone = values[_key]
        time(60) //开启倒计时
        getCaptcha &&
          getCaptcha({ ...ncpcData, cellphone })
            .then((resp: any) => {
              if (resp.id) {
                setAllowSubmit && setAllowSubmit()
                setCaptchaData({ captchaId: resp.id })
                getConfigData && getConfigData({ captchaId: resp.id })
              }
            })
            .catch((error: any) => {
              showMessage.error(error.message)
              timer.current && clearTimeout(timer.current) //清除定时器
              time(0)
            })
      })
    }
  }

  return (
    <div className="do-captch-modal-content">
      <div>
        <Form layout="horizontal">
          <FormItem {...formItemLayout} label={i18n.get('输入手机号') + ':'} className="mb-20">
            {getFieldDecorator('phone', {
              initialValue: phoneNumber,
              rules: [
                { required: true, whitespace: true, message: i18n.get('请输入手机号') },
                { validator: checkPhone },
              ],
            })(
              <EncryptPhone
                isReadonly={!!phoneNumber}
                placeholder={i18n.get('请输入手机号')}
                disabled={!!phoneNumber}
              />,
            )}
          </FormItem>
          <FormItem {...formItemLayout} label={i18n.get('滑动验证码') + ':'} className="mb-20">
            <div className="ln">
              <div id="ncpcdom" />
            </div>
          </FormItem>
          <FormItem {...formItemLayout} className="mb-20" label={i18n.get('输入验证码') + ':'}>
            {getFieldDecorator('code', {
              initialValue: '',
              rules: [{ required: true, whitespace: true, message: i18n.get('请输入验证码') }],
            })(
              <Input
                className="code"
                placeholder={i18n.get('请输入验证码')}
                suffix={
                  <div
                    onClick={getCaptchaCode}
                    style={
                      !disabled
                        ? { backgroundColor: ' #eda85a' }
                        : { backgroundColor: ' #b6b6b6', cursor: 'not-allowed' }
                    }
                    className="captcha-code">
                    {captchaTip}
                  </div>
                }
              />,
            )}
          </FormItem>
        </Form>
      </div>
      {/*短信防控滑动验证服务必须引入*/}
      <div id="_umfp" />
    </div>
  )
}

export default Form.create()(forwardRef(InnerRender))

const EncryptPhone = forwardRef(({ isReadonly, ...rest }: any, ref: any) => {
  const getPhone = (phone: string) => phone.replace(/^(\d{3})\d{4}(\d+)/, '$1****$2')

  return (
    <Fragment>
      {isReadonly ? <Input {...rest} value={getPhone(rest.value)} /> : <Input {...rest} />}
    </Fragment>
  )
})
