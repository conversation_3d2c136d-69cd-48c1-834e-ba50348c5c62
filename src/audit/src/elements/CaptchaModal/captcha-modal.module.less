@import '~@ekuaibao/eui-styles/less/token.less';

.do-captch-modal-wrapper{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    :global{
        .nc-container {
            .nc_scale {
                span{
                    height: 34px
                }
            }
        } 
        
        .do-captch-modal-content {
            width: 400px;
            margin-top: 20px;
            margin-bottom: 40px;
            .ant-input-suffix{
                margin-left: 14px;
                border-radius: @radius-2;
                right: 0;
            }
            .captcha-code{
                height: 32px;
                line-height: 32px;
                padding-left: 10px;
                padding-right: 10px;
                font-size: 12px;
                color: @color-white-1;
                cursor: pointer;
            }
        }
        .do-captch-modal-header{
            width: 100%
        }
        .do-captch-modal-footer{
            width: 100%;
            .ant-btn:first-child{
                margin-right: 16px;
            }
        }
    }
    
}

