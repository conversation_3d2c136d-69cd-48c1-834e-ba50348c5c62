import React, { useState, useImperativeHandle, forwardRef } from 'react';
import { Modal } from '@hose/eui';
import { showMessage } from '@ekuaibao/show-util'
import ModalRender from './ModalRender';


interface IProps {
    getCaptcha: Function
    getConfigData: Function
    successCb: Function
    phoneNumber?: string
    isMustPhone?: boolean
}

const CaptchaModal = ({ getCaptcha, getConfigData, successCb, phoneNumber, isMustPhone=true }: IProps, ref: any) => {

    const [ visible, setVisible ] = useState(false)
    const [ isDestroy, setIsDestroy ] = useState(false)
    
    const show = () => {
        if(isMustPhone && !Boolean(phoneNumber)){
            showMessage.error(i18n.get('您还没有绑定手机号，请先绑定手机号。'))
        }else{
            setVisible(true)
        }  
    }

    const hide = () => setVisible(false)

    const destroy = () => {
        hide();
        setIsDestroy(true)
    }

    const onCancelHandle = () => hide()

    useImperativeHandle(ref, () => ({ 
        show,
        hide,
        destroy
     }));

    return <div id={'do-captch-modal-entry'}>
        {
            isDestroy 
            ? null
            : (
                <Modal 
                    open={visible}
                    className={`do-captch-modal custom-modal-layer`}
                    width={600}
                >
                    { 
                        visible 
                        ? <ModalRender 
                            onCancelHandle={onCancelHandle} 
                            getCaptcha={getCaptcha} 
                            getConfigData={getConfigData} 
                            successCb={successCb}
                            phoneNumber={phoneNumber}
                        /> 
                        : <div style={{width: 600, height: 200}} />  //占位dom
                    }
                </Modal>
            )
        } 
    </div>
}

export default forwardRef(CaptchaModal);

