import '../audit.less'
import React from 'react'
import classNames from 'classnames'
import { Icon } from 'antd'
import { Toolt<PERSON>, Popover, Checkbox, Button, Input } from '@hose/eui'
import { app, app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import * as actions from '../audit-action'
import MSG_ICON from '../images/mail-icon.svg'
import OrderTypeLabel from './Channels/OrderTypeLabel'
import { getScenes, saveScenes } from '../util/fetchUtil'
import FilterBar from '../record-table/filter-bar'
const OFFLINE_FINISH_TIME_AVAILABLE = 'OFFLINE_FINISH_TIME_AVAILABLE'
const { Search } = Input

@EnhanceConnect(state => ({
  offlineFinishTimeAvailable: state['@audit'].offlineFinishTimeAvailable,
}))
export default class PendingPayingTableHead extends React.Component {
  constructor(props) {
    super()
    this.state = {
      checked: props.bindPhoneInfo ? props.bindPhoneInfo.enable : false,
      popVisible: false,
      payingCheckVisible: false,
      offlineFinishTimeAvailable: props.offlineFinishTimeAvailable,
    }
  }

  componentDidMount = async () => {
    api.dispatch(actions.getPayBindPhoneInfo())
    const hasPayingCheckCharge =
      (await api.invokeService('@custom-specification:check:power:code', {
        powerCode: '170055',
      })) || {}
    this.setState({ payingCheckVisible: hasPayingCheckCharge?.value })
    this.getUpdatePayFinishedTime()
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.bindPhoneInfo !== nextProps.bindPhoneInfo) {
      this.setState({
        checked: nextProps.bindPhoneInfo ? nextProps.bindPhoneInfo.enable : false,
      })
    }
  }

  handleSearch = value => {
    this.props.onSearch && this.props.onSearch(value)
  }

  handleBindPhone(data) {
    this.setState({
      popVisible: false,
    })
    api.open('@audit:PayingBindPhone', { data }).then(data => {
      api.dispatch(actions.setPayBindPhone(data)).then(v => {
        api.dispatch(actions.getPayBindPhoneInfo())
      })
    })
  }

  handleSaveEnable() {
    api.dispatch(actions.openPayMessage({ enable: this.state.checked })).then(_ => {
      this.setState({
        popVisible: false,
      })
    })
  }

  handleCheckChange(e) {
    this.setState({
      checked: e.target.checked,
    })
  }

  changeVisible(value) {
    api.dispatch(actions.getPayBindPhoneInfo()).then(v => {
      this.setState({
        popVisible: value,
      })
    })
  }

  handleImport = () => {
    api.open('@bills:ImportDetailByExcel', { type: 'auditPaid' })
  }

  renderBindPhonePop() {
    let { bindPhoneInfo } = this.props
    return (
      <div className="ekb-bind-phone-popover-content">
        <div className="line">
          <span className="color-gray">
            {i18n.get('绑定手机')}
            {i18n.get('：')}
          </span>
          {bindPhoneInfo.cellphone}
          <a className="action" onClick={this.handleBindPhone.bind(this, bindPhoneInfo)} data-testid="pay-pendingPayingTableHead-editPhone-link">
            {i18n.get('修改绑定手机')}
          </a>
        </div>
        <div className="line">
          <Checkbox checked={this.state.checked} onChange={this.handleCheckChange.bind(this)}>
            {i18n.get('开启短信通知')}
          </Checkbox>
        </div>
        <div className="line-action">
          <Button category="secondary" onClick={this.changeVisible.bind(this, false)} data-testid="pay-pendingPayingTableHead-cancel-btn">
            {i18n.get('取消')}
          </Button>
          <Button category="primary" onClick={this.handleSaveEnable.bind(this)} data-testid="pay-pendingPayingTableHead-save-btn">
            {i18n.get('保存')}
          </Button>
        </div>
      </div>
    )
  }

  renderBindButton(flg = true) {
    return (
      <Tooltip placement="bottomRight" title={i18n.get('支付结果短信通知服务')}>
        <div className="msg-btn" onClick={flg ? this.handleBindPhone.bind(this) : this.changeVisible.bind(this, true)}>
          <img src={MSG_ICON} alt="" />
        </div>
      </Tooltip>
    )
  }

  renderHeaderBtn = () => {
    const { showRrefeshBtn, isShowLeftLabel, onRefresh } = this.props
    if (showRrefeshBtn) {
      return (
        <Button category="secondary" className="refresh-btn" onClick={onRefresh} data-testid="pay-pendingPayingTableHead-refresh-btn">
          <Icon type="reload" theme="outlined" />
          {i18n.get('刷新支付结果')}
        </Button>
      )
    }
    return isShowLeftLabel ? (
      <span></span>
    ) : (
      <label className="lbl">{i18n.get('支付中的单据')}</label>
      // <FilterBar      //支付中的数据先不加筛选了
      //   shouldDisabledDate={this.props.shouldDisabledDate}
      //   style={{ marginBottom: '10px' }}
      //   sdate={this.props.startDate}
      //   edate={this.props.endDate}
      //   queryType={'tobepaid'}
      //   showSearch={false}
      //   showBillType={false}
      //   onChange={this.props.handleFilterBarChange}
      // />
    )
  }

  renderBindContent = () => {
    const { bindPhoneInfo, isShowRightBindButton } = this.props
    if (bindPhoneInfo) {
      return (
        <Popover
          visible={this.state.popVisible}
          placement="bottomRight"
          title={i18n.get('支付结果短信通知服务')}
          content={this.renderBindPhonePop()}
          trigger="click"
        >
          {this.renderBindButton(false)}
        </Popover>
      )
    }
    return isShowRightBindButton ? null : this.renderBindButton()
  }

  setOfflineFinishedTime = result => {
    const avaliableObjStr = result?.value?.filter?.[0]
    try {
      const avaliableObj = JSON.parse(avaliableObjStr)
      api.dispatch(actions.setOfflineFinishedTime(avaliableObj?.offlineFinishTimeAvailable))
    } catch (error) {
      api.dispatch(actions.setOfflineFinishedTime(false))
    }
  }

  getUpdatePayFinishedTime = async () => {
    const result = await getScenes(OFFLINE_FINISH_TIME_AVAILABLE)
    this.setOfflineFinishedTime(result)
  }

  handleUpdatePayFinishedTimeChange = async e => {
    const result = await saveScenes({
      type: OFFLINE_FINISH_TIME_AVAILABLE,
      filter: [JSON.stringify({ offlineFinishTimeAvailable: e.target.checked })],
    })
    this.setOfflineFinishedTime(result)
  }

  render() {
    const {
      searchPlaceholder,
      isShowRightBindButton,
      showCheckBtn,
      offlineFinishTimeAvailable,
      showUpdatePayFinishedTimeCheckbox,
    } = this.props
    const { payingCheckVisible } = this.state
    return (
      <div className="table-header-c">
        {this.renderHeaderBtn()}
        <div className={classNames('search-bar-pending', { clearMR: isShowRightBindButton })}>
          {showUpdatePayFinishedTimeCheckbox && (
            <OrderTypeLabel
              label={
                <Checkbox checked={offlineFinishTimeAvailable} onChange={this.handleUpdatePayFinishedTimeChange}>
                  {i18n.get('更新线下支付完成时间')}
                </Checkbox>
              }
              tooltipTitle={i18n.get('当支付方式为线下支付时，在支付中页面点击确认支付，可输入该批次实际支付完成时间')}
            />
          )}
          {payingCheckVisible && showCheckBtn && (
            <Button category="primary" className="check-btn" onClick={this.handleImport} data-testid="pay-pendingPayingTableHead-check-btn">
              {i18n.get('校验支付结果')}
            </Button>
          )}
          <Search
            allowClear={true}
            placeholder={searchPlaceholder || i18n.get('输入搜索批次号、标题、单号')}
            style={{ width: 280, flexShrink: 0, marginLeft: 'auto', marginRight: 16 }}
            onSearch={this.handleSearch}
          />
          {isShowRightBindButton ? null : <span className="line-txt" />}
          {this.renderBindContent()}
        </div>
      </div>
    )
  }
}
