import React from "react";
import style from "./checkPayMethodStatusBar.module.less";
import classNames from "classnames";

// eslint-disable-next-line react/display-name
export const LoadingFill = React.memo(() => (
  <svg viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M25 0C38.8072 0 50 11.1929 50 25C50 38.8072 38.8072 50 25 50C11.1929 50 0 38.8072 0 25C0 11.1929 11.1929 0 25 0Z"
      fill="#E8F3FF" />
    <path
      d="M26.6667 38.3334L28.3334 31.6667H32.4084C34.0748 29.8463 34.9993 27.468 35.0001 25.0001C35.0001 20.4501 31.9534 16.4917 27.7934 15.2601C27.1134 15.0601 26.6667 14.4101 26.6667 13.7001C26.6667 12.6217 27.6951 11.7917 28.7301 12.0951C34.2767 13.7284 38.3334 18.9317 38.3334 25.0001C38.3334 31.7284 33.1434 37.4217 26.6667 38.3334ZM11.6667 25.0001C11.6667 18.2734 16.8567 12.5784 23.3334 11.6667L21.6667 18.3334H17.5917C15.9254 20.1539 15.0009 22.5321 15.0001 25.0001C15.0001 29.5501 18.0467 33.5084 22.2067 34.7384C22.5372 34.8435 22.8251 35.052 23.028 35.3332C23.2308 35.6144 23.3379 35.9534 23.3334 36.3001C23.3334 37.3784 22.3051 38.2084 21.2701 37.9051C15.7234 36.2717 11.6667 31.0684 11.6667 25.0001Z"
      fill="#165DFF" />
  </svg>
));

// eslint-disable-next-line react/display-name
export const SuccessFill = React.memo(() => (
  <svg viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M25 0C38.8071 0 50 11.1929 50 25C50 38.8071 38.8071 50 25 50C11.1929 50 0 38.8071 0 25C0 11.1929 11.1929 0 25 0Z"
      fill="#E8FFEA" />
    <path
      d="M33.1497 17.4262L34.4264 18.4976C35.1319 19.0889 35.2238 20.1401 34.6322 20.8453C34.6321 20.8454 34.632 20.8455 34.6315 20.8453L23.4849 34.122C22.8928 34.8265 21.8418 34.9183 21.1365 34.3271L18.583 32.1845L30.8013 17.6314C31.3934 16.9268 32.4444 16.835 33.1497 17.4262Z"
      fill="#00B42A" />
    <path
      d="M14.3055 26.2355L15.4238 24.9936C16.03 24.3203 17.063 24.2546 17.7496 24.8456L25.589 31.5929L23.4561 34.1593C22.8678 34.8673 21.817 34.9642 21.1091 34.3759C21.1003 34.3686 21.0917 34.3613 21.0831 34.3538L14.4529 28.6105C13.7571 28.0078 13.6817 26.9553 14.2844 26.2595C14.2913 26.2515 14.2984 26.2435 14.3055 26.2355Z"
      fill="#00B42A" />
  </svg>

));

// eslint-disable-next-line react/display-name
export const FailedFill = React.memo(() => (
  <svg viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M25 0C38.8072 0 50 11.1929 50 25C50 38.8072 38.8072 50 25 50C11.1929 50 0 38.8072 0 25C0 11.1929 11.1929 0 25 0Z"
      fill="#FFECE8" />
    <path
      d="M32.2183 16.1906L33.8682 17.8405C34.3889 18.3612 34.3889 19.2054 33.8682 19.7261L28.5634 25.03L33.8682 30.3346C34.3889 30.8553 34.3889 31.6996 33.8682 32.2203L32.2183 33.8702C31.6976 34.3909 30.8534 34.3909 30.3327 33.8702L25.0267 28.565L19.7261 33.8682C19.2054 34.3889 18.3612 34.3889 17.8405 33.8682L16.1906 32.2183C15.6699 31.6976 15.6699 30.8534 16.1906 30.3327L21.4917 25.03L16.1906 19.728C15.6699 19.2073 15.6699 18.3631 16.1906 17.8424L17.8405 16.1925C18.3612 15.6718 19.2054 15.6718 19.7261 16.1925L25.0284 21.495L30.3327 16.1906C30.8534 15.6699 31.6976 15.6699 32.2183 16.1906Z"
      fill="#F53F3F" />
  </svg>
));


type CheckPayMethodStatusBarProps = {
  isValidating: boolean
  available: boolean
  error?: string
  showModal: boolean
  onReCheck?: () => void
}


export const CheckPayMethodStatusBar: React.VFC<CheckPayMethodStatusBarProps> = props => {
  const {
    isValidating,
    available,
    error,
    onReCheck
  } = props;

  return (
    <div className={style.check_pay_method_status_bar}>
      {isValidating && (
        <span className={classNames(style.check_pay_method_status, "loading")}>
          <span className={style.check_pay_method_status_tip}>{i18n.get('支付通道检测中，请稍后')}</span>
          <LoadingFill />
        </span>
      )}
      {!isValidating && available && !error && (
        <span className={classNames(style.check_pay_method_status)}>
          <span className={style.check_pay_method_status_tip}>{i18n.get('支付通道通畅，可以发起支付')}</span>
          <SuccessFill />
        </span>
      )}
      {!isValidating && (!available || error) && (
        <div className={classNames(style.check_pay_method_status)}>
          <span className={style.check_pay_method_status_tip}>{i18n.get('{channel}支付通路连接异常，请联系管理员', {channel: i18n.get('财资大管家')})}</span>
          <FailedFill />
          <span className={style.check_pay_method_status_action}>
            <span className={style.check_pay_method_status_button} onClick={onReCheck} data-testid="pay-checkPayMethodStatusBar-retry-btn">重试</span>
          </span>
        </div>
      )}
    </div>
  )
};
