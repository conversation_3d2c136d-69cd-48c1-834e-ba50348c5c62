export const checkCreditCode = (_rule: any, value: string, callback: (mag?: string) => void) => {
  const regs = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/
  if (!regs.test(value)) {
    return callback(i18n.get('请输入正确的统一社会信用代码'))
  }
  callback()
}

export const checkIDCard = (_rule: any, value: string, callback: (mag?: string) => void) => {
  const regs = /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|[1-2]\d|30|31)\d{3}[\dXx]$/
  if (!regs.test(value)) {
    return callback(i18n.get('请输入正确的身份证'))
  }
  callback()
}

export const checkPhone = (_rule: any, value: string, callback: (mag?: string) => void) => {
  const regs = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/
  if (!regs.test(value)) {
    return callback(i18n.get('请输入正确的手机'))
  }
  callback()
}

export const checkEmail = (_rule: any, value: string, callback: (mag?: string) => void) => {
  const regs = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  if (!regs.test(value)) {
    return callback(i18n.get('请输入正确的邮箱'))
  }
  callback()
}