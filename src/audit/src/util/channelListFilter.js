export function sortSelectPaymentChannelList(channelList, powerList = {}) {
  const { OpenAPI, K3Cloud, WalletPower, XJLPPower, souchePower, fingardPower, CHANGJIEPay, ALIPAY_switch } = powerList
  const arr = []
  if (Array.isArray(channelList)) {
    channelList.forEach(item => {
      if (item.channel === 'CHANPAY' && CHANGJIEPay) {
        arr.push(item)
      }
      if (item.channel === 'CREDITEASE' && XJLPPower) {
        arr.push(item)
      }
      if (item.channel === 'ERP' && (OpenAPI || K3Cloud)) {
        arr.push(item)
      }
      if (item.channel === 'WALLET' && WalletPower) {
        arr.push(item)
      }
      if (item.channel === 'SOUCHE' && souchePower) {
        arr.push(item)
      }
      if (item.channel === 'FINGARD' && fingardPower) {
        arr.push(item)
      }
      if (item.channel === 'OFFLINE') {
        arr.push(item)
      }
      if (item.channel === 'ALIPAY' && ALIPAY_switch) {
        arr.push(item)
      }
    })
  }
  return arr
}
