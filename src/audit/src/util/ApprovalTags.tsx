import React from 'react'
import { Space, Tag, Popover } from '@hose/eui'
const getTagColor = (type, status) => {
  if (status === 0) {
    return 'neu'
  }
  switch (type) {
    case 'AUTO':
      return 'cyan'
    case 'MANUAL':
      return 'pri'
    default:
      return 'pri'
  }
}

export function renderTags(tags: any[], styleObj?: any) {
  return (
    <Space wrap style={styleObj || {}}>
      {tags.map(item => <Tag color={getTagColor(item.tagType, item.status)}>
        {item?.label}
      </Tag>)}
    </Space>
  )
}

export default function ApprovalTags(props: any) {
  const { tags, tagsStyle } = props
  if (!tags?.length) return null
  if (tags?.length === 1) {
    return renderTags(tags, tagsStyle)
  }
  return (
    <div>
      {renderTags([tags[0]], tagsStyle)}
      <Popover content={renderTags(tags)}>
        <span style={{ marginLeft: 8 }}>
          <Tag>{i18n.get('更多...+{__k0}', {__k0: tags?.length - 1})}</Tag>
        </span>
      </Popover>
    </div>
  )
}
