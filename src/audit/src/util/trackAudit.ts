/**
 *  Created by pw on 2021/3/11 下午12:57.
 */

import { app } from '@ekuaibao/whispered'

export function trackAuditFistTime(backlogParam: any) {
  if (!!window.__AUDIT_FIRST_TIME) {
    const status = backlogParam?.status
    if (status && status.length) {
      const [firstStatus] = status
      track('待办列表首次加载', 'myFlowTableFirstTime', firstStatus)
    } else {
      track('待办列表首次加载', 'myFlowTableFirstTime')
    }
  }
  window.__AUDIT_FIRST_TIME = 0
}

export function trackAuditNextTime(backlogParam: any) {
  const currentPage = backlogParam?.page?.currentPage
  if (currentPage > 1) {
    const status = backlogParam?.status
    if (status && status.length) {
      const [firstStatus] = status
      track('待办列表点击下一页', 'myFlowTableNextTime', firstStatus)
    } else {
      track('待办列表点击下一页', 'myFlowTableNextTime')
    }
  }
}
export function newTrack(key: string, options: any) {
  const staff = app.getState()['@common'].userinfo?.staff
  window.TRACK &&
    window.TRACK(key, {
      source: window.__PLANTFORM__,
      companyId: staff?.corporation?.id,
      corName: staff?.corporation?.name,
      userId: staff?.userId,
      userName: staff?.name,
      time: new Date().getTime(),
      ...options,
    })
}
function track(actionName: string, eventName: string, status?: string) {
  // if (window.TRACK) {
  //   const userInfo = app.getState()['@common'].userinfo?.data
  //   const corpId = userInfo?.staff?.corporationId?.id
  //     ? userInfo?.staff?.corporationId.id
  //     : userInfo?.staff?.corporationId
  //   const params: any = {
  //     actionName,
  //     corpId,
  //     decice: 'Web',
  //     status,
  //   }
  //   if (eventName === 'myFlowTableFirstTime') {
  //     params[eventName] = Date.now() - window.__AUDIT_FIRST_TIME
  //   }
  //   window.TRACK('myAuditTableTime', params)
  // }
}
