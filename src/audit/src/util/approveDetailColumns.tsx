/**************************************
 * Created By LinK On 2023/7/31 16:21.
 **************************************/
import React from 'react'
import ApprovalTags from './ApprovalTags'

const approveDetailColumns = () => {
  return [
    {
      title: i18n.get('单据模版'),
      dataIndex: 'form.flowSpecificationId.name',
      sorter: true,
      width: 200,
      className: 'fs-14',
      dataType: 'text',
      filterType: 'list',
      key: 'form.flowSpecificationId.name',
      label: i18n.get('单据模版'),
      value: 'form.flowSpecificationId.name',
    },
    {
      title: i18n.get('单号'),
      dataIndex: 'form.flow.form.code',
      sorter: false,
      width: 130,
      className: 'fs-14',
      dataType: 'text',
      key: 'form.flow.form.code',
      label: i18n.get('单号'),
      value: 'form.flow.form.code',
    },
    {
      title: i18n.get('审批意见'),
      dataIndex: 'form.tags',
      sorter: false,
      width: 300,
      key: 'form.tags',
      label: i18n.get('审批意见'),
      value: 'form.tags',
      render: (value: any) => {
        if (!value) return '-'
        return <ApprovalTags tags={value}/>
      }
    },
  ]
}

export default approveDetailColumns