import React from 'react'
import { parseQuery2Select } from '@ekuaibao/lib/lib/parseQuery2Select'
import { app as api } from '@ekuaibao/whispered'
const fetchFixer = api.require<any>('@elements/data-grid/fetchFixer').default
const { parseMeta2ColumnOthers, parseColumnFilter }: any = api.require('@elements/data-grid/columnOthers')
import { Icon } from 'antd'
import { getAuditSecondList, getAuditPayPlanList, getPaymentPayPlanList } from '../audit-action'

export function parseOptions({
  options,
  entityInfo = {},
  fieldMap,
  dataLinkList,
  flowId,
  type = 'DATALINK',
}: any) {
  let param = Object.assign({}, options)
  const { name } = options
  param = fetchFixer(param, { ...fieldMap, 'form.dataLinkCount': { name: 'form.dataLinkCount' } })
  let { filters = {} } = param
  const { searchText } = param
  param.filters = filters
  const { fields } = entityInfo
  let columns = []
  if (searchText && searchText.length) {
    columns = fields.slice().filter(
      //@i18n-ignore
      (f: any) =>
        f.name.indexOf('_编号') > -1 ||
        f.name.indexOf('_name') > -1 ||
        f.name.indexOf('_提交人') > -1,
    )
    columns = columns.map((f: any) => {
      if (f.name.indexOf('_提交人') > -1) {
        //@i18n-ignore
        return `form.${f.name}.name`
      } else {
        return `form.${f.name}`
      }
    })
  }
  if (name === 'receipt') {
    // 回单管理
    columns = ['form.E_system_单号_编号', 'code']
  }
  let query = parseQuery2Select(param, undefined, columns, dataLinkList, flowId)
  query = query.value()
  return query
}

export function fnInitColumns(fields: any) {
  let columns: any[] = []
  const columnsList: any = {}
  Object.keys(fields).forEach(key => {
    columnsList[key] = fields[key].map((column: any) => ({
      ...newParseColumn(column),
      width: column.width || 220,
      allowHiding: true,
      allowReordering: true,
      allowGrouping: true,
      lookup: column.filterType === 'list' &&
        column.filterDataSource instanceof Array && {
          dataSource: column.filterDataSource,
          displayExpr: 'label',
          valueExpr: 'value',
        },
    }))
    columns = columns.concat(columnsList[key])
  })
  return { columns, columnsList }
}

function newParseColumn(column: any) {
  let { name: dataIndex, dataType, label } = column
  const type = dataType?.type
  const newName = dataIndex?.split('.').pop()
  const property = { ...column, name: newName }
  const others = parseMeta2ColumnOthers(newName, property)
  const filterOptions = parseColumnFilter(property)

  switch (dataIndex) {
    case 'form.E_system_paymentPlan_提交人':
    case 'paymentId.form.E_system_paymentPlan_提交人':
      dataIndex = dataIndex += '.name'
      break
    case 'form.E_system_paymentPlan_支付金额':
    case 'paymentId.form.E_system_paymentPlan_支付金额':
      dataIndex = dataIndex += '.standard'
      break
  }

  return {
    name: dataIndex,
    title: label,
    dataIndex,
    dataType: type,
    key: dataIndex,
    sorter: newName !== 'imageUrl',
    label,
    value: dataIndex,
    className: 'fs-14',
    filterType: newName !== 'imageUrl' ? type : null,
    property: column,
    ...others,
    ...filterOptions
  }
}

export function ColumnIcon(data: any) {
  const { from, line, instance, fnChangePayPlanDataSource } = data
  const onExpandRow = async () => {
    instance?.expandRow(line?.id)
    let batchData: any = []
    let auditPayPlanData: any = []
    let paymentsPayPlanData: any = []
    switch (from) {
      case 'batch':
        batchData = await getAuditSecondList(line?.id)
        break
      case 'payPlan':
        auditPayPlanData = await getAuditPayPlanList({ batchId: line?.batchId, flowId: line?.id })
        break
      case 'payments':
        paymentsPayPlanData = await getPaymentPayPlanList(line?.id)
        break
      default:
        break
    }
    let res: any = []
    switch (from) {
      case 'batch':
        res = batchData
        break
      case 'payPlan':
        res = auditPayPlanData
        break
      case 'payments':
        res = paymentsPayPlanData
        break
      default:
        break
    }
    fnChangePayPlanDataSource?.(line, res)
  }

  const onCollapseRow = () => {
    instance?.collapseRow(line?.id)
    fnChangePayPlanDataSource?.(line, [])
  }

  return (
    <div style={{ marginLeft: '-12px' }}>
      {instance?.isRowExpanded?.(line?.id) ? (
        <div onClick={onCollapseRow}>
          <Icon type="caret-down" style={{ transform: 'scale(.6)' }} />
        </div>
      ) : (
        <div onClick={onExpandRow}>
          <Icon type="caret-right" style={{ transform: 'scale(.6)' }} />
        </div>
      )}
    </div>
  )
}

export function PaymentReviewColumnIcon(data: any) {
  const { from, line, instance, fnChangePayPlanDataSource } = data
  const onExpandRow = async () => {
    instance?.expandRow(line?.id)
    let paymentBatchData: any = []
    let paymentPlanData: any = []
    if (from === 'PaymentReviewBatch') {
      paymentBatchData = await getAuditSecondList(line?.batchId)
    } else {
      paymentPlanData = await getAuditPayPlanList({ batchId: line?.batchId, flowId: line?.id })
    }
    const res = from === 'PaymentReviewBatch' ? paymentBatchData : paymentPlanData
    fnChangePayPlanDataSource?.(line, res || [])
  }

  const onCollapseRow = () => {
    instance?.collapseRow(line?.id)
    fnChangePayPlanDataSource?.(line, [])
  }

  return (
    <div style={{ marginLeft: '-12px' }}>
      {instance?.isRowExpanded?.(line?.id) ? (
        <div onClick={onCollapseRow}>
          <Icon type="caret-down" style={{ transform: 'scale(.6)' }} />
        </div>
      ) : (
        <div onClick={onExpandRow}>
          <Icon type="caret-right" style={{ transform: 'scale(.6)' }} />
        </div>
      )}
    </div>
  )
}
