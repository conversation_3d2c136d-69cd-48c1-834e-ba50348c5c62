import { app as api } from '@ekuaibao/whispered'
import styles from './audit.approve.module.less'
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
const ETabs = api.require('@elements/ETabs')
import HistoryWrapper from './approve/HistoryWrapper'
import History from './approve/History'
import ApproveWrapper from './approve/ApproveWrapper'
import ApprovedWrapper from './approve/ApprovedWrapper'
import MessageCenter from '@ekuaibao/messagecenter'
import { connect } from '@ekuaibao/mobx-store'
import { Checkbox } from '@hose/eui'
import classnames from 'classnames'
import { Universal_Unique_Key } from './index'

const { UniversalComponent } = api.require('@elements/UniversalComponent')

@connect(store => ({ size: store.states['@layout'].size }))
@EnhanceConnect(state => ({
  baseDataProperties: state['@common'].globalFields.data,
  budgetPower: state['@common'].powers.Budget,
  staffs: state['@common'].staffs,
  userInfo: state['@common'].userinfo.data,
}))
export default class AuditApprove extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
    this.state = {
      isLightingMode: false,
      hideLightningApproveEnter: false,
    }
  }

  componentWillMount() {
    api.dataLoader('@common.staffs').load()
  }

  handleTabChange = type => {
    if (type === 'approving') {
      this.bus.reload && this.bus.reload()
    }
  }

  fnChangeMode(e) {
    this.setState({ isLightingMode: e.target.checked })
  }

  hideLightningApproveEnter = hideLightningApproveEnter => {
    this.setState({ hideLightningApproveEnter })
  }

  renderLightningApproveEnter = () => {
    if (this.state.hideLightningApproveEnter) return true
    return (
      <UniversalComponent uniqueKey={`${Universal_Unique_Key}.lightningApprove`}>
        <Checkbox
          className="lightning-send-btn lightning-send-content"
          onChange={this.fnChangeMode.bind(this)}>
          {i18n.get('闪电审批')}
        </Checkbox>
      </UniversalComponent>
    )
  }

  render() {
    const dataSource = [
      {
        tab: i18n.get('待审批'),
        children: (
          <ApproveWrapper
            {...this.props}
            bus={this.bus}
            hideLightningApproveEnter={this.hideLightningApproveEnter}
          />
        ),
        key: 'approving',
        isHidden: this.state.isLightingMode,
      },
      {
        tab: i18n.get('已审批'),
        children: <ApprovedWrapper {...this.props} />,
        key: 'approved',
        isHidden: this.state.isLightingMode,
      },
      {
        tab: i18n.get('审批记录'),
        children:  <History  {...this.props} />,
        key: 'history',
        isHidden: this.state.isLightingMode,
      },
      {
        tab: i18n.get('闪电审批'),
        children: <ApproveWrapper {...this.props} isLightingMode="true" bus={this.bus} />,
        key: 'lightning',
        isHidden: !this.state.isLightingMode,
      },
    ]
    const cls = classnames(styles.approve_wrapper, {
      [styles.approve_wrapper_layout5]: window.isNewHome,
    })
    return (
      <div id="audit-approve" className={cls}>
        <ETabs
          isHoseEUI={true}
          className="ekb-tab-line-left"
          defaultActiveKey={this.state.isLightingMode ? 'lightning' : 'approving'}
          tabBarStyle={{ paddingTop: 6, height: 40, width: '100%' }}
          onChange={this.handleTabChange}
          dataSource={dataSource}
        />
        {this.renderLightningApproveEnter()}
      </div>
    )
  }
}
