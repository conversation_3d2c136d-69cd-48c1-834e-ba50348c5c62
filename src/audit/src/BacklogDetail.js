import React, { PureComponent } from 'react'
import { UIContainer as Container, app as api } from '@ekuaibao/whispered'
import * as viewUtil from './view-util'
import { handleActionImplementation } from './service'
import { newTrack } from './util/trackAudit'
import qs from 'qs'

export default class BacklogDetail extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.state = { backlogType: undefined }
  }

  componentWillMount() {
    const params = qs.parse(location.search.slice(1))
    this.setState({ backlogType: params?.backlogType })
    this.loadData()
  }

  loadData = async () => {
    const [isBacklog, backlogId] = await isBacklogAndBacklogId(this.props)
    await api.dataLoader('@common.globalFields').load()
    const params = qs.parse(location.search.slice(1))
    const { flowId } = params || {}
    if (isBacklog) {
      return this.loadBacklog(backlogId)
    }
    if (params?.backlogType === 'carbonCopy') {
      const res = await api.invokeService('@audit:get:carbonCopy:by:flowId', { id: flowId })
      this.setState({ backlogType: res?.value?.state === 'READ' ? undefined : 'carbonCopy' })
    }

    return api
      .invokeService('@bills:get:flow-info', {
        id: backlogId,
      })
      .then(({ value }) => {
        const backlog = { id: -1, flowId: value, type: value.formType }
        this.setState({
          backlog,
        })
      })
  }

  loadBacklog = async backlogId => {
    const backlog = await api.invokeService('@audit:get:backlog-info', {
      id: backlogId,
    })
    if (backlog.state === 'PROCESSED') {
      backlog.id = -1
    }
    this.props.onChangeTitle?.(backlog)
    const riskTip = await viewUtil.getLoanRisk(backlog)
    this.setState({
      backlog,
      riskTip,
    })
  }

  handleFooterClick = (type, line) => {
    handleActionImplementation.call(this, {
      type,
      backlog: line,
      fn: () => {
        this.loadData()
        this.props.onClose && this.props.onClose()
      },
    })
  }

  handleMarkedReadClick = () => {
    const { backlog } = this.state
    api.invokeService('@audit:marked:read', { ids: backlog?.flowId.id }).then(_ => {
      // 标记已读之后把状态改掉
      this.setState({ backlogType: undefined })
      this.loadData()
    })
  }

  render() {
    const { backlog, riskTip } = this.state
    if (!backlog) return false
    const { backlogType } = this.state
    const backlogData = backlogType === 'carbonCopy' ? { ...backlog, state: 'UNREAD' } : backlog
    const { customStyle, bottomToolbar } = this.props
    const billDetail = document.getElementById('bill-detail')
    const offsetWidth = billDetail?.offsetWidth || 900
    return (
      <Container
        name="@bills:BillDetail"
        isEditConfig={backlog.id !== -1}
        isShowCondition
        riskTip={riskTip}
        backlog={backlogData}
        offsetWidth={offsetWidth}
        onFooterButtonsClick={this.handleFooterClick}
        reload={this.props.onClose ? this.props.onClose : this.loadData.bind(this)}
        invokeService="@audit:get:backlog-info"
        params={{
          id: backlog.id,
          type: backlog.type,
        }}
        backlogType={backlogType}
        customStyle={customStyle}
        bottomToolbar={bottomToolbar}
        onOpenOwnerLoanList={() => this.handleFooterClick(9, backlog)}
        onFooterMarkedReadClick={this.handleMarkedReadClick}
      />
    )
  }
}

const isBacklogAndBacklogId = async (props) => {
  let backlogId = props?.params?.id || ''
  let isBacklog = props?.params?.isBacklog
  const params = qs.parse(location.search.slice(1))
  const { flowId, isEmail } = params || {}
  // 路由参数上有值('true' | 'false')说明是从邮件审批跳转过来
  if (isBacklog) {
    // 审核人是否点击单据link跳转至报销单界面埋点
    newTrack('Email_approval_sent_display', {
      navWeight: i18n.get('邮件审批跳转'),
      Approval_page_display_source: isEmail ? '邮件' : '非邮件',
      flowID: flowId,
    })
  }
  if (isBacklog === 'true') {
    isBacklog = true
  }
  if (flowId) {
    const res = await api.invokeService('@bills:get:current:backLog', flowId)
    // 如果backlogId不存在，代表待办已经审批过不在当前用户身上
    if (res.id?.length) {
      backlogId = res.id
    } else {
      isBacklog = false
      backlogId = flowId
    }
  }
  if (props.isBackLog && !!props.backLogId) {
    isBacklog = true
    backlogId = props.backLogId
  }
  return [isBacklog, backlogId]
}