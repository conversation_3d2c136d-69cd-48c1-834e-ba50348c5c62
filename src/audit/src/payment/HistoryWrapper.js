/**************************************************
 * Created by nany<PERSON><PERSON><PERSON> on 20/09/2017 12:19.
 **************************************************/
import React from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import PaymentTable from '../record-table/payment-record-table'
import moment from 'moment'
import { app as api } from '@ekuaibao/whispered'
import * as actions from '../audit-action'
import { map } from 'lodash'
import key from '../key'
import { BaseHistoryComponent } from '../BaseHistoryComponent'
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')
import './HistoryWrapper.less'
import { connect } from '@ekuaibao/mobx-store'
import { trackApproved } from '../util/fetchUtil'

@connect(store => ({ size: store.states['@layout'].size }))
@EnhanceConnect(state => ({
  paymentRecords: state[key.ID].payment_record_list,
  dataRecords: state[key.ID].operation_records,
  dataBills: state[key.ID].operation_bills,
  channelList: state['@audit'].channelList,
}))
export default class OperationView extends BaseHistoryComponent {
  constructor() {
    super()
    let sdate = moment().subtract(1, 'months').format('YYYY-MM-DD') + ' 00:00:00'
    let edate = moment().format('YYYY-MM-DD') + ' 23:59:59'
    this.state = {
      queryType: 'payment', //record:历史记录 operation:操作纪录
      pagination: { current: 1, pageSize: 10 },
      sdate: sdate,
      edate: edate,
      billType: 'all',
      payState: 'all',
      searchText: '',
      inSearch: false,
      sorter: { order: 'descend', field: 'finishTime' },
    }
    this.filterMap = {
      date: `finishTime>${moment(sdate).valueOf()}&&finishTime<${moment(edate).valueOf()}`,
      state: `state.in("SUCCESS","FAILURE")`,
    }
    this.initSorter = { order: 'descend', field: 'finishTime' }
    this.initPage = { current: 1, pageSize: 10 }
    this.initDate = {
      sdate,
      edate,
    }
  }

  componentDidMount() {
    this.handlePaymentTableChange(this.state.pagination, this.state.queryType)
  }

  componentWillReceiveProps(nextProps) {
    let total = nextProps.paymentRecords.count
    const pager = this.state.pagination
    pager.total = total
    this.setState({ pagination: pager })
  }

  handlePaymentTableChange = (
    paginationData,
    filterData,
    sorter,
    isSearch = false,
    fresh = false,
  ) => {
    const pagination = paginationData ?? this.initPage
    const filter = filterData ?? {}
    this._setPager(pagination)

    if (filter.date) {
      let { sdate, edate } = filter.date
      this.setState({ sdate, edate })
    }

    if (filter.state) {
      this.setState({ payState: filter.state })
    }

    if (isSearch) {
      this.setState({
        searchText: filter.custom,
      })
    } else {
      const { searchText } = this.state
      if (searchText) {
        filter.custom = searchText
      }
    }

    let order = this._setSorter(sorter)
    this._setFilterMap(filter)
    let filterMap = this.filterMap
    let filterStr = map(filterMap, (v, key) => v).join('&&')
    let params = {
      start: (pagination.current - 1) * pagination.pageSize,
      count: pagination.pageSize,
      filter: filterStr,
      order,
    }
    const start = new Date().getTime()
    api
      .dispatch(
        actions.getPaymentRecord(params, undefined, pagination.pageMode === 'scroll' && !fresh),
      )
      .then(() => {
        const end = new Date().getTime()
        const t = end - start
        trackApproved(t, 'paymentRecordTime', '支付记录用时')
      })
  }

  handleRowClick = async flow => {
    startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()
    const action = await actions.getFlowInfo(flow.flowId)
    api.dispatch(action).then(flow => {
      api.open(
        '@bills:BillInfoPopup',
        {
          title: i18n.get('支付记录'),
          backlog: { id: -1, flowId: flow },
          onFooterButtonsClick: this.handlePrint,
          invokeService: '@audit:get:history-flow:info',
          // params: flow.flowId,
          params: flow.id,
        },
        true,
      )
    })
  }

  render() {
    return (
      <div className="history-wrapper">
        <PaymentTable
          {...this.props}
          sdate={this.state.sdate}
          edate={this.state.edate}
          pagination={this.state.pagination}
          queryType={this.state.queryType}
          searchText={this.state.searchText || ''}
          payState={this.state.payState}
          onRowClick={this.handleRowClick.bind(this)}
          onTableChange={this.handlePaymentTableChange.bind(this)}
        />
      </div>
    )
  }
}
