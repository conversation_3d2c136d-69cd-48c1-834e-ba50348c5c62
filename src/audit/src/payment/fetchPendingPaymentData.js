/**************************************************
 * Created by nany<PERSON>ingfeng on 28/09/2017 11:59.
 **************************************************/
import { app as api } from '@ekuaibao/whispered'
import { getPaymentBatch } from '../audit-action'
import { Resource } from '@ekuaibao/fetch'
const refreshAll = new Resource('/api/pay/v1/refreshAll')

export function fetchPendingPaymentData() {
  let param = {
    start: 0,
    count: 2999,
    order: 'asc(createTime)',
    filter: 'active==true',
    join: `accountId,accountCompany,/pay/v1/accounts`
  }
  api.dispatch(getPaymentBatch(param))
}

export function refreshPaymentForAntAliPay() {
  return refreshAll.GET()
}
