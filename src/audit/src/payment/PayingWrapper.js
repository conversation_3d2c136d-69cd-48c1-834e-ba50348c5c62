import './PayingWrapper.less'
import { app, app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
const LoaderWithLegacyData = app.require('@elements/data-grid-v2/LoaderWithLegacyData')
const { createNodeNameColumn, createNodeStaffColumn } = api.require('@elements/data-grid-v2/CreateColumn')
import { fetchBackLogs, getScenes, scenesFilter, trackBtnClick } from '../util/fetchUtil'
import MessageCenter from '@ekuaibao/messagecenter'
import { mapping } from '../util/mapping4paid'
import { getLoanRisk } from '../view-util'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import {
  handleActionImplementation,
  handlePrint,
  handlePrintRemind,
  handleRejectBills,
  handleTransfer,
  handleReceivePrint,
  handleHangUp,
} from '../service'
import { createActionColumn4Pay, createHangUpColumn } from '../util/columnsAndSwitcherUtil'
const { exportExcel } = app.require('@lib/export-excel-service')
import { searchBackLogsCalcNoPage, paymentPlanCreate } from '../audit-action'
import { EnhanceConnect } from '@ekuaibao/store'
import { fnFilterMapping, getInitScenes } from '../util/Utils'
import Animation from '../elements/Animation/Animation'
import './PayingWrapper.less'
import { newTrack } from '../util/trackAudit'
const { searchOptions, globalSearchOptions } = api.require('@lib/data-grid-v2/CustomSearchUtil')
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

const scenesType = 'PAYING'
const prefixColumns = { state: 'flowId', '*': 'flowId.form' }

@EnhanceConnect(state => ({
  specifications: state['@custom-specification'].specificationGroupsList,
  legalEntityCurrencyPower: state['@common'].powers.legalEntityCurrency,
  invoiceReviewPower: state['@common'].powers.invoiceReview,
  inBatchPaying: state['@audit'].inBatchPaying,
  // 涉及需求 https://hose2019.feishu.cn/wiki/wikcncXiMtXZQwNcS64OMEwSsJd#fTSuk8
  KA_GLOBAL_SEARCH_2: state['@common'].powers.KA_GLOBAL_SEARCH_2,
  showPrintBtn: state['@common'].showPrintBtn,
}))
export default class PayingWrapper extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
    this.state = {
      scenes: [],
      isLightingMode: this.props.isLightingMode,
    }
    window.__AUDIT_FIRST_TIME = Date.now()
  }
  fnfilterMCButs = () => {
    if (window.__PLANTFORM__ === 'MC') {
      this.buttons = this.buttons.filter(
        line => line.name !== 'approve_transfer' && line.name !== 'approve_sign_transfer',
      )
    }
  }
  componentDidMount() {
    this.bus.on('buttons:click', this.handleButtonsClick)
    this.bus.on('table:row:click', this.handleTableRowClick)
    this.bus.on('table:row:action', this.handleActions)
    this.bus.on('selectAllBtn:click', this.handleSelectAllBtnClick)
    this.bus.on('initScenes:action', this.initScenes)
    // 获取场景列表
    getScenes(scenesType).then(async res => {
      const { value } = res
      const { specifications = [] } = this.props
      if (specifications.length === 0) {
        await api.invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned', {}, { hiddenLoading: true })
      }
      this.initScenes(value)
    })

    paymentPlanCreate()
  }

  componentWillUnmount() {
    this.bus.un('buttons:click', this.handleButtonsClick)
    this.bus.un('table:row:click', this.handleTableRowClick)
    this.bus.un('table:row:action', this.handleActions)
    this.bus.un('selectAllBtn:click', this.handleSelectAllBtnClick)
    this.bus.un('initScenes:action', this.initScenes)
  }

  initScenes = data => {
    const { specifications, userInfo } = this.props
    const scenesData = getInitScenes({ data, prefix: 'flowId', specifications, isHasWaitInvoice: false })
    this.setState({ scenes: scenesData.scenes })
  }

  fetchPaying = async (params = {}, dimensionItems = {}) => {
    params.status = { state: ['PAYING'] }
    const { scenes } = this.state
    const { scene = 'all' } = params
    const findScene = scenes.find(s => s.sceneIndex === scene)
    findScene && (params.scene = findScene.scene || '')
    scene && this.setState({ scene, fetchParams: params, dimensionItems })

    const res = await fetchBackLogs(params, findScene, dimensionItems)
    this._currentDataSource = res.dataSource
    return res
  }

  handleSelectAllBtnClick = (params = {}) => {
    trackBtnClick('auditAllBtnClick', '待支付选择全部按钮点击')
    params.status = { state: ['PAYING'] }
    const { scenes, scene, fetchParams, dimensionItems, isLightingMode } = this.state
    const findScene = scenes.find(s => s.sceneIndex === scene)
    findScene && (params.scene = findScene.scene || '')
    if (isLightingMode) {
      params = { ...params, filters: this.fnGetLightingModeParam() }
    } else {
      if (fetchParams) {
        if (fetchParams.filters) params.filters = fetchParams.filters
        if (fetchParams.searchText) params.searchText = fetchParams.searchText
      }
    }

    const { legalEntityCurrencyPower, showPrintBtn } = this.props
    return searchBackLogsCalcNoPage(params, findScene, dimensionItems, legalEntityCurrencyPower).then(resp => {
      const data = {}
      resp &&
        resp.value &&
        resp.value.flows.length > 0 &&
        resp.value.flows.forEach(flow => {
          data[flow.id] = flow
        })
      const sum = (resp && resp.value && resp.value.formMoney) || 0
      const keys = Object.keys(data)
      let buttons = [
        {
          name: i18n.get('支付'),
          type: 'primary',
          key: 'pay',
        },
        {
          name: i18n.get('驳回'),
          type: 'normal',
          key: 'reject',
          dangerous: true,
        },
        {
          name: i18n.get('导出'),
          type: 'normal',
          key: 'export_all',
        },
        {
          name: i18n.get('打印单据'),
          type: 'normal',
          key: 'print',
        },
        {
          name: i18n.get('打印提醒'),
          type: 'normal',
          key: 'print_remind',
        },
        {
          name: i18n.get('转交审批'),
          type: 'normal',
          key: 'approve_transfer',
        },
        {
          name: i18n.get('加签审批'),
          type: 'normal',
          key: 'approve_sign_transfer',
        },
        {
          name: i18n.get('收到打印'),
          type: 'normal',
          key: 'recive_print',
        },
      ]

      if (showPrintBtn)
        buttons = buttons
          .slice(0, 4)
          .concat([
            {
              name: i18n.get('打印单据和发票'),
              type: 'normal',
              key: 'printInvoice',
            },
          ])
          .concat(buttons.slice(4, buttons.length))

      if (window.__PLANTFORM__ === 'MC') {
        buttons = buttons.filter(line => line.key === 'approve_transfer' && line.key !== 'approve_sign_transfer')
      }
      api.open('@layout:DataGridSelectAllModal', { keys, buttons, sum }).then(name => {
        trackBtnClick(`auditAllBtnClick_${name}`, `待支付选择全部弹窗里${name}按钮点击`)
        this.handleButtonsClick({ params, name, data, keys, isSelectAll: true })
      })
    })
  }

  fnGetLightingModeParam = () => {
    const data = this.bus.getDataSource && this.bus.getDataSource()
    const codes = data.map(line => line.flowId.form.code)
    return { 'flowId.form.code': codes }
  }

  handleButtonsClick = ({ params, name, data, keys, isSelectAll }) => {
    keys = keys ? keys : this.bus.getSelectedRowKeys()
    data = data ? data : this.bus.getSelectedRowData()
    switch (name) {
      case 'pay':
        return this._handlePayBills(keys, data, isSelectAll)
      case 'reject':
        return this._handleRejectBills(keys, data)
      case 'print':
        return this._handlePrintList(keys, data, this.reloadAndClearSelecteds, '0')
      case 'printInvoice':
        return this._handlePrintList(keys, data, this.reloadAndClearSelecteds, '1')
      case 'export_selected':
        return exportExcel({ exportType: 'export_selected', funcType: 'approve', data }, this.bus)
      case 'export_all':
        return this._handleExportAll(params)
      case 'print_remind':
        return this._handlePrintRemindList(keys, data)
      case 'approve_transfer':
        return this._handleTransfer(keys, data)
      case 'approve_sign_transfer':
        return this._handleTransfer(keys, data, true)
      case 'recive_print':
        const flowIds = Object.values(data).map(item => item.flowId.id)
        this.handleReceivePrint(flowIds, this.reloadAndClearSelecteds)
        break
      case 'hangUp':
        const ids = Object.values(data).map(item => item.id)
        this.handleHangUp(ids, () => {
          this.reloadAndClearSelecteds()
        })
        break
      default:
        break
    }
  }

  reloadAndClearSelecteds = () => {
    this.bus.clearSelectedRowKeys && this.bus.clearSelectedRowKeys()
    this.bus.reload &&
      this.bus.reload().then(() => {
        setTimeout(() => {
          this.bus.emit('table:select:current:row')
        }, 500)
      })
  }

  _handlePrintRemindList = (keys, data) => {
    const backLogs = Object.values(data)
    const flowIds = backLogs.map(element => element.flowId.id)
    handlePrintRemind.call(this, flowIds, this.__actionDone)
  }

  _handlePrintList(keys, data, fn, printInvoice) {
    // 待我支付需要按照排序顺序进行打印
    const sortByIndex = true
    handlePrint.call(this, keys, data, fn, sortByIndex, printInvoice)
  }

  _handleTransfer(keys, data, isSignNode) {
    handleTransfer.call(this, {
      ids: keys,
      data,
      showAfter: false,
      fn: this.__actionDone,
      isSignNode,
    })
  }

  async _handlePayBills(keys, data, isSelectAll) {
    newTrack('audit_unpaid_table_pay_btn')
    const backlogs = []
    keys.forEach(key => {
      data[key] && backlogs.push(data[key])
    })

    // 决定是否要传fetchParams
    let { fetchParams, isLightingMode } = this.state
    if (isSelectAll) {
      if (!fetchParams) {
        fetchParams = { status: { state: ['PAYING'] } }
      }
      let scene = this.fnGetScene()
      if (scene && scene.sceneIndex === 'waitInvoice') {
        scene = { scene: 'waitInvoice' }
      }
      fetchParams.scene = scene
      fetchParams.dimensionItems = this.state.dimensionItems || {}
      if (isLightingMode) {
        fetchParams.filters = this.fnGetLightingModeParam()
      }
    } else {
      fetchParams = undefined
    }

    this.__handleLine({ type: 6, line: backlogs, fetchParams })
  }

  __actionDone = () => {
    api.invokeService('@layout5:refresh:menu:data')
    this.reloadAndClearSelecteds()
  }

  __handleLine({ type, line, riskData, actionType, fetchParams }) {
    handleActionImplementation.call(this, {
      type,
      backlog: line,
      fn: this.__actionDone,
      riskData,
      actionType,
      fetchParams,
    })
  }

  handleReceivePrint = (key, fn) => {
    handleReceivePrint.call(this, key, fn)
  }

  handleHangUp = (key, fn) => {
    handleHangUp.call(this, key, fn)
  }

  _handleRejectBills(keys, data) {
    newTrack('audit_unpaid_table_reject_btn')
    handleRejectBills.call(this, keys, data, this.__actionDone)
  }

  fnGetScene() {
    const { scenes, scene } = this.state
    const findScenes = scenes.find(s => s.sceneIndex === scene)
    if (findScenes && findScenes.scene) return scene
    return findScenes
  }

  _handleExportAll(params) {
    let { status } = params
    params.filters = { ...params.filters }
    status = { ...status, state: ['PAYING'] }
    params = { ...params, status }
    params.scene = this.fnGetScene()
    exportExcel({ exportType: 'export_all', funcType: 'backlog', data: params, onlyAsyncExport: true }, this.bus)
  }

  __billInfoPopup = backlog => {
    getLoanRisk(backlog).then(riskTip => {
      const title = `${i18n.get(billTypeMap()[backlog.type])}${i18n.get('详情')}`
      api.open(
        '@bills:BillInfoPopup',
        {
          title,
          backlog,
          riskTip,
          invokeService: '@audit:get:backlog-info',
          params: { id: backlog.id, type: backlog.type },
          isEditConfig: this.props.state === 'APPROVING',
          isShowCondition: true,
          reload: this.bus.reload,
          source: 'payment',
          scene: 'APPROVER',
          needConfigButton: true,
          mask: false,
          onOpenOwnerLoanList: line => {
            this.__handleLine({ type: 9, line })
          },
          onFooterButtonsClick: (type, line, riskData) => {
            if (![3, 5, 6].includes(type)) {
              api.close()
            }
            setTimeout(() => {
              this.__handleLine({ type, line, riskData, actionType: 'single' })
            }, 0)
          },
        },
        true,
      )
    })
  }

  handleTableRowClick = backlog => {
    startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()

    if (api.require('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
      api.open('@bills:BillInfoDrawerV2', {
        currentId: backlog.flowId.id,
        flows: this._currentDataSource?.map((item => item.flowId)) || [],
        backlogs: this._currentDataSource,
        bus: this.bus,
        billDetailsProps: {
          isEditConfig: this.props.state === 'APPROVING',
          source: 'payment',
        },
        onOpenOwnerLoanList: line => {
          this.__handleLine({ type: 9, line })
        },
        onFooterButtonsClick: (type, line, riskData) => {
          if (![3, 5, 6].includes(type)) {
            api.close()
          }
          setTimeout(() => {
            this.__handleLine({ type, line, riskData, actionType: 'single' })
          }, 0)
        },
      })
      return
    }

    api
      .invokeService('@audit:get:backlog-info', {
        id: backlog.id,
        type: backlog.type,
      })
      .then(backlog => {
        this.__billInfoPopup(backlog)
      })
  }

  handleActions = (type, line) => {
    handleActionImplementation.call(this, {
      type,
      backlog: line,
      fn: this.__actionDone,
      actionType: 'single',
    })
  }

  getOptionButtons = () => {
    const btns = [
      { text: i18n.get('支付'), name: 'pay', type: 'primary' },
      { text: i18n.get('驳回'), name: 'reject', minorGroup: false, dangerous: true },
      { text: i18n.get('导出选中'), name: 'export_selected' },
      { text: i18n.get('打印单据'), name: 'print' },
      { text: i18n.get('打印单据和发票'), name: 'printInvoice' },
      { text: i18n.get('打印提醒'), name: 'print_remind' },
      { text: i18n.get('转交审批'), name: 'approve_transfer' },
      { text: i18n.get('加签审批'), name: 'approve_sign_transfer' },
      { text: i18n.get('收到打印'), name: 'recive_print' },
      { text: i18n.get('暂挂审批'), name: 'hangUp' },
    ]
    return this.props.showPrintBtn ? btns : btns.filter(v => v.name !== 'printInvoice')
  }

  buttons = this.getOptionButtons()

  render() {
    const { scenes, isLightingMode } = this.state
    const { baseDataProperties, invoiceReviewPower, inBatchPaying, KA_GLOBAL_SEARCH_2 } = this.props
    if (!scenes.length) {
      return null
    }
    this.fnfilterMCButs()
    return (
      <div className="paying-table-wrapper">
        <LoaderWithLegacyData
          newSearch={true}
          searchOptions={KA_GLOBAL_SEARCH_2 ? globalSearchOptions() : searchOptions()}
          enableGlobalSearch={KA_GLOBAL_SEARCH_2}
          lightingMode={isLightingMode}
          disabledScenes={isLightingMode}
          scenes={scenes}
          fetch={this.fetchPaying}
          scenesType={scenesType}
          buttons={this.buttons}
          onButtonClick={this.handleButtonsClick}
          onSelectedAll={this.handleSelectAllBtnClick}
          prefixColumns={prefixColumns}
          bus={this.bus}
          resource={scenesFilter}
          baseDataProperties={baseDataProperties}
          createAction={createActionColumn4Pay}
          mapping={fnFilterMapping(mapping, invoiceReviewPower)}
          status={'PAYING'}
          createHangUpColumn={createHangUpColumn}
          createNodeNameColumn={() => createNodeNameColumn({ dataIndex: 'flowId.nodeState.nodeName' })}
          createNodeStaffColumn={() =>
            createNodeStaffColumn({ dataIndex: 'flowId.nodeState.staffName', filterType: false })
          }
          useNewFieldSet
        />
        {inBatchPaying && (
          <div className="animation">
            <Animation />
            <div className="animation-text">{i18n.get('批量支付中...')}</div>
          </div>
        )}
      </div>
    )
  }
}
