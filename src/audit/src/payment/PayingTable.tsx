import React from 'react'
import * as DataGrid from '@ekuaibao/datagrid'
import { Column } from '@ekuaibao/datagrid/esm/types/column'
import { Data } from '@ekuaibao/datagrid/esm/types/dataSource'
import { Provider } from '@ekuaibao/datagrid/esm/State'
import { Language } from '@ekuaibao/datagrid/esm/i18n/Language'
import styles from '../paymentManagement/PaymentReviewWrap.module.less'
import { PayingSecondTable } from './PayingSecondTable'
import classNames from 'classnames'
import { ColumnIcon } from '../util/tableUtil'
import { PaginationConfig } from '@ekuaibao/datagrid/esm/types/pagination'
import { i18nText } from '../elements/AdjustPaymentPlan/components/DoContainer/lang'

export interface PayingTableProps {
  columns: Column[]
  secondTableColumns: Column[]
  dataSource: Data[]
  rowKey?: string
  isNeedHeight?: boolean
  getInstance: (instance: any) => void
  from?: string
  paginationProps: {
    totalLength: number
    pagination: {
      current: number
      size: number
    }
  }
  onPaginationChange?: (pagination: PaginationConfig) => void
  loadingError?: boolean
}

export interface PayingTableState {
  batchColumns: any[]
  payPlanDataSourceMap: any
}

export class PayingTable extends React.PureComponent<PayingTableProps, PayingTableState> {
  instance: any
  state: PayingTableState = {
    batchColumns: this.props.columns || [],
    payPlanDataSourceMap: {},
  }

  getInstance = (instance: any) => {
    this.instance = instance
    if (instance) {
      const iconCol = {
        title: '',
        dataIndex: 'id',
        allowReordering: false,
        allowGrouping: false,
        allowResizing: false,
        allowEditing: false,
        allowFiltering: false,
        sorter: false,
        width: 40,
        minWidth: 40,
        render: (_: any, record: any) => {
          return (
            <ColumnIcon
              from={this.props?.from}
              line={record}
              instance={instance}
              fnChangePayPlanDataSource={this.fnChangePayPlanDataSource}
            />
          )
        },
      }
      this.setState({ batchColumns: [iconCol, ...this.state.batchColumns] })
    }
    this.props.getInstance(instance)
  }

  fnChangePayPlanDataSource = (line: any, res: any) => {
    const map = this.state.payPlanDataSourceMap
    map[line?.id] = res
    this.setState({ payPlanDataSourceMap: { ...map } })
  }

  renderDetailTemplate = (props: any) => {
    const { rowKey, secondTableColumns, from } = this.props
    const { payPlanDataSourceMap } = this.state
    const payPlanDataSource = payPlanDataSourceMap[props?.data?.id] || []
    return (
      <PayingSecondTable
        {...props}
        key={props?.data?.id}
        from={from}
        rowKey={rowKey}
        columns={secondTableColumns}
        payPlanDataSource={payPlanDataSource}
      />
    )
  }

  handlePageChange = (pagination: PaginationConfig) => {
    this.props?.onPaginationChange?.(pagination)
  }

  render() {
    const { dataSource, isNeedHeight = true, paginationProps, loadingError } = this.props
    const { batchColumns } = this.state
    return (
      <div
        className={classNames(styles.container, isNeedHeight ? styles.containerHeight : styles.payPlanContainerHeight)}
      >
        <Provider>
          <Language texts={i18nText} />
          <DataGrid.TableWrapper
            rowKey="id"
            scrolling={{ mode: 'standard' }}
            standard
            className={`${styles.tableWrapper} ${loadingError ? styles.tableWrapperError : ''}`}
            dataSource={loadingError ? [] : dataSource}
            columns={batchColumns}
            allowColumnReordering
            allowColumnResizing
            getInstance={this.getInstance}
            isMultiSelect={false}
            isSingleSelect={false}
            RenderDetailTemplate={this.renderDetailTemplate}
            enabledDetailTemplate={false}
            pageIndex={paginationProps?.pagination?.current ?? 1}
            pageSize={paginationProps?.pagination?.size ?? 20}
            loadPanel={{
              enabled: true,
              showPane: false,
              text: i18n.get('加载中…'),
            }}
          />
          {paginationProps ? (
            <footer className={styles['table-footer-wrapper']}>
              <DataGrid.Pagination
                {...paginationProps}
                pageMode="pagination"
                onChange={this.handlePageChange}
                disabledScroll
                totalLength={loadingError ? 0 : paginationProps?.totalLength}
              />
            </footer>
          ) : (
            void 0
          )}
        </Provider>
      </div>
    )
  }
}

export default PayingTable
