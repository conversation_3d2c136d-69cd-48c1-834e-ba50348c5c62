import './PayingWrapper.less'
import { app, app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
const LoaderWithLegacyData = app.require('@elements/data-grid-v2/LoaderWithLegacyData')
import { fetchPayPlanBackLogs, getScenes, scenesFilter } from '../util/fetchUtil'
import MessageCenter from '@ekuaibao/messagecenter'
import { mapping } from '../util/mapping4paid'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import { handleActionImplementation, handleRejectBills } from '../service'
import { createActionDetailsColumn4Pay } from '../util/columnsAndSwitcherUtil'
import { EnhanceConnect } from '@ekuaibao/store'
import {
  customColumnPropertyMapping4PayPlan,
  fnFilterMapping,
  getInitScenes,
} from '../util/Utils'
import Animation from '../elements/Animation/Animation'
import { fnInitColumns } from '../util/tableUtil'
import { prepareRender, getLoanRisk } from '../view-util'
import { paymentPlanCreate, getBackLogByFlowId, getPayPlanFields } from '../audit-action'
const { searchOptionPayee, searchOptionPayNote, searchOptionSubmitter } = api.require('@lib/data-grid-v2/CustomSearchUtil')
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

const scenesType = 'PAYINGBYDETAILS'
const prefixColumns = { state: 'flowId', '*': '' }

@EnhanceConnect(state => ({
  specifications: state['@custom-specification'].specificationGroupsList,
  legalEntityCurrencyPower: state['@common'].powers.legalEntityCurrency,
  invoiceReviewPower: state['@common'].powers.invoiceReview,
  inBatchPaying: state['@audit'].inBatchPaying,
  dynamicChannelMap: state['@audit'].dynamicChannelMap,
  channelList: state['@audit'].channelList,
}))
export default class PayingWrapperByDetail extends PureComponent {
  bus = this.props.bus || new MessageCenter()

  state = {
    scenes: [],
    columns: [],
    columnsList: {},
  }

  async componentDidMount() {
    this.bus.on('buttons:click', this.handleButtonsClick)
    this.bus.on('table:row:click', this.handleTableRowClick)
    this.bus.on('table:row:action', this.handleActions)
    this.bus.on('initScenes:action', this.initScenes)
    const { dynamicChannelMap, channelList } = this.props
    const { value } = await getPayPlanFields()
    const { columns, columnsList } = fnInitColumns({
      flowForm: value.flowId,
      payPlanForm: value.form,
    })
    prepareRender(columns, dynamicChannelMap, channelList)
    this.setState({
      columns: [...columns],
      columnsList: { ...columnsList },
    })
    // 获取场景列表
    getScenes(scenesType).then(res => {
      this.initScenes(res?.value)
    })

    paymentPlanCreate()
  }

  componentWillUnmount() {
    this.bus.un('buttons:click', this.handleButtonsClick)
    this.bus.un('table:row:click', this.handleTableRowClick)
    this.bus.un('table:row:action', this.handleActions)
    this.bus.un('initScenes:action', this.initScenes)
  }

  initScenes = data => {
    const { specifications } = this.props
    const {
      columnsList: { payPlanForm },
    } = this.state
    const defaultColumns = [...payPlanForm.map(v => v.key).slice(0, -1), 'action']
    const scenesData = getInitScenes({data,prefix:'flowId', specifications, isHasWaitInvoice:false, defaultColumns})
    this.setState({ scenes:scenesData.scenes})
  }

  fetchPaying = async (params = {}, dimensionItems = {}) => {
    params.status = { 'flowId.state': ['paying'] }
    const { scenes } = this.state
    const { scene = 'all' } = params
    const findScene = scenes.find(s => s.sceneIndex === scene)
    findScene && (params.scene = findScene.scene || '')
    scene && this.setState({ scene, fetchParams: params, dimensionItems })

    const res = await fetchPayPlanBackLogs(params, findScene, dimensionItems, false)
    this._currentDataSource = res.dataSource
    return res
  }

  handleButtonsClick = ({ params, name, data, keys, isSelectAll }) => {
    keys = keys ? keys : this.bus.getSelectedRowKeys()
    data = data ? data : this.bus.getSelectedRowData()
    switch (name) {
      case 'pay':
        return this._handlePayBills(keys, data, isSelectAll)
      case 'reject':
        return this._handleRejectBills(keys, data)
    }
  }

  _handlePayBills = async (keys, data, isSelectAll) => {
    const backlogs = []
    keys.forEach(key => {
      data[key] && backlogs.push(data[key])
    })

    // 决定是否要传fetchParams
    let { fetchParams } = this.state
    if (isSelectAll) {
      if (!fetchParams) {
        fetchParams = { status: { 'flowId.state': ['paying'] } }
      }
      let scene = this.fnGetScene()
      if (scene && scene.sceneIndex === 'waitInvoice') {
        scene = { scene: 'waitInvoice' }
      }
      fetchParams.scene = scene
    } else {
      fetchParams = undefined
    }
    this.__handleLine({ type: 6, line: backlogs, fetchParams, isByDetail: true })
  }

  _handleRejectBills(keys, data) {
    handleRejectBills.call(this, keys, data, this.__actionDone, true)
  }

  __actionDone = () => {
    api.invokeService('@layout5:refresh:menu:data')
    this.reloadAndClearSelecteds()
  }

  reloadAndClearSelecteds = () => {
    this.bus.clearSelectedRowKeys && this.bus.clearSelectedRowKeys()
    this.bus.reload && this.bus.reload().then(() => {
      setTimeout(() => {
        this.bus.emit('table:select:current:row')
      }, 500);
    })
  }

  __handleLine({ type, line, riskData, fetchParams, isByDetail, tableSource }) {
    handleActionImplementation.call(this, {
      type,
      backlog: line,
      fn: this.__actionDone,
      riskData,
      fetchParams,
      isByDetail,
      tableSource,
    })
  }

  __billInfoPopup = backlog => {
    getLoanRisk(backlog).then(riskTip => {
      const title = `${i18n.get(billTypeMap()[backlog.type])}${i18n.get('详情')}`
      api.open(
        '@bills:BillInfoPopup',
        {
          title,
          backlog,
          riskTip,
          invokeService: '@audit:get:backlog-info',
          params: { id: backlog.id, type: backlog.type },
          isEditConfig: this.props.state === 'APPROVING',
          isShowCondition: true,
          reload: this.bus.reload,
          scene: 'APPROVER',
          needConfigButton: true,
          mask: false,
          onOpenOwnerLoanList: line => {
            this.__handleLine({ type: 9, line })
          },
          onFooterButtonsClick: (type, line, riskData) => {
            api.close()
            setTimeout(() => {
              this.__handleLine({ type, line, riskData, isByDetail: false, tableSource: 'partPay' })
            }, 0)
          },
        },
        true,
      )
    })
  }

  handleTableRowClick = row => {
    startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()

    if (api.require('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
      api.open('@bills:BillInfoDrawerV2', {
        currentId: row.flowId.id,
        flows: this._currentDataSource.map(v => v.flowId),
        bus: this.bus,
        billDetailsProps: {
          isEditConfig: this.props.state === 'APPROVING',
        },
        onOpenOwnerLoanList: line => {
          this.__handleLine({ type: 9, line })
        },
        onFooterButtonsClick: (type, line, riskData) => {
          api.close()
          setTimeout(() => {
            this.__handleLine({ type, line, riskData, isByDetail: false, tableSource: 'partPay' })
          }, 0)
        },
      })
      return
    }

    getBackLogByFlowId(row.flowId.id).then(this.__billInfoPopup)
  }

  handleActions = (type, line) => {
    handleActionImplementation.call(this, {
      type,
      backlog: line,
      fn: this.__actionDone,
      isByDetail: true,
    })
  }

  buttons = [
    { text: i18n.get('支付'), name: 'pay', type: 'primary' },
    { text: i18n.get('驳回'), name: 'reject', dangerous: true },
  ]

  render() {
    const { scenes, columns, columnsList } = this.state
    const { invoiceReviewPower, inBatchPaying } = this.props
    const customColumnPropertyMapping = customColumnPropertyMapping4PayPlan()
    if (!scenes.length) {
      return null
    }
    return (
      <div className="paying-table-wrapper">
        <LoaderWithLegacyData
          newSearch={true}
          searchOptions={[searchOptionPayee(), searchOptionPayNote(), searchOptionSubmitter()]}
          isGroup
          activeSceneIndex="all"
          columns={columns}
          columnsList={columnsList}
          scenes={scenes}
          fetch={this.fetchPaying}
          scenesType={scenesType}
          buttons={this.buttons}
          onButtonClick={this.handleButtonsClick}
          prefixColumns={prefixColumns}
          customColumnPropertyMapping={customColumnPropertyMapping}
          bus={this.bus}
          resource={scenesFilter}
          mapping={fnFilterMapping(mapping, invoiceReviewPower)}
          searchPlaceholder={i18n.get('搜索收款信息、支付概要或提交人')}
          createAction={createActionDetailsColumn4Pay}
          useNewFieldSet
        />
        {inBatchPaying && (
          <div className="animation">
            <Animation />
            <div className="animation-text">{i18n.get('批量支付中...')}</div>
          </div>
        )}
      </div>
    )
  }
}
