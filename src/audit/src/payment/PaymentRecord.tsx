import { app as api } from '@ekuaibao/whispered'
import { Column } from '@ekuaibao/datagrid/esm/types/column'
import { connect } from '@ekuaibao/mobx-store'
import { EnhanceConnect } from '@ekuaibao/store'
import React, { Component } from 'react'
import moment from 'moment'
import { DatePicker, Select } from 'antd'
import * as DataGrid from '@ekuaibao/datagrid'
import { PaginationConfig, PageMode } from '@ekuaibao/datagrid/esm/types/pagination'
const { filterChannels }: any = api.require('@components/utils/fnFilterPaymentChannel')
import * as actions from '../audit-action'
import key from '../key'
import * as viewUtil from '../view-util'
import styles from './PaymentRecord.module.less'
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

// 滚动模式下固定 pageSize
const SCROLL_PAGE_SIZE = 20

type PaidStatus = 'ALL' | 'SUCCESS' | 'FAILURE'
const paidLabelMap: { [key in PaidStatus]: string } = {
  ALL: i18n.get('全部'),
  SUCCESS: i18n.get('支付成功'),
  FAILURE: i18n.get('支付失败')
}

interface ActionParams {
  start: number
  count: number
  filter: string
  order: string
}
interface FilterMap {
  date?: string
  state?: string
  custom?: string
  ['accountCompany.name']?: string
  paymentChannel?: string
}

type Sorter = {
  field: string
  order: 'ascend' | 'descend'
}
interface PaymentRecordInnerProps {
  size: { x: number; y: number }
  dataRecords: any
  channelList: Array<{ channel: string; active: boolean }>
}

interface PaymentRecordProps extends PaymentRecordInnerProps {}

interface PaymentRecordState {
  dates: [string, string] // [startDate, endDate]
  paidStatus: PaidStatus
  pagination: PaginationConfig
  pageMode: PageMode
  sorter: Sorter
  loadedAllData: boolean
  dataSource: any[]
  total: number
}

@connect(store => ({
  size: store.states['@layout']['size']
}))
@EnhanceConnect((state: any) => ({
  dataRecords: state[key.ID].operation_records,
  channelList: state['@audit'].channelList,
  dynamicChannelMap: state['@audit'].dynamicChannelMap
}))
export class PaymentRecord extends Component<PaymentRecordProps, PaymentRecordState> {
  private dates: [string, string] = [
    moment()
      .subtract(1, 'months')
      .format('YYYY-MM-DD') + ' 00:00:00',
    moment().format('YYYY-MM-DD') + ' 23:59:59'
  ]
  private initialPagination: PaginationConfig = { current: 1, size: 10 }
  private initialFilter: FilterMap = {
    date: `finishTime>${moment(this.dates[0]).valueOf()}&&finishTime<${moment(this.dates[1]).valueOf()}`,
    state: `state.in("SUCCESS","FAILURE")`
  }
  private initialSorter: Sorter = { order: 'descend', field: 'finishTime' }
  // 历史记录表格的列内搜索(筛选)条件 和 表的整体搜索条件都存在这个对象里
  // 1. 原始的表格只有部分列有筛选
  // 2. 列内搜索是放在表格外部的
  // 3. 搜索通过拼 sql 语句来请求后端接口
  // 所以不确定能不能支持表格内部的每列搜索，暂时先禁用表格内部的每列筛选
  private filters = this.initialFilter
  private unmounted = false
  state: PaymentRecordState = {
    dates: this.dates,
    paidStatus: 'ALL',
    pagination: this.initialPagination,
    pageMode: 'pagination',
    // filters: this.initialFilter,
    // 接口貌似只支持单列排序
    sorter: this.initialSorter,
    loadedAllData: false,
    dataSource: [],
    total: 0
  }

  async componentDidMount() {
    const { dataSource, total } = await this.fetch(this.getActionParamsFromState())
    this.setState({ dataSource, total })
  }

  componentWillUnmount() {
    this.unmounted = true
  }

  render() {
    const { pagination, pageMode, sorter, dataSource, total } = this.state
    const columns = this.createColumns()
    const defaultVisibleColumns = columns.map(col => col.dataIndex)
    const dataGridSorters = { [sorter.field]: sorter.order }
    return (
      <div className={styles.container + ' records-operation-records-view'}>
        {this.renderTopFilterBar()}
        <div className={styles.body}>
          <DataGrid.TableWrapper
            className={styles.tableWrapper}
            columns={columns}
            dataSource={dataSource}
            defaultVisibleColumns={defaultVisibleColumns}
            allowColumnReordering
            allowColumnResizing
            isMultiSelect={false}
            sorters={dataGridSorters}
            groupPanel={{
              visible: true
            }}
            pageSize={pagination.size}
            onRowClick={this.handleRowClick}
            onSorterChange={this.handleSorterChange}
            onFilterChange={this.handleFilterChange}
            onReachBottom={this.handleReachBottom}
          />
          <DataGrid.Search
            placeholder={i18n.get('搜索单号或批次号')}
            onClear={this.handleClearSearch}
            onSearch={this.handleSearch}
            data-testid="pay-paymentRecord-search"
          />
        </div>
        <div className={styles.footer}>
          <DataGrid.Pagination
            totalLength={total}
            pagination={pagination}
            scrollPagination={{ size: SCROLL_PAGE_SIZE, current: pagination.current }}
            onChange={this.handlePaginationChange}
            pageMode={pageMode}
          />
        </div>
      </div>
    )
  }

  private renderTopFilterBar = () => {
    const { dates, paidStatus } = this.state
    const momentDates = dates.map(date => moment(date))
    return (
      <div className={styles.header + ' filter-bar horizontal'}>
        <div className="mr-10">{i18n.get('支付日期')}</div>
        <DatePicker.RangePicker
          size="large"
          style={{ width: 220 }}
          allowClear={false}
          value={momentDates}
          onChange={this.handleDateChange}
          data-testid="pay-paymentRecord-datePicker"
        />
        <span className="ml-20 mr-10">
          {i18n.get('支付状态')}
          {i18n.get('：')}
        </span>
        <Select size="large" value={paidStatus} style={{ width: 200 }} onChange={this.handlePaidStatusChange} data-testid="pay-paymentRecord-status-select">
          {Object.keys(paidLabelMap).map((status: PaidStatus) => (
            <Select.Option value={status} key={status}>
              {paidLabelMap[status]}
            </Select.Option>
          ))}
        </Select>
      </div>
    )
  }

  private fetch = async (params: ActionParams) => {
    const result: { count: number; items: any[] } = await api.dispatch(actions.getPaymentRecord(params))
    return { dataSource: result.items, total: result.count }
  }

  private handleDateChange = (dates: [moment.Moment, moment.Moment], dateStrings: [string, string]) => {
    const nextDates: [string, string] = [dateStrings[0] + ' 00:00:00', dateStrings[1] + ' 23:59:59']
    this.filters.date = `finishTime>${moment(nextDates[0]).valueOf()}&&finishTime<${moment(nextDates[1]).valueOf()}`
    this.setState(
      {
        dates: nextDates
      },
      async () => {
        const { dataSource, total } = await this.fetch(this.getActionParamsFromState())
        if (!this.unmounted) {
          this.setState({ dataSource, total })
        }
      }
    )
  }

  private handlePaidStatusChange = (status: PaidStatus) => {
    this.filters.state = status !== 'ALL' ? `state.in("${status}")` : `state.in("SUCCESS","FAILURE")`
    this.setState(
      {
        paidStatus: status
      },
      async () => {
        const { dataSource, total } = await this.fetch(this.getActionParamsFromState())
        if (!this.unmounted) {
          this.setState({ dataSource, total })
        }
      }
    )
  }

  private handlePaginationChange = (pagination: PaginationConfig, pageMode: PageMode) => {
    this.setState({ pagination, pageMode, loadedAllData: false }, async () => {
      const { dataSource, total } = await this.fetch(this.getActionParamsFromState())
      if (!this.unmounted) {
        this.setState({ dataSource, total })
      }
    })
  }

  private handleClearSearch = () => {
    this.handleSearch('')
  }

  private handleSearch = async (value: string) => {
    const customFilter = JSON.stringify(value).replace(/%/g, '\\\\%')
    if (customFilter) {
      this.filters.custom = `(batchChannelTradeNo.contains(${customFilter})||flowId.form.code.contains(${customFilter}))`
    } else {
      delete this.filters.custom
    }
    const { dataSource, total } = await this.fetch(this.getActionParamsFromState())
    if (!this.unmounted) {
      this.setState({ dataSource, total })
    }
  }

  private handleRowClick = async (record: any) => {
    const flow = record.flowDigest
    startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()
    const action = await actions.getFlowInfo(flow.id)
    api.dispatch(action).then((flow: any) => {
      api.open(
        '@bills:BillInfoPopup',
        {
          title: i18n.get('支付记录'),
          backlog: { id: -1, flowId: flow },
          onFooterButtonsClick: this.handlePrint,
          invokeService: '@audit:get:history-flow:info',
          params: flow.id
        },
        true
      )
    })
  }

  private handlePrint = (_type: any, backlog: any) => {
    // printBill(backlog.flowId)
  }
  private handleSorterChange = (sorter: { [columnIndex: string]: 'ascend' | 'descend' }) => {
    // 组件中 sorter 仅存在一个索引字段，所以只取第一个 sorter
    const sorterFiled = Object.keys(sorter)[0]
    const sorterValue = sorter[sorterFiled]
    this.setState(
      {
        sorter: { field: sorterFiled, order: sorterValue }
      },
      async () => {
        const { dataSource, total } = await this.fetch(this.getActionParamsFromState())
        if (!this.unmounted) {
          this.setState({ dataSource, total })
        }
      }
    )
  }

  // 当前组件仅支持 支付方式的列内筛选
  private handleFilterChange = async (filter: { [key: string]: string[] }) => {
    Object.keys(filter).forEach(columnIndex => {
      if (columnIndex === 'paymentChannel') {
        const filterValue = filter[columnIndex]
        if (filterValue && filterValue.length) {
          const filterStr = filterValue.map(v => JSON.stringify(v))
          this.filters[columnIndex] = `${key}.in(${filterStr})`
        } else {
          delete this.filters[columnIndex]
        }
      }
    })
    const { dataSource, total } = await this.fetch(this.getActionParamsFromState())
    if (!this.unmounted) {
      this.setState({ dataSource, total })
    }
  }

  private handleReachBottom = async () => {
    if (this.state.pageMode === 'pagination' || this.state.loadedAllData) {
      return
    }

    const { pagination, dataSource } = this.state
    const actionParams = this.getActionParamsFromState()
    const result = await this.fetch({
      ...actionParams,
      start: pagination.current * SCROLL_PAGE_SIZE
    })
    if (!this.unmounted) {
      if (result.dataSource && result.dataSource.length > 0) {
        this.setState({
          pagination: { current: pagination.current + 1, size: pagination.size },
          dataSource: [...dataSource, ...result.dataSource],
          total: result.total
        })
      } else {
        this.setState({
          loadedAllData: true
        })
      }
    }
  }

  private createColumns = (): Column[] => {
    const { channelList } = this.props
    const channels: Array<{ label: string; value: string }> = filterChannels(channelList)
    return [
      {
        title: i18n.get('支付日期'),
        label: i18n.get('支付日期'),
        dataIndex: 'finishTime',
        width: 120,
        sorter: true,
        render: viewUtil.tdDateTime.bind(this)
      },
      {
        title: i18n.get('单号'),
        label: i18n.get('单号'),
        dataIndex: 'flowDigest.code',
        width: 120,
        sorter: true,
        render: (code, line: any) => {
          return line.state === 'FAILURE'
            ? viewUtil.tdToolTipFailureText(code, line.respMsg, line.batchChannelTradeNo)
            : code
        }
      },
      {
        title: i18n.get('批次号'),
        label: i18n.get('批次号'),
        width: 150,
        dataIndex: 'batchChannelTradeNo',
        sorter: true,
        render: viewUtil.tdText.bind(this)
      },
      {
        title: i18n.get('收款信息'),
        label: i18n.get('收款信息'),
        width: 130,
        dataIndex: 'payee',
        render: viewUtil.tdAccountInfo.bind(this)
      },
      {
        title: i18n.get('付款账户'),
        label: i18n.get('付款账户'),
        width: 100,
        dataIndex: 'accountCompany.detail.name',
        render: viewUtil.tdText.bind(this)
      },
      {
        title: i18n.get('支付方式'),
        label: i18n.get('支付方式'),
        width: 100,
        dataIndex: 'paymentChannel',
        filterType: channels.length > 0 && 'list',
        render: viewUtil.tdChannel.bind(this),
        lookup: channels.length > 0 && { dataSource: channels, displayExpr: 'label', valueExpr: 'value' }
      },
      {
        title: i18n.get('支付金额({__k0})', { __k0: (window as any).CURRENCY_SYMBOL }),
        label: i18n.get('支付金额'),
        width: 120,
        sorter: true,
        dataIndex: 'flowDigest.paidAmount',
        render: viewUtil.tdAmount.bind(this)
      }
    ]
  }

  private getActionParamsFromState = () => {
    const { pagination, sorter, pageMode } = this.state
    const current = pagination.current
    let size = pagination.size
    size = pageMode === 'scroll' ? SCROLL_PAGE_SIZE : size
    const filterStr = Object.values(this.filters)
      .map(v => v)
      .join('&&')
    const orderStr = this.transToSorterStr(sorter)
    const params: ActionParams = {
      start: (current - 1) * size,
      count: size,
      filter: filterStr,
      order: orderStr
    }
    return params
  }

  private transToSorterStr = (sorter: Sorter) => {
    const { field, order } = sorter
    let transField = field
    if (field === 'flowDigest.paidAmount') {
      transField = 'flowId.form.payMoney'
    }
    if (field === 'flowDigest.code') {
      transField = 'flowId.form.code'
    }
    const transOrder = order.replace(/end/, '')
    return transOrder + '(' + transField + ')'
  }
}
