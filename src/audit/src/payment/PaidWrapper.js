import { app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import './PaidWrapper.less'
const LoaderWithLegacyData = api.require('@elements/data-grid-v2/LoaderWithLegacyData')
const { createNodeNameColumn, createNodeStaffColumn } = api.require('@elements/data-grid-v2/CreateColumn')
const { globalSearchOptions, searchOptions } = api.require('@lib/data-grid-v2/CustomSearchUtil')
import { fetchApproved } from '../util/fetchUtil'
import MessageCenter from '@ekuaibao/messagecenter'
import { mapping } from '../util/mapping4paid'
import { getPaymentPlanByPage } from '../audit-action'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import { handlePrint, rePay } from '../service'
const { exportExcel } = api.require('@lib/export-excel-service')
import { Resource } from '@ekuaibao/fetch'
import { EnhanceConnect } from '@ekuaibao/store'
import { fnFilterMapping, getInitScenes } from '../util/Utils'
import { preparePayPlanData } from '@ekuaibao/lib/lib/lib-util'
import { get } from 'lodash'
import { createActionColumn4Paid } from '../util/columnsAndSwitcherUtil'
import FilterBar from '../record-table/filter-bar'
import { getYearFirstDay } from '../util/Utils'
import moment from 'moment'
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

const paid = new Resource('/api/flow/v2/filter')
const scenesType = 'PAID'
const prefixColumns = { state: '$', '*': 'form' }
@EnhanceConnect(state => ({
  specifications: state['@custom-specification'].specificationGroupsList,
  invoiceReviewPower: state['@common'].powers.invoiceReview,
  legalEntityCurrencyPower: state['@common'].powers.legalEntityCurrency,
  KA_REEXCHANGE_PROCESSING: state['@common'].powers.KA_REEXCHANGE_PROCESSING,
  // 涉及需求 https://hose2019.feishu.cn/wiki/wikcncXiMtXZQwNcS64OMEwSsJd#fTSuk8
  KA_GLOBAL_SEARCH_2: state['@common'].powers.KA_GLOBAL_SEARCH_2,
  showPrintBtn: state['@common'].showPrintBtn,
}))
export default class PaidWrapper extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
    this.state = {
      scenes: [],
      total: 0,
      startDate: getYearFirstDay(),
      endDate: moment().format('YYYY-MM-DD') + ' 23:59:59',
      searchKey: 'E_system_paymentPlan_支付完成日期',
      controlDate: false,
    }
    window.__AUDIT_FIRST_TIME = Date.now()
  }

  componentDidMount() {
    api.invokeService('@audit:get:ppayment:plans:control').then(res => {
      this.setState({ controlDate: res.value })
    })
    this.bus.on('buttons:click', this.handleButtonsClick)
    this.bus.on('table:row:click', this.handleTableRowClick)
    this.bus.on('initScenes:action', this.initScenes)
    // 获取场景列表
    paid.GET('/$type', { type: scenesType }, undefined, { hiddenLoading: true }).then(async res => {
      const { value } = res
      const { specifications = [] } = this.props
      if (specifications.length === 0) {
        await api.invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned', {}, { hiddenLoading: true })
      }
      this.initScenes(value)
    })
  }

  componentWillUnmount() {
    this.bus.un('buttons:click', this.handleButtonsClick)
    this.bus.un('table:row:click', this.handleTableRowClick)
    this.bus.un('initScenes:action', this.initScenes)
  }

  initScenes = data => {
    const { specifications, userInfo } = this.props
    const scenesData = getInitScenes({ data, prefix: '', specifications, isHasWaitInvoice: false })
    this.setState({ scenes: scenesData.scenes })
  }

  __status = { state: ['paid', 'archived'], 'approvedFlow.action': 'PAID' }

  fetchPaid = (params = {}, dimensionItems = {}) => {
    params.status = this.__status
    const { scenes, startDate, endDate, searchKey } = this.state
    const { scene = 'all' } = params
    const findScene = scenes.find(s => s.sceneIndex === scene)
    findScene && (params.scene = findScene.scene || '')
    const newParams = {
      ...params,
      filters: params.filters['form.payDate']
        ? params.filters
        : {
            ...params.filters,
            [`form.payDate`]: { start: moment(startDate).valueOf(), end: moment(endDate).valueOf() }, //全局根据支付日期进行筛选
          },
    }
    scene && this.setState({ scene, fetchParams: newParams, dimensionItems })
    return fetchApproved(newParams, findScene, dimensionItems).then(result => {
      this._currentDataSource = result.dataSource
      this.setState({ total: result?.total })
      return result
    })
  }

  handleSelectAllBtnClick = (params = {}) => {
    params.status = this.__status
    const { scenes, scene, fetchParams } = this.state
    const findScene = scenes.find(s => s.sceneIndex === scene)
    findScene && (params.scene = findScene.scene || '')

    if (fetchParams) {
      if (fetchParams.filters) params.filters = fetchParams.filters
      if (fetchParams.searchText) params.searchText = fetchParams.searchText
    }
    const { total } = this.state
    const buttons = [
      {
        name: i18n.get('导出'),
        type: 'normal',
        key: 'export_all',
      },
    ]
    api
      .open('@layout:DataGridSelectAllModal', {
        totalCount: total,
        buttons,
        isBillsTotalMoney: true,
      })
      .then(name => {
        this.handleButtonsClick({ name, data: {}, keys: [] })
      })
  }

  handleButtonsClick = ({ name, data, keys }) => {
    keys = keys ? keys : this.bus.getSelectedRowKeys()
    data = data ? data : this.bus.getSelectedRowData()
    switch (name) {
      case 'print':
        return handlePrint(keys, data, undefined, false, '0')
      case 'printInvoice':
        return handlePrint(keys, data, undefined, false, '1')
      case 'export_selected':
        return exportExcel({ exportType: 'export_selected', funcType: 'approve', data }, this.bus)
      case 'export_all':
        const params = this.state.fetchParams
        return this._handleExportAll(params)
    }
  }

  fnGetScene = () => {
    const { scenes, scene } = this.state
    const findScenes = scenes.find(s => s.sceneIndex === scene)
    if (findScenes && findScenes.scene) return scene
    return findScenes
  }

  _handleExportAll = params => {
    const { userInfo } = this.props
    params.filters = { ...params.filters, 'approvedFlow.approverId': userInfo.staff.id }
    const scene = this.fnGetScene()
    params.scene = scene
    params.total = this.state.total
    exportExcel({ exportType: 'export_all', funcType: 'approve', data: params, needAsyncExport: true }, this.bus, true)
  }

  __handleRePay = flow => {
    const details = flow.form.details
    // payPlanMode: true为按金额多收款，flase为按明细多收款
    // multiplePayeesMode: 判断是否是多收款人单据
    // payeePayPlan: 按收款信息汇总明细金额
    const multiplePayeesMode = get(flow, 'form.multiplePayeesMode', false)
    if (multiplePayeesMode) {
      api
        .open('@audit:RepaymentModal', {
          flow: flow,
          form: flow.form,
          formType: flow.formType,
        })
        .then(data => {
          if (!data.length) return
          api.close()
          let f = { ...flow, details: data, isUseToReexchangePlanIds: data.map(i => i?.isUseToReexchangePlanId) || [] }
          const payeePayPlan = get(flow, 'form.payeePayPlan', false)
          const payPlanMode = get(flow, 'form.payPlanMode', false)
          if (flow.form && (payPlanMode || payeePayPlan)) {
            const isUseToReexchangePlanIds =
              flow?.form?.payPlan
                ?.filter(item => data?.some(i => item?.dataLinkId === i?.key))
                ?.map(item => item?.isUseToReexchangePlanId) || []
            f = { ...flow, payPlan: preparePayPlanData(data), isUseToReexchangePlanIds }
          }
          return rePay({ flowId: f })
        })
        .then(_ => {
          this.bus.reload()
        })
    } else {
      api.close()
      let f = { ...flow, details }
      if (flow.form) {
        f = { ...flow, payPlan: preparePayPlanData(details) }
      }
      //  去掉Promise.resolve()后，api.close()将失效
      return Promise.resolve()
        .then(_ => rePay({ flowId: f }))
        .then(_ => {
          this.bus.reload()
        })
    }
  }

  __billInfoPopup = async flow => {
    const { form } = flow
    const paymentAccountId = get(form, 'paymentAccountId.id')
    let hasReexchange = false
    let payPlans = []
    if (paymentAccountId) {
      const searchParams = {
        filterBy: `sourceId=="${flow.id}"`,
      }
      const res = await getPaymentPlanByPage(searchParams)
      payPlans = get(res, 'value.originalList', [])
      hasReexchange = payPlans.some(item => item.form['E_system_paymentPlan_支付状态'] === 'REEXCHANGE')
      if (hasReexchange) {
        // payPlanMode: true为按金额多收款，flase为按明细多收款
        // multiplePayeesMode: 判断是否是多收款人单据
        // payeePayPlan: 按收款信息汇总明细金额
        const multiplePayeesMode = get(flow, 'form.multiplePayeesMode', false)
        const payPlanMode = get(flow, 'form.payPlanMode', false)
        const payeePayPlan = get(flow, 'form.payeePayPlan', false)
        const details = get(flow, `form.details`, [])
        if (multiplePayeesMode) {
          if (payPlanMode || payeePayPlan) {
            payPlans.forEach((item, i) => {
              const payPlan = form.payPlan.find(el => el.dataLinkId === item.form.E_system_paymentPlan_srcId)
              payPlan.state = item.form['E_system_paymentPlan_支付状态']
              payPlan.isUseToReexchangePlanId = item?.id
              if (payeePayPlan) {
                const target = details?.find(el => el.feeTypeForm.detailId === item.form.E_system_paymentPlan_srcId)
                if (target?.feeTypeForm) {
                  const payeeId = target.feeTypeForm.feeDetailPayeeId
                  payPlan.dataLinkForm['E_system_支付计划_收款信息'] = payeeId
                }
              }
            })
          } else {
            payPlans.forEach((item, i) => {
              const detail = details.find(el => el.feeTypeForm.detailId === item.form.E_system_paymentPlan_srcId)
              detail.state = item.form['E_system_paymentPlan_支付状态']
              detail.isUseToReexchangePlanId = item?.id
            })
          }
        } else {
          const reexchangePlanIds =
            payPlans
              ?.filter(item => item.form?.['E_system_paymentPlan_支付状态'] === 'REEXCHANGE')
              ?.map(item => item?.id) || []
          flow.notMultiplePayeesModeReexchangePlanIds = reexchangePlanIds
        }
      }
    }
    const title = `${i18n.get(billTypeMap()[flow.formType])}${i18n.get('详情')}`
    const params = {
      title,
      invokeService: '@bills:get:flow-info',
      params: { id: flow.id },
      backlog: { id: -1, flowId: flow },
      reload: this.bus.reload,
      mask: false,
    }
    // 暂时关闭已支付页面单据中的重新支付功能
    if (this.props.KA_REEXCHANGE_PROCESSING && hasReexchange) {
      params.extendFooterBtn = {
        label: i18n.get('重新支付'),
        onClick: e => this.__handleRePay(flow),
      }
    }
    api.open('@bills:BillInfoPopup', params, true)
  }

  handleTableRowClick = flow => {
    startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()

    if (api.require('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
      api.open('@bills:BillInfoDrawerV2', {
        currentId: flow.id,
        flows: this._currentDataSource,
        bus: this.bus,
      })
      return
    }

    api.invokeService('@bills:get:flow-info', { id: flow.id }).then(resp => {
      this.__billInfoPopup(resp.value)
    })
  }

  getOptionButtons = () => {
    const btns = [
      { text: i18n.get('导出选中'), name: 'export_selected' },
      { text: i18n.get('打印单据'), name: 'print' },
      { text: i18n.get('打印单据和发票'), name: 'printInvoice' },
    ]
    return this.props.showPrintBtn ? btns : btns.filter(v => v.name !== 'printInvoice')
  }

  buttons = this.getOptionButtons()

  handleReExchange = () => {
    api.open('@audit:ReExchangeModal', { byStaff: true })
  }
  reexchangeEntry = () => {
    const { KA_REEXCHANGE_PROCESSING } = this.props
    return KA_REEXCHANGE_PROCESSING ? (
      <div className="paid-table-reexchange-entry" onClick={this.handleReExchange} data-testid="pay-paidWrapper-reexchange-entry">
        {i18n.get('退汇管理')}
      </div>
    ) : null
  }
  handleFilterBarChange = date => {
    this.setState({ startDate: date.sdate, endDate: date.edate }, () => {
      this.bus.reload(true)
    })
  }
  handleSearchKeyChange = value => {
    this.setState({ searchKey: value }, () => {
      this.bus.reload(true)
    })
  }
  onSearch = value => {
    this.bus.search(value)
  }
  onNewSearch = value => {
    this.bus.searchCustom(value)
  }
  render() {
    const { scenes, isLightingMode } = this.state
    const { baseDataProperties, invoiceReviewPower, KA_GLOBAL_SEARCH_2 } = this.props
    if (!scenes.length) {
      return null
    }
    return (
      <div className="paid-table-wrapper">
        <LoaderWithLegacyData
          enableGlobalSearch={KA_GLOBAL_SEARCH_2}
          lightingMode={isLightingMode}
          scenes={scenes}
          fetch={this.fetchPaid}
          scenesType={scenesType}
          buttons={this.buttons}
          onButtonClick={this.handleButtonsClick}
          onSelectedAll={this.handleSelectAllBtnClick}
          prefixColumns={prefixColumns}
          isHiddenSearch={true}
          bus={this.bus}
          resource={paid}
          headerStyle={{ display: 'flex', justifyContent: 'space-between' }}
          headerTopRender={() => (
            <FilterBar
              newSearch={true}
              searchOptions={KA_GLOBAL_SEARCH_2 ? globalSearchOptions('form') : searchOptions('form')}
              shouldDisabledDate={this.state.controlDate}
              style={{ marginBottom: '10px' }}
              sdate={this.state.startDate}
              edate={this.state.endDate}
              queryType={'auditPaid'}
              searchKey={this.state.searchKey}
              searchInputPlaceholder={
                KA_GLOBAL_SEARCH_2 ? i18n.get('搜索文本、数字类信息或提交人') : i18n.get('搜索标题、单号或提交人')
              }
              onSearch={this.onSearch}
              onNewSearch={this.onNewSearch}
              handleSearchKeyChange={this.handleSearchKeyChange}
              showBillType={false}
              onChange={this.handleFilterBarChange}
              data-testid="pay-paidWrapper-filterBar"
            />
          )}
          baseDataProperties={baseDataProperties}
          createAction={createActionColumn4Paid}
          menuBar={this.reexchangeEntry}
          mapping={fnFilterMapping(mapping, invoiceReviewPower)}
          createNodeNameColumn={() => createNodeNameColumn({ dataIndex: 'flowId.nodeState.nodeName' })}
          createNodeStaffColumn={() =>
            createNodeStaffColumn({ dataIndex: 'flowId.nodeState.staffName', filterType: false })
          }
          useNewFieldSet
        />
      </div>
    )
  }
}
