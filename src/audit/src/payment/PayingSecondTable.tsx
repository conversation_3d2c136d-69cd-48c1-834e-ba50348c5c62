import React from 'react'
import * as DataGrid from '@ekuaibao/datagrid'
import { Column } from '@ekuaibao/datagrid/esm/types/column'
import { Data } from '@ekuaibao/datagrid/esm/types/dataSource'
import styles from './table.module.less'
import { Map } from '@ekuaibao/datagrid/esm/types/utils'
import PaymentItemsTable from '../elements/PaymentItemsTable'
import { ColumnIcon } from '../util/tableUtil'
import { get } from 'lodash'

export interface PayingSecondTableProps {
  columns: Column[]
  data: {
    flowDigests: Data[]
    paymentPlans: any
  }
  rowKey?: any
  payPlanDataSource?: any[]
  from?: string
}

export interface PayingSecondTableState {
  filters: Map<any>
  sorters: Map<'ascend' | 'descend'>
  dataSource: Array<any>
  payPlanColumns: any[]
  detailDataMap: any
}
export class PayingSecondTable extends React.PureComponent<
  PayingSecondTableProps,
  PayingSecondTableState
> {
  private instance: any
  state: PayingSecondTableState = {
    filters: {},
    sorters: {},
    dataSource: this.props.payPlanDataSource || [],
    payPlanColumns: this.props.columns || [],
    detailDataMap: {},
  }
  componentDidMount() {
    if (this.instance) {
      this.instance.on('initialized', () => {
        const scrollable = this.instance.getScrollable()
        if (scrollable) {
          scrollable.option('rowRenderingMode', 'standard')
        }
      })
    }
  }

  componentWillReceiveProps(nextProps: any) {
    if (this.props.payPlanDataSource !== nextProps.payPlanDataSource) {
      this.setState({ dataSource: nextProps?.payPlanDataSource || [] })
    }
  }

  private sortImplementation = (
    isObject: false,
    type: string,
    sort: 'ascend',
    a: any,
    b: any,
    key: string,
    temp?: string,
  ) => {
    const aValue = get(a, key, '')
    const bValue = get(b, key, '')

    if (isObject && temp) {
      switch (type) {
        case 'number':
          if (sort === 'ascend') {
            return aValue[temp] - bValue[temp]
          } else {
            return bValue[temp] - aValue[temp]
          }
        case 'string':
          if (sort === 'ascend') {
            return aValue[temp].localeCompare(bValue[temp])
          } else {
            return bValue[temp].localeCompare(aValue[temp])
          }
      }
    } else {
      switch (type) {
        case 'number':
          if (sort === 'ascend') {
            return aValue - bValue
          } else {
            return bValue - aValue
          }
        case 'string':
          if (sort === 'ascend') {
            return aValue.localeCompare(bValue)
          } else {
            return bValue.localeCompare(aValue)
          }
      }
    }
  }

  private sortData = (data: any, key: string, sort: string) => {
    return data?.sort((a: any, b: any) => {
      const type = typeof get(a, key, '')
      const isNull = get(a, key, '') && get(b, key, '')
      if (isNull) {
        if (type === 'object') {
          let temp = 'standard'
          temp = a[key].hasOwnProperty(temp) ? temp : 'accountName'
          return this.sortImplementation(true, typeof a[key][temp], sort, a, b, key, temp)
        } else {
          return this.sortImplementation(false, type, sort, a, b, key)
        }
      }
    })
  }

  private handleSorterChange = (sorters: Map<'ascend' | 'descend'>) => {
    const sorterKey = Object.keys(sorters)[0]
    const sorterValue = sorters[Object.keys(sorters)[0]]
    const { filters } = this.state
    const { payPlanDataSource } = this.props
    let sortData = null
    if (Object.keys(filters).length) {
      const keys = Object.keys(filters)
      const values = Object.values(filters)
      sortData = this.filterData(payPlanDataSource, keys, values)
    }
    sortData = this.sortData(sortData || payPlanDataSource, sorterKey, sorterValue)
    this.setState({ sorters, dataSource: sortData, filters })
  }

  private filterData = (data: any, keys: string[], values: any[]) => {
    let filterRes = data || []
    keys &&
      keys.forEach((key, idx) => {
        filterRes =  filterRes?.filter(item => {
          let result = null
          const temp = get(item, key, '')
          if (Array.isArray(values[idx])) {
            if (typeof values[idx][0] === 'object') {
              result =
                values[idx][0].format('x') - 0 <= temp && values[idx][1].format('x') - 0 >= temp
            } else if (typeof values[idx][0] === 'string') {
              result = values[idx].includes(temp)
            }
          } else if (
            typeof values[idx] === 'object' &&
            values[idx].hasOwnProperty('start') &&
            values[idx].hasOwnProperty('end')
          ) {
            result = temp <= values[idx].end && temp >= values[idx].start
          } else {
            if (temp && typeof temp === 'string') {
              result = temp.indexOf(values[idx].toUpperCase()) > -1
            } else if (temp && typeof temp === 'object') {
              result = temp.accountName.indexOf(values[idx]) > -1
            }
          }
          return !!result
        })
      })
    return filterRes
  }

  private handleFilterChange = (filters: Map<any>) => {
    const keys = Object.keys(filters)
    let values = Object.values(filters) || []
    const { sorters, dataSource } = this.state
    const { payPlanDataSource } = this.props
    values = values.filter(item => item != undefined)
    if (!values.length) {
      this.setState({ filters, dataSource: payPlanDataSource || [], sorters })
      return
    }
    values =
      values.length &&
      values.map(item => {
        if (Array.isArray(item)) {
          return [...item]
        } else if (!!item) {
          return item
        }
      })
    let filterData = this.filterData(payPlanDataSource, keys, values)
    if (keys.length === 0) {
      filterData = dataSource || []
    }
    if (Object.keys(sorters).length) {
      const sorterKey = Object.keys(sorters)[0]
      const sorterValue = sorters[Object.keys(sorters)[0]]
      filterData = this.sortData(filterData, sorterKey, sorterValue)
    }
    this.setState({ filters, dataSource: filterData, sorters })
  }

  fnChangePayPlanDataSource = (line: any, res: any) => {
    const map = this.state.detailDataMap
    map[line?.id] = res
    this.setState({ detailDataMap: { ...map } })
  }

  getInstance = (instance: any) => {
    this.instance = instance
    const { from } = this.props
    const flag = instance && from !== 'payments'
    if (flag) {
      const iconCol = {
        title: '',
        dataIndex: 'id',
        allowReordering: false,
        allowGrouping: false,
        allowResizing: false,
        allowEditing: false,
        allowFiltering: false,
        sorter: false,
        width: 40,
        minWidth: 40,
        render: (_: any, record: any) => {
          return (
            <ColumnIcon
              from="payPlan"
              line={record}
              instance={instance}
              fnChangePayPlanDataSource={this.fnChangePayPlanDataSource}
            />
          )
        },
      }
      this.setState({ payPlanColumns: [iconCol, ...this.state.payPlanColumns] })
    }
  }

  renderDetailTemplate = (props: any) => {
    const { detailDataMap } = this.state
    const detailData = detailDataMap[props?.data?.id] || []
    return <PaymentItemsTable {...props} detailData={detailData} />
  }

  render() {
    const { sorters, filters, dataSource, payPlanColumns } = this.state
    const { rowKey } = this.props
    return (
      <div className={styles.subContainer}>
        <div className={styles.body}>
          <DataGrid.TableWrapper
            scrolling={{
              mode: 'standard',
            }}
            sorters={sorters}
            filters={filters}
            standard
            getInstance={this.getInstance}
            rowKey={rowKey || 'id'}
            className={styles.tableWrapper}
            dataSource={dataSource}
            columns={payPlanColumns}
            onSorterChange={rowKey ? this.handleSorterChange : null}
            onFilterChange={rowKey ? this.handleFilterChange : null}
            allowColumnReordering={true}
            allowColumnResizing
            isMultiSelect={false}
            isSingleSelect={false}
            RenderDetailTemplate={rowKey ? undefined : this.renderDetailTemplate}
            enabledDetailTemplate={false}
            pageIndex={1}
            pageSize={dataSource.length}
          />
        </div>
      </div>
    )
  }
}
