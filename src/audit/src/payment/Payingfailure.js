import { app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
const LoaderWithLegacyData = api.require('@elements/data-grid-v2/LoaderWithLegacyData')
const DataGridWrapper = api.require('@elements/data-grid/DataGridWrapper')
import { fetchBackLogs } from '../util/fetchUtil'
import MessageCenter from '@ekuaibao/messagecenter'
import { mapping } from '../util/mapping4paid'
import * as viewUtil from '../view-util'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import {
  handleActionImplementation,
  handlePrint,
  handlePrintRemind,
  handleReceivePrint,
} from '../service'
import { createActionColumn4Pay } from '../util/columnsAndSwitcherUtil'
const { exportExcel } = api.require('@lib/export-excel-service')
import { searchBackLogsCalcNoPage } from '../audit-action'
import { Resource } from '@ekuaibao/fetch'
import { EnhanceConnect } from '@ekuaibao/store'
import { getInitScenes } from '../util/Utils'
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

const payingWrapper = new Resource('/api/flow/v2/filter')
const scenesType = 'PAYING'
const prefixColumns = { state: 'flowId', '*': 'flowId.form' }

@EnhanceConnect(state => ({
  specifications: state['@custom-specification'].specificationGroupsList,
  legalEntityCurrencyPower: state['@common'].powers.legalEntityCurrency,
}))
export default class PayingFailureWrapper extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus || new MessageCenter()
    this.state = {
      scenes: [],
      isLightingMode: this.props.isLightingMode,
    }
  }

  componentDidMount() {
    this.bus.on('buttons:click', this.handleButtonsClick)
    this.bus.on('table:row:click', this.handleTableRowClick)
    this.bus.on('table:row:action', this.handleActions)
    this.bus.on('selectAllBtn:click', this.handleSelectAllBtnClick)
    this.bus.on('initScenes:action', this.initScenes)
    // 获取场景列表
    payingWrapper.GET('/$type', { type: scenesType }).then(async res => {
      const { value } = res
      const { specifications = [] } = this.props
      if (specifications.length === 0) {
        await api.invokeService(
          '@custom-specification:get:specificationGroups:withSpecificationVersioned',
        )
      }
      this.initScenes(value)
    })
  }

  componentWillUnmount() {
    this.bus.un('buttons:click', this.handleButtonsClick)
    this.bus.un('table:row:click', this.handleTableRowClick)
    this.bus.un('table:row:action', this.handleActions)
    this.bus.un('selectAllBtn:click', this.handleSelectAllBtnClick)
    this.bus.un('initScenes:action', this.initScenes)
  }

  initScenes = data => {
    const { specifications} = this.props
    const scenesData = getInitScenes({data,prefix:'flowId',specifications,isHasWaitInvoice:false})
    this.setState({ scenes:scenesData.scenes})
  }

  fetchPaying = (params = {}, dimensionItems = {}) => {
    params.status = { state: ['PAYING'] }
    const { scenes } = this.state
    const { scene = 'all' } = params
    const findScene = scenes.find(s => s.sceneIndex === scene)
    findScene && (params.scene = findScene.scene || '')
    scene && this.setState({ scene, fetchParams: params, dimensionItems })

    return fetchBackLogs(params, findScene, dimensionItems)
  }

  handleSelectAllBtnClick = (params = {}) => {
    params.status = { state: ['PAYING'] }
    const { scenes, scene, fetchParams, dimensionItems, isLightingMode } = this.state
    const findScene = scenes.find(s => s.sceneIndex === scene)
    findScene && (params.scene = findScene.scene || '')
    if (isLightingMode) {
      params = { ...params, filters: this.fnGetLightingModeParam() }
    } else {
      if (fetchParams) {
        if (fetchParams.filters) params.filters = fetchParams.filters
        if (fetchParams.searchText) params.searchText = fetchParams.searchText
      }
    }
    const { legalEntityCurrencyPower } = this.props
    return searchBackLogsCalcNoPage(
      params,
      findScene,
      dimensionItems,
      legalEntityCurrencyPower,
    ).then(resp => {
      let data = {}
      resp &&
        resp.value &&
        resp.value.flows.length > 0 &&
        resp.value.flows.forEach(flow => {
          data[flow.id] = flow
        })
      let sum = (resp && resp.value && resp.value.formMoney) || 0
      let keys = Object.keys(data)
      let buttons = [
        {
          name: i18n.get('重新支付'),
          type: 'primary',
          key: 'repay',
        },
        {
          name: i18n.get('导出'),
          type: 'normal',
          key: 'export_all',
        },
        {
          name: i18n.get('打印'),
          type: 'normal',
          key: 'print',
        },
        {
          name: i18n.get('打印提醒'),
          type: 'normal',
          key: 'print_remind',
        },
        {
          name: i18n.get('收到打印'),
          type: 'normal',
          key: 'recive_print',
        },
      ]

      api.open('@layout:DataGridSelectAllModal', { keys, buttons, sum }).then(name => {
        this.handleButtonsClick({ params, name, data, keys })
      })
    })
  }

  fnGetLightingModeParam = () => {
    const data = this.bus.getDataSource && this.bus.getDataSource()
    const codes = data.map(line => line.flowId.form.code)
    return { 'flowId.form.code': codes }
  }

  handleButtonsClick = ({ params, name, data, keys }) => {
    keys = keys ? keys : this.bus.getSelectedRowKeys()
    data = data ? data : this.bus.getSelectedRowData()
    switch (name) {
      case 'repay':
        return this._handlePayBills(keys, data)
      case 'print':
        return this._handlePrintList(keys, data, this.reloadAndClearSelecteds)
      case 'export_selected':
        return exportExcel({ exportType: 'export_selected', funcType: 'approve', data }, this.bus)
      case 'export_all':
        return this._handleExportAll(params)
      case 'print_remind':
        return this._handlePrintRemindList(keys, data)
      case 'recive_print':
        const flowIds = Object.values(data).map(item => item.flowId.id)
        this.handleReceivePrint(flowIds, this.reloadAndClearSelecteds)
        break
    }
  }

  reloadAndClearSelecteds = () => {
    this.bus.clearSelectedRowKeys && this.bus.clearSelectedRowKeys()
    this.bus.reload && this.bus.reload()
  }

  _handlePrintRemindList = (keys, data) => {
    const backLogs = Object.values(data)
    const flowIds = backLogs.map(element => element.flowId.id)
    handlePrintRemind.call(this, flowIds, this.__actionDone)
  }

  _handlePrintList(keys, data, fn) {
    handlePrint.call(this, keys, data, fn)
  }

  _handlePayBills(keys, data) {
    let backlogs = []
    keys.forEach(key => {
      data[key] && backlogs.push(data[key])
    })
    this.__handleLine(6, backlogs)
  }

  __actionDone = () => {
    api.invokeService('@layout5:refresh:menu:data')
    this.reloadAndClearSelecteds()
  }

  __handleLine(type, line) {
    handleActionImplementation.call(this, { type, backlog: line, fn: this.__actionDone })
  }

  handleReceivePrint = (key, fn) => {
    handleReceivePrint.call(this, key, fn)
  }

  fnGetScene() {
    const { scenes, scene } = this.state
    let findScenes = scenes.find(s => s.sceneIndex === scene)
    if (findScenes && findScenes.scene) return scene
    return findScenes
  }

  _handleExportAll(params) {
    let { status } = params
    status = { ...status, state: ['PAYING'] }
    params = { ...params, status }
    params.scene = this.fnGetScene()
    exportExcel({ exportType: 'export_all', funcType: 'backlog', data: params }, this.bus)
  }

  __billInfoPopup = backlog => {
    viewUtil.getLoanRisk(backlog).then(riskTip => {
      let title = `${i18n.get(billTypeMap()[backlog.type])}${i18n.get('详情')}`
      api.open(
        '@bills:BillInfoPopup',
        {
          title: title,
          backlog,
          riskTip: riskTip,
          invokeService: '@audit:get:backlog-info',
          params: { id: backlog.id, type: backlog.type },
          isEditConfig: this.props.state === 'APPROVING',
          isShowCondition: true,
          noCheckPermissions: true,
          reload: this.bus.reload,
          onOpenOwnerLoanList: line => {
            this.__handleLine(9, line)
          },

          onFooterButtonsClick: (type, line) => {
            api.close()
            setTimeout(() => {
              this.__handleLine(type, line)
            }, 0)
          },
        },
        true,
      )
    })
  }

  handleTableRowClick = backlog => {
    startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()
    api
      .invokeService('@audit:get:backlog-info', {
        id: backlog.id,
        type: backlog.type,
      })
      .then(backlog => {
        this.__billInfoPopup(backlog)
      })
  }

  handleActions = (type, line) => {
    handleActionImplementation.call(this, { type, backlog: line, fn: this.__actionDone })
  }

  buttons = [
    { text: i18n.get('重新支付'), name: 'repay', type: 'primary' },
    { text: i18n.get('导出选中'), name: 'export_selected' },
    { text: i18n.get('打印'), name: 'print' },
    { text: i18n.get('打印提醒'), name: 'print_remind' },
    { text: i18n.get('收到打印'), name: 'recive_print' },
  ]

  selectAllBtnStyles = { color: 'var(--brand-base)' }

  render() {
    const { scenes, isLightingMode } = this.state
    const { baseDataProperties, budgetPower, staffs, size } = this.props
    if (!scenes.length) {
      return null
    }
    if (window.isNewHome) {
      return (
        <LoaderWithLegacyData
          lightingMode={isLightingMode}
          disabledScenes={isLightingMode}
          scenes={scenes}
          fetch={this.fetchPaying}
          scenesType={scenesType}
          buttons={this.buttons}
          onButtonClick={this.handleButtonsClick}
          onSelectedAll={this.handleSelectAllBtnClick}
          prefixColumns={prefixColumns}
          bus={this.bus}
          resource={payingWrapper}
          baseDataProperties={baseDataProperties}
          createAction={createActionColumn4Pay}
          mapping={mapping}
          status={'PAYING'}
        />
      )
    } else {
      return (
        <DataGridWrapper
          disabledHeader={this.state.isLightingMode}
          isLightingMode={this.state.isLightingMode}
          status={'PAYING'}
          bus={this.bus}
          resource={payingWrapper}
          baseDataProperties={baseDataProperties}
          budgetPower={budgetPower}
          scenesType={scenesType}
          staffs={staffs}
          fetch={this.fetchPaying}
          size={size}
          selectAllBtnStyles={this.selectAllBtnStyles}
          buttons={this.buttons}
          scenes={scenes}
          prefixColumns={prefixColumns}
          mapping={mapping}
          actions={createActionColumn4Pay}
        />
      )
    }
  }
}
