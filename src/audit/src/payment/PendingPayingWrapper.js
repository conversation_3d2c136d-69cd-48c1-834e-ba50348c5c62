import React, { Component } from 'react'
import { app } from '@ekuaibao/whispered'
import styles from './PendingPayingTable.module.less'
import { message } from '@hose/eui'
import { EnhanceConnect } from '@ekuaibao/store'
import PayingPendingTableHead from '../elements/pending-paying-table-head'
import { batchRefresh } from '../service'
import { refreshPaymentForAntAliPay } from './fetchPendingPaymentData'
import { showMessage } from '@ekuaibao/show-util'
import key from '../key'
import * as viewUtil from '../view-util'
import CaptchModal from '../elements/CaptchaModal'
import { getPaymentsCaptcha, enumSmsCheck } from '../elements/CaptchaModal/actions/fetch'
import * as actions from '../audit-action'

const withLoader = app.require('@elements/data-grid-v2/withLoader')
const PayingTable = withLoader(() => import('./PayingTable'))


const { injectErrorDom } = app.require('@elements/LoadingError')

function tableColumns() {
  const documentWidth = document.body.getBoundingClientRect().width
  return [
    {
      title: i18n.get('批次号'),
      dataIndex: 'channelTradeNo',
      width: documentWidth > 1952 ? '16%' : 180,
      render: viewUtil.channelTradeNoText.bind(this),
    },
    {
      title: i18n.get('付款账户'),
      dataIndex: 'account.accountName',
      width: documentWidth > 1952 ? '14%' : 140,
      render: viewUtil.tdAccountName.bind(this),
    },
    {
      title: i18n.get('支付方式'),
      width: documentWidth > 1952 ? '9%' : 110,
      dataIndex: 'channel',
      render: viewUtil.tdChannel.bind(this),
    },
    {
      title: i18n.get('支付总金额'),
      width: documentWidth > 1952 ? '9%' : 110,
      dataIndex: 'balance',
      render: viewUtil.tdAmount.bind(this),
    },
    {
      title: i18n.get('发起支付时间'),
      dataIndex: 'createTime',
      width: documentWidth > 1952 ? '16%' : 150,
      render: viewUtil.tdDateTime.bind(this),
    },
    {
      title: i18n.get('操作'),
      width: documentWidth > 1952 ? '24%' : 256,
      dataIndex: 'actions',
      className: 'actions-wrapper',
      render: viewUtil.paymentBatchAction('paymentBatch'),
    },
  ]
}

function secondTableColumns() {
  let columns = [
    {
      title: i18n.get('标题'),
      width: '15%',
      dataIndex: 'title',
      render: viewUtil.tdText.bind(this),
    },
    {
      title: i18n.get('单号'),
      width: '12%',
      dataIndex: 'code',
      render: viewUtil.tdText.bind(this),
    },
    {
      title: i18n.get('单据类型'),
      width: '12%',
      dataIndex: 'specificationName',
      render: viewUtil.tdText.bind(this),
    },
    {
      title: i18n.get('提交日期'),
      width: '12%',
      dataIndex: 'submitDate',
      render: viewUtil.tdDate.bind(this),
    },
    {
      title: i18n.get('收款账户'),
      width: '12%',
      dataIndex: 'payee',
      render: viewUtil.tdAccountInfo.bind(this),
    },
    {
      title: i18n.get('支付金额'),
      width: '12%',
      dataIndex: 'payMoney',
      render: viewUtil.tdAmount.bind(this),
    },
    {
      title: i18n.get('实付金额'),
      width: '12%',
      dataIndex: 'paidMoney',
      render: viewUtil.tdAmount.bind(this),
    },
    {
      title: i18n.get('操作'),
      width: '24%',
      dataIndex: 'actions',
      className: 'actions-wrapper',
      render: viewUtil.paymentAction('payment'),
    },
  ]
  return columns
}

@EnhanceConnect(state => ({
  dataList: state[key.ID].payment_batch_list,
  bindPhoneInfo: state[key.ID].bindPhoneInfo,
  CHANGJIEPay: state['@common'].powers.CHANGJIEPay,
  SOUCHE: state['@common'].powers.SOUCHE,
  dynamicChannelMap: state['@audit'].dynamicChannelMap,
  userInfo: state['@common'].userinfo.data,
}))
export default class PendingPayingWrapper extends Component {
  tableColumns = tableColumns.call(this).map(c => {
    c.sorter = false
    return c
  })
  secondTableColumns = secondTableColumns.call(this).map(c => {
    c.sorter = false
    return c
  })

  hideErrorUI = null
  retryFn = null
  retryFnWrapper = fn => {
    const retryFn = async (...args) => {
      this.retryFn = () => {
        return retryFn(...args)
      }
      this.clearErrorUI()
      this.instance && this.instance.beginCustomLoading()
      try {
        await fn.call(this, ...args)
        this.clearErrorUI()
        this.retryFn = null
        this.setState({ loadingError: false })
      } catch (error) {
        this.setState({ loadingError: true }, () => {
          this.showErrorUI()
        })
      } finally {
        this.instance && this.instance.endCustomLoading()
      }
    }
    return retryFn
  }

  constructor(props) {
    super(props)
    this.state = {
      size: 20,
      total: 0,
      current: 1,
      searchText: '',
      dataList: props.dataList.items,
      refreshCHANPAYV2: false,
      loadingError: false,
    }
  }

  modalRef = React.createRef()
  checkPayStatusAction = null

  shouldComponentUpdate(nextProps, nextState) {
    for (let i in nextProps) {
      if (nextProps[i] !== this.props[i]) {
        return i !== 'size'
      }
    }
    for (let i in nextState) {
      if (nextState[i] !== this.state[i]) {
        return true
      }
    }
    return false
  }

  origin_handleRefreshAntAliPayState = () => {
    const { dynamicChannelMap = {} } = this.props
    const { searchText } = this.state

    this.setState({ current: 1 })

    return app
      .invokeService('@audit:getPaymentBatchPromise', {
        start: 0,
        count: this.state.size,
        searchValue: searchText,
      })
      .then(data => {
        const items = data.items || []
        const hasAntAliPayMutiPayBatch = items.find(
          el => dynamicChannelMap[el.channel] && dynamicChannelMap[el.channel].refreshable,
        )
        if (hasAntAliPayMutiPayBatch) {
          message.loading(i18n.get('正在获取支付结果'))
          refreshPaymentForAntAliPay().then(
            res => {
              if (res && res.state === 'SUCCESS') {
                this.getBatchList()
              } else {
                app.invokeService('@layout5:refresh:menu:data', {}, true)
              }
              showMessage.info(i18n.get('已更新支付结果'), 2)
            },
            error => {
              app.invokeService('@layout5:refresh:menu:data', {}, true)
              showMessage.info(error?.errorMessage || i18n.get('支付结果获取失败'), 2)
            },
          )
        } else {
          app.invokeService('@layout5:refresh:menu:data', {}, true)
        }
      })
  }

  handleRefreshAntAliPayState = this.retryFnWrapper(this.origin_handleRefreshAntAliPayState)

  async componentDidMount() {
    const { bus } = this.props
    bus.on('audit-payment:tab:change:during', this.tabChange)
    this.handleRefreshAntAliPayState()
    const refreshCHANPAYV2 = await actions.getRefreshBtnClickable()
    this.setState({ refreshCHANPAYV2 })

    setTimeout(() => {
      if (this.instance) {
        this.instance.resize()
        const scrollable = this.instance.getScrollable()
        if (scrollable) {
          scrollable.option('rowRenderingMode', 'standard')
        }
      }
    }, 500)
  }

  componentWillMount() {
    app.on('reload:pending:paying', this.getBatchList)
    app.on('audit-payment:show:captcha:modal', this.checkPayStatus)
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('audit-payment:tab:change:during', this.tabChange)
    app.un('reload:pending:paying', this.getBatchList)
    app.un('audit-payment:show:captcha:modal', this.checkPayStatus)
  }

  tabChange = async _ => {
    this.handleRefreshAntAliPayState()
    this.instance?.collapseAll(-1)
    const refreshCHANPAYV2 = await actions.getRefreshBtnClickable()
    this.setState({ refreshCHANPAYV2 })
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.dataList !== nextProps.dataList) {
      this.setState({ dataList: nextProps.dataList.items, total: nextProps.dataList.count })
    }
  }

  getBatchList = (data = {}) => {
    this.instance?.collapseAll(-1)
    let { current, size } = data
    const { searchText } = this.state
    const fetchData = {}

    size ?? (size = this.state.size)
    if (!current) {
      current = 1
      this.setState({ current: 1 })
    }

    fetchData.start = (current - 1) * size
    fetchData.count = size
    fetchData.searchValue = searchText

    return app.invokeService('@audit:getPaymentBatch', fetchData)
  }

  handleSearch = this.retryFnWrapper(value => {
    return new Promise(resolve => {
      this.setState({ current: 1, searchText: value }, () => {
        resolve(app.invokeService('@audit:getPaymentBatch', {
          start: 0,
          count: this.state.size,
          searchValue: value,
        }))
      })
    })
  })

  handleRefresh = this.retryFnWrapper(async () => {
    this.origin_handleRefreshAntAliPayState()
    if (this.state.refreshCHANPAYV2) {
      await batchRefresh(this.getBatchList)
    }
  })

  getInstance = instance => {
    this.instance = instance
  }

  captchaSuccess = (code, other) => {
    const captchaId = other?.captchaId || ''
    const captchaCode = code || ''
    this.checkPayStatusAction?.({
      captchaId,
      captchaCode,
    })
  }

  checkPayStatus = ({ _checkPayStatus, line }) => {
    this.checkPayStatusAction = params =>
      _checkPayStatus(line, params, this.modalRef.current.destroy)
    const { dynamicChannelMap = {} } = this.props
    const _channel = line?.channel || ''
    //需要短信校验
    const isNeedSmsCheck = (dynamicChannelMap[_channel] || {})?.smsCheck || []
    if (Array.isArray(isNeedSmsCheck) && isNeedSmsCheck.includes(enumSmsCheck.QUERY)) {
      this.modalRef.current.show()
    } else {
      _checkPayStatus(line)
    }
  }

  handlePaginationChange = this.retryFnWrapper(pagination => {
    return new Promise((resolve, reject) => {
      this.setState({ current: pagination.current, size: pagination.size }, () => {
        resolve(this.getBatchList({ current: pagination.current, size: pagination.size }))
      })
    })
  })

  waitForInstance = () => {
    return new Promise(resolve => {
      const timer = setInterval(() => {
        if (this.instance) {
          clearInterval(timer)
          resolve(this.instance)
        }
      }, 100)
    })
  }

  showErrorUI() {
    setTimeout(async () => {
      if (!this.instance) {
        this.instance = await this.waitForInstance()
      }

      const container = this.instance.element?.()?.querySelector('.dx-datagrid-rowsview')
      this.hideErrorUI = injectErrorDom(container, {
        refreshFn: () => {
          this.clearErrorUI()
          return this.retryFn?.()
        }
      })
    }, 300)
  }

  clearErrorUI() {
    if (this.hideErrorUI) {
      this.hideErrorUI?.()
      this.hideErrorUI = null
    }
  }

  render() {
    const { userInfo } = this.props
    const phoneNumber = userInfo?.staff?.cellphone || ''
    const { dataList, refreshCHANPAYV2, size, total, current, loading, loadingError } = this.state
    return (
      <div className={`${styles['pennding-approve-expense-bills-view']} ${styles['paying-table']}`}>
        <div className="table-def">
          <PayingPendingTableHead
            {...this.props}
            showCheckBtn
            showRrefeshBtn
            onSearch={this.handleSearch}
            onRefresh={this.handleRefresh}
            showUpdatePayFinishedTimeCheckbox
          />
          <PayingTable
            from="batch"
            columns={this.tableColumns}
            dataSource={dataList}
            getInstance={this.getInstance}
            secondTableColumns={this.secondTableColumns}
            paginationProps={{
              totalLength: total,
              pagination: { current, size },
            }}
            onPaginationChange={this.handlePaginationChange}
            loadingError={loadingError}
          />
        </div>
        <CaptchModal
          ref={this.modalRef}
          getCaptcha={getPaymentsCaptcha}
          successCb={this.captchaSuccess}
          phoneNumber={phoneNumber}
        />
      </div>
    )
  }
}
