/**************************************************
 * Created by nany<PERSON>ingfeng on 20/09/2017 12:36.
 **************************************************/
import React, { PureComponent } from 'react'
import { fetchBackLogs } from '../util/fetchUtil'
import MessageCenter from '@ekuaibao/messagecenter'
import { mapping } from '../util/mapping4paid'
import { app as api } from '@ekuaibao/whispered'
import { searchBackLogsCalcNoPage } from '../audit-action'
import { createActionColumn4PartialPay } from '../util/columnsAndSwitcherUtil'
const LoaderWithLegacyData = api.require<any>('@elements/data-grid-v2/LoaderWithLegacyData')
// @ts-ignore
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
// @ts-ignore
const { exportExcel } = api.require('@lib/export-excel-service')
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

import {
  handleActionImplementation,
  handlePrint,
  handlePrintRemind,
  handleReceivePrint,
} from '../service'

import { Resource } from '@ekuaibao/fetch'
import { EnhanceConnect } from '@ekuaibao/store'
import { fnFilterMapping, getDefaultScenes } from '../util/Utils'
import { get } from 'lodash'
import * as viewUtil from '../view-util'
const { searchOptions, globalSearchOptions } = api.require('@lib/data-grid-v2/CustomSearchUtil')

const paid = new Resource('/api/flow/v2/filter')
const payingWrapper = new Resource('/api/flow/v2/filter')
const scenesType = 'PARTIAL_PAYMENT'
const prefixColumns = { state: 'flowId', '*': 'flowId.form' }

export interface PartialPaymentProps {
  bus?: MessageCenter
  specifications: any[]
  invoiceReviewPower: boolean
  legalEntityCurrencyPower: boolean
  baseDataProperties: any[]
  state: string
  KA_GLOBAL_SEARCH_2: boolean
}

export interface PartialPaymentState {
  scenes: any[]
  scene: any
  fetchParams: any
  dimensionItems: any
}

// @ts-ignore
@EnhanceConnect(state => ({
  specifications: state['@custom-specification'].specificationGroupsList,
  invoiceReviewPower: state['@common'].powers.invoiceReview,
  legalEntityCurrencyPower: state['@common'].powers.legalEntityCurrency,
  // 涉及需求 https://hose2019.feishu.cn/wiki/wikcncXiMtXZQwNcS64OMEwSsJd#fTSuk8
  KA_GLOBAL_SEARCH_2: state['@common'].powers.KA_GLOBAL_SEARCH_2,
}))
export default class PartialPaymentWrapper extends PureComponent<
  PartialPaymentProps,
  PartialPaymentState
> {
  bus: any
  inPartialPayment: boolean
  _currentDataSource: any[] = []

  constructor(props: PartialPaymentProps) {
    super(props)
    this.bus = props.bus || new MessageCenter()
    this.inPartialPayment = true
    this.state = {
      scenes: [],
      scene: undefined,
      fetchParams: undefined,
      dimensionItems: undefined,
    }
  }

  componentDidMount() {
    this.bus.on('buttons:click', this.handleButtonsClick)
    this.bus.on('table:row:click', this.handleTableRowClick)
    this.bus.on('table:row:action', this.handleActions)
    this.bus.on('initScenes:action', this.initScenes)
    // 获取场景列表
    paid.GET('/$type', { type: scenesType }, undefined, { hiddenLoading: true }).then(async res => {
      const { value } = res
      const { specifications = [] } = this.props
      if (specifications.length === 0) {
        await api.invokeService(
          '@custom-specification:get:specificationGroups:withSpecificationVersioned',
          {},
          { hiddenLoading: true },
        )
      }
      this.initScenes(value)
    })
  }

  componentWillUnmount() {
    this.bus.un('buttons:click', this.handleButtonsClick)
    this.bus.un('table:row:click', this.handleTableRowClick)
    this.bus.un('table:row:action', this.handleActions)
    this.bus.un('initScenes:action', this.initScenes)
  }

  initScenes = (data: any) => {
    let { specifications } = this.props
    const defaultScenes = getDefaultScenes('', ['expense', 'loan', 'requisition'], specifications)
    const allScenes = { text: i18n.get('全部'), scene: 'all', active: true, sceneIndex: 'all' }
    const filter = data
      ? data.filter.map((d: string) => {
          let temp = JSON.parse(d)
          if (temp.scene) {
            let newScene = defaultScenes.filter(item => item.sceneIndex === temp.scene)
            if (newScene.length > 0) {
              temp = newScene[0]
            }
          }
          return temp
        })
      : defaultScenes
    const scenes = !!~filter.findIndex((el: any) => el.scene === 'all')
      ? filter
      : [allScenes, ...filter]
    this.setState({ scenes })
  }

  __status = { state: ['PROCESSING'] }

  fetchPaying = async (params: any = {}, dimensionItems = {}) => {
    // 在请求时处理query中的filterBy
    params.partialPayState = true

    params.status = this.__status
    const { scenes } = this.state
    const { scene = 'all' } = params
    const findScene = scenes.find(s => s.sceneIndex === scene)
    findScene && (params.scene = findScene.scene || '')
    scene && this.setState({ scene, fetchParams: params, dimensionItems })
    const res = await fetchBackLogs(params, findScene, dimensionItems)
    this._currentDataSource = res.dataSource
    return res
  }

  handleSelectAllBtnClick = (params: any = {}) => {
    // 在请求时处理query中的filterBy
    params.partialPayState = true

    params.status = this.__status
    const { scenes, scene, fetchParams, dimensionItems } = this.state
    const findScene = scenes.find(s => s.sceneIndex === scene)
    findScene && (params.scene = findScene.scene || '')
    if (fetchParams) {
      if (fetchParams.filters) params.filters = fetchParams.filters
      if (fetchParams.searchText) params.searchText = fetchParams.searchText
    }
    const { legalEntityCurrencyPower } = this.props
    return searchBackLogsCalcNoPage(
      params,
      findScene,
      dimensionItems,
      legalEntityCurrencyPower,
    ).then((resp: any) => {
      let data: any = {}
      const respValue = get(resp, 'value.flows', [])
      if (respValue.length > 0) {
        resp.value.flows.forEach((flow: any) => {
          data[flow.id] = flow
        })
      }
      let sum = (resp && resp.value && resp.value.formMoney) || 0
      let keys = Object.keys(data)
      let buttons = [
        {
          name: i18n.get('支付'),
          type: 'primary',
          key: 'pay',
        },
        {
          name: i18n.get('导出'),
          type: 'normal',
          key: 'export_all',
        },
        {
          name: i18n.get('打印'),
          type: 'normal',
          key: 'print',
        },
        {
          name: i18n.get('打印提醒'),
          type: 'normal',
          key: 'print_remind',
        },
        {
          name: i18n.get('收到打印'),
          type: 'normal',
          key: 'recive_print',
        },
      ]

      // @ts-ignore
      api.open('@layout:DataGridSelectAllModal', { keys, buttons, sum }).then((name: string) => {
        this.handleButtonsClick({ params, name, data, keys })
      })
    })
  }

  _handlePrintRemindList = (keys: string[], data: any) => {
    const backLogs = Object.values(data)
    const flowIds = backLogs.map((element: any) => element.flowId.id)
    handlePrintRemind.call(this, flowIds, this.__actionDone)
  }

  _handlePrintList(keys: any[], data: any, fn: any) {
    handlePrint.call(this, keys, data, fn)
  }

  _handlePayBills(keys: any[], data: any) {
    let backlogs: any[] = []
    keys.forEach(key => {
      data[key] && backlogs.push(data[key])
    })
    this.__handleLine(6, backlogs)
  }

  __handleLine(type: number, line: any) {
    handleActionImplementation.call(this, {
      type,
      backlog: line,
      fn: this.__actionDone,
      tableSource: 'partPay',
    })
  }

  __actionDone = () => {
    api.invokeService('@layout5:refresh:menu:data')
    this.reloadAndClearSelecteds()
  }

  reloadAndClearSelecteds = () => {
    this.bus.clearSelectedRowKeys && this.bus.clearSelectedRowKeys()
    this.bus.reload && this.bus.reload()
  }

  handleReceivePrint = (key: string[], fn: any) => {
    handleReceivePrint.call(this, key, fn)
  }

  _handleExportAll(params: any) {
    let { status } = params
    status = { ...status, state: ['PROCESSING'] }
    params = { ...params, status }
    params.scene = this.fnGetScene()
    exportExcel({ exportType: 'export_all', funcType: 'backlog', data: params }, this.bus)
  }

  handleButtonsClick = ({ params, name, data, keys }: any) => {
    keys = keys ? keys : this.bus.getSelectedRowKeys()
    data = data ? data : this.bus.getSelectedRowData()
    switch (name) {
      case 'pay':
        return this._handlePayBills(keys, data)
      case 'print':
        return this._handlePrintList(keys, data, this.reloadAndClearSelecteds)
      case 'export_selected':
        return exportExcel({ exportType: 'export_selected', funcType: 'backlog', data }, this.bus)
      case 'export_all':
        return this._handleExportAll(params)
      case 'print_remind':
        return this._handlePrintRemindList(keys, data)
      case 'recive_print':
        const flowIds = Object.values(data).map((item: any) => item.flowId.id)
        this.handleReceivePrint(flowIds, this.reloadAndClearSelecteds)
        break
    }
  }

  fnGetScene = () => {
    const { scenes, scene } = this.state
    let findScenes = scenes.find(s => s.sceneIndex === scene)
    if (findScenes && findScenes.scene) return scene
    return findScenes
  }

  __billInfoPopup = (backlog: any) => {
    viewUtil.getLoanRisk(backlog).then(riskTip => {
      let title = `${i18n.get(billTypeMap()[backlog.type])}${i18n.get('详情')}`
      // @ts-ignore
      return api.open(
        '@bills:BillInfoPopup',
        {
          title: title,
          backlog,
          riskTip: riskTip,
          invokeService: '@audit:get:backlog-info',
          params: { id: backlog.id, type: backlog.type },
          isEditConfig: false,
          isShowCondition: true,
          reload: this.bus.reload,
          mask: false,
          onOpenOwnerLoanList: (line: any) => {
            this.__handleLine(9, line)
          },
          onFooterButtonsClick: (type: number, line: any) => {
            setTimeout(() => {
              this.__handleLine(type, line)
            }, 0)
            // @ts-ignore
            return api.close()
          },
        },
        true,
      )
    })
  }

  handleTableRowClick = (backlog: any) => {
    startOpenFlowPerformanceStatistics && startOpenFlowPerformanceStatistics()

    if (api.require<any>('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
      api.open('@bills:BillInfoDrawerV2', {
        currentId: backlog.flowId.id,
        flows: this._currentDataSource.map((item => item.flowId)),
        billDetailsProps: {
          isEditConfig: false,
        },
        bus: this.bus,
        onOpenOwnerLoanList: (line: any) => {
          this.__handleLine(9, line)
        },
        onFooterButtonsClick: (type: number, line: any) => {
          setTimeout(() => {
            this.__handleLine(type, line)
          }, 0)
          // @ts-ignore
          return api.close()
        },
      })
      return
    }

    api
      .invokeService('@audit:get:backlog-info', {
        id: backlog.id,
        type: backlog.type,
      })
      .then((backlog: any) => {
        this.__billInfoPopup(backlog)
      })
  }

  handleActions = (type: number, line: any) => {
    handleActionImplementation.call(this, { type, backlog: line, fn: this.__actionDone })
  }

  buttons = [
    { text: i18n.get('支付'), name: 'pay', type: 'primary' },
    { text: i18n.get('导出选中'), name: 'export_selected' },
    { text: i18n.get('打印'), name: 'print' },
    { text: i18n.get('打印提醒'), name: 'print_remind' },
    { text: i18n.get('收到打印'), name: 'recive_print' },
  ]

  render() {
    const { scenes } = this.state
    const { baseDataProperties, invoiceReviewPower, KA_GLOBAL_SEARCH_2 } = this.props
    if (!scenes.length) {
      return null
    }
    return (
      <LoaderWithLegacyData
        newSearch={true}
        searchOptions={KA_GLOBAL_SEARCH_2 ? globalSearchOptions() : searchOptions()}
        enableGlobalSearch={KA_GLOBAL_SEARCH_2}
        scenes={scenes}
        fetch={this.fetchPaying}
        scenesType={scenesType}
        buttons={this.buttons}
        onButtonClick={this.handleButtonsClick}
        onSelectedAll={this.handleSelectAllBtnClick}
        prefixColumns={prefixColumns}
        bus={this.bus}
        resource={payingWrapper}
        baseDataProperties={baseDataProperties}
        createAction={createActionColumn4PartialPay}
        mapping={fnFilterMapping(mapping as any, invoiceReviewPower)}
        useNewFieldSet
      />
    )
  }
}
