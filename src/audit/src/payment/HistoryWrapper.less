@import '~@ekuaibao/web-theme-variables/styles/default';
@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/eui-styles/less/token.less';

.history-wrapper{
  display: flex;
  flex-direction: column;
  flex: 1;
  border-radius: 4px;
  width: auto;
  background-color: #ffffff;
  position: relative;
  .operation-tabs {
    flex: 1;
  }
  .filter-bar {
    padding: 15px 20px;
    font-size: 12px;
    flex-shrink: 0;
  }
  .td-status-skip,
  .td-status-send,
  .td-status-receive,
  .td-status-agree,
  .td-status-autoAgree,
  .td-status-select-approver,
  .td-status-pay,
  .td-status-confirm,
  .td-status-addnode {
    font-size: 12px;
    color: var(--brand-base);
  }
  .td-status-reject,
  .td-status-nullify {
    font-size: 12px;
    color: @error-color;
  }
  .ant-pagination {
    position: absolute;
    right: 18px;
    bottom: 0;
    z-index: 3;
  }
  .highlight {
    color: var(--brand-base);
  }

  .mr-16 {
    margin-right: 16px;
  }

  .hide {
    visibility: hidden;
  }

  .table-def {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    border-radius: 4px;
    background-color: @input-bg;
    position: relative;
    overflow: hidden;
    .filter-bar {
      padding: 15px 20px;
      font-size: 12px;
      flex-shrink: 0;
    }

    .table-header-c {
      height: 51px;
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-shrink: 0;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #e6e6e6;
      .chk-box {
        margin-left: 18px;
      }

      .refresh-btn {
        margin-left: 12px;
        font-size: 14px;
      }
      .lbl {
        height: 18px;
        font-size: 13px;
        text-align: center;
        color: @text-color;
        margin-left: 12px;
        display: flex;
        align-items: center;
        .pending-button {
          width: 200px;
          height: 28px;
          border-radius: 4px;
          background-color: #fffce6;
          border: solid 1px #ece9d2;
          margin-left: 10px;
          line-height: 28px;
          font-size: 12px;
          padding: 0 5px;
          cursor: pointer;
          display: flex;
          justify-content: space-between;
          .warn-color {
            color: @error-color;
          }
          .check-txt {
            color: var(--brand-base);
          }
        }
      }
      .search-bar {
        width: 250px;
        margin-right: 20px;
      }
      .search-bar-pending {
        display: flex;
        width: 320px;
        margin-right: 20px;
        justify-content: space-between;
        align-items: center;
        .search {
          margin-right: 20px;
        }
        .line-txt {
          width: 1px;
          height: 16px;
          background-color: #d5d5d5;
        }
        .msg-btn {
          margin-left: 20px;
        }
      }
      .clearMR {
        margin-right: 0;
      }

      .btn-group {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        .btn {
          margin-left: 20px;
        }

        a.bh-a {
          color: #000000;
        }
        .icon-cell {
          margin-left: 5px;
        }
        .icon-lbl {
          height: 17px;
          font-size: 12px;
          color: @text-color;
        }
        .search-inp {
          width: 300px;
        }
      }
    }
    .e-content-wrapper {
      .table-line-tr {
        .actions-wrapper {
          padding: 0;
          .line {
            background-color: @border-color-base;
            height: 26px;
            width: 1px;
          }
        }
      }
    }
  }

  .wb-bw {
    white-space: normal;
  }
}
