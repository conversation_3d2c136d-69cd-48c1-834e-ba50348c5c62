.container {
  display: flex;
  width: 100%;
  flex-direction: column;
  height: 100%;
  padding: 0 16px;
  margin-top: 8px;
  overflow: auto;
}

.containerHeight {
  height: calc(100vh - 200px);
}

.payPlanContainerHeight {
  height: calc(100vh - 160px);
}

.subContainer {
  display: flex;
  width: 100%;
  flex-direction: column;
  height: auto;
  padding: 0 16px;
  margin-top: 8px;
  overflow: visible;
}

.tableWrapper {
  &:before {
    content: ' ';
    position: absolute;
    border-top: 1px solid #ddd;
    width: 100%;
    z-index: 100;
  }
}
