/************************************************
 * Created By nanyuantingfeng On 6/7/16 11:48.
 ************************************************/
@import '~@ekuaibao/web-theme-variables/styles/default';
@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/eui-styles/less/token.less';

.approve-expense-bills-view {
  display: flex;
  flex-direction: column;
  flex: 1;
  border-radius: 4px;
  width: 100%;
  background-color: #ebeff2;

  .mr-16 {
    margin-right: 16px;
  }

  .hide {
    visibility: hidden;
  }

  .filter-bar {
    padding: 15px 20px;
    font-size: 12px;
    flex-shrink: 0;
  }

  .table-def {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    border-radius: 4px;
    background-color: @input-bg;
    position: relative;
    overflow: hidden;

    .filter-bar {
      padding: 15px 20px;
      font-size: 12px;
      flex-shrink: 0;
    }

    .table-header-c {
      height: 51px;
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-shrink: 0;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #e6e6e6;

      .chk-box {
        margin-left: 18px;
      }

      .refresh-btn {
        margin-left: 12px;
        font-size: 14px;
      }

      .lbl {
        height: 18px;
        font-size: 13px;
        text-align: center;
        color: @text-color;
        margin-left: 12px;
        display: flex;
        align-items: center;

        .pending-button {
          width: 200px;
          height: 28px;
          border-radius: 4px;
          background-color: #fffce6;
          border: solid 1px #ece9d2;
          margin-left: 10px;
          line-height: 28px;
          font-size: 12px;
          padding: 0 5px;
          cursor: pointer;
          display: flex;
          justify-content: space-between;

          .warn-color {
            color: @error-color;
          }

          .check-txt {
            color: var(--brand-base);
          }
        }
      }

      .search-bar {
        width: 250px;
        margin-right: 20px;
      }

      .search-bar-pending {
        display: flex;
        width: 320px;
        margin-right: 20px;
        justify-content: space-between;
        align-items: center;

        .search {
          margin-right: 20px;
        }

        .line-txt {
          width: 1px;
          height: 16px;
          background-color: #d5d5d5;
        }

        .msg-btn {
          margin-left: 20px;
        }
      }

      .clearMR {
        margin-right: 0;
      }

      .btn-group {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;

        .btn {
          margin-left: 20px;
        }

        a.bh-a {
          color: #000000;
        }

        .icon-cell {
          margin-left: 5px;
        }

        .icon-lbl {
          height: 17px;
          font-size: 12px;
          color: @text-color;
        }

        .search-inp {
          width: 300px;
        }
      }
    }

    .e-content-wrapper {
      .table-line-tr {
        .actions-wrapper {
          padding: 0;

          .line {
            background-color: @border-color-base;
            height: 26px;
            width: 1px;
          }
        }
      }
    }
  }

  .wb-bw {
    white-space: normal;
  }
}

.approve-loan-bills-view {
  .approve-expense-bills-view;
}

.payment-expense-bills-view {
  .approve-expense-bills-view;
}

.records-operation-records-view {
  .approve-expense-bills-view;
  width: auto;
  background-color: #ffffff;
  position: relative;

  .operation-tabs {
    flex: 1;
  }

  .td-status-skip,
  .td-status-send,
  .td-status-receive,
  .td-status-agree,
  .td-status-autoAgree,
  .td-status-select-approver,
  .td-status-pay,
  .td-status-confirm,
  .td-status-addnode {
    font-size: 12px;
    color: var(--brand-base);
  }

  .td-status-reject,
  .td-status-nullify {
    font-size: 12px;
    color: @error-color;
  }

  .ant-pagination {
    position: absolute;
    right: 18px;
    bottom: 0;
    z-index: 3;
  }

  .highlight {
    color: var(--brand-base);
  }
}

.popup-audit-content {
  padding-top: 17px;
  padding-left: 12px;
  padding-right: 12px;

  .header {
    font-size: 15px;
    color: #343434;
    position: relative;

    .title {
      width: 210px;
      height: 21px;
      font-size: 15px;
      color: #343434;
      margin-left: 8px;
      margin-top: 10px;
    }

    .sub-title {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-left: 8px;
      margin-top: 9px;

      .l0 {
        opacity: 0.7;
        font-size: 12px;
        color: #343434;
      }

      .l1 {
        opacity: 0.7;
        font-size: 12px;
        color: #343434;
        margin-left: 17px;
      }

      .l2 {
        width: 50px;
        height: 19px;
        line-height: 19px;
        border-radius: 3px;
        background-color: @info-color;
        margin-left: 18px;
        font-size: 11px;
        color: @input-bg;
        text-align: center;
        align-items: center;
      }
    }

    .amount {
      width: 110px;
      height: 25px;
      font-size: 18px;
      color: #2685ac;
      position: absolute;
      right: 24px;
      top: 1px;
    }
  }

  .content {
    margin-left: 8px;

    .detail {
      display: flex;
      justify-content: flex-start;

      .left {
        margin-right: 22px;

        .line {
          margin-top: 10px;
          font-size: 12px;
          color: @text-color;
          display: flex;
          align-items: center;
          justify-content: flex-start;

          div:first-child {
            margin-right: 16px;
            width: 60px;
            font-size: 12px;
            color: @text-color;
          }

          div:last-child {
            width: 256px;
            font-size: 12px;
            color: @text-color;
          }
        }
      }

      .right {
        border-left: 1px solid #e6e6e6;

        .line {
          margin-top: 10px;
          margin-left: 22px;
          font-size: 12px;
          color: @text-color;
          display: flex;
          align-items: center;
          justify-content: flex-start;

          div:first-child {
            margin-right: 6px;
            width: 58px;
            font-size: 12px;
            color: @text-color;
          }

          div:nth-child(2n) {
            width: 61px;
            font-size: 12px;
            color: var(--brand-base);
          }

          div:last-child {
            font-size: 12px;
            color: @text-color;
            text-align: end;
            width: 200px;
          }
        }
      }
    }
  }

  .content2 {
    .header {
      height: 17px;
      font-size: 12px;
      color: @text-color;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-top: 20px;
      margin-bottom: 13px;

      div:first-child {
        width: 3px;
        height: 14px;
        background-color: var(--brand-base);
        margin-right: 7px;
      }

      div:nth-child(2n) {
        width: 92px;
        height: 17px;
        font-size: 12px;
        color: @text-color;
      }

      div:last-child {
        width: 627px;
        height: 17px;
        font-size: 12px;
        color: var(--brand-base);
        text-align: end;
      }
    }

    .line {
      height: 70px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      border-top: 1px solid #e6e6e6;

      .i0 {
        width: 30px;
        height: 30px;
        background-color: @warning-color;
        margin-right: 11px;
      }

      .i1 {
        margin-right: 36px;
      }

      .i2 {
        margin-right: 337px;

        div:first-child {
          width: 132px;
          height: 17px;
          font-size: 12px;
          color: @text-color;
        }

        div:last-child {
          width: 132px;
          height: 17px;
          font-size: 12px;
          color: @text-color;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }

      .i3 {
        width: 30px;
        height: 30px;
        border-radius: 4px;
        margin-right: 16px;
        background-color: darkcyan;
      }

      .i4 {
        text-align: end;
        width: 80px;

        div:first-child {
          font-size: 12px;
          color: @text-color;
        }

        div:last-child {
          font-size: 11px;
          color: #b4b4b4;
        }
      }
    }
  }

  .ant-tabs-nav-container {
    font-size: 12px;
  }
}

.reject-bill-modal-content {
  .reject-bill-wrapper {
    max-height: 600px;
    overflow-y: auto;

    .ant-form {
      width: 100%;
      padding: @space-6 @space-6 0 @space-6;
    }

    .flow-config-con {
      padding: 0 0 @space-6 @space-6;
    }

    .money_wrapper_line {
      height: @space-4;
      background: var(--eui-line-divider-module);
    }

    .reject-node-choose {
      .font-size-2;
      color: var(--eui-text-title);
      padding-left: @space-6;
      padding-bottom: @space-6;
    }

    .turn-down-title {
      .font-size-2;
      color: var(--eui-text-title);
      padding-top: @space-6;
      padding-bottom: @space-4;
    }

    .turn-down-radio {
      margin: 0px @space-6;

      .radio-title {
        .font-size-2;
        color: var(--eui-text-title);
      }

      .img {
        width: 336px;
        margin-left: 25px;
      }
    }

    .content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 35px 0px;

      .amount {
        font-size: 16px;
        font-weight: 500;
        color: var(--brand-base);
        margin-bottom: 5px;
      }

      .remark {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        background-color: @input-bg;
        margin-bottom: 15px;

        .title {
          margin-right: 10px;
        }

        .method-line {
          width: 320px;
          margin-top: 5px;
        }
      }

      .info {
        font-size: 12px;
        color: #9c9c9c;
        margin-left: 10px;
      }
    }

    .attachment-wrapper {
      width: 100%;
      padding-left: 16px;

      .ant-btn {
        border: none;
        margin-left: -15px;
        background-color: #ffffff;
      }

      &:hover {
        .ant-btn {
          color: var(--brand-base);
        }
      }
    }

    .flow-allow-modal-footer {
      display: flex;
      justify-content: flex-end;
      width: 100%;
      // border-top: 1px solid #dcdcdc;
      height: 52px;
      padding: 10px 16px 0 16px;

      .signature-container {
        display: flex;
        flex: 1;

        .signature-title {
          margin-top: 5px;
          margin-right: 8px;
          //width: 90px;
          height: 14px;
          font-size: 14px;
          line-height: 1;
          color: #54595b;
        }
      }

      .modal-footer-button {
        display: flex;
        flex-direction: row;

        .btn-ml {
          margin-left: 17px;
        }
      }
    }

    .justify-content-between {
      justify-content: space-between;
    }

    .justify-content-end {
      justify-content: flex-end;
    }
  }
}

.select-member-model {
  width: 100%;
  height: 100%;

  .remark {
    width: 100%;
    height: 89px;
    border-radius: 4px;
    background-color: @input-bg;
    margin-top: 11px;
    overflow: auto;
  }
}

.audit-modal-flow-type-layer {
  display: flex;
  flex-direction: column;

  .line {
    display: flex;
    flex-direction: row;

    div:first-child {
      font-size: 12px;
      margin-right: 10px;
    }

    div:last-child {
      font-size: 12px;
    }
  }
}

.custom-filter-dropdown {
  padding: 8px;
  border-radius: 6px;
  background: #ffffff;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  display: flex;
  position: relative;

  .clear {
    position: absolute;
    top: 15px;
    right: 85px;
    color: var(--brand-base);
  }
}

.show-close {
  .ant-modal-close {
    color: rgba(0, 0, 0, 0.85);
    display: inherit !important;
  }
}

.batch-show-agree-modal {
  box-sizing: border-box;
  //min-height: 60px;
  overflow-x: hidden;
  overflow-y: auto;
  max-height: 120px;
  padding: 8px 16px;
  border-radius: 4px;
  background-color: rgba(29, 43, 61, 0.03);

  .batch-item {
    font-size: 14px;
    line-height: 22px;
    color: rgba(29, 43, 61, 0.5);
    margin: 4px 0;

    .mark {
      width: 0;
      height: 0;
      display: inline-block;
      vertical-align: middle;
      border: 2px solid rgba(29, 43, 61, 0.5);
    }
  }
}

.batch-show-modal {
  margin-bottom: -30px;

  .modal-title {
    font-size: 14px;
    margin-bottom: 16px;
  }

  .batch-content {
    min-height: 60px;
    overflow: auto;
    max-height: 120px;
    margin-bottom: 10px;

    .batch-item {
      font-size: 13px;
      margin-bottom: 5px;

      span:first-child {
        margin-right: 24px;
      }
    }
  }
}

.batch-show-title {
  text-align: center;

  .title {
    height: 24px;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    color: #1d2b3d;
  }

  .title-content {
    margin-top: 4px;
    height: 22px;
    font-size: 14px;
    line-height: 22px;
    color: rgba(29, 43, 61, 0.75);
  }
}

.custom-filter-dropdown input {
  width: 130px;
  margin-right: 8px;
}

.payment-feetype-icon {
  margin-right: 12px;
  width: 28px;
  height: 28px;
  border-radius: 28px;
}

.ekb-account-info-popover {
  min-width: 240px;
  max-width: 400px;

  .header {
    padding: 10px 0;
    font-size: 12px;
    font-weight: 500;
    border-bottom: 1px solid #eeeeee;
  }

  .body {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-top: 15px;
    margin-bottom: 15px;

    .line1 {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: normal;

      img {
        margin-right: 5px;
      }
    }

    .line2 {
      margin-left: 25px;
    }

    .line3 {
      color: @color-black-3;
      margin-left: 25px;
    }
  }
}

.ekb-bind-phone-popover-content {
  width: 300px;
  height: 150px;
  padding: 20px;

  .line {
    margin-bottom: 15px;

    .action {
      color: var(--brand-base);
      cursor: pointer;
      margin-left: 10px;
    }
  }

  .line-action {
    width: 100%;
    text-align: right;

    button {
      margin-left: 15px;
    }
  }
}
