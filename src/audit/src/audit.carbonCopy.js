import { app as api } from '@ekuaibao/whispered'
import styles from './audit.carbonCopy.module.less'
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
const ETabs = api.require('@elements/ETabs')
import UnReadWrapper from './carbonCopy/unReadWrapper'
import ReadWrapper from './carbonCopy/readWrapper'
import MessageCenter from '@ekuaibao/messagecenter'
import { connect } from '@ekuaibao/mobx-store'
import classnames from 'classnames'

@connect(store => ({ size: store.states['@layout'].size }))
@EnhanceConnect(state => ({
  baseDataProperties: state['@common'].globalFields.data,
  budgetPower: state['@common'].powers.Budget,
  staffs: state['@common'].staffs,
}))
export default class AuditSendBill extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = new MessageCenter()
  }

  componentWillMount() {
    api.dataLoader('@common.staffs').load()
  }

  handleTabChange = type => {
    if (type === 'read') {
      this.bus.reload && this.bus.reload()
    }
  }

  render() {
    const dataSource = [
      {
        tab: i18n.get('未读'),
        children: <UnReadWrapper {...this.props} />,
        key: 'unread',
      },
      {
        tab: i18n.get('已读'),
        children: <ReadWrapper {...this.props} bus={this.bus} />,
        key: 'read',
      },
    ]
    return (
      <div id="audit-carbonCopy" className={styles.send_wrapper}>
        <ETabs
          isHoseEUI={true}
          className="ekb-tab-line-left"
          defaultActiveKey="unread"
          tabBarStyle={{ paddingTop: 6, height: 40, width: '100%' }}
          onChange={this.handleTabChange}
          dataSource={dataSource}
        />
      </div>
    )
  }
}
