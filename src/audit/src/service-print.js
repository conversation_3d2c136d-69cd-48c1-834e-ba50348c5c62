import { app as api } from '@ekuaibao/whispered'
import { dealPrintData } from '@ekuaibao/lib/lib/lib-util'
import { showMessage } from '@ekuaibao/show-util'

export function handlePrint(selectedRowKeys, selectedRowData, done, sortByIndex = false, printInvoice, printAttachment) {
  const isLimitColumns = api.getState()['@common'].toggleManage?.['tg_flowlist_filed_filter']
  if (printInvoice === '1' && isLimitColumns) {
    // 表格查询某些字段已经不返回原始模板配置信息，那么从模板列表去找模板信息并合并到 selectedRowData 数据中
    api.invokeService('@custom-specification:get:specificationGroups').then(_ => {
      const specificationGroupsMap = {}
      Array.isArray(_?.items) &&
        _?.items.forEach(groupItem => {
          const { specifications = [] } = groupItem
          Array.isArray(specifications) &&
            specifications.forEach(specItem => {
              const { id } = specItem
              specificationGroupsMap[id] = specItem
            })
        })
      Object.values(selectedRowData).forEach(row => {
        const id = row?.flowId?.form?.specificationId?.id || ''
        const originalId = id.split(':').length ? id.split(':')[0] : ''
        if (originalId && specificationGroupsMap[originalId]) {
          // 这里数据拼装的依据来自于 dealPrintData 方法中的处理逻辑
          row.flowId.form.specificationId.originalId = specificationGroupsMap[originalId]
        }
      })

      const data = dealPrintData(selectedRowKeys, selectedRowData)
      if (selectedRowData.showAllFeeType) data.showAllFeeType = true
      doPrint(data, true, done, sortByIndex, printInvoice)
    })
  } else {
    const data = dealPrintData(selectedRowKeys, selectedRowData)
    if (selectedRowData.showAllFeeType) data.showAllFeeType = true
    doPrint(data, true, done, sortByIndex, printInvoice, printAttachment)
  }
}

export function doPrint(data, withPrivilegeId, done, sortByIndex = false, printInvoice, printAttachment) {
  const printAsyn = api.getState()['@common'].asyncPrintConfig //异步打印配置
  const printAsynConfig = printAsyn?.items ?? []
  let dataNum = 20,
    dataDetailsNum = 60,
    invoiceNum = 5 //dataNum-单据数量限制,dataDetailsNum-单张单据的费用明细数量限制,invoiceNum-发票数量限制
  printAsynConfig.length &&
    printAsynConfig.map(line => {
      if (line?.type === 'invoice') {
        invoiceNum = line?.num
      } else if (line?.type === 'flow') {
        dataNum = line?.num
      } else if (line?.type === 'details') {
        dataDetailsNum = line?.num
      }
    })
  let { privilegeId = '', showAllFeeType, isTablePreview, isAsync = false } = data
  let selectKeys = []
  if (withPrivilegeId) {
    selectKeys = data.map(line => {
      return line.flowId.concat(':').concat(line.privilegeId ? line.privilegeId : privilegeId)
    })
  } else {
    selectKeys = data.map(obj => obj.flowId)
  }

  let invoiceSumSize = 0
  const isContainerPrintInvoice =
    data.filter(s => {
      invoiceSumSize += s.invoicesSize
      return s.isPrintInvoice
    }).length > 0
  //统计单张单据里面是否存在明细数量超过限定值的单据
  const isDataDetailsNum =
    data.filter(s => {
      return s?.details?.length > dataDetailsNum
    }).length > 0
  let isAnsy =
    isAsync ||
    (printInvoice === '1' && isContainerPrintInvoice && invoiceSumSize > invoiceNum) ||
    data?.length > dataNum ||
    isDataDetailsNum
  if (isAnsy && !isTablePreview) {
    api
      .open('@audit:PrintModal', {
        done: done,
        data: data,
        withPrivilegeId: withPrivilegeId,
        selectedRowKeys: selectKeys,
        privilegeId: data.privilegeId,
        showAllFeeType,
        sortByIndex,
        printInvoice,
        dataNum,
        dataDetailsNum,
      })
      .then(_ => {
        showMessage.success(i18n.get('创建成功'))
      })
  } else {
    api.invokeService('@share:print:list', {
      isTablePreview,
      flowId: selectKeys,
      withPrivilegeId: withPrivilegeId,
      isAsyn: isAnsy,
      callback: done,
      privilegeId: data.privilegeId,
      showAllFeeType,
      sortByIndex,
      printInvoice,
      printAttachment,
    })
  }
}
