import { Resource, Fetch } from '@ekuaibao/fetch'
import { app } from '@ekuaibao/whispered'
const channel = new Resource('/api/pay/v2/channel')
const search = new Resource('/api/pay/v1/branches/search')

export function fetchChannel(accountSort) {
  return channel.GET('/$accountSort', { accountSort })
}

export function fetchResult(filters) {
  return search.POST('', { ...filters })
}

export function trackApproved(time, name, nameStr) {
  window.TRACK &&
    window.TRACK(name, {
      actionName: i18n.get(nameStr),
      corpId: Fetch.ekbCorpId,
      branchSearchingTime: time,
    })
}

export function trackCustomPayment(key, options = {}) {
  const userInfo = app.getState?.()?.['@common']?.userinfo
  const { staff, corporation } = userInfo

  window?.TRACK?.(key, {
    trackTime: new Date().getTime(),
    userId: staff?.userId,
    userName: staff?.name,
    companyId: corporation?.id,
    corName: corporation?.name,
    ...options,
  })
}
