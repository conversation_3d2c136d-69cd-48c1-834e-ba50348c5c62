import { getV } from '@ekuaibao/lib/lib/help'

export default function disableMC (data: any = {}, targetOperateName: string = '') {
  const type: string = getV(data, 'type', '')
  if (data.hasOwnProperty('type') && type === 'FREEDOM') {
    return true
  }
  const permissions: Array<any> = getV(data, 'permissions', [])
  if (permissions.length) {
    const allName = 'ALL'
    const targetPermission = permissions.find(item =>
        item.name === targetOperateName.toUpperCase() ||
        item.name === allName
      ) || {}
    return !!targetPermission.auth
  }
  return false
}
