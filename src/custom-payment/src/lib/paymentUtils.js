export function searchPayment(list = [], text = '') {
  let searchText = text.trim().toLowerCase()
  return list.filter(
    el =>
      (el.name && !!~el.name.toLowerCase().indexOf(searchText)) ||
      (el.accountName && !!~el.accountName.toLowerCase().indexOf(searchText)) ||
      (el.code && !!~el.code.toLowerCase().indexOf(searchText)) ||
      (el.remark && !!~el.remark.toLowerCase().indexOf(searchText))
  )
}

// 付款账户根据条件添加字段
export const getFormItemsArrHSBC = () => {
  return [
    {
      label: '国家/地区（Country/Area）',
      name: 'extensions.country',
      max: 140,
      type: 'select',
      payer: '付款人地址',
      required: true,
    },
    {
      label: '城市（City）',
      name: 'extensions.city',
      max: 140,
      type: 'select',
      payer: '付款人地址',
      required: true,
    },
    {
      label: '镇/区（Town/District）',
      name: 'extensions.town',
      max: 140,
      type: 'input',
      payer: '付款人地址',
      required: true,
    },
    {
      label: '街道（Street）',
      name: 'extensions.street',
      max: 140,
      type: 'input',
      payer: '付款人地址',
      required: true,
    },
    {
      label: '币种（Currency）',
      name: 'extensions.currency',
      max: 140,
      type: 'multiple',
      notChineseOREnglish: true,
      required: true,
    },
    {
      label: '邮编（Zip code）',
      name: 'extensions.zipCode',
      max: 140,
      type: 'input',
      notChineseOREnglish: true,
      required: true,
    },
    {
      label: '付款银行所在国家',
      name: 'extensions.bankCountry',
      max: 140,
      type: 'select',
      notChineseOREnglish: true,
      required: true,
    },
    {
      label: 'BIC代码',
      name: 'extensions.bicCode',
      max: 140,
      type: 'input',
      required: true,
    },
    {
      label: 'Account Name',
      name: 'extensions.accountName',
      max: 140,
      type: 'input',
      onlyEn: true,
      required: true,
    },
    {
      label: 'Bank Name',
      name: 'extensions.bankName',
      max: 140,
      type: 'input',
      onlyEn: true,
      required: true,
    },
    {
      label: 'Country/Area',
      name: 'extensions.countryEn',
      max: 140,
      type: 'select',
      onlyEn: true,
      payer: 'Payer address',
      required: true,
    },
    {
      label: 'City',
      name: 'extensions.cityEn',
      max: 140,
      type: 'select',
      onlyEn: true,
      payer: 'Payer address',
      required: true,
    },
    {
      label: 'Town/District',
      name: 'extensions.townEn',
      max: 140,
      type: 'input',
      onlyEn: true,
      payer: 'Payer address',
      required: true,
    },
    {
      label: 'Street',
      name: 'extensions.streetEn',
      max: 140,
      type: 'input',
      onlyEn: true,
      payer: 'Payer address',
      required: true,
    },
    {
      label: 'Bank Address',
      name: 'extensions.bankAddress',
      max: 140,
      type: 'input',
      onlyEn: true,
      required: false,
    },
  ]
}

// 后台不愿意返回，让前端写死的，我有罪
export const getFormItemsCityCode = () => [
  { key: '090', value: i18n.get('所罗门群岛'), eName: 'SB' },
  { key: '092', value: i18n.get('英属维尔京群岛'), eName: 'VG' },
  { key: '096', value: i18n.get('文莱'), eName: 'BN' },
  { key: '100', value: i18n.get('保加利亚'), eName: 'BG' },
  { key: '104', value: i18n.get('缅甸'), eName: 'MM' },
  { key: '108', value: i18n.get('布隆迪'), eName: 'BI' },
  { key: '112', value: i18n.get('白俄罗斯'), eName: 'BY' },
  { key: '116', value: i18n.get('柬埔寨'), eName: 'KH' },
  { key: '120', value: i18n.get('喀麦隆'), eName: 'CM' },
  { key: '124', value: i18n.get('加拿大'), eName: 'CA' },
  { key: '132', value: i18n.get('佛得角'), eName: 'CV' },
  { key: '136', value: i18n.get('开曼群岛'), eName: 'KY' },
  { key: '140', value: i18n.get('中非'), eName: 'CF' },
  { key: '144', value: i18n.get('斯里兰卡'), eName: 'LK' },
  { key: '148', value: i18n.get('乍得'), eName: 'TD' },
  { key: '152', value: i18n.get('智利'), eName: 'CL' },
  { key: '156', value: i18n.get('中国'), eName: 'CN' },
  { key: '158', value: i18n.get('中国台湾'), eName: 'TW' },
  { key: '162', value: i18n.get('圣诞岛'), eName: 'CX' },
  { key: '166', value: i18n.get('科科斯（基林）群岛'), eName: 'CC' },
  { key: '170', value: i18n.get('哥伦比亚'), eName: 'CO' },
  { key: '174', value: i18n.get('科摩罗'), eName: 'KM' },
  { key: '175', value: i18n.get('马约特'), eName: 'YT' },
  { key: '178', value: i18n.get('刚果（布）'), eName: 'CG' },
  { key: '180', value: i18n.get('刚果（金）'), eName: 'CD' },
  { key: '184', value: i18n.get('库克群岛'), eName: 'CK' },
  { key: '188', value: i18n.get('哥斯达黎加'), eName: 'CR' },
  { key: '191', value: i18n.get('克罗地亚'), eName: 'HR' },
  { key: '192', value: i18n.get('古巴'), eName: 'CU' },
  { key: '196', value: i18n.get('塞浦路斯'), eName: 'CY' },
  { key: '203', value: i18n.get('捷克'), eName: 'CZ' },
  { key: '204', value: i18n.get('贝宁'), eName: 'BJ' },
  { key: '208', value: i18n.get('丹麦'), eName: 'DK' },
  { key: '212', value: i18n.get('多米尼克'), eName: 'DM' },
  { key: '214', value: i18n.get('多米尼加'), eName: 'DO' },
  { key: '218', value: i18n.get('厄瓜多尔'), eName: 'EC' },
  { key: '222', value: i18n.get('萨尔瓦多'), eName: 'SV' },
  { key: '729', value: i18n.get('苏丹'), eName: 'SD' },
  { key: '728', value: i18n.get('南苏丹'), eName: 'SS' },
  { key: '226', value: i18n.get('赤道几内亚'), eName: 'GQ' },
  { key: '231', value: i18n.get('埃塞俄比亚'), eName: 'ET' },
  { key: '232', value: i18n.get('厄立特里亚'), eName: 'ER' },
  { key: '233', value: i18n.get('爱沙尼亚'), eName: 'EE' },
  { key: '234', value: i18n.get('法罗群岛'), eName: 'FO' },
  { key: '238', value: i18n.get('福克兰群岛-马尔维纳斯群岛'), eName: 'FK' },
  { key: '239', value: i18n.get('南乔治亚岛和南桑德韦奇岛'), eName: 'GS' },
  { key: '242', value: i18n.get('斐济'), eName: 'FJ' },
  { key: '246', value: i18n.get('芬兰'), eName: 'FI' },
  { key: '250', value: i18n.get('法国'), eName: 'FR' },
  { key: '254', value: i18n.get('法属圭亚那'), eName: 'GF' },
  { key: '258', value: i18n.get('法属波利尼西亚'), eName: 'PF' },
  { key: '260', value: i18n.get('法属南部领地'), eName: 'TF' },
  { key: '262', value: i18n.get('吉布提'), eName: 'DJ' },
  { key: '266', value: i18n.get('加蓬'), eName: 'GA' },
  { key: '268', value: i18n.get('格鲁吉亚'), eName: 'GE' },
  { key: '270', value: i18n.get('冈比亚'), eName: 'GM' },
  { key: '275', value: i18n.get('巴勒斯坦'), eName: 'PS' },
  { key: '276', value: i18n.get('德国'), eName: 'DE' },
  { key: '288', value: i18n.get('加纳'), eName: 'GH' },
  { key: '292', value: i18n.get('直布罗陀'), eName: 'GI' },
  { key: '296', value: i18n.get('基里巴斯'), eName: 'KI' },
  { key: '300', value: i18n.get('希腊'), eName: 'GR' },
  { key: '304', value: i18n.get('格陵兰'), eName: 'GL' },
  { key: '308', value: i18n.get('格林纳达'), eName: 'GD' },
  { key: '312', value: i18n.get('瓜德罗普'), eName: 'GP' },
  { key: '316', value: i18n.get('关岛'), eName: 'GU' },
  { key: '320', value: i18n.get('危地马拉'), eName: 'GT' },
  { key: '324', value: i18n.get('几内亚'), eName: 'GN' },
  { key: '328', value: i18n.get('圭亚那'), eName: 'GY' },
  { key: '634', value: i18n.get('卡塔尔'), eName: 'QA' },
  { key: '638', value: i18n.get('留尼汪'), eName: 'RE' },
  { key: '642', value: i18n.get('罗马尼亚'), eName: 'RO' },
  { key: '643', value: i18n.get('俄罗斯联邦'), eName: 'RU' },
  { key: '646', value: i18n.get('卢旺达'), eName: 'RW' },
  { key: '654', value: i18n.get('圣赫勒拿'), eName: 'SH' },
  { key: '659', value: i18n.get('圣基茨和尼维斯'), eName: 'KN' },
  { key: '660', value: i18n.get('安圭拉'), eName: 'AI' },
  { key: '662', value: i18n.get('圣卢西亚'), eName: 'LC' },
  { key: '666', value: i18n.get('圣皮埃尔和密克隆'), eName: 'PM' },
  { key: '670', value: i18n.get('圣文森特和格林纳丁斯'), eName: 'VC' },
  { key: '674', value: i18n.get('圣马力诺'), eName: 'SM' },
  { key: '678', value: i18n.get('圣多美和普林西比'), eName: 'ST' },
  { key: '682', value: i18n.get('沙特阿拉伯'), eName: 'SA' },
  { key: '686', value: i18n.get('塞内加尔'), eName: 'SN' },
  { key: '688', value: i18n.get('塞尔维亚'), eName: 'RS' },
  { key: '690', value: i18n.get('塞舌尔'), eName: 'SC' },
  { key: '694', value: i18n.get('塞拉利昂'), eName: 'SL' },
  { key: '702', value: i18n.get('新加坡'), eName: 'SG' },
  { key: '703', value: i18n.get('斯洛伐克'), eName: 'SK' },
  { key: '704', value: i18n.get('越南'), eName: 'VN' },
  { key: '705', value: i18n.get('斯洛文尼亚'), eName: 'SI' },
  { key: '706', value: i18n.get('索马里'), eName: 'SO' },
  { key: '710', value: i18n.get('南非'), eName: 'ZA' },
  { key: '716', value: i18n.get('津巴布韦'), eName: 'ZW' },
  { key: '724', value: i18n.get('西班牙'), eName: 'ES' },
  { key: '732', value: i18n.get('西撒哈拉'), eName: 'EH' },
  { key: '740', value: i18n.get('苏里南'), eName: 'SR' },
  { key: '744', value: i18n.get('斯瓦尔巴岛和扬马延岛'), eName: 'SJ' },
  { key: '748', value: i18n.get('斯威士兰'), eName: 'SZ' },
  { key: '752', value: i18n.get('瑞典'), eName: 'SE' },
  { key: '756', value: i18n.get('瑞士'), eName: 'CH' },
  { key: '760', value: i18n.get('叙利亚'), eName: 'SY' },
  { key: '762', value: i18n.get('塔吉克斯坦'), eName: 'TJ' },
  { key: '764', value: i18n.get('泰国'), eName: 'TH' },
  { key: '768', value: i18n.get('多哥'), eName: 'TG' },
  { key: '772', value: i18n.get('托克劳'), eName: 'TK' },
  { key: '776', value: i18n.get('汤加'), eName: 'TO' },
  { key: '780', value: i18n.get('特立尼达和多巴哥'), eName: 'TT' },
  { key: '784', value: i18n.get('阿联酋'), eName: 'AE' },
  { key: '788', value: i18n.get('突尼斯'), eName: 'TN' },
  { key: '792', value: i18n.get('土耳其'), eName: 'TR' },
  { key: '795', value: i18n.get('土库曼斯坦'), eName: 'TM' },
  { key: '796', value: i18n.get('特克斯和凯科斯群岛'), eName: 'TC' },
  { key: '798', value: i18n.get('图瓦卢'), eName: 'TV' },
  { key: '800', value: i18n.get('乌干达'), eName: 'UG' },
  { key: '804', value: i18n.get('乌克兰'), eName: 'UA' },
  { key: '807', value: i18n.get('前南马其顿'), eName: 'MK' },
  { key: '818', value: i18n.get('埃及'), eName: 'EG' },
  { key: '826', value: i18n.get('英国'), eName: 'GB' },
  { key: '833', value: i18n.get('马恩岛'), eName: 'IM' },
  { key: '834', value: i18n.get('坦桑尼亚'), eName: 'TZ' },
  { key: '840', value: i18n.get('美国'), eName: 'US' },
  { key: '850', value: i18n.get('美属维尔京群岛'), eName: 'VI' },
  { key: '854', value: i18n.get('布基纳法索'), eName: 'BF' },
  { key: '858', value: i18n.get('乌拉圭'), eName: 'UY' },
  { key: '860', value: i18n.get('乌兹别克斯坦'), eName: 'UZ' },
  { key: '862', value: i18n.get('委内瑞拉'), eName: 'VE' },
  { key: '876', value: i18n.get('瓦利斯和富图纳'), eName: 'WF' },
  { key: '882', value: i18n.get('萨摩亚'), eName: 'WS' },
  { key: '887', value: i18n.get('也门'), eName: 'YE' },
  { key: '894', value: i18n.get('赞比亚'), eName: 'ZM' },
  { key: '332', value: i18n.get('海地'), eName: 'HT' },
  { key: '334', value: i18n.get('赫德岛和麦克唐纳岛'), eName: 'HM' },
  { key: '336', value: i18n.get('梵蒂冈'), eName: 'VA' },
  { key: '340', value: i18n.get('洪都拉斯'), eName: 'HN' },
  { key: '344', value: i18n.get('中国香港'), eName: 'HK' },
  { key: '348', value: i18n.get('匈牙利'), eName: 'HU' },
  { key: '352', value: i18n.get('冰岛'), eName: 'IS' },
  { key: '356', value: i18n.get('印度'), eName: 'IN' },
  { key: '360', value: i18n.get('印度尼西亚'), eName: 'ID' },
  { key: '364', value: i18n.get('伊朗'), eName: 'IR' },
  { key: '368', value: i18n.get('伊拉克'), eName: 'IQ' },
  { key: '372', value: i18n.get('爱尔兰'), eName: 'IE' },
  { key: '376', value: i18n.get('以色列'), eName: 'IL' },
  { key: '380', value: i18n.get('意大利'), eName: 'IT' },
  { key: '384', value: i18n.get('科特迪瓦'), eName: 'CI' },
  { key: '388', value: i18n.get('牙买加'), eName: 'JM' },
  { key: '392', value: i18n.get('日本'), eName: 'JP' },
  { key: '398', value: i18n.get('哈萨克斯坦'), eName: 'KZ' },
  { key: '400', value: i18n.get('约旦'), eName: 'JO' },
  { key: '404', value: i18n.get('肯尼亚'), eName: 'KE' },
  { key: '408', value: i18n.get('朝鲜'), eName: 'KP' },
  { key: '410', value: i18n.get('韩国'), eName: 'KR' },
  { key: '414', value: i18n.get('科威特'), eName: 'KW' },
  { key: '417', value: i18n.get('吉尔吉斯斯坦'), eName: 'KG' },
  { key: '418', value: i18n.get('老挝'), eName: 'LA' },
  { key: '422', value: i18n.get('黎巴嫩'), eName: 'LB' },
  { key: '426', value: i18n.get('莱索托'), eName: 'LS' },
  { key: '428', value: i18n.get('拉脱维亚'), eName: 'LV' },
  { key: '430', value: i18n.get('利比里亚'), eName: 'LR' },
  { key: '434', value: i18n.get('利比亚'), eName: 'LY' },
  { key: '438', value: i18n.get('列支敦士登'), eName: 'LI' },
  { key: '440', value: i18n.get('立陶宛'), eName: 'LT' },
  { key: '442', value: i18n.get('卢森堡'), eName: 'LU' },
  { key: '446', value: i18n.get('中国澳门'), eName: 'MO' },
  { key: '450', value: i18n.get('马达加斯加'), eName: 'MG' },
  { key: '454', value: i18n.get('马拉维'), eName: 'MW' },
  { key: '458', value: i18n.get('马来西亚'), eName: 'MY' },
  { key: '462', value: i18n.get('马尔代夫'), eName: 'MV' },
  { key: '466', value: i18n.get('马里'), eName: 'ML' },
  { key: '470', value: i18n.get('马耳他'), eName: 'MT' },
  { key: '474', value: i18n.get('马提尼克'), eName: 'MQ' },
  { key: '478', value: i18n.get('毛里塔尼亚'), eName: 'MR' },
  { key: '480', value: i18n.get('毛里求斯'), eName: 'MU' },
  { key: '484', value: i18n.get('墨西哥'), eName: 'MX' },
  { key: '492', value: i18n.get('摩纳哥'), eName: 'MC' },
  { key: '496', value: i18n.get('蒙古'), eName: 'MN' },
  { key: '498', value: i18n.get('摩尔多瓦'), eName: 'MD' },
  { key: '499', value: i18n.get('黑山'), eName: 'ME' },
  { key: '500', value: i18n.get('蒙特塞拉特'), eName: 'MS' },
  { key: '504', value: i18n.get('摩洛哥'), eName: 'MA' },
  { key: '508', value: i18n.get('莫桑比克'), eName: 'MZ' },
  { key: '512', value: i18n.get('阿曼'), eName: 'OM' },
  { key: '516', value: i18n.get('纳米比亚'), eName: 'NA' },
  { key: '520', value: i18n.get('瑙鲁'), eName: 'NR' },
  { key: '524', value: i18n.get('尼泊尔'), eName: 'NP' },
  { key: '528', value: i18n.get('荷兰'), eName: 'NL' },
  { key: '533', value: i18n.get('阿鲁巴'), eName: 'AW' },
  { key: '540', value: i18n.get('新喀里多尼亚'), eName: 'NC' },
  { key: '548', value: i18n.get('瓦努阿图'), eName: 'VU' },
  { key: '554', value: i18n.get('新西兰'), eName: 'NZ' },
  { key: '558', value: i18n.get('尼加拉瓜'), eName: 'NI' },
  { key: '562', value: i18n.get('尼日尔'), eName: 'NE' },
  { key: '566', value: i18n.get('尼日利亚'), eName: 'NG' },
  { key: '570', value: i18n.get('纽埃'), eName: 'NU' },
  { key: '574', value: i18n.get('诺福克岛'), eName: 'NF' },
  { key: '578', value: i18n.get('挪威'), eName: 'NO' },
  { key: '580', value: i18n.get('北马里亚纳'), eName: 'MP' },
  { key: '581', value: i18n.get('美国本土外小岛屿'), eName: 'UM' },
  { key: '583', value: i18n.get('密克罗尼西亚联邦'), eName: 'FM' },
  { key: '584', value: i18n.get('马绍尔群岛'), eName: 'MH' },
  { key: '585', value: i18n.get('帕劳'), eName: 'PW' },
  { key: '586', value: i18n.get('巴基斯坦'), eName: 'PK' },
  { key: '591', value: i18n.get('巴拿马'), eName: 'PA' },
  { key: '598', value: i18n.get('巴布亚新几内亚'), eName: 'PG' },
  { key: '600', value: i18n.get('巴拉圭'), eName: 'PY' },
  { key: '604', value: i18n.get('秘鲁'), eName: 'PE' },
  { key: '608', value: i18n.get('菲律宾'), eName: 'PH' },
  { key: '612', value: i18n.get('皮特凯恩'), eName: 'PN' },
  { key: '616', value: i18n.get('波兰'), eName: 'PL' },
  { key: '620', value: i18n.get('葡萄牙'), eName: 'PT' },
  { key: '624', value: i18n.get('几内亚比绍'), eName: 'GW' },
  { key: '626', value: i18n.get('东帝汶'), eName: 'TL' },
  { key: '630', value: i18n.get('波多黎各'), eName: 'PR' },
  { key: '004', value: i18n.get('阿富汗'), eName: 'AF' },
  { key: '008', value: i18n.get('阿尔巴尼亚'), eName: 'AL' },
  { key: '010', value: i18n.get('南极洲'), eName: 'AQ' },
  { key: '012', value: i18n.get('阿尔及利亚'), eName: 'DZ' },
  { key: '016', value: i18n.get('美属萨摩亚'), eName: 'AS' },
  { key: '020', value: i18n.get('安道尔'), eName: 'AD' },
  { key: '024', value: i18n.get('安哥拉'), eName: 'AO' },
  { key: '028', value: i18n.get('安提瓜和巴布达'), eName: 'AG' },
  { key: '031', value: i18n.get('阿塞拜疆'), eName: 'AZ' },
  { key: '032', value: i18n.get('阿根廷'), eName: 'AR' },
  { key: '036', value: i18n.get('澳大利亚'), eName: 'AU' },
  { key: '040', value: i18n.get('奥地利'), eName: 'AT' },
  { key: '044', value: i18n.get('巴哈马'), eName: 'BS' },
  { key: '048', value: i18n.get('巴林'), eName: 'BH' },
  { key: '050', value: i18n.get('孟加拉国'), eName: 'BD' },
  { key: '051', value: i18n.get('亚美尼亚'), eName: 'AM' },
  { key: '052', value: i18n.get('巴巴多斯'), eName: 'BB' },
  { key: '056', value: i18n.get('比利时'), eName: 'BE' },
  { key: '060', value: i18n.get('百慕大'), eName: 'BM' },
  { key: '064', value: i18n.get('不丹'), eName: 'BT' },
  { key: '068', value: i18n.get('玻利维亚'), eName: 'BO' },
  { key: '070', value: i18n.get('波黑'), eName: 'BA' },
  { key: '072', value: i18n.get('博茨瓦纳'), eName: 'BW' },
  { key: '074', value: i18n.get('布维岛'), eName: 'BV' },
  { key: '076', value: i18n.get('巴西'), eName: 'BR' },
  { key: '084', value: i18n.get('伯利兹'), eName: 'BZ' },
  { key: '086', value: i18n.get('英属印度洋领地'), eName: 'IO' }
]

export const getOptionTitle = (el, item) => {
  if (el.name.includes('city')) {
    if (el.onlyEn) return item.state ? `${item.state}-${item.name}` : item.name
    return item.cnState ? `${item.cnState}-${item.cnName}` : item.cnName
  }
  return el.onlyEn ? item.fullEname : item.fullCname
}

export const getPlaceholder = (el, showLabel) => {
  if (el.notChineseOREnglish) return i18n.get('请输入{__k0}', { __k0: showLabel })
  return el.onlyEn
    ? i18n.get(`请输入{__k0}_English`, { __k0: showLabel })
    : i18n.get(`请输入{__k0}_Chinese`, { __k0: showLabel })
}

export const getCityStr = (city) => {
  const cnStr = city.cnState ? `${city.cnState}-${city.cnName}` : city.cnName
  const enStr = city.state ? `${city.state}-${city.name}` : city.name
  return { cnStr, enStr }
}
