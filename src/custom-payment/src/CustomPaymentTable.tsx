//@ts-ignore
import styles from './CustomPaymentTable.module.less'
import React, { FC, useState, useEffect, useCallback } from 'react'
import {
  Button,
  Space,
  Input,
  message,
  Divider,
  Tabs,
  Modal,
  Tag,
  Tooltip,
  Ellipsis,
} from '@hose/eui'
import { app } from '@ekuaibao/whispered'
import { OutlinedGeneralSetting, OutlinedTipsAdd } from '@hose/eui-icons'
import { PayeeInfoIF } from '@ekuaibao/ekuaibao_types'
import CustomBreadcrumb, { PathItem } from './elements/CustomBreadcrumb'
const { Search } = Input
//@ts-ignore
import { ProTable } from '@hose/pro-table'
import { EnhanceConnect } from '@ekuaibao/store'
import ConfigProviderWrapper from './elements/ConfigProviderWrapper'
import { useObserver } from 'mobx-react-lite'
import { provider, useInstance } from '@ekuaibao/react-ioc'
import { PermissionVm } from './vms/Permission.vm'
import { Universal_Unique_Key } from './index'
const { UniversalComponent } = app.require('@elements/UniversalComponent') as any
import actions, { postExportExcelAccounts } from './custom-payment-action'
import MessageCenter from '@ekuaibao/messagecenter'
import { fnCheckAntAlipayBindStateFormEKB, fnPayerListFilter } from './custom-payment-fetch-util'
import { Fetch } from '@ekuaibao/fetch'
const EKBIcon = app.require<any>('@elements/ekbIcon')

export type RuleType = {
  staffId: string
  payerIds: string[]
  configId?: string
}

export type RuleItem = Partial<{
  id: string
  staffId: PayeeInfoIF
  payerIds: string[]
  timestamp: number
  createTime: number
  updateTime: number
  operatorId: string
  operatorName: string
}>

interface SearchInfo {
  current: number
  pageSize: number
  searchText: string
  type: 'all' | 'disabled'
}
const bus = new MessageCenter()
const CustomPaymentTable: FC = (props: any) => {
  const vm = useInstance<PermissionVm>(PermissionVm.NAME)
  const [loading, setLoading] = useState<boolean>(false)
  const [list, setList] = useState<any[]>([])
  const [searchInfo, setSearchInfo] = useState<SearchInfo>({
    current: 1,
    pageSize: 10,
    type: 'all',
    searchText: '',
  })
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [total, setTotal] = useState<number>(0)
  const path: PathItem[] = [
    { name: i18n.get('资金管理') },
    { name: i18n.get(`资金账户管理`) },
    { name: i18n.get('银行付款账户') },
  ]

  const handleSaveAccount = useCallback(
    (result: any) => {
      const { data, state } = result
      const dataId = data.id //actions.putPayment执行后，data里的id会消失
      if (state === 'edit') {
        app.dispatch(actions.putPayment(data)).then(
          () => {
            getPayAccountList()
            fnBindAlipay(data, dataId)
            handleInsertAssist(`修改${data.accountName}付款账户`) // @i18n-ignore
          },
          (err: any) => {
            message.error(err.message)
          },
        )
      } else {
        app.dispatch(actions.postPayment(data)).then(
          (res: any) => {
            setSearchInfo({ ...searchInfo, current: 1 })
            fnBindAlipay(data, res.id)
            handleInsertAssist(`创建${data.accountName}付款账户`) // @i18n-ignore
          },
          (err: any) => {
            message.error(err.message)
          },
        )
      }
    },
    [searchInfo],
  )

  useEffect(() => {
    app.invokeService('@common:get:bank:channels')
    app.dataLoader('@common.staffs').load()
    app.invokeService('@common:get:mc:permission:byName', 'ACCOUNT_PAY').then((result: any) => {
      vm.initData(result.value)
    })
  }, [])

  useEffect(() => {
    bus.watch('payment:save:click', handleSaveAccount)
    return () => {
      bus.un('payment:save:click', handleSaveAccount)
    }
  }, [handleSaveAccount])

  useEffect(() => {
    getPayAccountList()
  }, [searchInfo])

  const handleInsertAssist = (title: string) => {
    app.invokeService('@common:insert:assist:record', {
      title,
    })
  }
  const fnBindAlipay = (data: any, id: string) => {
    //确认支付宝绑定状态
    if (data.channels.includes('ANTALIPAY')) {
      fnCheckAntAlipayBindStateFormEKB(id)
    }
  }

  const getPayAccountList = () => {
    const { current, pageSize, type, searchText } = searchInfo
    const start = (current - 1) * pageSize
    const count = pageSize
    Fetch.GET('/api/pay/v2/accounts', {
      join: 'bankLinkId,bankLinkId,/pay/v1/banks',
      filter: fnPayerListFilter({ searchText, isShowActive: type === 'all' }),
      nameLike: searchText && searchText !== '企业' ? searchText : '',
      start,
      count,
    }).then((res: any) => {
      setLoading(false)
      setList(res?.items || [])
      setTotal(res.count ?? 0)
    })
  }

  const handleOnSearch = (value: string) => {
    setSearchInfo({ ...searchInfo, searchText: value, current: 1 })
  }

  const handleAdd = () => {
    app.open('@custom-payment:CreateAccountFormPopup', { bus, title: i18n.get('新建企业付款账户') })
  }
  const handleEdit = (record: any) => {
    app.open('@custom-payment:CreateAccountFormPopup', {
      ...record,
      state: 'edit',
      bus,
      title: i18n.get('编辑企业付款账户'),
      onRestoreOrDisable: handleStopAccount,
    })
    handleInsertAssist(`查看${record.accountName}付款账户`) // @i18n-ignore
  }
  const handleStopAccount = (item: any, checked: boolean) => {
    if (!checked) {
      Modal.confirm({
        title: i18n.get(`{__k0}：`, { __k0: i18n.get('停用') }) + item.name,
        content: i18n.get('停用后该账户在支付时不可见'),
        okText: i18n.get('确定'),
        cancelText: i18n.get('取消'),
        onOk: () => {
          app.dispatch(actions.disable({ id: item.id })).then(() => getPayAccountList())
          handleInsertAssist(`停用${item.accountName}付款账户`) // @i18n-ignore
        },
      })
    } else {
      app.dispatch(actions.restore({ id: item.id })).then(() => getPayAccountList())
      handleInsertAssist(`启用${item.accountName}付款账户`) // @i18n-ignore
    }
  }
  const columns = [
    {
      title: i18n.get('户名'),
      dataIndex: 'accountName',
      key: 'accountName',
      width: 232,
      render: (accountName: any, record: any) => {
        const { name, code } = record
        const showName = name ? name : accountName
        if (accountName.length < 4) {
          return (
            <span className="bankInfo_table_remark">
              {showName}
              {code ? (
                <span style={{ color: 'var(--eui-text-caption)' }}>{`(${code})`}</span>
              ) : null}
            </span>
          )
        }
        return (
          <Tooltip
            getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
            title={`${showName}${code ? `(${code})` : ''}`}
            placement="topLeft">
            <span className="bankInfo_table_remark">
              {showName}
              {code ? (
                <span style={{ color: 'var(--eui-text-caption)' }}>{`(${code})`}</span>
              ) : null}
            </span>
          </Tooltip>
        )
      },
    },
    {
      title: i18n.get('账户与账号'),
      dataIndex: 'bankName',
      key: 'bankName',
      width: 323,
      render: (_: string, payerInfo: PayeeInfoIF) => {
        const accountNo = payerInfo.accountNo || payerInfo.cardNo
        const name = payerInfo.branch || payerInfo.bankName || payerInfo.bank || payerInfo.name
        const iconUrl = payerInfo.icon
        return (
          <div className="bankInfo_table_item">
            <img src={iconUrl ? `${iconUrl}?type=new` : ''} />
            <div>
              <Ellipsis className="bankInfo_table_item_name" content={name} />
              {accountNo && (
                <div className="bankInfo_table_item_num">
                  {accountNo?.replace(/\s/g, '')?.replace(/(.{4})/g, '$1 ')}
                </div>
              )}
            </div>
          </div>
        )
      },
    },
    {
      title: i18n.get('默认支付方式'),
      dataIndex: 'defaultChannel',
      width: 200,
      key: 'defaultChannel',
      render: (defaultChannel: string) => {
        return (
          <Tag
            color="neu"
            icon={<EKBIcon name={props.dynamicChannelMap?.[defaultChannel]?.icon} />}>
            <span style={{ color: 'var(--eui-text-title, rgba(29, 33, 41, 0.90))' }}>
              {props.dynamicChannelMap?.[defaultChannel]?.name}
            </span>
          </Tag>
        )
      },
    },
    {
      title: i18n.get('状态'),
      dataIndex: 'active',
      width: 100,
      key: 'active',
      valueEnum: {
        true: {
          text: i18n.get('启用'),
          status: 'Success',
        },
        false: {
          text: i18n.get('停用'),
          status: 'Error',
        },
      },
    },
    {
      title: i18n.get('备注信息'),
      dataIndex: 'remark',
      key: 'remark',
      width: 162,
      render: (remark: any) => {
        if (remark.length < 4) {
          return <span className="bankInfo_table_remark">{remark}</span>
        }
        return (
          <Tooltip
            getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
            title={remark}
            placement="topLeft">
            <span className="bankInfo_table_remark">{remark}</span>
          </Tooltip>
        )
      },
    },
    {
      title: i18n.get('操作'),
      dataIndex: 'action',
      key: 'action',
      width: 100,
      fixed: 'right',
      render: (_: any, record: any) => {
        return (
          <Space>
            <Button
              category="text"
              size="small"
              onClick={() => handleEdit(record)}
              theme="highlight"
              data-testid={`pay-customPayment-edit-button-${record.id}`}>
              {i18n.get('编辑')}
            </Button>
          </Space>
        )
      },
    },
  ]
  const handlePageChange = (page: number, pageSize: number) => {
    setSearchInfo({ ...searchInfo, current: page, pageSize: pageSize })
  }
  const handleImport = () => {
    app.open('@bills:ImportDetailByExcel', { type: 'payer' }).then(() => {
      setSearchInfo({ ...searchInfo, current: 1 })
      handleInsertAssist(`导入付款账户`)
    })
  }
  const handleOnTabsChange = (key: string) => {
    setSearchInfo({ ...searchInfo, type: key as 'all' | 'disabled', current: 1 })
  }
  const handleExportAll = () => {
    const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
    const { filter, nameLike } = fnGetUrlParams()
    Fetch.GET(`/api/pay/v2/accounts/exportWay?${filter}${nameLike}`).then((v) => {
      if (v.value.exportWay === 'async') {
        app.open('@layout:AsyncExportModal').then((res: any) => {
          Fetch.GET(`/api/pay/v2/accounts/export/excel/async?${filter}${nameLike}`, {
            taskName: res.taskName,
          })
        })
      } else {
        const exportUrl = `${Fetch.fixOrigin(
          location.origin,
        )}/api/pay/v2/accounts/export/excel?corpId=${ekbCorpId}&${filter}${nameLike}`
        app.emit('@vendor:download', exportUrl)
        message.success(i18n.get(`导出成功`))
      }
    })
    handleInsertAssist(`导出付款账户`) // @i18n-ignore
  }

  const fnGetUrlParams = () => {
    const { type, searchText } = searchInfo
    const filterStr = fnPayerListFilter({ searchText, isShowActive: type === 'all' })
    const _filterStr = encodeURIComponent(filterStr)
    const filter = _filterStr ? `filter=${_filterStr}` : ''
    const nameLike =
      searchText && searchText !== '企业' ? `&nameLike=${encodeURIComponent(searchText)}` : '' // @i18n-ignore
    return { filter, filterStr, nameLike }
  }

  const handleExportBySelected = async (selectedRowKeys: string[], onCleanSelected: any) => {
    const { searchText } = searchInfo
    const { filterStr } = fnGetUrlParams()

    const result = await postExportExcelAccounts({
      filter: filterStr,
      nameLike: searchText && searchText !== '企业' ? searchText : '',
      ids: selectedRowKeys,
    })
    if (result?.url) {
      app.emit('@vendor:download', result?.url, result?.fileName)
      message.success('导出成功')
      onCleanSelected()
    } else {
      message.error('导出失败')
    }
  }
  const getPayerConfig = async () => {
    const config = await app.invokeService('@common:get:payer:shared')
    return config.value
  }
  const handleSetBtn = async () => {
    const config = await getPayerConfig()
    const {
      id,
      byHand,
      dimensionId,
      dimensionField,
      autoSummary,
      fieldId,
      useSpecial,
      fieldLabel,
      useLegalEntityAccount,
    } = config

    app
      .open('@custom-payment:PayerAccountSets', {
        id: id,
        useSpecial: useSpecial,
        byHand: byHand,
        dimensionId: dimensionId,
        dimensionField: dimensionField,
        autoSummary: autoSummary,
        fieldId: fieldId,
        fieldLabel: fieldLabel,
        useLegalEntityAccount,
      })
      .then((data) => {
        app.dispatch(actions.savePayerConfig(data)).then(() => {
          message.success('设置完成')
        })
      })
  }

  return useObserver(() => {
    const { channelList = [] } = props
    const activeLength = channelList.filter(
      (item: any) => item.needRemark && item.channel !== 'CHANPAY',
    ).length
    const chanpay = channelList.find((item: any) => item.channel == 'CHANPAY') || {}
    const enabled = chanpay.active ? !chanpay.active : activeLength > 0
    return (
      <div className={styles['commonWrapper']}>
        <CustomBreadcrumb path={path} />
        <div className="tableWrapper">
          <div className="tableTop">
            <span style={{ flexShrink: 0 }}>{i18n.get('银行付款账户')}</span>
            {vm.create && (
              <div style={{ flexShrink: 0, display: 'flex', alignItems: 'center' }}>
                <Space>
                  <UniversalComponent uniqueKey={`${Universal_Unique_Key}.importButtonGroup`}>
                    <Button category="secondary" onClick={handleImport} data-testid="pay-customPayment-import-button">
                      {i18n.get('导入')}
                    </Button>
                  </UniversalComponent>
                  <UniversalComponent uniqueKey={`${Universal_Unique_Key}.importButtonGroup`}>
                    <Button disabled={total === 0} category="secondary" onClick={handleExportAll} data-testid="pay-customPayment-export-all-button">
                      {i18n.get('导出全部')}
                    </Button>
                  </UniversalComponent>
                  <Button data-testid="pay-customPaymentAdd-button" onClick={handleAdd} icon={<OutlinedTipsAdd />}>
                    {i18n.get('新建')}
                  </Button>
                </Space>
                {enabled && (
                  <>
                    <UniversalComponent uniqueKey={`${Universal_Unique_Key}.settingBtn`}>
                      <Divider type="vertical" style={{ height: 24 }} />
                    </UniversalComponent>
                    <UniversalComponent uniqueKey={`${Universal_Unique_Key}.settingBtn`}>
                      <Button
                        category="secondary"
                        onClick={handleSetBtn}
                        icon={<OutlinedGeneralSetting />}
                        data-testid="pay-customPayment-setting-button"
                      />
                    </UniversalComponent>
                  </>
                )}
              </div>
            )}
          </div>
          <div className="tableContent">
            <ConfigProviderWrapper>
              <Tabs
                onChange={handleOnTabsChange}
                defaultActiveKey="all"
                items={[
                  { label: i18n.get('全部'), key: 'all' },
                  { label: i18n.get('已停用'), key: 'disabled' },
                ]}
                data-testid="pay-customPayment-status-tabs"
              />
              <Search
                data-testid="pay-customPaymentSearch-input"
                style={{ width: 334, marginBottom: 16 }}
                onSearch={handleOnSearch}
                placeholder={i18n.get(`搜索开户名、备注名、编码或备注`)}
              />
              <ProTable
                loading={loading}
                search={false}
                toolbar={{ style: { display: 'none' } }}
                columns={columns}
                dataSource={list}
                scroll={{
                  y:
                    selectedRowKeys.length > 0
                      ? 'calc(100vh - 356px - 102px)'
                      : 'calc(100vh - 292px - 102px)',
                  x: 1040,
                }}
                rowKey="id"
                rowSelection={{
                  selectedRowKeys: selectedRowKeys,
                  preserveSelectedRowKeys: true,
                  onChange: (selectedRowKeys: React.Key[]) => {
                    setSelectedRowKeys(selectedRowKeys)
                  },
                }}
                tableAlertRender={({ selectedRowKeys, onCleanSelected }: any) => (
                  <Space size={32}>
                    <Space size={8}>
                      <span>已选 {selectedRowKeys.length} 项</span>
                      <Button
                        onClick={onCleanSelected}
                        category="text"
                        size="small"
                        theme="highlight"
                        data-testid="pay-customPayment-cancel-selection-button">
                        {i18n.get('取消选择')}
                      </Button>
                    </Space>
                  </Space>
                )}
                tableAlertOptionRender={({ selectedRowKeys, onCleanSelected }: any) => {
                  return (
                    <Space size={8}>
                      <Button
                        category="text"
                        onClick={() => handleExportBySelected(selectedRowKeys, onCleanSelected)}
                        theme="highlight"
                        size="small"
                        data-testid="pay-customPayment-export-selected-button">
                        {i18n.get('导出所选')}
                      </Button>
                    </Space>
                  )
                }}
                pagination={{
                  total,
                  current: searchInfo.current,
                  pageSize: searchInfo.pageSize,
                  showTotal: (total: number) => i18n.get('共 {__k0} 条', { __k0: total }),
                  onChange: handlePageChange,
                  showSizeChanger: true,
                  showQuickJumper: true,
                }}
              />
            </ConfigProviderWrapper>
          </div>
        </div>
      </div>
    )
  })
}
export default provider([PermissionVm.NAME, PermissionVm])(
  EnhanceConnect((state: any) => ({
    channelList: state['@audit'].channelList,
    ALIPAY_switch: state['@common'].powers.ALIPAY_switch,
    staffs: state['@common'].staffs,
    roles: state['@common'].roleList,
    departmentTree: state['@common'].department.data,
    dynamicChannelMap: state['@audit'].dynamicChannelMap,
  }))(CustomPaymentTable as any),
)
