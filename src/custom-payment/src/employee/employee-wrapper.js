import moment from 'moment'
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import { showMessage } from '@ekuaibao/show-util'
import DataGrid from 'ekbc-datagrid'
import './employee.less'
import MessageCenter from '@ekuaibao/messagecenter'
import { ID } from '../key'
import { app as api } from '@ekuaibao/whispered'
import actions from '../custom-payment-action'
import { EnhanceLayerManager } from '@ekuaibao/enhance-layer-manager'
import { Fetch } from '@ekuaibao/fetch'

function tableColums() {
  return [
    {
      title: i18n.get('姓名'),
      dataIndex: 'name',
      sorter: true,
      key: 'name'
    },
    {
      title: i18n.get('开通日期'),
      dataIndex: 'walletOpenInfo.createTime',
      sorter: true,
      key: 'walletOpenInfo.createTime',
      render: text => {
        return text ? moment(text).format('YYYY-MM-DD HH:mm') : '-'
      }
    },
    {
      title: i18n.get('手机号'),
      dataIndex: 'walletOpenInfo.mobilePhone',
      filterMultiple: false,
      sorter: true,
      key: 'walletOpenInfo.mobilePhone',
      render: tel => {
        return tel ? tel.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : '-'
      }
    },
    {
      title: i18n.get('状态'),
      dataIndex: 'walletOpenInfo.id',
      sorter: true,
      render: id => {
        return id ? i18n.get('已开通') : i18n.get('未开通')
      }
    },
    {
      title: i18n.get('操作'),
      dataIndex: 'walletOpenInfo.id',
      key: 'action',
      render: (id, record) => {
        return <span>{id ? '-' : <a onClick={this.createWallet.bind(this, record)} data-testid="pay-custom-payment-open-wallet">{i18n.get('开通')}</a>}</span>  
      }
    }
  ]
}

import { connect } from '@ekuaibao/mobx-store'

@EnhanceLayerManager([
  {
    key: 'WalletOpenView',
    getComponent: () => import('./elements/WalletOpenView'),
    width: 550,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal'
  }
])
@connect(store => ({ size: store.states['@layout'].size }))
@EnhanceConnect(state => ({
  staffs: state['@common'].staffs,
  searchStaffText: state[ID].storage.searchStaffText
}))
export default class EmployeeWrapper extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      dataSource: [],
      total: 0,
      page: { currentPage: 1, pageSize: 10 },
      sorters: { 'walletOpenInfo.createTime': 'descend' }
    }
    this.searchStaffText = ''
    this.bus = new MessageCenter()
  }

  componentWillMount() {
    this.bus.on('buttons:click', this.handleButtonsClick)
  }

  componentWillUnmount() {
    this.bus.un('buttons:click', this.handleButtonsClick)
  }

  componentDidMount() {
    this.getpayEmployeeList()
  }

  componentWillReceiveProps(np) {
    if (np.searchStaffText !== this.props.searchStaffText) {
      this.searchStaffText = np.searchStaffText
      this.dataGrid.setState({ currentPage: 1 })
      this.getpayEmployeeList()
    }
  }

  // 获取员工钱包列表
  getpayEmployeeList() {
    let { page, sorters } = this.state
    let params = { page, sorters }
    this.fetchPending(params).then(data => {
      this.setState(data)
    })
  }

  // 处理列表数据请求
  fetchPending = params => {
    let { searchStaffText } = this
    let sorters = params.sorters && Object.keys(params.sorters).length > 0 ? params.sorters : this.state.sorters

    let queryParams = {
      ...params,
      sorters
    }

    if (searchStaffText && searchStaffText.length) {
      queryParams = {
        ...queryParams,
        searchText: searchStaffText,
        searchColumns: ['walletOpenInfo.mobilePhone', 'name']
      }
    } else {
      //params 会携带上一次请求的参数
      delete queryParams.searchText
      delete queryParams.searchColumns
    }

    return api
      .dispatch(actions.getEmployeeList(queryParams))
      .then(data => {
        const { count, items } = data
        this.setState({ dataSource: items, total: count })
        return { dataSource: items, total: count }
      })
      .catch(e => {
        showMessage.error(e)
        this.setState({ dataSource: [], total: 0 })
        return { dataSource: [], total: 0 }
      })
  }

  // 处理按钮点击事件
  handleButtonsClick = ({ name, _data, keys }) => {
    switch (name) {
      case 'batch_selected':
        break
      case 'batch_all':
        break
    }
  }

  // 创建员工钱包
  createWallet(item) {
    api.open('@custom-payment:WalletOpenView', { item }).then(data => {
      const walletId = this.props.accountInfo.id
      api.dispatch(actions.postStaffOpen({ ...data, id: walletId })).then(() => {
        this.getpayEmployeeList()
      })
    })
  }

  buttons = [
    { text: i18n.get('批量开通'), name: 'batch_all', isBindMultiSelect: false, isBindTotal: true },
    { text: i18n.get('开通选中'), name: 'batch_selected' }
  ]

  // 渲染员工钱包管理表格
  render() {
    const { size } = this.props
    const { dataSource, total } = this.state
    const lang = Fetch.staffSetting ? Fetch.staffSetting.language : Fetch.defaultLanguage
    return (
      <div className="table-def">
        <DataGrid
          ref={node => {
            this.dataGrid = node
          }}
          bus={this.bus}
          scroll={{ y: size.y - 220 }}
          dataSource={dataSource}
          total={total}
          columns={tableColums.apply(this)}
          rowKey={record => record['id']}
          useFixedHeader={true}
          rowSelection={null}
          disabledSwitcher={true}
          disabledHeader={true}
          rowClassName={() => 'table-line-tr'}
          fetch={this.fetchPending}
          lang={lang}
        />
      </div>
    )
  }
}
