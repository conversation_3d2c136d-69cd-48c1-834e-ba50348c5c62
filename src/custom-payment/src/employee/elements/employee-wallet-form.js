import { app } from '@ekuaibao/whispered'
import React from 'react'
import { Icon } from 'antd'
import { EnhanceStackerManager } from '@ekuaibao/enhance-stacker-manager'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
const Breadcrumb = app.require('@elements/ekbc-basic/breadcrumb')

@EnhanceModal({
  title: '',
  footer: [],
  className: 'ekb-custom-modal'
})
@EnhanceStackerManager([
  {
    key: 'WalletOpenView',
    getComponent: () => import('./WalletOpenView'),
    title: i18n.get('开通钱包')
  },
  {
    key: 'WalletOpenAgreementView',
    getComponent: () => import('./WalletOpenAgreementView'),
    title: i18n.get('合思钱包服务协议')
  }
])
export default class EmployeeWalletForm extends React.PureComponent {
  constructor(props) {
    super(props)
  }

  componentDidMount() {
    this.loadStackerView(this.props)
  }

  // 加载栈视图
  loadStackerView(props) {
    let {
      data: { viewKey },
      stackerManager,
      ...others
    } = props
    switch (viewKey) {
      case 'WalletOpenView':
        stackerManager.push(viewKey, {})
        break
      case 'WalletOpenAgreementView':
        stackerManager.push(viewKey, {})
        break
      default:
        stackerManager.push(viewKey, {
          ...others
        })
        break
    }
  }

  // 处理菜单点击事件
  handleMenuClick(line, i) {
    let { stackerManager } = this.props
    switch (line.key) {
      case 'WalletOpenView':
        stackerManager.open(i, {})
        break
      case 'WalletOpenAgreementView':
        stackerManager.open(i, {})
        break
      default:
        stackerManager.open(i, { ...line })
        break
    }
  }

  // 处理取消操作
  handleCancel = () => {
    this.props.layer.emitCancel()
    this.props.stackerManager.open(0, {})
  }

  // 渲染面包屑导航
  renderBreadcrumb() {
    const array = this.props.stackerManager.values()
    let items = []
    array.forEach((line, i) => {
      items.push({
        key: i,
        onClick: () => this.handleMenuClick(line, i),
        title: line.title
      })
    })
    return <Breadcrumb items={items} data-testid="pay-custom-payment-breadcrumb" />
  }

  // 渲染员工钱包表单组件
  render() {
    return (
      <div className="create-account-modal">
        <div className="modal-header">
          {this.renderBreadcrumb()}
          <Icon className="cross-icon" type="cross" onClick={this.handleCancel} data-testid="pay-custom-payment-form-close" />
        </div>
        <div>{this.props.children}</div>
      </div>
    )
  }
}
