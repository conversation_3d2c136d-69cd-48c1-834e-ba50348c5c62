import { app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { Form, Input, Button, Checkbox, Icon } from 'antd'
import styles from './WalletOpenView.module.less'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'

const EnhanceFormCreate = api.require('@elements/enhance/enhance-form-create')

const FormItem = Form.Item

let layout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 }
}
@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer',
  maskClosable: false
})
@EnhanceFormCreate()
export default class WalletOpenView extends PureComponent {
  constructor(props) {
    super(props)

    this.state = {
      doc: {},
      data: {},
      agree: false
    }
  }

  componentDidMount() {
    let data = this.props.item
    this.setState({
      data
    })
  }

  // 处理确认开通操作
  handleOK = () => {
    if (!this.state.agree) return
    const { validateFieldsAndScroll, resetFields } = this.props.form
    validateFieldsAndScroll((errors, values) => {
      if (!!errors) return
      values.staffId = this.state.data.id
      this.setState(
        {
          doc: values
        },
        () => {
          this.props.onOk(this.state.doc)
          resetFields()
        }
      )
    })
  }

  // 处理协议同意状态变化
  onChange = e => {
    this.setState({ agree: e.target.checked })
  }

  // 处理取消操作
  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  // 打开协议查看弹窗
  handleAgreement() {
    api.open('@custom-payment:WalletOpenAgreementView', {})
  }

  // 验证身份证号格式
  checkIdCard = (_rule, value, callback) => {
    let regString = '^(\\d{18}|\\d{15}|\\d{17}[x|X])$'
    let reg = new RegExp(regString)
    if (!reg.test(value)) return callback(i18n.get('身份证号长度必须为15位或18位'))
    callback()
  }

  // 验证银行卡号格式
  checkBankNo(_rule, value, callback) {
    if (value) {
      if (!/^[0-9a-zA-Z-]*$/.test(value)) return callback(i18n.get('银行账号格式不正确'))
      if (!value.length) return callback(i18n.get('银行账号不能为空'))
      if (value.length > 32) return callback(i18n.get('银行账号不能超过32位'))
    }
    callback()
  }

  // 渲染钱包开通表单
  render() {
    const { getFieldDecorator } = this.props.form
    let doc = this.state.data
    return (
      <div id={'custom-payment_walletOpenView'} className={styles['WalletOpenCon']}>
        <Form>
          <div className="modal-header mb-15">
            <div className="title flex">{i18n.get('开通钱包')}</div>
            <Icon className="cross-icon" type="cross" onClick={this.handleCancel} data-testid="pay-custom-payment-wallet-open-close" />
          </div>
          <FormItem label={i18n.get('姓名')} {...layout}>
            {getFieldDecorator('name', {
              initialValue: doc && doc.name,
              rules: [
                { required: true, whitespace: true, message: i18n.get('请输入姓名') },
                { max: 50, message: i18n.get('姓名不能超过50个字符') }
              ]
            })(<Input placeholder={i18n.get('请输入姓名')} data-testid="pay-custom-payment-name-input" />)}
          </FormItem>

          <FormItem label={i18n.get('手机号')} {...layout}>
            {getFieldDecorator('phone', {
              initialValue: doc && doc.cellphone,
              rules: [
                { required: true, whitespace: true, message: i18n.get('请输入手机号') },
                { max: 11, message: i18n.get('手机号长度不能超过11位') }
              ]
            })(<Input placeholder={i18n.get('请输入手机号')} data-testid="pay-custom-payment-phone-input" />)}
          </FormItem>
          <FormItem label={i18n.get('身份证')} {...layout}>
            {getFieldDecorator('idCard', {
              initialValue: doc && doc.idCard,
              rules: [
                { required: true, whitespace: true, message: i18n.get('请输入身份证号') },
                { validator: this.checkIdCard }
              ]
            })(<Input placeholder={i18n.get('请输入身份证号')} data-testid="pay-custom-payment-idcard-input" />)}
          </FormItem>
          <FormItem label={i18n.get('银行卡号')} {...layout}>
            {getFieldDecorator('cardNo', {
              initialValue: doc && doc.cardNo,
              rules: [
                { required: true, whitespace: true, message: i18n.get('请输入银行卡号') },
                { validator: this.checkBankNo }
              ]
            })(<Input placeholder={i18n.get('请输入银行卡号')} data-testid="pay-custom-payment-cardno-input" />)}
          </FormItem>
          <div className="ant-row ant-form-item">
            <div className="ant-col-6 ant-form-item-label" />
            <div className="ant-col-14 ant-form-item-control-wrapper">
              <Checkbox onChange={this.onChange.bind(this)} checked={this.state.agree} data-testid="pay-custom-payment-agreement-checkbox">
                {i18n.get('已阅读并接受')}
              </Checkbox>
              <b
                style={{ fontWeight: 'normal', color: 'var(--brand-base)', cursor: 'pointer' }}
                onClick={this.handleAgreement.bind(this)}
                data-testid="pay-custom-payment-agreement-link"
              >
                {i18n.get('《')}
                {i18n.get('合思钱包服务协议')}
                {i18n.get('》')}
              </b>
            </div>
          </div>
          <div className="modal-footer">
            <Button key="cancel" size="large" className="mr-20" onClick={this.handleCancel} data-testid="pay-custom-payment-wallet-open-cancel">
              {i18n.get('取消')}
            </Button>
            <Button
              key="ok"
              type="primary"
              size="large"
              className={this.state.agree ? 'mr-10' : 'mr-10 btn-disabled'}
              onClick={this.handleOK}
              data-testid="pay-custom-payment-wallet-open-confirm"
            >
              {i18n.get('开通')}
            </Button>
          </div>
        </Form>
      </div>
    )
  }
}
