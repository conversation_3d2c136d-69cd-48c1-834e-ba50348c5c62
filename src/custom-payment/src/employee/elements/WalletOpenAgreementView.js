// 钱包开通协议查看组件
import React, { PureComponent } from 'react'
import { Button, Icon } from 'antd'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import InternetProtocol from './Protocol'
import styles from './WalletOpenAgreementView.module.less'

@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer',
  maskClosable: false
})
export default class WalletOpenAgreementView extends PureComponent {
  constructor(props) {
    super(props)
  }

  // 处理取消操作
  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  // 渲染钱包开通协议弹窗
  render() {
    return (
      <div id={'custom-payment_walletOpenAgreementView'} className={styles['WalletAgreementCon']}>
        <div className="modal-header">
          <div className="title flex">{i18n.get('合思钱包服务协议')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleCancel} data-testid="pay-custom-payment-agreement-close" />
        </div>
        <div className="content">
          <InternetProtocol />
        </div>
        <div className="btnCon mt-15">
          <Button type="primary" onClick={this.handleCancel} data-testid="pay-custom-payment-agreement-confirm">
            {i18n.get('确定')}
          </Button>
        </div>
      </div>
    )
  }
}
