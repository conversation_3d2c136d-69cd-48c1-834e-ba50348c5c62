@import '~@ekuaibao/web-theme-variables/styles/default';
@import '~@ekuaibao/web-theme-variables/styles/colors';

.table-def {
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  border-radius: 4px;
  background-color: @input-bg;
  position: relative;
  overflow: hidden;
}

.ekb-paying-pending-table {
  .ant-table-expanded-row > td:last-child {
    padding: 0 48px 0 8px;
  }
  .ant-table-expanded-row > td:last-child .ant-table-thead th {
    border-bottom: 1px solid #e9e9e9;
  }
  .ant-table-expanded-row > td:last-child .ant-table-thead th:first-child {
    padding-left: 0;
  }
  .ant-table-expanded-row > td:last-child .ant-table-row td:first-child {
    padding-left: 0;
  }
  .ant-table-expanded-row .ant-table-tbody .ant-table-row {
    background: #fbfbfb !important;
  }
  .ant-table-expanded-row .ant-table-row:last-child td {
    border: none;
  }
  .ant-table-expanded-row .ant-table-thead > tr > th {
    background: #fbfbfb !important;
  }
  .table-operation a:not(:last-child) {
    margin-right: 24px;
  }
  .highlight {
    color: var(--brand-base);
  }
}
