import { GlobalFieldIF } from '@ekuaibao/ekuaibao_types'

export const conditionTypeGroup = [
  { label: i18n.get('需要'), value: 'payScene' },
  { label: i18n.get('不需要'), value: 'default' },
]

export const specialCaseGroup = [
  { label: i18n.get('没有'), value: 'FALSE' },
  { label: i18n.get('有'), value: 'TRUE' },
]

export const specialStandardGroup = [
  { label: i18n.get('按统一标准，额外还可以选择更多申请单'), value: 'TRUE' },
  { label: i18n.get('不按统一标准，仅选择固定的模版'), value: 'FALSE' },
]

export const FeeIncFieldSpecialGroup = [
  { label: i18n.get('在统一标准之外还需要补充其他字段'), value: 'TRUE' },
  { label: i18n.get('不按统一标准，仅按照下方规则'), value: 'FALSE' },
]

export const controlConfig = {
  select_requisition: {
    type: 'select_requisition',
    specificationIds: [],
    isSpecialCase: 'FALSE',
    isStandard: 'TRUE',
    specialCase: [
      {
        type: 'select_requisition',
        staffs: [],
        specificationIds: [],
      },
    ],
  },
  fee_inc_field: {
    type: 'fee_inc_field',
    specialCase: [
      {
        type: 'fee_inc_field',
        staffs: [],
        feeIncField: [
          {
            feeTypeIds: [],
            fields: [],
          },
        ],
      },
    ],
    isSpecialCase: 'FALSE',
    isStandard: 'TRUE',
    feeIncField: [
      {
        feeTypeIds: [],
        fields: [],
      },
    ],
  },
  card_limit: {
    type: 'card_limit',
    isSpecialCase: 'FALSE',
    specialCase: [
      {
        type: 'card_limit',
        staffs: [],
        singleTimeLimit: null,
        singleDayLimit: null,
      },
    ],
    singleTimeLimit: null,
    singleDayLimit: null,
  },
  fee_standard: {
    type: 'fee_standard',
    specificationIds: '',
    checked: false,
  },
  requisition_limit: {
    type: 'requisition_limit',
    checked: true,
  },
  scene_ocr: {
    type: 'scene_ocr',
    staffs: [],
  },
}

export type GlobalGroupType = {
  label: 'text' | 'number' | 'money' | 'date' | 'dateRange'
  options: GlobalFieldIF[]
}

type ConditionType = 'payScene' | 'default'
export type IsSpecialCaseType = 'TRUE' | 'FALSE'

export type IsStandardType = 'TRUE' | 'FALSE' | null
export type ConditionsType = {
  type: ConditionType
  staffs: string[]
  departments: string[]
  feeTypeIds: string[]
}

export type ConditionConfigType = {
  conditionType: ConditionType
  conditions: ConditionsType[]
}

export type CommonConfigType = {
  isSpecialCase: IsSpecialCaseType
  isStandard?: IsStandardType
  specialCase: SpecialCaseType[]
}

export type ControlType =
  | 'select_requisition' // 需要选择申请事项
  | 'fee_inc_field' // 费用类型需要补充哪些字段
  | 'card_limit' // 校验易商卡额度限制
  | 'fee_standard' // 校验费用标准
  | 'requisition_limit' // 检验申请事项额度

export type FeeIncField = {
  feeTypeIds: string[]
  fields: GlobalFieldIF[]
}

export type SpecialCaseType = {
  type: ControlType
  staffs?: string[]
  specificationIds?: string[]
  feeIncField?: FeeIncField[]
  singleTimeLimit?: number | null
  singleDayLimit?: number | null
}

export type ControlConfigType = {
  type: ControlType
  isSpecialCase?: IsSpecialCaseType
  specialCase?: SpecialCaseType[]
  feeIncField?: FeeIncField[]
  isStandard?: IsStandardType
  specificationIds?: string[]
  singleTimeLimit?: number
  singleDayLimit?: number
  singleDayUnit?: string
}

export type ConfigValue = {
  id?: string
  name: string
  conditionConfig: ConditionConfigType
  controlIds: DefaultControlIds[]
  [key: string]: any
}

export type whiteListType = {
  whiteList: { staffs: string[]; departments: string[]; singleTimeLimit: number }[]
}

export type Rules = {
  checked: boolean
  edit: boolean
  id: string
  name: string
  type: ControlType
  control: ControlConfigType
}

export type DefaultControlIds = {
  order: number
  title: string
  rules: Rules[]
}

export const drawerControlList = [
  'select_requisition',
  'fee_inc_field',
  'card_limit',
  'fee_standard',
  'scene_ocr',
]
