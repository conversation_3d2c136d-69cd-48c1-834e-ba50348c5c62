.controlConfigWidget {
  display: flex;

  .controlWrapper {
    width: 33.33%;
    height: 160px;
    border-radius: 12px;
    background-color: var(--eui-bg-body-overlay);
    margin-right: 16px;
    min-width: 320px;
    padding: 16px;
    overflow: auto;
    :global {
      .control-wrapper-title {
        &::after {
          display: inline-block;
          margin-left: 2px;
          color: var(--eui-function-danger-500);
          font: var(--eui-font-body-b1);
          content: "*";
        }
      }
    }

    &:last-of-type {
      margin-right: 0;
    }

    .controlItem {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 12px;

      .checkbox {
        flex: 1;
      }
    }
  }
}
