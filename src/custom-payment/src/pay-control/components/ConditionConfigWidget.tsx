import { Button, Space, TreeSelect } from '@hose/eui'
import React, { FC } from 'react'
import { ConditionConfigType } from '../types'
// @ts-ignore
import styles from './ConditionConfigWidget.module.less'
import SelectStaffDepartmentWidget from './SelectStaffDepartmentWidget'
import { OutlinedEditDeleteTrash, OutlinedTipsAdd } from '@hose/eui-icons'
import { showMessage } from '@ekuaibao/show-util'
interface IProps {
  value?: ConditionConfigType
  feeTypeList: any[]
  onChange?: (value: ConditionConfigType | any) => void
}

const ConditionConfigWidget: FC<IProps> = ({ value, feeTypeList, onChange }) => {
  const conditionType = value?.conditionType
  const conditions = value?.conditions || []

  const handleStaffSingleChange = (val: { departments?: string[]; staffs: string[] }) => {
    const newValue = { ...value }
    newValue.conditions = [
      { type: 'default', staffs: val.staffs, departments: val.departments || [], feeTypeIds: [] },
    ]
    onChange?.(newValue)
  }

  const handleStaffListChange = (
    val: { departments?: string[]; staffs: string[] },
    index: number,
  ) => {
    const newValue = { ...value }
    const currentCondition = newValue?.conditions?.[index] || {}
    // @ts-ignore
    newValue?.conditions?.[index] = {
      ...currentCondition,
      staffs: val.staffs,
      departments: val.departments,
    }
    onChange?.(newValue)
  }

  const handleFeeTypeChange = (feeTypeIds: string[], index: number) => {
    const newValue = { ...value }
    const currentCondition = newValue?.conditions?.[index] || {}
    // @ts-ignore
    newValue?.conditions?.[index] = {
      ...currentCondition,
      feeTypeIds,
    }
    onChange?.(newValue)
  }

  const handDeleteConditionItem = (index: number) => {
    if (value?.conditions.length === 1) {
      showMessage.warning('至少保留一个')
      return
    }
    const newValue = { ...value }
    newValue.conditions?.splice(index, 1)
    onChange?.(newValue)
  }

  const handleAddConditionConfig = () => {
    const newValue = { ...value }
    newValue.conditions?.push({ type: 'payScene', staffs: [], departments: [], feeTypeIds: [] })
    onChange?.(newValue)
  }
  return (
    <div className={styles.conditionConfigWidget}>
      {conditionType === 'default' ? (
        <SelectStaffDepartmentWidget
          wrapperStyle={{ with: '100%', marginTop: 10 }}
          value={conditions[0]}
          onChange={handleStaffSingleChange}
          needDepartment
        />
      ) : (
        <div className={styles.conditionConfigListWrapper}>
          {conditions.map((item, index) => {
            return (
              <Space key={index}>
                <SelectStaffDepartmentWidget
                  wrapperStyle={{ with: '100%' }}
                  onChange={(v) => handleStaffListChange(v, index)}
                  value={{
                    departments: item.departments || [],
                    staffs: item.staffs || [],
                    departmentsIncludeChildren: false,
                  }}
                  placeholder={i18n.get('请选择人员或部门')}
                  needDepartment
                />
                <span>{i18n.get('进行')}</span>
                <TreeSelect
                  style={{ width: 230 }}
                  value={item.feeTypeIds || []}
                  treeNodeFilterProp={'title'}
                  dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                  placeholder={i18n.get('请选择费用')}
                  allowClear
                  showSearch
                  multiple
                  onChange={(v) => handleFeeTypeChange(v, index)}
                  treeData={feeTypeList}
                />
                <span>{i18n.get('消费时')}</span>
                <OutlinedEditDeleteTrash onClick={() => handDeleteConditionItem(index)} />
              </Space>
            )
          })}
          <Space>
            <Button
              icon={<OutlinedTipsAdd />}
              theme="highlight"
              size="small"
              category="text"
              onClick={handleAddConditionConfig}>
              {i18n.get('添加范围')}
            </Button>
          </Space>
        </div>
      )}
    </div>
  )
}

export default ConditionConfigWidget
