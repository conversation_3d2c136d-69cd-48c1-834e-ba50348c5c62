import React, { FC } from 'react'
import { ConfigProvider } from '@hose/eui'
import { Fetch } from '@ekuaibao/fetch'
import enUS from '@hose/eui/es/locale/en_US'
import zhCN from '@hose/eui/es/locale/zh_CN'
const euiLocalValue = Fetch.lang === 'en-US' ? enUS : zhCN
interface IProps {}
const ConfigProviderWrapper: FC<IProps> = (props) => {
  return <ConfigProvider locale={euiLocalValue}>{props.children}</ConfigProvider>
}

export default ConfigProviderWrapper
