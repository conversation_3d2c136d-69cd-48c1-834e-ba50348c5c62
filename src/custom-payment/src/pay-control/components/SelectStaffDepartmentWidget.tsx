import React, { FC, useEffect, useState } from 'react'
import { app } from '@ekuaibao/whispered'
const TagShow = app.require<any>('@components/tag-show')
const { getDeptItemsByIds, getRoleItemByIds, getStaffItemByIds } =
  app.require<any>('@lib/staff-tag-util')
// @ts-ignore
import styles from './SelectStaffDepartmentWidget.module.less'

type Value = {
  departments?: string[]
  roles?: string[]
  staffs: string[]
  departmentsIncludeChildren?: boolean
}

interface IProps {
  bus?: any
  value: Value
  placeholder?: string
  disabled?: boolean
  showIncludeChildren?: boolean
  onChange?: (value: Value, objValue: Value) => void
  wrapperStyle?: any
  needDepartment?: boolean
  needRole?: boolean
}
const SelectStaffDepartmentWidget: FC<IProps> = (props) => {
  const { value, placeholder, onChange, bus, disabled, showIncludeChildren, wrapperStyle } = props
  const [tags, setTags] = useState([])
  useEffect(() => {
    parseValue()
  }, [value])
  const parseValue = async () => {
    const [t, isChange, newValue, newObjValue] = await valueParse(value, showIncludeChildren)
    setTags(t)
    if (isChange) {
      onChange?.(newValue, newObjValue)
    }
  }
  const handleSelectStaff = async () => {
    const { needDepartment, needRole } = props
    const { departments = [], roles = [], staffs = [] } = value || {}
    const data = [
      {
        type: 'department-member',
        checkIds: staffs,
      },
    ]
    if (needDepartment) {
      data.push({
        type: 'department',
        checkIds: departments,
      })
    }
    if (needRole) {
      data.push({
        type: 'role',
        checkIds: roles,
      })
    }
    const checkedList: any[] = await app.open('@organizationManagement:SelectStaff', {
      multiple: true,
      data,
    })
    const staffData = checkedList.find((o) => o.type === 'department-member') || {}
    const departmentData = checkedList.find((o) => o.type === 'department') || {}
    const roleData = checkedList.find((o) => o.type === 'role') || {}
    const staffIds = staffData.checkIds || []
    const departmentIds = departmentData.checkIds || []
    const roleIds = roleData.checkIds || []
    const val = { ...value, departments: departmentIds, staffs: staffIds, roles: roleIds }
    const objValue = {
      ...value,
      departments: departmentData.checkList || [],
      staffs: staffData.checkList || [],
      roles: roleData.checkList || [],
    }
    onChange?.(val, objValue)
  }
  const handleChange = (values: string[]) => {
    const va: Record<string, any> = { departments: [], staffs: [], roles: [] }
    const objVa: Record<string, any> = { departments: [], staffs: [], roles: [] }
    const keyMap: Record<string, any> = {
      department: 'departments',
      staff: 'staffs',
      role: 'roles',
    }
    tags
      .filter((item: any) => item.id !== 'departmentsIncludeChildren' && values.includes(item.id))
      .forEach((item: any) => {
        const key = keyMap[item.type]
        va[key].push(item.id)
        objVa[key].push(item)
      })
    onChange?.({ ...value, ...va }, { ...value, ...objVa })
    if (bus?.has('fields:blur')) {
      bus.emit('fields:blur')
    }
  }
  return (
    <div className={styles.tagShowWrapper} style={wrapperStyle}>
      <TagShow
        placeholder={placeholder}
        tags={tags}
        disabled={disabled}
        onClick={handleSelectStaff}
        onChange={handleChange}
      />
    </div>
  )
}
const valueParse = async (value: Value, showIncludeChildren = false) => {
  if (!value) {
    return [[], false]
  }
  const departmentIds = value.departments || []
  const staffIds = value.staffs || []
  const roleIds = value.roles || []
  const [departments, staffs, roles] = await Promise.all([
    getDeptItemsByIds(departmentIds)(),
    getStaffItemByIds(staffIds)(),
    getRoleItemByIds(roleIds)(),
  ])
  // @ts-ignore
  const data = [...departments, ...staffs, ...roles]
  const isChange =
    departmentIds.length !== departments.length ||
    staffIds.length !== staffs.length ||
    roleIds.length !== roles.length
  const result: any = []
  if (showIncludeChildren && value.departmentsIncludeChildren) {
    result.push({
      id: 'departmentsIncludeChildren',
      label: i18n.get('含子部门'),
      showAvatar: false,
      tagOptions: { color: 'pri', fill: 'outline', closable: false },
    })
  }
  return [
    result.concat(data),
    isChange,
    {
      ...value,
      // @ts-ignore
      departments: departments.map((item) => item.id),
      // @ts-ignore
      staffs: staffs.map((item) => item.id),
      // @ts-ignore
      roles: roles.map((item) => item.id),
    },
    { ...value, departments, staffs, roles },
  ]
}
export default SelectStaffDepartmentWidget
