import React, { FC, useEffect, useState } from 'react'
// @ts-ignore
import styles from './ControlConfigWidget.module.less'
import {
  ConditionConfigType,
  ControlConfigType,
  DefaultControlIds,
  GlobalGroupType,
  drawerControlList,
} from '../types'
import { cloneDeep } from 'lodash'
import { Checkbox } from '@hose/eui'
import { OutlinedEditEdit } from '@hose/eui-icons'
import { CheckboxChangeEvent } from '@hose/eui/es/components/checkbox'
import { showMessage } from '@ekuaibao/show-util'
import { app } from '@ekuaibao/whispered'
import { toJS } from 'mobx'
import { FormInstance } from '@hose/eui/es/components/form'
import { uniq } from 'lodash'

interface IProps {
  form: FormInstance
  globalFields: GlobalGroupType[]
  specificationGroups: any[]
  allSpecificationGroups: any[]
  conditionConfig: ConditionConfigType
  value?: DefaultControlIds[]
  feeTypeList: any[]
  onChange?: (value: ControlConfigType[] | any) => void
}

const ControlConfigWidget: FC<IProps> = (props) => {
  const {
    globalFields,
    specificationGroups,
    allSpecificationGroups,
    conditionConfig,
    feeTypeList,
    form,
    value,
    onChange,
  } = props
  const [authStaffStaffMap, setAuthStaffStaffMap] = useState<any>()
  const [authStaffDepartmentStaffMap, setAuthStaffDepartmentStaffMap] = useState<any>()

  useEffect(() => {
    setAuthStaffStaffMap(app.getState()['@common'].authStaffStaffMap)
    setAuthStaffDepartmentStaffMap(app.getState()['@common'].authStaffDepartmentStaffMap)
  })

  const getStaffList = () => {
    let staffs: string[] = []
    toJS(conditionConfig)?.conditions?.forEach((it) => {
      staffs = staffs.concat(it?.staffs)
      it?.departments.forEach((dep) => {
        const depStaff = authStaffDepartmentStaffMap[dep]?.staffList?.map((it: any) => it?.id)
        staffs = staffs.concat(depStaff)
      })
    })

    const staffList: string[] = []
    Array.from(new Set(staffs)).forEach((it) => {
      const item = authStaffStaffMap?.[it]
      if (item) {
        const { id, name } = item
        staffList.push({
          ...item,
          label: name,
          value: id,
        })
      }
    })

    return { staffs: Array.from(new Set(staffs)), staffList }
  }

  const handleCheckChange = (e: CheckboxChangeEvent, id: string) => {
    const newValue = cloneDeep(value)
    newValue?.forEach((item) => {
      item.rules.forEach((it) => {
        if (it.id === id) {
          it.checked = e.target.checked
        }
      })
    })
    onChange?.(newValue)
  }

  const handleDrawerChange = (data: any) => {
    const { type, order, control, result } = data
    const newValue = cloneDeep(value)
    newValue?.forEach((it) => {
      if (it.order === order) {
        it.rules.forEach((oo) => {
          if (oo.type === type) {
            oo.control = {
              ...control,
              ...result,
            }
          }
        })
      }
    })
    onChange?.(newValue)
  }
  const getFeetypeList = () => {
    const conditionConfig = form.getFieldValue('conditionConfig')
    const ids = conditionConfig.conditions.reduce((acc: string[], cur: any) => {
      return acc.concat(cur.feeTypeIds)
    }, [])
    return feeTypeList.filter((it) => uniq(ids).includes(it.id))
  }
  const handleOpenDetailSettingDrawer = (type: string, order: number) => {
    const newValue = cloneDeep(value)
    const { checked, control } =
      newValue?.find((item) => item.order === order)?.rules?.find((item) => item.type === type) ||
      {}
    const { staffs, staffList } = getStaffList()
    if (!checked) {
      showMessage.warning('请打钩')
      return
    }

    if (!staffs.length) {
      showMessage.warning('请选择人员或部门')
      return
    }

    switch (type) {
      case 'select_requisition':
        app
          .open('@custom-payment:SelectRequisitionConfigDrawer', {
            control,
            specificationGroups,
            staffs,
            staffList,
          })
          .then((result: any) => {
            handleDrawerChange({ type, order, control, result })
          })
        break
      case 'fee_inc_field':
        app
          .open('@custom-payment:FeeIncFieldConfigDrawer', {
            control,
            globalFields,
            feeTypeList: getFeetypeList(),
            staffs,
            staffList,
          })
          .then((result: any) => {
            handleDrawerChange({ type, order, control, result })
          })
        break
      case 'card_limit':
        app
          .open('@custom-payment:CardLimitConfigDrawer', { control, staffs, staffList })
          .then((result: any) => {
            handleDrawerChange({ type, order, control, result })
          })
        break
      case 'fee_standard':
        app
          .open('@custom-payment:FeeStandardConfigDrawer', { control, allSpecificationGroups })
          .then((result: any) => {
            handleDrawerChange({ type, order, control, result })
          })
        break
      case 'scene_ocr':
        app
          .open('@custom-payment:SceneOCRConfigDrawer', {
            control,
            staffs,
            staffList,
          })
          .then((result: any) => {
            handleDrawerChange({ type, order, control, result })
          })
        break
      default:
        break
    }
  }

  return (
    <div className={styles.controlConfigWidget}>
      {value?.map((item) => {
        return (
          <div key={item.order} className={styles.controlWrapper}>
            <span className={item.order === 2 ? 'control-wrapper-title' : ''}>
              {item.order}.{i18n.get(item.title)}
            </span>
            {item?.rules?.map((it) => {
              const disabled = it?.type === 'select_requisition' || it?.type === 'requisition_limit'
              return (
                <div key={it.id} className={styles.controlItem}>
                  <Checkbox
                    disabled={disabled}
                    checked={it.checked}
                    onChange={(e) => handleCheckChange(e, it.id)}
                    className={styles.checkbox}>
                    {i18n.get(it.name)}
                  </Checkbox>
                  {drawerControlList.includes(it.type) && it.checked && (
                    <OutlinedEditEdit
                      onClick={() => handleOpenDetailSettingDrawer(it.type, item.order)}
                      fontSize={16}
                      color={`var(--eui-primary-pri-500)`}
                    />
                  )}
                </div>
              )
            })}
          </div>
        )
      })}
    </div>
  )
}

export default ControlConfigWidget
