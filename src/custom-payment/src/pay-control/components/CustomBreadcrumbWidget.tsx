import React, { <PERSON> } from 'react'
import { Breadcrumb } from '@hose/eui'
const BreadcrumbItem = Breadcrumb.Item
export interface PathItem {
  name: string
  onClick?: () => void
}
interface IProps {
  path: PathItem[]
}
const CustomBreadcrumbWidget: FC<IProps> = ({ path }) => {
  return (
    <Breadcrumb>
      {path.map((item, index) => {
        const { name, onClick } = item
        return (
          <BreadcrumbItem key={index}>
            {onClick ? <a onClick={onClick}>{i18n.get(name)}</a> : i18n.get(name)}
          </BreadcrumbItem>
        )
      })}
    </Breadcrumb>
  )
}

export default CustomBreadcrumbWidget
