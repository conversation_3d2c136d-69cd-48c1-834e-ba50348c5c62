import React, { FC, useEffect } from 'react'
import { useInstance, provider } from '@ekuaibao/react-ioc'
import { useObserver } from 'mobx-react-lite'
// @ts-ignore
import styles from './ECardPayControlViewSetting.module.less'
import CustomBreadcrumbWidget, { PathItem } from './components/CustomBreadcrumbWidget'
import { IKeel } from '@ekuaibao/keel'
import { Button, Form, Input, Space } from '@hose/eui'
import { OutlinedDirectionArrowLeft } from '@hose/eui-icons'
import ConditionConfigWidget from './components/ConditionConfigWidget'
import { ECardPayControlViewSettingVm } from '../vms/ECardPayControlViewSetting.vm'
import { ConditionConfigType, ConfigValue, Rules } from './types'
import ControlConfigWidget from './components/ControlConfigWidget'
import { saveControlConfig } from '../e-card-pay-control-action'
import { showMessage } from '@ekuaibao/show-util'

const FormItem = Form.Item

interface IProps {
  record: any
  keel: IKeel
}

const ECardPayControlViewSetting: FC<IProps> = (props) => {
  const { record, keel } = props
  const [form] = Form.useForm()
  const vm = useInstance<ECardPayControlViewSettingVm>(ECardPayControlViewSettingVm?.NAME)
  const handleBack = () => {
    keel.closeTo(0)
  }
  const path: PathItem[] = [
    { name: '易商卡' },
    { name: '消费管控', onClick: handleBack },
    { name: record ? '编辑规则' : '新增规则' },
  ]

  const handleSave = () => {
    form.validateFields().then((res: ConfigValue) => {
      let controlIds = [] as Rules[]
      res.controlIds.forEach((it) => {
        controlIds = controlIds.concat(it.rules)
      })
      const params = {
        ...vm.configValue,
        ...res,
        ...res.conditionConfig,
        controlIds,
      }
      // @ts-ignore
      delete params.conditionConfig
      saveControlConfig(params)
        .then((_) => {
          keel.closeTo(0)
        })
        .catch((e) => {
          showMessage.error(e.errorMessage || e.message)
        })
    })
  }

  const onValuesChange = (_changesValue: any, values: ConfigValue) => {
    vm.configValue = {
      ...vm.configValue,
      ...values,
    }
  }

  const validatorConditionConfig = (_rule: any, value: ConditionConfigType, callback: any) => {
    const { conditionType, conditions } = value
    const hasEmpty = conditions.find((it) => it.departments.length === 0 && it.staffs.length === 0)
    const hasEmptyFeeTypeIds = conditions.find((it) => it.feeTypeIds.length === 0)
    if (conditionType === 'default' && hasEmpty) {
      return callback('请补充人员或部门')
    } else if (hasEmpty || hasEmptyFeeTypeIds) {
      return callback('请补充人员或部门或费用')
    }
    return callback()
  }

  useEffect(() => {
    vm.fetchFeeTypeByECardConfig()
    vm.fetchConfigValue(record)
    vm.fetchGlobalFields()
    vm.fetchAllSpecificationGroups()
  }, [record])

  return useObserver(() => {
    form.setFieldsValue(vm.configValue)
    return (
      <div className={styles.eCardPayControlViewSettingWrapper}>
        <CustomBreadcrumbWidget path={path} />
        <div className={styles.container}>
          <div className={styles.header}>
            <div className={styles.left}>
              <OutlinedDirectionArrowLeft onClick={handleBack} data-testid="pay-eCard-control-back-icon" />
              <span className={styles.span}>{record ? '编辑规则' : '新增规则'}</span>
            </div>
            <Space>
              <Button category="secondary" onClick={handleBack} data-testid="pay-eCard-control-cancel-button">
                {i18n.get('取消')}
              </Button>
              <Button onClick={handleSave} data-testid="pay-eCard-control-save-button">{i18n.get('保存')}</Button>
            </Space>
          </div>
          <div className={styles.content}>
            <Form
              name="vertical"
              layout="vertical"
              style={{ width: '100%' }}
              form={form}
              initialValues={vm.configValue}
              onValuesChange={onValuesChange}>
              <FormItem
                name="name"
                label={i18n.get('规则名称')}
                className={styles.shortWrapper}
                rules={[
                  { required: true, message: '规则名称不能为空' },
                  { max: 50, message: '最大长度是50' },
                  { pattern: /^[^\s]*$/, message: '禁止输入空格' },
                ]}>
                <Input placeholder={i18n.get('请输入规则名称')} data-testid="pay-eCard-control-rule-name-input" />
              </FormItem>
              <FormItem
                name="conditionConfig"
                rules={[
                  { required: true, message: '请配置消费场景' },
                  { validator: validatorConditionConfig },
                ]}
                className={styles.shortWrapper}
                label={
                  <>
                    适用范围
                    <span>{'（需要检验消费场景）'}</span>
                  </>
                }>
                <ConditionConfigWidget feeTypeList={vm.data} />
              </FormItem>
              <FormItem
                name="controlIds"
                label={i18n.get('管控规则')}
                rules={[{ required: true, message: '请选择' }]}>
                <ControlConfigWidget
                  globalFields={vm.globalFields}
                  feeTypeList={vm.data}
                  form={form}
                  specificationGroups={vm.specificationGroups}
                  allSpecificationGroups={vm.allSpecificationGroups}
                  conditionConfig={vm.configValue.conditionConfig}
                />
              </FormItem>
            </Form>
          </div>
        </div>
      </div>
    )
  })
}

export default provider([ECardPayControlViewSettingVm.NAME, ECardPayControlViewSettingVm])(
  ECardPayControlViewSetting as any,
)
