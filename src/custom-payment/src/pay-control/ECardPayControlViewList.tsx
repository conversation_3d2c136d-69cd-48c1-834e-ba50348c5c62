import React, { FC, useEffect, useMemo, useState } from 'react'
// @ts-ignore
import styles from './ECardPayControlViewList.module.less'
import CustomBreadcrumbWidget, { PathItem } from './components/CustomBreadcrumbWidget'
import { Button, Space, Table, Switch, Popconfirm, Tooltip } from '@hose/eui'
import ConfigProviderWrapper from './components/ConfigProviderWrapper'
import { IKeel } from '@ekuaibao/keel'
import { app } from '@ekuaibao/whispered'
import { useObserver } from 'mobx-react-lite'
import { provider, useInstance } from '@ekuaibao/react-ioc'
import { ECardPayControlViewListVm } from '../vms/ECardPayControlViewList.vm'
import {
  activeControlConfig,
  copyControlConfig,
  delControlConfig,
  setRuleListOrder,
} from '../e-card-pay-control-action'
import { showModal, showMessage } from '@ekuaibao/show-util'
import {
  SortEnd,
  SortableContainer,
  SortableContainerProps,
  SortableElement,
  SortableHandle,
} from 'react-sortable-hoc'
import './drag-sorting-handler.less'
import { arrayMoveImmutable } from 'array-move'
import { OutlinedEditDrag } from '@hose/eui-icons'

interface IProps {
  keel: IKeel
}
const ECardPayControlViewList: FC<IProps> = (props) => {
  const { keel } = props
  const vm = useInstance<ECardPayControlViewListVm>(ECardPayControlViewListVm.NAME)
  const path: PathItem[] = [{ name: '易商卡' }, { name: '消费管控' }]
  const [showLevel, setShowLevel] = useState(false)
  const handleCancelLevel = () => {
    setShowLevel(false)
    vm.fetchControlListData()
  }
  const handleUpdateLevel = () => {
    if (!showLevel) {
      setShowLevel(true)
    } else {
      if (showLevel) {
        showModal.confirm({
          title: i18n.get(`调整优先级`),
          content: i18n.get(`确定调整？`),
          onOk: async () => {
            const ids = vm.dataSource.map((it) => it.id)
            setRuleListOrder(ids)
              .then((res) => {
                if (res.value) {
                  setShowLevel(false)
                  vm.init()
                }
              })
              .catch((e) => {
                showMessage.error(e.errorMessage || e.message)
              })
          },
        })
      }
    }
  }
  const handleAddEditRule = (record?: string) => {
    keel.open('ECardPayControlViewSetting', { record })
  }

  const handleCopy = (id: string) => {
    copyControlConfig(id)
      .then((_) => {
        vm.init()
      })
      .catch((e) => {
        showMessage.error(e.errorMessage || e.message)
      })
  }
  const handleDelete = (id: string) => {
    delControlConfig(id)
      .then((_) => {
        vm.init()
      })
      .catch((e) => {
        showMessage.error(e.errorMessage || e.message)
      })
  }
  const handleChangeStatus = async (active: boolean, id: string) => {
    activeControlConfig({ active, id })
      .then((_) => {
        vm.init()
      })
      .catch((e) => {
        showMessage.error(e.errorMessage || e.message)
      })
  }

  const getConditionRuleRange = (value: any[]) => {
    const rule = [] as string[]
    value?.forEach((item) => {
      const staffs = item?.staffList?.map((it: any) => it?.name) || []
      const departments = item?.departmentList?.map((it: any) => it?.name) || []
      const feeTypeIds = item?.feeTypeList?.map((it: any) => it?.name) || []
      // @ts-ignore
      const left = [...staffs, ...departments].join('、')
      rule.push(`「${left}」进行${feeTypeIds.length ? `「${feeTypeIds.join('、')}」` : ''} 消费时`)
    })
    return rule
  }

  const columns = useMemo(() => {
    const columns = [
      {
        title: i18n.get('优先级'),
        dataIndex: 'order',
        width: '10%',
      },
      {
        title: i18n.get('模版名称'),
        dataIndex: 'name',
        width: '20%',
        ellipsis: true,
        render: (text: string) => (
          <Tooltip placement="topLeft" title={text}>
            {text}
          </Tooltip>
        ),
      },
      {
        title: i18n.get('适用范围'),
        dataIndex: 'conditions',
        ellipsis: true,
        width: '40%',
        render: (value: any) => {
          return getConditionRuleRange(value).map((it, index) => (
            <Tooltip placement="topLeft" title={it}>
              <div key={index} className="eui-table-cell-ellipsis">
                {it}
              </div>
            </Tooltip>
          ))
        },
      },
      {
        title: i18n.get('启用状态'),
        dataIndex: 'active',
        width: '10%',
        render: (active: boolean, record: any) => (
          <Switch
            size="small"
            checked={active}
            onChange={(checked) => handleChangeStatus(checked, record.id)}
            data-testid={`pay-eCard-control-status-switch-${record.id}`}
          />
        ),
      },
      {
        title: i18n.get('操作'),
        dataIndex: 'action',
        width: '20%',
        render: (_text: any, record: any) => {
          const { id } = record
          return (
            <Space>
              <Button
                size="small"
                category="text"
                theme="highlight"
                onClick={() => handleAddEditRule(record)}
                data-testid={`pay-eCard-control-edit-button-${record.id}`}>
                {i18n.get('编辑')}
              </Button>
              <Popconfirm
                title={i18n.get('确定复制该规则吗？')}
                okText={i18n.get('确定')}
                content={i18n.get('复制后名称为副本，状态为禁用中，需启用后生效')}
                cancelText={i18n.get('取消')}
                onConfirm={() => handleCopy(id)}>
                <Button size="small" category="text" theme="highlight" data-testid={`pay-eCard-control-copy-button-${id}`}>
                  {i18n.get('复制')}
                </Button>
              </Popconfirm>
              <Popconfirm
                title={i18n.get('确定删除该规则吗？')}
                okText={i18n.get('删除')}
                content={i18n.get('删除后无法撤回，请谨慎操作')}
                cancelText={i18n.get('取消')}
                okButtonProps={{ theme: 'danger' }}
                onConfirm={() => handleDelete(id)}>
                <Button size="small" category="text" theme="highlight" data-testid={`pay-eCard-control-delete-button-${id}`}>
                  {i18n.get('删除')}
                </Button>
              </Popconfirm>
            </Space>
          )
        },
      },
    ]
    if (showLevel) {
      columns.unshift({
        title: '',
        dataIndex: 'sort',
        width: "40px",
        // @ts-ignore
        render: () => <DragHandle />,
      })
    }
    return columns
  }, [showLevel])

  const onSortEnd = ({ oldIndex, newIndex }: SortEnd) => {
    if (oldIndex !== newIndex) {
      const newData = arrayMoveImmutable(vm.dataSource.slice(), oldIndex, newIndex).filter(
        (el: any) => !!el,
      )
      vm.dataSource = newData
    }
  }

  useEffect(() => {
    vm.init()
    app.invokeService('@common:get:organizationinfo')
  }, [])

  const DraggableContainer = (props: SortableContainerProps) => (
    // @ts-ignore
    <SortableBody
      useDragHandle
      // @ts-ignore
      disableAutoscroll
      helperClass="row-dragging"
      onSortEnd={onSortEnd}
      {...props}
    />
  )
  const DraggableBodyRow: React.FC<any> = ({ className, style, ...restProps }) => {
    const index = vm.dataSource.findIndex((x) => x.id === restProps['data-row-key'])
    // @ts-ignore
    return <SortableItem index={index} {...restProps} />
  }
  const getButtons = () => {
    if (!vm.dataSource?.length) {
      return null
    }
    if (showLevel) {
      return (
        <>
          <Button category="secondary" onClick={handleUpdateLevel} data-testid="pay-eCard-control-confirm-adjust">
            {i18n.get('确定调整')}
          </Button>
          <Button category="secondary" onClick={handleCancelLevel} data-testid="pay-eCard-control-cancel-adjust">
            {i18n.get('取消调整')}
          </Button>
        </>
      )
    }
    return (
      <Button category="secondary" onClick={handleUpdateLevel} data-testid="pay-eCard-control-adjust-level">
        {i18n.get('调整优先级')}
      </Button>
    )
  }
  return useObserver(() => {
    return (
      <div className={styles.viewWrapper}>
        <CustomBreadcrumbWidget path={path} />
        <div className={styles.tableWrapper}>
          <div className={styles.tableTop}>
            <span>{i18n.get('消费管控规则')}</span>
            <Space wrap>
              {getButtons()}
              <Button onClick={() => handleAddEditRule()} data-testid="pay-eCard-control-add-rule">{i18n.get('新增规则')}</Button>
            </Space>
          </div>
          <ConfigProviderWrapper>
            <Table
              loading={vm.loading}
              dataSource={vm.dataSource}
              size="middle"
              columns={columns}
              rowKey="id"
              components={{
                body: {
                  wrapper: DraggableContainer,
                  row: DraggableBodyRow,
                },
              }}
              scroll={{
                y: 'calc(100vh - 244px)',
              }}
              pagination={false}
            />
          </ConfigProviderWrapper>
        </div>
      </div>
    )
  })
}

const DragHandle = SortableHandle(() => (
  <OutlinedEditDrag fontSize={16} style={{ cursor: 'grab', color: 'var(--eui-icon-n2)' }} />
))

const SortableBody = SortableContainer((props: React.HTMLAttributes<HTMLTableSectionElement>) => (
  <tbody {...props} />
))
const SortableItem = SortableElement((props: React.HTMLAttributes<HTMLTableRowElement>) => (
  <tr {...props} />
))
export default provider([ECardPayControlViewListVm.NAME, ECardPayControlViewListVm])(
  ECardPayControlViewList as any,
)
