import { app as api } from '@ekuaibao/whispered'
import './create-account-form.less'

import { EnhanceConnect } from '@ekuaibao/store'
const SelectPaymentThirdMethod: any = api.require(
  '@elements/payment-form-field/select-payment-method',
)
import React from 'react'
// @ts-ignore
import { fnCompareProps } from '@ekuaibao/lib/lib/lib-util'
import ExtendSetting from './elements/extendSetting'
import { getError } from './elements/extendContent'
import { get, cloneDeep, set, debounce, unionBy } from 'lodash'
// @ts-ignore
import classNames from 'classnames'
// @ts-ignore
const { getDeptItemsByIds, getItemByIds } = api.require('@lib/lib-util')
import actions, { getCustomerRegNumberList } from './custom-payment-action'
import { fetchChannel, trackCustomPayment } from './lib/fetchUtil'
import {
  getFormItemsArrHSBC,
  getFormItemsCityCode,
  getOptionTitle,
  getPlaceholder,
  getCityStr,
} from './lib/paymentUtils'
import { showMessage } from '@ekuaibao/show-util'
import { fnCheckAntAlipayBindStateFormEKB } from './custom-payment-fetch-util'
import PayerRemarkConfig from './payer-remark-config'
import { T } from '@ekuaibao/i18n'

import { provider, inject } from '@ekuaibao/react-ioc'
import { observer } from 'mobx-react'
import { PermissionVm } from './vms/Permission.vm'
import { Universal_Unique_Key } from './index'
import {
  Space,
  Tag,
  Tooltip,
  Select,
  Checkbox,
  Radio,
  Form,
  Input,
  Button,
  Switch,
} from '@hose/eui'
import { OutlinedTipsInfo } from '@hose/eui-icons'
import { Fetch } from '@ekuaibao/fetch'
import SelectBranchTypeField from './SelectBranchTypeField'
const StaffSelect = api.require<any>('@components/select-staff-heavy/staff-select-range')
// @ts-ignore
const { showFunctionByKey, UniversalComponent } = api.require('@elements/UniversalComponent')
const IntelligentFillingModal: any = api.require('@elements/IntelligentFillingModal')

const RadioGroup = Radio.Group
const FormItem = Form.Item
const Option = Select.Option
let timeout: any

const accountChannel = () => [
  { label: i18n.get('银行卡'), value: 'BANK', name: i18n.get('银行卡号') },
  { label: i18n.get('支付宝'), value: 'ALIPAY', name: i18n.get('支付宝账号') },
  { label: i18n.get('海外账号'), value: 'OVERSEABANK', name: i18n.get('银行卡号(Card No.)') },
  { label: i18n.get('支票'), value: 'CHECK', name: i18n.get('支票号') },
  { label: i18n.get('承兑汇票'), value: 'ACCEPTANCEBILL', name: i18n.get('承兑汇票号') },
  { label: i18n.get('其它'), value: 'OTHER' },
]

const accountChannelNameMap = () => ({
  BANK: i18n.get('银行卡号'),
  ALIPAY: i18n.get('支付宝账号'),
  OVERSEABANK: i18n.get('银行卡号(Card No.)'),
  CHECK: i18n.get('支票号'),
  ACCEPTANCEBILL: i18n.get('企业账号（户口号）'),
})

const visibilityMap: any = {
  true: 'ALL',
  false: 'SECTION',
}
const HSBC_BANK_NAME = ['香港上海汇丰银行'] //接口无法返回bankCode,要求通过汉字判断
interface Props {
  sort?: any
  MCPermission?: any
  form?: any
  id: string
  state: string
  channels: Array<any>
  payAccountList: Array<any>
  layer: any
  bus: any
  externalList: Array<any>
  roles: Array<any>
  staffs: Array<any>
  departmentTree: any
  active: boolean
  accountName: string
  name: string
  onRestoreOrDisable: any
  channelList: Array<any>
  formChanpaySuccessModal: any
  visible: any
  searchCountries: any
  searchCities: any
  KA_FOREIGN_ACCOUNT?: any
}

interface States {
  doc: any
  data: any
  visibility: any
  bankList: any
  targetList: any
  visibleValue: any
  selectedValues: any
  isRemark: any
  defaultChannel: any
  branchBankName: string
  customerRegNumberList: any
}

@EnhanceConnect((state: any) => ({
  branchList: state['@common'].branchByCityIdList,
  channelList: state['@audit'].channelList,
  payAccountList: state['@common'].newPayAccount.list,
  staffs: state['@common'].staffs,
  roles: state['@common'].roleList,
  MCPermission: state['@common'].MCPermission,
  departmentTree: state['@common'].department.data,
  KA_FOREIGN_ACCOUNT: state['@common'].powers.KA_FOREIGN_ACCOUNT,
  allCurrencyRates: state['@common'].allCurrencyRates,
  standardCurrency: state['@common'].standardCurrency,
  searchCountries: state['@custom-payment'].searchCountries.items,
  searchCities: state['@custom-payment'].searchCities.items,
  userInfo: state['@common'].userinfo.data,
}))
@provider(['formPermission', PermissionVm])
@observer
export default class CompanyAccountForm extends React.PureComponent<Props | any, States | any> {
  @inject('formPermission') permission?: PermissionVm
  private city: any = undefined
  private form: any = React.createRef()
  private formContent: any
  constructor(props: any) {
    super(props)
    const visibleValue =
      (props.visibility && props.visibility.fullVisible) || !props.visibility ? 'ALL' : 'SECTION'
    this.state = {
      doc: {},
      data: props,
      visibility:
        props.state === 'edit'
          ? props.visibility
          : {
              departments: [],
              staffs: [`${props?.userInfo?.staff?.id}`],
              roles: [],
              departmentsIncludeChildren: false,
            },
      bankList: [],
      targetList: [],
      visibleValue,
      selectedValues: this.valueParse(props.visibility),
      isRemark: !!props.name || !!props.code || false,
      defaultChannel: props.defaultChannel || 'OFFLINE',
      configType: props.configType || 'GLOBAL',
      branchBankName: props.bank,
      customerRegNumberList: [],
      recevingCurrencyEnable: props.recevingCurrencyEnable || false,
      bankCustodianAccount: props.bankCustodianAccount || false,
      recevingCurrencyScope: {
        fullVisible: true,
        currencyIds: [],
      },
      allCurrencyList: [],
    }
    this.city = undefined
  }

  componentWillMount() {
    api.dataLoader('@common.channelList').load()
  }

  getChannel = (channel?: any) => {
    const { sort, channelList } = this.props
    fetchChannel(channel || sort || 'BANK').then(
      (data) => {
        const items = data.items
        let list: any = items || []
        const offlineIndex = list?.findIndex((target: any) => target?.code === 'OFFLINE')
        if (offlineIndex !== -1) {
          list.splice(offlineIndex, 1)
          list.unshift(channelList.find((i: any) => i.channel === 'OFFLINE'))
        }
        this.setState({ targetList: list })
        this.forceUpdate()
      },
      (err) => {
        showMessage.error(err.message)
      },
    )
  }

  fnCheckAntAlipayBindState = () => {
    if (this.props.state !== 'edit') return
    const { channels = [], id } = this.props
    // 是否绑定支付宝
    if (channels.includes('ANTALIPAY')) {
      fnCheckAntAlipayBindStateFormEKB(id, this.fnCancelAntAlipayChannel)
    }
  }

  fnCancelAntAlipayChannel = () => {
    const { channels = [] } = this.props
    this.setState({ defaultChannel: 'OFFLINE' })
    this.form.current?.setFieldsValue({
      channels: channels.filter((el: any) => el !== 'ANTALIPAY'),
    })
  }

  componentDidMount() {
    const countryId = get(this.props, 'extensions.country')
    const countryStr = get(this.props, 'extensions.countryStr')
    const cityStr = get(this.props, 'extensions.cityStr')
    const countryEnStr = get(this.props, 'extensions.countryEnStr')
    const cityEnStr = get(this.props, 'extensions.cityEnStr')
    const bankCountryStr = get(this.props, 'extensions.bankCountryStr')
    const shortCode = get(this.props, 'extensions.shortCode')
    const bankCountry_shortCode = get(this.props, 'extensions.bankCountry_shortCode')
    const recevingCurrencyEnable = get(this.props, 'recevingCurrencyEnable')
    const bankCustodianAccount = get(this.props, 'bankCustodianAccount')
    const recevingCurrencyScope = get(this.props, 'recevingCurrencyScope')
    const { KA_FOREIGN_ACCOUNT } = this.props
    if (KA_FOREIGN_ACCOUNT) {
      api.dataLoader('@custom-payment.searchCountries').load()
    }
    this.setState({
      countryStr,
      cityStr,
      countryEnStr,
      cityEnStr,
      bankCountryStr,
      shortCode,
      bankCountry_shortCode,
      recevingCurrencyEnable,
      bankCustodianAccount,
      recevingCurrencyScope: recevingCurrencyScope ?? {
        fullVisible: true,
        currencyIds: [],
      },
    })
    if (countryId) {
      api.invokeService('@custom-payment:get:CityList', countryId)
    }
    this.setInitData(this.props, this.fnCheckAntAlipayBindState)
    api.invokeService('@common:get:bank:provinces')
    api.invokeService('@common:get:powers') // 刷新当前企业已购买的服务
    this.getChannel()
    const { MCPermission = {}, ...others } = this.props
    if (MCPermission.type === 'MC') {
      this.permission?.initPermissions(others)
    } else {
      this.permission?.initData({ type: MCPermission.type })
    }
    if (this.props.state === 'edit') {
      api.dispatch(actions.getRemarkSetting(this.props.id)).then((result: any) => {
        if (!result || !result.value || result.value.configType == 'GLOBAL') {
          this.setState({ configType: 'GLOBAL' })
          this.form.current?.setFieldsValue({
            configType: 'GLOBAL',
          })
        } else {
          const {
            byHand,
            useSpecial,
            dimensionId,
            dimensionField,
            autoSummary,
            fieldId,
            fieldLabel,
          } = result.value
          this.setState({
            byHand,
            useSpecial,
            dimensionId,
            dimensionField,
            autoSummary,
            fieldId,
            fieldLabel,
            configType: 'PRIVATE',
          })
          this.form.current?.setFieldsValue({
            configType: 'PRIVATE',
          })
        }
      })
    }
    this.getCustomerRegNumberList()
    this.fnGetAllCurrencyList()
  }

  componentWillReceiveProps(nextProps: any) {
    const fn = fnCompareProps(this.props, nextProps)
    fn('data', (data: any) => {
      this.setInitData(data)
    })
  }

  fnGetAllCurrencyList = async () => {
    const res = await Fetch.GET(`/api/v1/basedata/enumItems/byEnumCode/$currency`)
    this.setState({ allCurrencyList: res?.items || [] })
  }

  setInitData = (data: any, fn?: any) => {
    if (data.state === 'edit') {
      this.setState(
        {
          data,
        },
        () => fn && fn(),
      )
    }
  }

  componentDidUpdate() {
    if (this.advancedOptionCallBack) {
      this.advancedOptionCallBack()
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      this.advancedOptionCallBack = () => {}
    }
  }

  getCustomerRegNumberList = async () => {
    const res = await getCustomerRegNumberList('TICKET_CMB')
    this.setState({ customerRegNumberList: res })
  }

  checkChannels = (channels = []) => {
    return !!~channels.findIndex((channel) => channel === 'CHANPAY' || channel === 'CHANPAYV2')
  }

  checkAliPayNo(_rule: any, value: any, callback: any) {
    if (value) {
      if (!value.length) return callback(i18n.get('支付宝账号不能为空'))
      if (value.length > 100) return callback(i18n.get('支付宝账号不能超过100位'))
    }
    callback()
  }

  resetFields = () => {
    this.form.current?.resetFields()
  }

  getBranchList(searchKey = '') {
    if (this.city) {
      api.invokeService('@common:get:branch:by:cityid:list', {
        cityId: this.city.cityId,
        searchKey: searchKey,
      })
    }
  }

  handleOK = () => {
    const { state, id, bus } = this.props
    this.form.current
      ?.validateFields()
      .then((values: any) => {
        const {
          defaultChannel,
          bankList,
          visibility,
          data,
          countryStr,
          cityStr,
          countryEnStr,
          cityEnStr,
          bankCountryStr,
          shortCode,
          bankCountry_shortCode,
          recevingCurrencyScope,
        } = this.state
        if (!recevingCurrencyScope?.fullVisible && !recevingCurrencyScope?.currencyIds?.length) {
          return
        }
        const advancedOption = get(values, 'advancedOption')
        if (advancedOption) {
          const {
            filter: { type, max, min },
          } = advancedOption
          const error = getError(type, max, min)
          if (error) {
            advancedOption.error = error
            return
          }
        }

        const flag = this.onPrivateConfigType(values)
        if (!flag) return

        if (state === 'edit') {
          values.id = id
        }
        values.recevingCurrencyScope = recevingCurrencyScope
        values.bankCustodianAccount = values.bankCustodianAccount ?? false
        values.asPayer = true
        values.asPayee = false
        if (values.accountNo) values.accountNo = values.accountNo.replace(/\s/g, '')
        values.defaultChannel = defaultChannel
        values.bankLinkNo = data?.bankLinkNo
        values.branchId = data?.branchId?.id
        const targetBank = bankList.find((b: any) => b.id === values.branch)
        if (targetBank) {
          values.branch = targetBank?.name
          values.bankLinkNo = targetBank?.code
          values.branchId = targetBank?.id
        }
        if (values.visibility === 'SECTION') {
          visibility.fullVisible = false
          values.visibility = visibility
        } else {
          visibility.fullVisible = true
          values.visibility = visibility
        }
        if (advancedOption) {
          delete advancedOption.error
        }
        const obj = {
          countryStr,
          cityStr,
          countryEnStr,
          cityEnStr,
          bankCountryStr,
          shortCode,
          bankCountry_shortCode,
        }
        const extensions = get(values, 'extensions')
        if (extensions) {
          set(values, 'extensions', { ...extensions, ...obj })
        }
        bus && bus.invoke('payment:save:click', { data: values, state: data.state })
        this.handleCancel()
      })
      .catch((err: any) => {
        if (err.errorFields?.[0]) {
          const fieldName = err.errorFields[0].name.join('_')

          const errorElement = document.getElementById(fieldName)
          if (errorElement) {
            errorElement.scrollIntoView({
              behavior: 'smooth',
              block: 'center',
            })
          }
        }
        throw err
      })
  }

  onPrivateConfigType = (values: any) => {
    let flag = true
    const { byHand, useSpecial, dimensionId, dimensionField, autoSummary, fieldId, fieldLabel } =
      this.state

    if (values.configType === 'PRIVATE') {
      values.payerInfoConfig = {
        byHand,
        useSpecial,
        dimensionId,
        dimensionField,
        autoSummary,
        fieldId,
        fieldLabel,
      }

      if (this.state.useSpecial) {
        if (
          this.state.dimensionId === null ||
          this.state.dimensionId === undefined ||
          this.state.dimensionId === '' ||
          this.state.dimensionField === null ||
          this.state.dimensionField === undefined ||
          this.state.dimensionField === ''
        ) {
          // @ts-ignore
          this.refs.remarkConfig.refs.userSpecialWarn.style.display = 'inline'
          flag = false
        }
      }
      if (this.state.autoSummary) {
        if (
          this.state.fieldId === null ||
          this.state.fieldId === undefined ||
          this.state.fieldId === ''
        ) {
          // @ts-ignore
          this.refs.remarkConfig.refs.fieldWarn.style.display = 'inline'
          flag = false
        }
      }
    }

    return flag
  }

  handleOnBranchSearch = (value: any) => {
    if (timeout) {
      clearTimeout(timeout)
      timeout = null
    }
    timeout = setTimeout(() => {
      this.getBranchList(value)
    }, 500)
  }

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  advancedOptionCallBack: () => void = () => {}
  advancedOptionChange = (value: any) => {
    this.form.current?.setFieldsValue({ advancedOption: value })
    this.advancedOptionCallBack = () => {
      if (value && this.formContent && this.formContent.scrollTo) {
        this.formContent.scrollTo(0, this.formContent.scrollHeight)
      }
    }
  }

  handleChannelChange = (channels: any) => {
    let { data } = this.state
    this.form.current?.setFieldsValue({ channels })
    data = { ...data, channels }
    this.setState({ data })
  }

  handleCancel = () => {
    this.resetFields()
    this.props.layer.emitCancel()
  }

  checkCode = (_rule: any, value: any, callback: any) => {
    if (value) {
      let { data } = this.state
      let isRepeat = !!this.props.payAccountList
        .filter((v: any) => v.code !== data.code)
        .find((o: any) => o.code === value)
      if (isRepeat) return callback(i18n.get('账户编码不能重复'))
      if (value.length > 140) return callback(i18n.get('账户编码不能超过140位'))
    }
    callback()
  }

  checkName = (_rule: any, value: any, callback: any) => {
    if (value) {
      let { data } = this.state
      let isRepeat = !!this.props.payAccountList
        .filter((v: any) => v.code !== data.code)
        .find((o: any) => o.name === value)
      if (isRepeat) return callback(i18n.get('账户备注名称不能重复'))
      if (value.length > 140) return callback(i18n.get('账户备注不能超过140位'))
    }
    callback()
  }

  checkBankNo = (_rule: any, value: any, callback: any) => {
    if (value) {
      if (!/^[0-9a-zA-Z-]*$/.test(value.replace(/\s/g, '')))
        return callback(i18n.get('银行账号格式不正确'))
      if (!value.length) return callback(i18n.get('银行账号不能为空'))
      if (value.length > 32) return callback(i18n.get('银行账号不能超过32位'))
    }
    callback()
  }

  checkEn = (_rule: any, value: any, callback: any) => {
    if (value) {
      if (value.match(/[\u4e00-\u9fa5]/)) return callback('Please enter in English')
    }
    callback()
  }

  onSetRemarkChange = (e: any) => {
    trackCustomPayment('custom_payment_set_rename')
    this.setState({ isRemark: e.target.checked })
  }

  valueParse = (value: any) => {
    if (!value) {
      return []
    }

    return [
      ...getDeptItemsByIds(this.props.departmentTree, value.departments || []),
      ...getItemByIds(this.props.staffs, value.staffs || []),
      ...getItemByIds(this.props.roles, value.roles || []),
      ...getItemByIds(this.props.externalList, value.staffs || []),
    ]
  }

  handleValueChange = (value: any) => {
    const data = {
      ...value,
      fullVisible: false,
    }
    this.setState({ visibility: data })
  }

  onAccountTypeChange = (e: any) => {
    const { data } = this.state
    this.form.current?.setFieldsValue({ accountNo: '', channels: ['OFFLINE'] })
    this.setState({ data: { ...data, sort: e, channels: ['OFFLINE'] }, defaultChannel: 'OFFLINE' })
    this.getChannel(e)
  }

  onVisibleChange = (e: any) => {
    let obj: any = {
      visibleValue: e.target.value,
      visibility: {
        departments: [],
        staffs: [`${this.props?.userInfo?.staff?.id}`],
        roles: [],
        departmentsIncludeChildren: false,
      },
    }
    if (e.target.value === 'SECTION') {
      obj.visibility.fullVisible = false
    }
    this.setState({ ...obj })
  }
  setPayerConfig = (e: any) => {
    this.setState({ configType: e.target.value })
    if (e.target.value === 'PRIVATE') {
      this.setState({
        byHand: true,
        useSpecial: false,
        dimensionId: '',
        dimensionField: 'NAME',
        autoSummary: true,
        fieldId: 'title',
        fieldLabel: '标题',
      })
    }
  }

  onCurrencyEnableChange = (e: any) => {
    this.setState({
      recevingCurrencyScope: { fullVisible: e?.target?.value === 'ALL', currencyIds: [] },
    })
  }

  handleCurrencyChange = (currencyIds: any) => {
    this.setState({ recevingCurrencyScope: { ...this.state?.recevingCurrencyScope, currencyIds } })
  }

  handleCheck = (name: any, e: any) => {
    if (name == 'useSpecial') {
      this.setState({ byHand: true })
    }
    if (name == 'byHand' && this.state.byHand) {
      this.setState({ useSpecial: false })
    }

    if (name == 'byHand' || name == 'useSpecial' || name == 'autoSummary') {
      this.setState((prevState: any) => {
        return {
          [name]: !prevState[name],
        }
      })
    } else {
      if (name == 'fieldId') {
        this.setState({
          // @ts-ignore
          fieldLabel: this.refs.remarkConfig.state.fieldList.find((v) => v.name === e).label,
        })
        // @ts-ignore
        this.refs.remarkConfig.refs.fieldWarn.style.display = 'none'
      }
      if (
        (name === 'dimensionId' && this.state.dimensionField != '') ||
        (name === 'dimensionField' && this.state.dimensionId != '')
      ) {
        // @ts-ignore
        this.refs.remarkConfig.refs.userSpecialWarn.style.display = 'none'
      }
      this.setState({ [name]: e })
    }
  }

  onRestoreOrDisable = () => {
    const { onRestoreOrDisable, id = '', name = '', accountName = '', active = true } = this.props
    onRestoreOrDisable && onRestoreOrDisable({ id, name, accountName }, !active)
    this.props.layer.emitCancel()
  }

  onSetDefault = (defaultChannel: any) => {
    this.setState({ defaultChannel })
  }

  handleAdvancedSearch = () => {
    api.open('@custom-payment:AdvancedSearch').then((data: any) => {
      this.setState({ branchBankName: data?.bankName })
      if (data?.id) {
        this.setState({ bankList: [data] }, () => {
          this.form.current?.setFieldsValue({ branch: data?.id })
        })
      }
    })
  }
  onChangeBrunch = (value: any) => {
    const { bankList } = this.state
    this.form.current?.setFieldsValue({ branch: value })
    const bank = bankList?.find((i: any) => i.id === value)
    bank && bank.bankId && this.setState({ branchBankName: bank.bankId.name })
  }

  onSearchBankList = debounce((searchKey) => {
    const key = searchKey.trim()
    const flag = /^[\u4e00-\u9fff]+$/.test(key)
    if (flag && key?.length > 5) {
      api
        .dispatch(actions.getBanks({ searchKey: key }))
        .then((data: any) => {
          this.setState({ bankList: data.items })
        })
        .catch((err: any) => {
          showMessage.error(err)
        })
    }
  }, 1000)

  onSelectChange = (value: string, name: string, e: any) => {
    const { searchCountries, searchCities } = this.props
    const { setFieldsValue, setFieldValue } = this.form.current
    //countryStr countryEnStr cityStr cityEnStr bankCountryStr shortCode bankCountry_shortCode 保存下拉框value值对应全程，在收款变更记录中使用
    if (['extensions.country', 'extensions.countryEn'].includes(name)) {
      let selectCountry = searchCountries.filter((item: any) => item.id === e.key)
      let countryStr = selectCountry[0]?.fullCname
      let countryEnStr = selectCountry[0]?.fullEname
      let shortCode = selectCountry[0]?.shortCode
      this.setState({
        countryStr,
        countryEnStr,
        shortCode,
      })
    }
    if (['extensions.city', 'extensions.cityEn'].includes(name)) {
      const selectCity = searchCities.filter((item: any) => item.id === e.key)
      let cityStr = null
      let cityEnStr = null

      if (selectCity[0]) {
        const { cnStr, enStr } = getCityStr(selectCity[0])
        cityStr = cnStr
        cityEnStr = enStr
      }

      this.setState({
        cityStr,
        cityEnStr,
      })
    }
    if (name === 'extensions.bankCountry') {
      let selectCountry = searchCountries.filter((item: any) => item.id === e.key)
      let bankCountryStr = selectCountry[0]?.fullCname
      let bankCountry_shortCode = selectCountry[0]?.shortCode
      this.setState({
        bankCountryStr,
        bankCountry_shortCode,
      })
      return
    }
    const formItemMap: any = {
      'extensions.country': 'extensions.countryEn',
      'extensions.city': 'extensions.cityEn',
      'extensions.countryEn': 'extensions.country',
      'extensions.cityEn': 'extensions.city',
    }
    // 同标签中英文联动
    const labelname = formItemMap[name]
    if (labelname.includes('country')) {
      setFieldValue(
        labelname.split('.'),
        searchCountries.filter((item: any) => item.id === e.key)[0]?.id,
      )
    } else {
      setFieldValue(
        labelname.split('.'),
        searchCities.filter((item: any) => item.id === e.key)[0]?.id,
      )
    }
    // 切换国家 清空城市
    if (name.includes('country')) {
      setFieldValue(['extensions', 'city'], undefined)
      setFieldValue(['extensions', 'cityEn'], undefined)
      this.setState({
        cityStr: null,
        cityEnStr: null,
      })
    }
    if (value && name.includes('country')) {
      api.invokeService('@custom-payment:get:CityList', e.key)
    }
  }

  initAcceptanceBillValue = (doc: any) => {
    return this.state?.customerRegNumberList?.find((r: any) => r.id === doc?.accountNo)
      ? doc?.accountNo
      : undefined
  }

  handleAccountNoBlur = async () => {
    const { setFieldsValue, getFieldsValue } = this.form.current
    const values = await getFieldsValue()
    const { accountNo, sort } = values || {}
    if (sort === 'BANK' && accountNo?.length > 14) {
      const res = await api.invokeService('@custom-payment:get:BranchByCardNo', accountNo)
      setFieldsValue({ branch: res?.value?.id })
      if (res?.value?.id) {
        this.setState({ bankList: [res?.value] })
      }
    }
  }

  handleIntelligentFillingValue = (value: any) => {
    const { setFieldsValue } = this.form.current
    const _value = value.trim()
    const data = JSON.stringify(_value)
    const text = data.replace(/^"|"$/g, '')
    api
      .invokeService('@custom-payment:post:Parsing', { text })
      .then((res: any) => {
        const { accountName, accountNo, branches } = res?.value || {}
        setFieldsValue({ accountName, accountNo, branch: branches?.[0]?.id })
        showMessage.success(i18n.get('已填入可识别信息，请确认'))
        if (branches?.[0]?.id) {
          this.setState({ bankList: branches })
        }
      })
      .catch((err: any) => {
        showMessage.error(err?.errorMessage)
      })
  }

  renderOverseaFieldForHSBC = (sortV: any, doc: any) => {
    const {
      KA_FOREIGN_ACCOUNT,
      allCurrencyRates,
      standardCurrency,
      searchCountries = [],
      searchCities = [],
    } = this.props
    const { branchBankName } = this.state
    const channels = doc.channels || []
    const isHSBCbank =
      channels.includes('HSBCPAY') ||
      (channels.includes('CBSPAY') && HSBC_BANK_NAME.includes(branchBankName))
    const formItemsArrHSBC =
      sortV === 'OVERSEABANK'
        ? getFormItemsArrHSBC()
        : getFormItemsArrHSBC().filter((item) => item.name !== 'extensions.bankCountry')
    const selectMap = {
      'extensions.country': searchCountries,
      'extensions.city': searchCities,
      'extensions.countryEn': searchCountries,
      'extensions.cityEn': searchCities,
      'extensions.bankCountry': searchCountries,
    }
    const allCurrency = unionBy([...allCurrencyRates, standardCurrency], 'numCode')
    if ((sortV === 'OVERSEABANK' || sortV === 'BANK') && KA_FOREIGN_ACCOUNT && isHSBCbank) {
      return (
        <>
          {formItemsArrHSBC.map((el) => {
            const label = el.payer ? `${el.payer}-${el.label}` : el.label
            const __k0 = el.payer
              ? `${i18n.get(el.payer)}-${i18n.get(el.label)}`
              : i18n.get(el.label)
            const placeholder = getPlaceholder(el, __k0)
            if (el.type === 'select') {
              return (
                <FormItem
                  key={el.name}
                  label={label}
                  name={el.name.split('.')}
                  initialValue={get(doc, el.name)}
                  rules={[
                    { required: el?.required, whitespace: true, message: placeholder },
                    { validator: el.onlyEn ? this.checkEn : undefined },
                  ]}>
                  <Select
                    onChange={(value: any, e: any) => this.onSelectChange(value, el.name, e)}
                    className="select"
                    placeholder={placeholder}
                    getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
                    showSearch={true}
                    filterOption={(input: string, option: any) => {
                      return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }}>
                    {get(selectMap, el.name).map((item: any) => {
                      const optionTitle = getOptionTitle(el, item)
                      return (
                        <Option key={item.id} value={item.id}>
                          {optionTitle}
                        </Option>
                      )
                    })}
                  </Select>
                </FormItem>
              )
            } else if (el.type === 'multiple') {
              // 币种
              let checkedValue = get(doc, el.name, [standardCurrency.numCode])
              let initialValue: any = []
              checkedValue.forEach((item: any) => {
                let currencyItem = allCurrency.filter((currency) => currency.numCode === item)
                if (currencyItem[0]) {
                  initialValue.push(item)
                }
              })
              return (
                <FormItem
                  key={el.name}
                  label={label}
                  name={el.name.split('.')}
                  initialValue={initialValue}
                  rules={[{ required: el?.required, message: placeholder }]}>
                  <Select
                    className="select multiple"
                    mode="multiple"
                    placeholder={placeholder}
                    getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
                    showSearch={true}
                    filterOption={(input: string, option: any) => {
                      return option.props.children.indexOf(input) >= 0
                    }}>
                    {allCurrency.map((item) => {
                      return (
                        <Option key={item.numCode} value={item.numCode}>
                          {`${item.name} ${item.strCode}`}
                        </Option>
                      )
                    })}
                  </Select>
                </FormItem>
              )
            } else {
              return (
                <FormItem
                  key={el.name}
                  label={label}
                  name={el.name.split('.')}
                  initialValue={get(doc, el.name)}
                  rules={[
                    {
                      max: el.max,
                      message:
                        i18n.get(el.label) + i18n.get('不能超过{__k0}个文字', { __k0: el.max }),
                    },
                    { required: el?.required, whitespace: true, message: placeholder },
                    { validator: el.onlyEn ? this.checkEn : undefined },
                  ]}>
                  <Input placeholder={placeholder} />
                </FormItem>
              )
            }
          })}
        </>
      )
    }
    return null
  }

  renderBranchLabel = (label: string, name: string) => {
    const titleMap: any = {
      accountName: i18n.get('您在银行开立账户时输入的姓名、公司名或组织机构名称'),
      remark: i18n.get('如您的账户做特殊用途，区别其他账户，可添加账户备注'),
      branch: i18n.get('您可以在对应的合同中查询此信息'),
    }
    return (
      <>
        {i18n.get(label)}
        <Tooltip placement="top" title={titleMap[name]}>
          <OutlinedTipsInfo
            style={{ fontSize: 14, color: 'var(--eui-icon-n2)', marginTop: 3, marginLeft: 4 }}
          />
        </Tooltip>
      </>
    )
  }

  tagRender = (props: any) => {
    const { value, closable, onClose } = props
    const { allCurrencyList } = this.state
    const item: any = allCurrencyList?.find((v: any) => v?.numCode === value)
    const onPreventMouseDown = (event: any) => {
      event.preventDefault()
      event.stopPropagation()
    }
    return (
      <Tag className="tag-item" onClick={onPreventMouseDown} closable={closable} onClose={onClose}>
        {item?.name}
      </Tag>
    )
  }

  renderCreateAccount = () => {
    const {
      data: doc,
      isRemark,
      visibility,
      visibleValue,
      targetList,
      defaultChannel,
      bankList = [],
      byHand,
      useSpecial,
      branchBankName,
      configType,
      dimensionId,
      dimensionField,
      autoSummary,
      fieldId,
      customerRegNumberList,
      recevingCurrencyEnable,
      bankCustodianAccount,
      recevingCurrencyScope,
      allCurrencyList,
    } = this.state
    const needValueFlag =
      !recevingCurrencyScope?.fullVisible && !recevingCurrencyScope?.currencyIds?.length
    const isChanPay = this.checkChannels(
      doc.state === 'edit' ? doc.channels : this.form?.current?.getFieldValue('channels'),
    )
    const advancedOption = cloneDeep(doc.advancedOption)
    const accountCode = i18n.get('请输入账户编码')
    const accountName = i18n.get('请输入真实姓名、公司名或组织机构名称')
    const accountBranch = i18n.get('请输入至少6个汉字的银行网点')
    const sortV = doc.sort || 'BANK'
    const accountChannelNameMapEnum: any = accountChannelNameMap()
    let accountChanel = accountChannel()
    // @ts-ignore
    if (window.IS_SMG) {
      accountChanel = accountChanel && accountChanel.filter((line) => line.value !== 'ALIPAY')
    }
    accountChanel = accountChanel?.filter(
      (chanel) => !!showFunctionByKey(`${Universal_Unique_Key}.${chanel.value}`),
    )
    const showBankCustodianAccountCheckBox =
      branchBankName === '招商银行' && doc.channels?.includes('CMBCBS8')
    //Bank custodian account
    const cityCodeList = getFormItemsCityCode()
    const currency = api.getState('@common').powers.Currency
    return (
      <Form ref={this.form} layout="vertical" scrollToFirstError compact>
        <div className="form-content-group-title mb-16">{i18n.get('基础信息')}</div>
        <Space className="sortSpace">
          <FormItem
            label={i18n.get('账户类别')}
            name={'sort'}
            initialValue={(doc && doc.sort) || 'BANK'}
            rules={[{ required: true, message: i18n.get('请选择账户类别') }]}>
            <Select onChange={this.onAccountTypeChange} className="select" data-testid="pay-customPaymentAccountType-select">
              {accountChanel.map((item, idx) => {
                return (
                  <Option key={item.value + idx} value={item.value}>
                    {item.label}
                  </Option>
                )
              })}
            </Select>
          </FormItem>
          {sortV === 'BANK' && (
            <IntelligentFillingModal
              handleIntelligentFillingValue={this.handleIntelligentFillingValue}
            />
          )}
        </Space>

        <FormItem
          label={this.renderBranchLabel('开户名', 'accountName')}
          name="accountName"
          initialValue={doc && doc.accountName}
          rules={[
            { required: true, whitespace: true, message: accountName },
            { max: 140, message: i18n.get('开户名不能超过140个字') },
          ]}>
          <Input data-testid="pay-customPaymentAccountName-input" placeholder={accountName} />
        </FormItem>

        <div className="mb-12">
          <Checkbox className="fs-14" checked={isRemark} onChange={this.onSetRemarkChange} data-testid="pay-customPaymentRemark-checkbox">
            <T name="设置备注名称或编码" />
          </Checkbox>
        </div>
        {isRemark && (
          <FormItem
            label={i18n.get('账户备注名称')}
            name="name"
            initialValue={doc?.name}
            rules={[{ whitespace: true, validator: this.checkName }]}>
            <Input placeholder={i18n.get('请输入账户备注名称')} data-testid="pay-customPaymentName-input" />
          </FormItem>
        )}
        {isRemark && (
          <FormItem
            label={i18n.get('账户编码')}
            name="code"
            initialValue={doc?.code}
            rules={[{ whitespace: true, validator: this.checkCode }]}>
            <Input placeholder={accountCode} data-testid="pay-customPaymentCode-input" />
          </FormItem>
        )}
        {sortV !== 'OTHER' &&
          (sortV !== 'ACCEPTANCEBILL' ? (
            <FormItem
              label={accountChannelNameMapEnum[sortV]}
              name="accountNo"
              initialValue={doc && doc.accountNo}
              rules={[
                {
                  required: true,
                  whitespace: true,
                  message: i18n.get(`请输入{__k0}`, { __k0: accountChannelNameMapEnum[sortV] }),
                },
                { validator: sortV === 'ALIPAY' ? this.checkAliPayNo : this.checkBankNo },
              ]}>
              <Input
                data-testid="pay-customPaymentAccountNo-input"
                placeholder={i18n.get(`请输入{__k0}`, { __k0: accountChannelNameMapEnum[sortV] })}
                onBlur={this.handleAccountNoBlur}
              />
            </FormItem>
          ) : (
            <FormItem
              label={accountChannelNameMapEnum[sortV]}
              name="accountNo"
              initialValue={this.initAcceptanceBillValue(doc)}
              rules={[
                {
                  required: true,
                  whitespace: true,
                  message: i18n.get(`请输入{__k0}`, { __k0: accountChannelNameMapEnum[sortV] }),
                },
              ]}>
              <Select className="select" placeholder={i18n.get('请选择企业账号（户口号）')} data-testid="pay-customPaymentAcceptanceBill-select">
                {customerRegNumberList?.map((item: any) => {
                  return (
                    <Option key={item.id} value={item.id}>
                      {item.name}
                    </Option>
                  )
                })}
              </Select>
            </FormItem>
          ))}

        {sortV === 'OVERSEABANK' && (
          <FormItem
            label={i18n.get('银行名称(Bank Name)')}
            name="bankName"
            initialValue={doc && doc.bankName}
            rules={[
              {
                required: this.props.KA_FOREIGN_ACCOUNT,
                whitespace: true,
                message: i18n.get('银行名称(Bank Name)'),
              },
            ]}>
            <Input placeholder={i18n.get('银行名称(Bank Name)')} data-testid="pay-customPaymentBankName-input" />
          </FormItem>
        )}
        {sortV === 'OVERSEABANK' && (
          <FormItem
            label={i18n.get('银行国际代码(Swift Code)')}
            name="swiftCode"
            initialValue={doc && doc.swiftCode}
            rules={[
              { required: true, whitespace: true, message: i18n.get('银行国际代码(Swift Code)') },
              {
                validator: (_: any, value: any, callback: any) => {
                  if (!value || value.length === 8 || value.length === 11) {
                    return callback()
                  }
                  return callback('只能输入8位或11位')
                },
              },
            ]}>
            <Input placeholder={i18n.get('银行国际代码(Swift Code)')} data-testid="pay-customPaymentSwiftCode-input" />
          </FormItem>
        )}
        {sortV === 'OVERSEABANK' && (
          <FormItem
            label={i18n.get('银行所在地区代码(Nation Code)')}
            name="nationCode"
            initialValue={doc && doc.nationCode}
            rules={[
              {
                required: true,
                whitespace: true,
                message: i18n.get('银行所在地区代码(Nation Code)'),
              },
            ]}>
            <Select
              style={{ width: '100%' }}
              showSearch
              optionFilterProp="children"
              getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
              placeholder={i18n.get('请选择银行所在地区代码(Nation Code)')}
              data-testid="pay-customPaymentNationCode-select">
              {cityCodeList.map((item: any) => (
                <Option key={item.key} value={item.key}>
                  {item.value}
                </Option>
              ))}
            </Select>
          </FormItem>
        )}
        {sortV === 'OVERSEABANK' && (
          <FormItem
            label={i18n.get('联行号(Bank Code)')}
            name="bankCode"
            initialValue={doc && doc.bankCode}
            rules={[{ whitespace: true, message: i18n.get('联行号(Bank Code)')}]}>
            <Input placeholder={i18n.get('联行号(Bank Code)')} data-testid="pay-customPaymentBankCode-input" />
          </FormItem>
        )}
        {sortV === 'OVERSEABANK' && (
          <FormItem
            label={i18n.get('支行号(Branch Code)')}
            name="branchCode"
            initialValue={doc && doc.branchCode}
            rules={[{ whitespace: true, message: i18n.get('支行号(Branch Code)')}]}>
            <Input placeholder={i18n.get('支行号(Branch Code)')} data-testid="pay-customPaymentBranchCode-input" />
          </FormItem>
        )}
        {sortV === 'BANK' && (
          <FormItem
            className="branch"
            label={this.renderBranchLabel('银行网点', 'branch')}
            name="branch"
            initialValue={doc && (doc?.branchId?.name || doc?.branch)}
            rules={[
              {
                required: true,
                whitespace: true,
                message: i18n.get('请输入至少6个汉字的银行网点'),
              },
            ]}>
            <SelectBranchTypeField
              accountBranch={accountBranch}
              onSearchBankList={this.onSearchBankList}
              onChangeBrunch={this.onChangeBrunch}
              bankList={bankList}
              handleAdvancedSearch={this.handleAdvancedSearch}
              data-testid="pay-customPaymentBranch-select"
            />
          </FormItem>
        )}
        <FormItem
          className="remark"
          label={this.renderBranchLabel('备注信息', 'remark')}
          name="remark"
          initialValue={doc && doc.remark}
          rules={[
            { required: false, whitespace: true, message: i18n.get('请输入备注信息') },
            { max: 140, message: i18n.get('备注信息不能超过140个字') },
          ]}>
          <Input.TextArea placeholder={i18n.get('请输入备注信息')} data-testid="pay-customPaymentRemark-input" />
        </FormItem>
        <FormItem
          className="channels"
          colon={false}
          label={<span className="form-content-group-title mt-16">{i18n.get('支付方式')}</span>}
          name="channels"
          initialValue={doc.channels ? doc.channels : ['OFFLINE']}>
          <SelectPaymentThirdMethod
            defaultChannel={
              doc.defaultChannel === defaultChannel ? doc.defaultChannel : defaultChannel
            }
            channels={targetList}
            walletHide={true}
            onChange={this.handleChannelChange}
            onSetDefault={this.onSetDefault}
          />
        </FormItem>
        {sortV === 'BANK' && doc?.channels?.includes('CMBCBS8OVS') && (
          <>
            <FormItem
              label={i18n.get('汇款人省/州')}
              name={['extensions', 'sendProvince']}
              initialValue={doc && doc.extensions?.sendProvince}
              rules={[
                { required: true, whitespace: true, message: i18n.get('请输入汇款人省/州') },
                {
                  max: 35,
                  message: i18n.get('不能超过{__k0}个文字', { __k0: 35 }),
                },
              ]}>
              <Input placeholder={i18n.get('请输入汇款人省/州')} />
            </FormItem>
            <FormItem
              label={i18n.get('汇款人城镇/市')}
              name={['extensions', 'sendCity']}
              initialValue={doc && doc.extensions?.sendCity}
              rules={[
                { required: true, whitespace: true, message: i18n.get('请输入汇款人城镇/市') },
                {
                  max: 35,
                  message: i18n.get('不能超过{__k0}个文字', { __k0: 35 }),
                },
              ]}>
              <Input placeholder={i18n.get('请输入汇款人城镇/市')} />
            </FormItem>
            <FormItem
              label={i18n.get('汇款人街道名')}
              name={['extensions', 'sendStreet']}
              initialValue={doc && doc.extensions?.sendStreet}
              rules={[
                { required: true, whitespace: true, message: i18n.get('请输入汇款人街道名') },
                {
                  max: 35,
                  message: i18n.get('不能超过{__k0}个文字', { __k0: 70 }),
                },
              ]}>
              <Input placeholder={i18n.get('请输入汇款人街道名')} />
            </FormItem>
          </>
        )}
        {this.renderOverseaFieldForHSBC(sortV, doc)}
        <FormItem
          label={<span className="form-content-group-title mt-16">{i18n.get('可见性')}</span>}
          name="visibility"
          initialValue={
            (doc.visibility && visibilityMap[doc.visibility.fullVisible]) || visibleValue
          }>
          <RadioGroup className="account-type" onChange={this.onVisibleChange}>
            <div>
              <Radio value="ALL" data-testid="pay-customPaymentVisibility-all-radio">
                <T name="全员可见" />
              </Radio>
              <Radio value="SECTION" data-testid="pay-customPaymentVisibility-section-radio">
                <T name="部分可见" />
              </Radio>
            </div>
            {visibleValue === 'SECTION' && (
              <div className="currency-select mt-8">
                <StaffSelect
                  multiple
                  includeSubWrapperStyle={{ marginTop: '8px' }}
                  showIncludeChildren={true}
                  className="subject-select"
                  placeholder={i18n.get('请选择人员、角色(职级)或部门')}
                  value={visibility ?? doc.visibility}
                  tagShowStyle={{ width: '100%' }}
                  onValueChange={this.handleValueChange}
                  data-testid="pay-customPaymentStaff-select"
                />
              </div>
            )}
          </RadioGroup>
        </FormItem>
        {currency && (
          <>
            <FormItem
              className="currency-item"
              colon={false}
              label={null}
              name="recevingCurrencyEnable"
              initialValue={recevingCurrencyEnable}>
              <>
                <div
                  style={{ display: 'flex', alignItems: 'center' }}
                  className="form-content-group-title mt-16">
                  <span className="mr-8">{i18n.get('允许选择支付币种')}</span>
                  <Switch
                    checked={recevingCurrencyEnable}
                    size="small"
                    onChange={() => {
                      this.form.current?.setFieldsValue({
                        recevingCurrencyEnable: !recevingCurrencyEnable,
                      })
                      this.setState({ recevingCurrencyEnable: !recevingCurrencyEnable })
                    }}
                    data-testid="pay-customPaymentCurrencyEnable-switch"
                  />
                </div>
                <div className="currency-tips">
                  {i18n.get(
                    '未开启，付款账户不支持修改币种，默认法人实体本位币，开启后，支付确认时可在限定范围内选择实际付款币种。',
                  )}
                </div>
                {recevingCurrencyEnable && (
                  <div className="currency-select mt-8">
                    <RadioGroup
                      value={recevingCurrencyScope?.fullVisible ? 'ALL' : 'PART'}
                      onChange={this.onCurrencyEnableChange}
                      data-testid="pay-customPaymentCurrencyRange-radioGroup">
                      <Radio value="ALL" data-testid="pay-customPaymentCurrency-all-radio">
                        <T name="全部币种" />
                      </Radio>
                      <Radio value="PART" data-testid="pay-customPaymentCurrency-part-radio">
                        <T name="部分币种" />
                      </Radio>
                    </RadioGroup>
                    {!recevingCurrencyScope?.fullVisible && (
                      <>
                        <Select
                          className={classNames('mt-8', { 'red-color-border': needValueFlag })}
                          placeholder={i18n.get('请选择')}
                          mode="multiple"
                          showArrow
                          tagRender={this.tagRender}
                          value={recevingCurrencyScope?.currencyIds}
                          style={{ width: '100%' }}
                          onChange={this.handleCurrencyChange}
                          filterOption={(input: string, option: any) =>
                            option?.props?.item?.strCode
                              ?.toLowerCase()
                              ?.indexOf(input.toLowerCase()) >= 0 ||
                            option?.props?.item?.name
                              ?.toLowerCase()
                              ?.indexOf(input.toLowerCase()) >= 0
                          }
                          data-testid="pay-customPaymentCurrency-select">
                          {allCurrencyList.map((item: any) => (
                            <Option
                              key={item?.numCode}
                              value={item?.numCode}
                              item={item}
                              className="option-wrap">
                              <div className="currency-option">
                                <div className="left">
                                  <img className="currency-img" src={item?.icon} />
                                </div>
                                <div className="right">
                                  <div className="name">{item?.name}</div>
                                  <div className="code">{`代码：${item?.strCode}（${item?.numCode}）`}</div>
                                </div>
                              </div>
                            </Option>
                          ))}
                        </Select>
                        {needValueFlag && (
                          <div className="red-color-tips">{i18n.get('币种不能为空')}</div>
                        )}
                      </>
                    )}
                  </div>
                )}
              </>
            </FormItem>
          </>
        )}
        {showBankCustodianAccountCheckBox && (
          <FormItem
            className="currency-item"
            colon={false}
            label={null}
            name="bankCustodianAccount"
            initialValue={bankCustodianAccount}>
            <>
              <div
                style={{ display: 'flex', alignItems: 'center' }}
                className="form-content-group-title mt-16">
                <span className="mr-8">{i18n.get('是否为银行托管账户')}</span>
                <Switch
                  checked={bankCustodianAccount}
                  size="small"
                  onChange={() => {
                    this.form.current?.setFieldsValue({
                      bankCustodianAccount: !bankCustodianAccount,
                    })
                    this.setState({ bankCustodianAccount: !bankCustodianAccount })
                  }}
                  data-testid="pay-customPaymentCustodianAccount-switch"
                />
              </div>
              <div className="currency-tips">
                {i18n.get(
                  '勾选后，支付时可以选择普通划拨支付方式，生成的付款申请单将出现在CBS8至普通划拨分类中目前仅支持招商银行CBS8平台下的招商银行账户。',
                )}
              </div>
            </>
          </FormItem>
        )}
        <UniversalComponent uniqueKey={`${Universal_Unique_Key}.accountSet`}>
          <FormItem
            label={
              <span className="form-content-group-title mt-16">{i18n.get('支付摘要配置')}</span>
            }
            style={{ marginTop: 16 }}
            name="configType"
            initialValue={configType}>
            <RadioGroup style={{ width: '100%' }} onChange={this.setPayerConfig} data-testid="pay-customPaymentConfigType-radioGroup">
              <Radio value="GLOBAL" className="radio visible-radio" checked data-testid="pay-customPaymentConfigType-global-radio">
                <T name="使用全局配置" />
              </Radio>
              <Radio value="PRIVATE" className="radio visible-radio radio-left-text" data-testid="pay-customPaymentConfigType-private-radio">
                <T name="单独配置" />
                <span
                  style={{ color: 'var(--eui-text-placeholder)', font: 'var(--eui-font-body-r1)' }}>
                  {i18n.get('（超出银行摘要的限定长度，将自动截取）')}
                </span>
              </Radio>
              {configType === 'PRIVATE' && (
                <div className="currency-select mt-8">
                  <PayerRemarkConfig
                    ref="remarkConfig"
                    byHand={byHand}
                    useSpecial={useSpecial}
                    dimensionField={dimensionField}
                    dimensionId={dimensionId}
                    autoSummary={autoSummary}
                    fieldId={fieldId}
                    handleCheck={this.handleCheck}
                  />
                </div>
              )}
            </RadioGroup>
          </FormItem>
        </UniversalComponent>

        {isChanPay && (
          <FormItem name="advancedOption" initialValue={advancedOption}>
            <ExtendSetting onChange={this.advancedOptionChange} />
          </FormItem>
        )}
      </Form>
    )
  }

  render() {
    const { state, active, formChanpaySuccessModal = false } = this.props
    if (!this.props.visible) {
      return false
    }
    return (
      <div id={'custom-payment_companyAccountForm'} className="create-account-modal">
        <div className="form-content" ref={(node) => (this.formContent = node)}>
          {this.renderCreateAccount()}
        </div>
        {this.permission?.create && (
          <div className="account-footer">
            <Space>
              <Button data-testid="pay-customPaymentSave-button" key="ok" onClick={this.handleOK}>
                <T name="保存" />
              </Button>
              {state === 'edit' && !formChanpaySuccessModal && (
                <Button
                  category="secondary"
                  theme={active ? 'danger' : 'highlight'}
                  onClick={this.onRestoreOrDisable}
                  data-testid="pay-customPaymentEnable-button">
                  {active ? i18n.get('停用') : i18n.get('启用')}
                </Button>
              )}
              <Button key="cancel" category="secondary" onClick={this.handleCancel} data-testid="pay-customPaymentCancel-button">
                <T name="取消" />
              </Button>
            </Space>
          </div>
        )}
      </div>
    )
  }
}
