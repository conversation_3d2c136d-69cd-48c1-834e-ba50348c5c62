/**
 * Created by <PERSON><PERSON> on 2019/7/30.
 */
import { app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { Select, Checkbox, Space } from '@hose/eui'
import { T } from '@ekuaibao/i18n'
import { get } from 'lodash'
const commonActions = api.invokeServiceAsLazyValue('@common:import:action')
const Option = Select.Option

export default class PayerRemarkConfig extends PureComponent<any> {
  state = {
    dimensions: [],
    fieldList: [],
  }

  componentDidMount() {
    const orgId = get(this.props, 'orgData.orgId')
    api.invokeService(`@custom-specification:get:dimensions`, orgId).then((res: any) => {
      this.setState({ dimensions: res.items })
    })
    api.dispatch(commonActions.value.getBaseDataProperties()).then((res: any) => {
      const fieldList: any[] = []
      res.items.forEach((it: any) => {
        if (it && it.dataType.type == 'text') {
          fieldList.push(it)
        }
      })
      this.setState({ fieldList })
    })
  }
  render() {
    const {
      from,
      byHand,
      useSpecial,
      handleCheck,
      dimensionField,
      dimensionId,
      autoSummary,
      fieldId,
      useLegalEntityAccount,
    } = this.props
    const { dimensions, fieldList } = this.state
    return (
      <Space direction="vertical" style={{ width: '100%' }}>
        <Checkbox checked={byHand} onChange={(e) => handleCheck('byHand', e)} name="byHand" data-testid="pay-payerRemark-byHand-checkbox">
          <T name="手工填写" />
        </Checkbox>
        <div style={{ marginLeft: 24, display: 'flex', alignItems: 'center' }}>
          <Checkbox
            checked={useSpecial}
            onChange={(e) => handleCheck('useSpecial', e)}
            name="useSpecial"
            data-testid="pay-payerRemark-useSpecial-checkbox">
            <T name="必须使用特定字段" />
          </Checkbox>
          <Select
            style={{ flex: 1, marginRight: 8 }}
            onChange={(e) => handleCheck('dimensionId', e)}
            placeholder={i18n.get('请选择引用的档案')}
            value={dimensionId === '' || dimensionId === null ? undefined : dimensionId}
            data-testid="pay-payerRemark-dimensionId-select">
            {dimensions.map((v: any) => (
              <Option key={v.name} value={v.name}>
                {v.name}
              </Option>
            ))}
          </Select>
          <span>
            <T name="的" />
          </span>
          <Select
            style={{ width: 120, marginLeft: 8 }}
            placeholder={i18n.get('请选择')}
            value={dimensionField === '' || dimensionField === null ? undefined : dimensionField}
            onChange={(e) => handleCheck('dimensionField', e)}
            data-testid="pay-payerRemark-dimensionField-select">
            <Option value="NAME">{i18n.get('名称')}</Option>
            <Option value="CODE">{i18n.get('编码')}</Option>
          </Select>
          <span
            ref="userSpecialWarn"
            style={{
              display: 'none',
              color: 'var(--eui-function-danger-500)',
              marginLeft: '8px',
            }}>
            {i18n.get('请选择')}
          </span>
        </div>

        <div style={{ widows: '100%', display: 'flex', alignItems: 'center' }}>
          <Checkbox
            checked={autoSummary}
            onChange={(e) => handleCheck('autoSummary', e)}
            name="autoSummary"
            data-testid="pay-payerRemark-autoSummary-checkbox">
            {i18n.get('自动填写，来自单据字段')}
          </Checkbox>
          <Select
            placeholder={i18n.get('请选择单据字段')}
            onChange={(e) => handleCheck('fieldId', e)}
            style={{ flex: 1 }}
            value={fieldId === '' || fieldId === null ? undefined : fieldId}
            id="field"
            data-testid="pay-payerRemark-fieldId-select">
            {fieldList.map((v: any) => (
              <Option key={v.name} value={v.name}>
                {v.label}
              </Option>
            ))}
          </Select>
          <span
            ref="fieldWarn"
            style={{
              display: 'none',
              color: 'var(--eui-function-danger-500)',
              marginLeft: '8px',
            }}>
            {i18n.get('请选择')}
          </span>
        </div>
        {from === 'globalSetting' && (
          <Checkbox
            checked={useLegalEntityAccount}
            onChange={(e) => handleCheck('useLegalEntityAccount', e)}
            name="useLegalEntityAccount"
            data-testid="pay-payerRemark-useLegalEntityAccount-checkbox">
            <T name="发起支付时，若相关法人实体配置的付款账户为空则无可用支付账户" />
          </Checkbox>
        )}
      </Space>
    )
  }
}
