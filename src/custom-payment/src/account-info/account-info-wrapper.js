import { app as api } from '@ekuaibao/whispered'
import React, { Component } from 'react'
import { Card, Row, Col } from 'antd'
const Money = api.require('@elements/puppet/Money')

import actions from '../custom-payment-action'

export default class AccountInfoWrapper extends Component {
  constructor(props) {
    super(props)
    this.state = {
      accountInfo: props.accountInfo,
      payMoneyBig: 0
    }
  }

  componentDidMount() {
    api.dispatch(actions.getWalletBalance(this.state.accountInfo.id)).then(result => {
      this.setState({
        payMoneyBig: result && Number(result.balance)
      })
    })
  }

  render() {
    let { accountInfo, payMoneyBig } = this.state
    let corpInfo = accountInfo.corpInfo
    return (
      <div className="account-content">
        <Card className="mb-20" title={i18n.get('联系人')} style={{ width: '100%' }} noHovering={true}>
          <Row className="row pt-10 pb-10">
            <Col className="label" span={6}>
              {i18n.get('姓名')}
            </Col>
            <Col span={18}>{corpInfo.contactor}</Col>
          </Row>
          <Row className="row pt-10 pb-10">
            <Col className="label" span={6}>
              {i18n.get('联系手机')}
            </Col>
            <Col span={18}>{corpInfo.contactorMobile}</Col>
          </Row>
          <Row className="row pt-10 pb-10">
            <Col className="label" span={6}>
              {i18n.get('联系邮箱')}
            </Col>
            <Col span={18}>{corpInfo.email}</Col>
          </Row>
        </Card>
        <Card className="mb-20" title={i18n.get('账户资金')} style={{ width: '100%' }} noHovering={true}>
          <Row className="row pt-10 pb-10">
            <Col className="label" span={6}>
              {i18n.get('主体名称')}
            </Col>
            <Col span={18}>{corpInfo.companyName}</Col>
          </Row>
          <Row className="row pt-10 pb-10">
            <Col className="label" span={6}>
              {i18n.get('可用余额')}
            </Col>
            <Col span={18}>
              <Money currencySize={12} valueSize={14} color="#545454" value={payMoneyBig.toFixed(2)} />
            </Col>
          </Row>
        </Card>
        <Card className="mb-20" title={i18n.get('结算账户')} style={{ width: '100%' }} noHovering={true}>
          <Row className="row pt-10 pb-10">
            <Col className="label" span={6}>
              {i18n.get('开户名称')}
            </Col>
            <Col span={18}>{corpInfo.electronicAccountName}</Col>
          </Row>
          <Row className="row pt-10 pb-10">
            <Col className="label" span={6}>
              {i18n.get('电子账户')}
            </Col>
            <Col span={18}>{corpInfo.electronicAccount}</Col>
          </Row>
          <Row className="row pt-10 pb-10">
            <Col className="label" span={6}>
              {i18n.get('开户行')}
            </Col>
            <Col span={18}>{corpInfo.electronicAccountBank}</Col>
          </Row>
        </Card>
      </div>
    )
  }
}
