import { Select } from '@hose/eui'
import React, { FC } from 'react'
import { T } from '@ekuaibao/i18n'

interface Props {
  accountBranch: string
  onSearchBankList: (searchKey: string) => void
  onChangeBrunch: (value: string) => void
  bankList: any[]
  value?: string
  handleAdvancedSearch: () => void
}
const SelectBranchTypeField: FC<Props> = (props: Props) => {
  const { accountBranch, onSearchBankList, onChangeBrunch, bankList, value, handleAdvancedSearch } =
    props
  return (
    <>
      <Select
        data-testid="pay-customPaymentBranch-select"
        id="branch"
        className="select"
        getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
        showSearch
        value={value}
        placeholder={accountBranch}
        defaultActiveFirstOption={false}
        filterOption={false}
        showArrow={true}
        onSearch={onSearchBankList}
        onChange={onChangeBrunch}
        notFoundContent={null}>
        {bankList.map((d: any) => (
          <Select.Option data-testid={`pay-customPaymentBranchOption-${d.name}`} key={d.id}>{d.name}</Select.Option>
        ))}
      </Select>
      <span className="dis-f">
        <T name="找不到所需网点? 试试" />
        <span className="filter-text" onClick={handleAdvancedSearch} data-testid="pay-customPaymentBranch-advanced-search">
          <T name="条件搜索" />
        </span>
      </span>
    </>
  )
}
export default SelectBranchTypeField
