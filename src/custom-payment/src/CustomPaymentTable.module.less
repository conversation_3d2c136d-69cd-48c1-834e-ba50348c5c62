.commonWrapper {
  padding: 16px;
  background-color: var(--eui-bg-filler);
  flex: 1;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  :global {
    .tableWrapper {
      background-color: white;
      border-radius: 8px;
      margin-top: 8px;
      display: flex;
      overflow: hidden;
      flex-direction: column;
      flex: 1;
      .tableTop {
        padding: 16px 16px 12px;
        display: flex;
        flex-direction: row;
        font: var(--eui-font-head-b1);
        color: var(--eui-text-title);
        align-items: center;
        justify-content: space-between;
      }
      .tableContent {
        width: 100%;
        display: flex;
        flex-direction: column;
        padding: 0 16px 16px;
        overflow: hidden;
        .eui-pro-card-body {
          padding: 0 !important;
        }
        .bankInfo_table_remark {
          overflow: hidden; /* 隐藏溢出内容 */
          text-overflow: ellipsis; /* 用省略号表示溢出内容 */
          display: -webkit-box; /* 使用 WebKit 的盒模型 */
          -webkit-box-orient: vertical; /* 垂直排列子元素 */
          -webkit-line-clamp: 2; /* 限制显示的行数为 2 */
        }
        .eui-badge-dot {
          width: 6px;
          height: 6px;
          margin-bottom: 2px;
        }
        .bankInfo_table_item {
          display: flex;
          flex-direction: row;
          align-items: center;
          img {
            width: 24px;
            height: 24px;
            margin-right: 4px;
          }
          .bankInfo_table_item_name {
            color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
            font: var(--eui-font-body-r1);
          }
          .bankInfo_table_item_num {
            margin-top: 2px;
            color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
            font: var(--eui-font-body-r1);
          }
        }
      }
      .tabs-top-no-padding {
        .eui-tabs-nav .eui-tabs-nav-wrap {
          padding: 0;
        }
      }
      .table-row-click {
        :hover {
          cursor: pointer;
        }
      }
      .table-row-disabled {
        color: var(--eui-text-disabled) !important;
        .__person-wrapper {
          color: var(--eui-text-disabled) !important;
        }
      }
    }
  }
}
