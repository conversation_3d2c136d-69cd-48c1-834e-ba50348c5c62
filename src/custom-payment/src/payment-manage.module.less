.custom-payment {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100%;
  background-color: #ffffff;

  :global {
    .ekb-tab-content {
      display: flex;
      > div {
        height: auto;
      }
    }

    .ant-tabs-tabpane {
      height: 100%;
    }

    .filter-bar {
      padding: 15px 20px;
      font-size: 12px;
      flex-shrink: 0;
    }

    .account-content {
      display: flex;
      flex-direction: column;
      flex: 1;
      width: 100%;
      background-color: #ffffff;
      padding: 20px 15px;
      font-size: 14px;
      .ant-card-head {
        background-color: #f7f7f7;
      }
      .label {
        color: #8c8c8c;
      }
    }
  }
}

.records-operation-view {
  .custom-payment;
}
