import styles from './payment-manage.module.less'
import React, { PureComponent } from 'react'
import { app as api } from '@ekuaibao/whispered'
import AccountBankWrapper from './elements/account-bank-wrapper'
import classNames from 'classnames'

export default class AccountManageWrapper extends PureComponent {
  constructor(props) {
    super(props)
  }

  componentWillMount() {
    api.dataLoader('@common.staffs').load()
  }

  render() {
    const { searchComponent = null } = this.props
    return (
      <div className={classNames('account-manage-wrapper', styles['custom-payment'])}>
        {searchComponent}
        <AccountBankWrapper {...this.props} />
      </div>
    )
  }
}
