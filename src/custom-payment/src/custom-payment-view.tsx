import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { EnhanceStackerManager } from '@ekuaibao/enhance-stacker-manager'
import MessageCenter from '@ekuaibao/messagecenter'
import { showModal, showMessage } from '@ekuaibao/show-util'
import React, { PureComponent } from 'react'
import { Button } from 'antd'
const EKBIcon = api.require('@elements/ekbIcon')
import './custom-payment-style.less'
import actions from './custom-payment-action'
import { ID } from './key'
import classNames from 'classnames'
import { fnCheckAntAlipayBindStateFormEKB } from './custom-payment-fetch-util'
import { provider, inject } from '@ekuaibao/react-ioc'
import { observer } from 'mobx-react'
import { PermissionVm } from './vms/Permission.vm'
import { getV } from '@ekuaibao/lib/lib/help'
import { Fetch } from '@ekuaibao/fetch'
import { Universal_Unique_Key } from './index'
import { trackCustomPayment } from './lib/fetchUtil'

const ButtonGroup = Button.Group

const SearchInput = api.require('@elements/search-input') as any
const EKBBreadcrumb = api.require('@ekb-components/business/breadcrumb') as any
const Breadcrumb = api.require('@elements/ekbc-basic/breadcrumb') as any
const { UniversalComponent } = api.require('@elements/UniversalComponent')

interface Props {
  stackerManager?: any
  setState: (data: any) => {}
  payAccountListCount: number
}

interface States {
  page: string
  pageCurrent: number
  searchText: string
  isWalletManager: boolean
  hideFooter: boolean
}

@EnhanceConnect(
  (state: any) => ({
    payAccountListCount: state['@common'].newPayAccount.count,
    WalletPower: state['@common'].powers.Wallet,
    channelList: state['@audit'].channelList,
    ALIPAY_switch: state['@common'].powers.ALIPAY_switch,
  }),
  null,
  `${ID}/storage`,
)
@EnhanceStackerManager([
  {
    key: 'PaymentPage',
    getComponent: () => import('./account-manage-wrapper'),
    title: i18n.get('付款账户'),
  },
  {
    key: 'WalletManage',
    getComponent: () => import('./payment-manage-wrapper'),
    title: i18n.get('账户管理'),
  },
])
@provider(['permission', PermissionVm])
@observer
export default class CustomPaymentView extends PureComponent<Props, States> {
  @inject('permission') permission?: PermissionVm
  private bus: MessageCenter
  private searchComponent: any
  constructor(props: any) {
    super(props)
    this.bus = props.bus || new MessageCenter()
    this.state = {
      page: '',
      pageCurrent: 1,
      searchText: '',
      isWalletManager: false,
      hideFooter: false,
    }
    this.searchComponent = (
      <div className="right-buttons">
        <SearchInput
          className="search"
          placeholder={i18n.get('搜索开户名称、备注名称、编码或备注')}
          onSearch={this.handleSearch}
          onChange={this.handleSearchInputChange}
          data-testid="pay-customPayment-search-input"
        />
      </div>
    )
  }

  __handleInsertAssist = (title: string) => {
    api.invokeService('@common:insert:assist:record', {
      title,
    })
  }

  componentWillMount() {
    api.invokeService('@common:get:bank:channels')
    this.bus.on('payment:handle:edit:account', this.handleEditAccount) //编辑账户
    this.bus.on('payment:handle:create:wallet', this.handleCreateWallet) //创建钱包
    this.bus.on('payment:handle:detail:wallet', this.handleDetailWallet) //查看钱包详情
    this.bus.on('payment:handle:edit:wallet', this.handleEditWallet) //编辑钱包
    this.bus.on('payment:push:stacker', this.handlePushStacker)
    this.bus.watch('payment:save:click', this.handleSaveAccount) //保存付款账户
  }

  componentDidMount() {
    this.props.setState({ searchText: '' })
    this.getPayAccountList().then(() => {
      this.handleActionPushStacker()
    })
    const data = api.getState()['@common'].userinfo.data
    const permissions = getV(data, 'permissions', [])
    let isManager = permissions.filter((o) => o === 'CORP_WALLET_MANAGE')
    if (isManager && isManager.length > 0) {
      this.setState({ isWalletManager: true })
    }
    api.invokeService('@common:get:mc:permission:byName', 'ACCOUNT_PAY').then((result: any) => {
      this.permission?.initData(result.value)
    })

    trackCustomPayment('custom_payment_view')
  }

  componentWillUnmount() {
    this.bus.un('payment:handle:edit:account', this.handleEditAccount)
    this.bus.un('payment:handle:create:wallet', this.handleCreateWallet)
    this.bus.un('payment:handle:detail:wallet', this.handleDetailWallet)
    this.bus.un('payment:handle:edit:wallet', this.handleEditWallet)
    this.bus.un('payment:push:stacker', this.handlePushStacker)
    this.bus.un('payment:save:click', this.handleSaveAccount) //保存付款账户
  }

  getPayAccountList(data?: any) {
    return api.invokeService('@common:get:newpay:accounts', data)
  }

  handlePushStacker = (key: string, props: any) => {
    this.props.stackerManager.push(key, { bus: this.bus, ...props })
    this.setState({ page: key })
  }

  clickWalletHideFooter = (hideFooter: boolean) => {
    this.setState({ hideFooter })
  }

  handleActionPushStacker = () => {
    const { isWalletManager } = this.state
    this.props.stackerManager.clear()
    this.props.stackerManager.push('PaymentPage', {
      bus: this.bus,
      searchComponent: this.searchComponent,
      isWalletManager,
      clickWalletHideFooter: this.clickWalletHideFooter,
    })
    this.setState({ page: 'PaymentPage' })
  }

  handleStackerItemClick = (line: any, i: number) => {
    const { isWalletManager } = this.state
    this.clickWalletHideFooter(false)
    this.props.stackerManager.open(i, {
      bus: this.bus,
      searchComponent: this.searchComponent,
      isWalletManager,
      clickWalletHideFooter: this.clickWalletHideFooter,
    })
    this.setState({ page: line.key })
  }

  handleCreateAccount = () => {
    api.open('@custom-payment:CreateAccountFormPopup', { bus: this.bus ,title: i18n.get('新建企业付款账户')})
  }

  handleSaveAccount = (result: any) => {
    const { data, state } = result
    const dataId = data.id //actions.putPayment执行后，data里的id会消失
    if (state === 'edit') {
      api.dispatch(actions.putPayment(data)).then(
        () => {
          this.resetPageCurAndRefush()
          this.fnBindAlipay(data, dataId)
          this.__handleInsertAssist(`修改${data.accountName}付款账户`) // @i18n-ignore
        },
        (err: any) => {
          showMessage.error(err.message)
        },
      )
    } else {
      api.dispatch(actions.postPayment(data)).then(
        (res: any) => {
          this.getPayAccountList()
          this.fnBindAlipay(data, res.id)
          this.__handleInsertAssist(`创建${data.accountName}付款账户`) // @i18n-ignore
        },
        (err: any) => {
          showMessage.error(err.message)
        },
      )
    }
  }

  fnBindAlipay = (data: any, id: string) => {
    //确认支付宝绑定状态
    if (data.channels.includes('ANTALIPAY')) {
      fnCheckAntAlipayBindStateFormEKB(id)
    }
  }

  handleEditAccount = (item: any) => {
    api.open('@custom-payment:CreateAccountFormPopup', {
      ...item,
      state: 'edit',
      bus: this.bus,
      title: i18n.get('编辑企业付款账户'),
      onRestoreOrDisable: this.handleStopAccount,
    })
    this.__handleInsertAssist(`查看${item.accountName}付款账户`) // @i18n-ignore
  }

  resetPageCurAndRefush = () => {
    this.setState({ pageCurrent: 1 })
    this.getPayAccountList()
  }

  handleStopAccount = (item: any, checked: boolean) => {
    if (!checked) {
      showModal.confirm({
        title: i18n.get(`{__k0}：`, { __k0: i18n.get('停用') }) + item.name,
        content: i18n.get('停用后该账户在支付时不可见'),
        okText: i18n.get('确定'),
        cancelText: i18n.get('取消'),
        onOk: () => {
          api.dispatch(actions.disable({ id: item.id })).then(() => this.resetPageCurAndRefush())
          this.__handleInsertAssist(`停用${item.accountName}付款账户`) // @i18n-ignore
        },
      })
    } else {
      api.dispatch(actions.restore({ id: item.id })).then(() => this.resetPageCurAndRefush())
      this.__handleInsertAssist(`启用${item.accountName}付款账户`) // @i18n-ignore
    }
  }

  handleCreateWallet = () => {
    api.open('@custom-payment:CompanyWalletForm').then(
      () => {
        showModal.success({
          title: i18n.get('您的资料已经提交'),
          content: i18n.get('银行需要1-3个工作日审核，请耐心等待'),
        })
        this.resetPageCurAndRefush()
      },
      (err) => {
        showMessage.error(err.message)
      },
    )
  }

  handleDetailWallet = () => {
    api.open('@custom-payment:CompanyWalletForm', {
      current: 2,
    })
  }

  handleEditWallet = (item: any, _e: any) => {
    api
      .open('@custom-payment:CompanyWalletForm', {
        current: 1,
        item: item,
      })
      .then((data: any) => {
        data.id = item.id
        api.dispatch(actions.postWalletOpen(data)).then(() => {
          this.resetPageCurAndRefush()
        })
      })
  }

  handleSearch = (value: any) => {
    //save to @custom-payment/searchText node by EnhanceConnnect
    this.props.setState({ searchText: value })
  }

  handleSearchInputChange = (e: any) => {
    this.props.setState({ searchText: e.target.value })
  }
  handleExport = () => {
    let ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
    let { menu, searchText, isShowActive, isFilterPayment } = this.props.state
    let filterStr = encodeURIComponent(
      this.fnPayerListFilter({ menu, searchText, isShowActive, isFilterPayment }),
    )
    let filter = filterStr ? `filter=${filterStr}` : ''
    const nameLike =
      searchText && searchText !== '企业' ? `&nameLike=${encodeURIComponent(searchText)}` : '' // @i18n-ignore
    let exportUrl = ''
    Fetch.GET(`/api/pay/v2/accounts/exportWay?${filter}${nameLike}`).then((v) => {
      if (v.value.exportWay === 'async') {
        api.open('@layout:AsyncExportModal').then((res: any) => {
          Fetch.GET(`/api/pay/v2/accounts/export/excel/async?${filter}${nameLike}`, {
            taskName: res.taskName,
          })
        })
      } else {
        exportUrl = `${Fetch.fixOrigin(
          location.origin,
        )}/api/pay/v2/accounts/export/excel?corpId=${ekbCorpId}&${filter}${nameLike}`
      }
      api.emit('@vendor:download', exportUrl)
    })
    this.__handleInsertAssist(`导出付款账户`) // @i18n-ignore
  }

  handleImport = () => {
    api.open('@bills:ImportDetailByExcel', { type: 'payer' }).then((_) => {
      this.getPayAccountList()
      this.__handleInsertAssist(`导入付款账户`) // @i18n-ignore
    })
  }
  fnPayerListFilter({ menu, searchText, isShowActive, isFilterPayment }) {
    const hasSearchText = `${searchText === '企业' ? ' owner=="CORPORATION"' : null}` // @i18n-ignore
    return [
      `${menu !== 'ALL' && menu ? 'state ==' + menu : null}`,
      `${searchText ? hasSearchText : null}`,
      `${isShowActive ? null : 'active == 1'}`,
      `(asPayer==true) && sort != "WALLET"`,
      `${isFilterPayment ? '(bankLinkNo=="null" || bankLinkNo=="")' : null}`,
    ]
      .filter((o) => {
        return o !== 'null'
      })
      .join('&&')
  }

  renderBreadcrumb() {
    let array = this.props.stackerManager.values()
    let items = array.map((line: any, key: number) => ({
      key,
      title: line.title,
      onClick: () => {
        this.handleStackerItemClick(line, key)
      },
    }))
    // @ts-ignore
    return window.isNewHome ? (
      <EKBBreadcrumb immersiveable={true} items={items} />
    ) : (
      <Breadcrumb items={items} />
    )
  }
  renderSetBtn = () => {
    // @ts-ignore
    const { channelList = [] } = this.props
    const activeLength = channelList.filter((item) => item.needRemark && item.channel !== 'CHANPAY')
      .length
    const chanpay = channelList.find((item) => item.channel == 'CHANPAY') || {}
    const enabled = chanpay.active ? !chanpay.active : activeLength > 0
    //00
    return (
      <UniversalComponent uniqueKey={`${Universal_Unique_Key}.settingBtn`}>
        {enabled && (
          <EKBIcon
            className="cur-p stand-20-icon"
            name="#EDico-setting"
            onClick={this.handleSetBtn}
            data-testid="pay-customPayment-setting-icon"
          />
        )}
      </UniversalComponent>
    )
  }

  getPayerConfig = async (_) => {
    let config = await api.invokeService('@common:get:payer:shared')
    return config.value
  }

  handleSetBtn = async () => {
    let config = await this.getPayerConfig()
    let {
      id,
      byHand,
      dimensionId,
      dimensionField,
      autoSummary,
      fieldId,
      useSpecial,
      fieldLabel,
      useLegalEntityAccount
    } = config

    api
      .open('@custom-payment:PayerAccountSets', {
        id: id,
        useSpecial: useSpecial,
        byHand: byHand,
        dimensionId: dimensionId,
        dimensionField: dimensionField,
        autoSummary: autoSummary,
        fieldId: fieldId,
        fieldLabel: fieldLabel,
        useLegalEntityAccount
      })
      .then((data) => {
        api.dispatch(actions.savePayerConfig(data))
      })
    trackCustomPayment('custom_payment_setting')
  }

  pageChange = (page: number, pageSize: number) => {
    this.setState({ pageCurrent: page }, () => {
      this.getPayAccountList({
        asPayer: true,
        start: (page - 1) * pageSize,
        count: page * pageSize,
      })
    })
  }

  render() {
    const { hideFooter } = this.state
    return (
      <div
        id={'custom-payment'}
        className={`layout-select-wrap custom-payment ${
          window.isNewHome ? 'custom-payment-layout5' : ''
        }`}>
        <div className="header">
          <div className="menu">
            {this.renderBreadcrumb()}
            {this.renderSetBtn()}
          </div>
        </div>
        {this.props.children}
        {this.permission?.create && (
          <div className={classNames('footer-wrapper footer', { hideFooter })}>
            <div className="btn-wrapper">
              <Button type="primary" className="mr-8" onClick={this.handleCreateAccount} data-testid="pay-customPayment-create-button">
                {i18n.get('新建')}
              </Button>
              <UniversalComponent uniqueKey={`${Universal_Unique_Key}.importButtonGroup`}>
                <ButtonGroup className="btn-group">
                  <Button onClick={this.handleExport.bind(this)} data-testid="pay-customPayment-export-button">{i18n.get('导出')}</Button>
                  <Button onClick={this.handleImport.bind(this)} data-testid="pay-customPayment-import-button">{i18n.get('导入')}</Button>
                </ButtonGroup>
              </UniversalComponent>
              {/* <Pagination simple current={pageCurrent} total={payAccountListCount} onChange={this.pageChange} /> */}
            </div>
          </div>
        )}
      </div>
    )
  }
}
