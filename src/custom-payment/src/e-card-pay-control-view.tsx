import React, { Component } from 'react'
import { app } from '@ekuaibao/whispered'
// @ts-ignore
import { Keel, registerComponentsCellar } from '@ekuaibao/keel'
const KeelSingleViewHeader = app.require<any>('@elements/puppet/KeelSingleViewHeader')
const KeelViewBody = app.require<any>('@elements/puppet/KeelViewBody')
@registerComponentsCellar([
  {
    key: 'ECardPayControlViewList',
    getComponent: () => import('./pay-control/ECardPayControlViewList'),
    title: '',
  },
  {
    key: 'ECardPayControlViewSetting',
    getComponent: () => import('./pay-control/ECardPayControlViewSetting'),
    title: '',
  },
])
export default class ECardPayControlView extends Component {
  render() {
    return (
      <Keel>
        <KeelSingleViewHeader viewKey={'ECardPayControlViewList'} showHeader={false} />
        <KeelViewBody classNameKey="content-main" {...this.props} />
      </Keel>
    )
  }
}
