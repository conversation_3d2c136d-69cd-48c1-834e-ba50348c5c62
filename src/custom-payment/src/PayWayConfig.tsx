/**
 *  Created by pw on 2022/3/8 下午5:04.
 */
import React from 'react'
import { app } from '@ekuaibao/whispered'
import { T } from '@ekuaibao/i18n'
// @ts-ignore
import CMBCBC_PNG from './images/CMBCBC.png'
// @ts-ignore
import CMBCBC_BG_PNG from './images/CMBCBC_BG_PNG.png'
import './PayWayConfig.less'

export default function () {
  const data: IPayWayCard[] = [
    {
      channel: 'CMBCBC',
      bankIcon: CMBCBC_PNG,
      bankName: '招商云直连',
      actionName: '前往设置',
      actionColor: '#C7162E',
      powerCode: 150003,
      backBg: CMBCBC_BG_PNG,
      action: (payWayCard) => handleZS(payWayCard),
    },
  ]

  const handleZS = async (payWayCard: IPayWayCard) => {
    app.open('@expansion-center:AppSettingsModal', {
      powerCode: payWayCard.powerCode,
    })
  }

  return (
    <div className="pay-way-config-wrapper">
      <div className="pay-way-config-content">
        <div className="title-wrapper">
          <div className="title-wrapper-title">
            <T name={'支付方式配置'} />
          </div>
        </div>
        <div className="pay-way-config-list">
          {data.map((payWayCard) => {
            return <PayWayCard payWayCard={payWayCard} />
          })}
        </div>
      </div>
    </div>
  )
}

interface PayWayCardProps {
  payWayCard: IPayWayCard
}

interface IPayWayCard {
  channel: string
  bankIcon: string
  bankName: string
  actionName: string
  actionColor: string
  backBg: string
  powerCode: number
  action: (payWayCard: IPayWayCard) => void
}

const PayWayCard = (props: PayWayCardProps) => {
  const { payWayCard } = props
  return (
    <div className="pay-way-card-item">
      <img className="bank-bg" src={payWayCard.backBg} />
      <div className="left-wrapper">
        <img className="bank-img" src={payWayCard.bankIcon} />
        <div className="bank-name">{payWayCard.bankName}</div>
      </div>
      <div className="right-action">
        <div
          style={{ backgroundColor: payWayCard.actionColor }}
          className="action-setting"
          onClick={() => payWayCard.action(payWayCard)}
          data-testid="pay-payWayConfig-setting-button"
        >
          {payWayCard.actionName}
        </div>
      </div>
    </div>
  )
}
