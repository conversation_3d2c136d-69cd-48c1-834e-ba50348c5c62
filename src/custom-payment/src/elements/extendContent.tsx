import React, { ReactElement } from 'react'
import { InputNumber, Select, Input } from '@hose/eui'
// @ts-ignore
import styles from './extendContent.module.less'
import { isEqual, pick } from 'lodash'
import classNames from 'classnames'

interface MenuProps {
  value: string
  label: string
}

interface Filter {
  type: string
  min?: string
  max?: string
}

export interface IProps extends StringAnyProps {
  onChange?: (value: any) => any
  value?: IValue
}

export interface IValue extends StringAnyProps {
  filter: Filter
  meetPayMode: string
  noMeetPayMode?: string
  error?: string
}
export default class ExtendContent extends React.Component<IProps, IValue> {
  state: IValue = { filter: { type: 'DIRECT_PAYMENT' }, meetPayMode: 'DIRECT' }

  optionMenu: MenuProps[] = [
    { value: 'SCOPE', label: i18n.get('限定最小值、最大值') },
    { value: 'MAX', label: i18n.get('金额大于等于') },
    { value: 'MIN', label: i18n.get('金额小于等于') },
  ]

  optionError: any = {
    require: i18n.get('限定金额不能为空'),
    valueError: i18n.get('最小值不能超过最大值'),
  }

  payType: MenuProps[] = [
    { value: 'DIRECT_PAYMENT', label: i18n.get('银行直付') },
    { value: 'OTHER_PAYMENT', label: i18n.get('银行代发') },
  ]

  static getDerivedStateFromProps(nextProps: IProps, prevState: IValue) {
    const { value } = nextProps
    const vForState = pick(value, 'filter', 'meetPayMode', 'error')
    const vForPreState = pick(prevState, 'filter', 'meetPayMode', 'error')
    if (!isEqual(vForPreState, vForState)) {
      return { ...prevState, ...vForState }
    }
    return null
  }

  optionChange = (key: string) => {
    this.onChange({ filter: { type: key } })
  }

  payTypeChange = (key: string) => {
    const noMeetPayMode = key === 'DIRECT_PAYMENT' ? 'OTHER_PAYMENT' : 'DIRECT_PAYMENT'
    this.onChange({ meetPayMode: key, noMeetPayMode })
  }

  minChange = (value: number | string) => {
    // @ts-ignore
    const min: string = value === undefined || value === null ? value : value.toString()
    const {
      filter: { type, max },
    } = this.state
    const error = getError(type, max, min)
    this.onChange({ filter: { type, max, min }, error })
  }

  maxChange = (value: number | string): void => {
    // @ts-ignore
    const max: string = value === undefined || value === null ? value : value.toString()
    const {
      filter: { type, min },
    } = this.state
    const error = getError(type, max, min)

    this.onChange({ filter: { type, min, max }, error })
  }

  inputNumber = (params: any) => <InputNumber min={0} {...params} />

  onChange = (value: any) => {
    const { value: propsValue } = this.props
    this.props?.onChange?.(Object.assign(propsValue, value))
  }

  renderItem = (params: { label: string; subView: ReactElement | ReactElement[] }) => {
    const { label, subView } = params
    return (
      <div className="content-item">
        <div className="item-label">{label}</div>
        <div className="item-view">{subView}</div>
      </div>
    )
  }

  renderOption = () => {
    const { error } = this.state
    const isError = !!error
    const optErrorCls = classNames('option-value', { 'has-error': isError })
    const errorCls = classNames({ hide: !isError }, { 'err-show': isError })

    const subView = [
      <div className="option-wrapper" key="wrapper">
        <Select
          style={{ width: 180 }}
          value={this.state.filter.type}
          options={this.optionMenu}
          onChange={this.optionChange}
          data-testid="pay-custom-payment-condition-select"
        />
        <div className={optErrorCls} style={{ marginLeft: 8 }}>
          {this.getOptsValue(optErrorCls)}
        </div>
      </div>,
      <div className={errorCls} key="err-show">
        {isError && this.optionError[error]}
      </div>,
    ]
    return this.renderItem({
      label: i18n.get('当对私支付金额满足以下条件时：'),
      subView: subView,
    })
  }

  getOptsValue(optErrorCls: string) {
    const { filter } = this.state
    const { type, min, max } = filter
    const maxComp = this.inputNumber({
      placeholder: i18n.get('请输入最大值'),
      onChange: this.maxChange,
      value: max,
      className: optErrorCls,
      key: 'max',
      "data-testid": 'pay-custom-payment-max-amount'
    })
    const minComp = this.inputNumber({
      placeholder: i18n.get('请输入最小值'),
      onChange: this.minChange,
      value: min,
      className: optErrorCls,
      key: 'min',
      "data-testid": 'pay-custom-payment-min-amount'
    })

    if (type === 'SCOPE') {
      return [minComp, maxComp]
    } else if (type === 'MAX') {
      return maxComp
    }

    return minComp
  }

  getPayTitle(payType: string, reverse = true) {
    const typeData = this.payType.find((item) =>
      reverse ? item.value === payType : item.value !== payType,
    )
    return typeData.label
  }

  renderPayType = () => {
    return this.renderItem({
      label: i18n.get('则使用支付方式：'),
      subView: (
        <Select
          value={this.state.meetPayMode}
          options={this.payType}
          onChange={this.payTypeChange}
          data-testid="pay-custom-payment-pay-type-select"
        />
      ),
    })
  }

  renderOtherPayType = () => {
    const { meetPayMode } = this.state
    return this.renderItem({
      label: i18n.get('否则使用支付方式：'),
      subView: <Input disabled value={this.getPayTitle(meetPayMode, false)} />,
    })
  }

  render() {
    return (
      <div className={styles['content-wrapper']}>
        <div className="common">{i18n.get('高级选项备注')}</div>
        <div className="content-wrapper">
          {this.renderOption()}
          {this.renderPayType()}
          {this.renderOtherPayType()}
        </div>
      </div>
    )
  }
}

export function getError(rangeType: string, maxAccount: string, minAccount: string): string {
  const isMaxNaN = !maxAccount && maxAccount !== '0'
  const isMinNaN = !minAccount && minAccount !== '0'

  if (rangeType === 'SCOPE') {
    if (isMaxNaN || isMinNaN) {
      return 'require'
    }
    if (parseFloat(maxAccount) <= parseFloat(minAccount)) {
      return 'valueError'
    }
  } else if ((rangeType === 'MAX' && isMaxNaN) || (rangeType === 'MIN' && isMinNaN)) {
    return 'require'
  }

  return ''
}
