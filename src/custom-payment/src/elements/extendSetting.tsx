import React from 'react'
import { Space, Switch } from '@hose/eui'
import ExtendContent, { IValue, IProps } from './extendContent'

interface IState {
  checked: boolean
}

export default class ExtendSetting extends React.Component<IProps, IState, IValue> {
  preValue = {
    filter: {
      type: 'SCOPE',
    },
    meetPayMode: 'DIRECT_PAYMENT',
    noMeetPayMode: 'OTHER_PAYMENT',
  }
  static getDerivedStateFromProps(nextProps: IProps, prevState: IState) {
    const { value } = nextProps
    const checked = Boolean(value)
    if (checked !== prevState.checked) {
      return {
        checked,
      }
    }
    return null
  }

  state: IState = { checked: false }

  onChange = (checked: boolean) => {
    if (checked) {
      this.props?.onChange?.(this.preValue)
    } else {
      this.props?.onChange?.(null)
    }
  }

  render() {
    const { checked } = this.state

    return (
      <div className="mt-16">
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span className="form-content-group-title mr-8">{i18n.get('高级选项')}</span>
          <Switch size="small" checked={checked} onChange={this.onChange} data-testid="pay-custom-payment-extend-switch" />
        </div>
        {checked && <ExtendContent {...this.props} />}
      </div>
    )
  }
}
