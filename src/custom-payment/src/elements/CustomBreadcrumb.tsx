import React from 'react'
import { Breadcrumb } from '@hose/eui'
const BreadcrumbItem = Breadcrumb.Item
import { uuid } from '@ekuaibao/helpers'

// 面包屑路径项接口定义
export interface PathItem {
  name: string
  onClick?: () => void
}

// 自定义面包屑组件属性接口
interface IProps {
  path: PathItem[]
}

// 自定义面包屑组件，用于显示页面导航路径
const CustomBreadcrumb: React.FC<IProps> = ({ path }) => {
  // 渲染面包屑导航
  return (
    <Breadcrumb>
      {path.map((item, index) => {
        const { name, onClick } = item
        const itemId = `pay-custom-payment-breadcrumb-item-${index}`

        return (
          <BreadcrumbItem key={uuid(8)}>
            {onClick ? (
              <a onClick={onClick} data-testid={itemId}>
                {i18n.get(name)}
              </a>
            ) : (
              <span>{i18n.get(name)}</span>
            )}
          </BreadcrumbItem>
        )
      })}
    </Breadcrumb>
  )
}

export default CustomBreadcrumb
