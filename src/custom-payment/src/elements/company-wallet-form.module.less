.company-wallet-modal {
  :global {
    .group-name {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 10px;
      padding-top: 15px;
      color: #6c6c6c;
    }
    .border-top-line {
      border-top: 1px solid #f2f2f2;
    }
    .upload-tips {
      margin-top: 30px;
      color: gray;
    }
    .modal-footer {
      justify-content: flex-start;
    }

    .wallet-form-wrapper {
      padding: 0 40px 20px 40px;
    }
    .inl-block {
      display: inline-block;
    }
    .step-content {
      border-radius: 6px;
      min-height: 200px;
      height: 450px;
      overflow-y: auto;
      h2,
      p {
        text-align: center;
      }
    }
    .steps-content {
      .step-content;
      padding-top: 10px;
    }
    .steps3-content {
      .step-content;
      padding-top: 80px;
    }
    .text-gray {
      color: #959898;
    }
    .avatar-uploader,
    .avatar {
      width: 120px;
      height: 120px;
    }
    .avatar-uploader {
      display: block;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      text-align: center;
      margin: 8px 0px;
      padding-top: 25px;
      .avatar {
        margin-top: -25px;
      }
      .upload-text {
        padding: 0px 10px;
        line-height: 17pt;
        font-size: 12px;
        color: #666;
      }
    }
    .avatar-uploader-trigger {
      font-size: 28px;
      color: #999;
    }

    .btn-disabled {
      background-color: #bbbdbd;
      border-color: #bbbdbd;
    }

    .required-label label:before {
      display: inline-block;
      margin-right: 4px;
      content: '*';
      line-height: 1;
      font-size: 12px;
      color: #ff7c7c;
    }
  }
}
