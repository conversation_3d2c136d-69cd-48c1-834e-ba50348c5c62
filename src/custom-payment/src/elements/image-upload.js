import React, { Component } from 'react'
import { Form, message, Upload, Icon } from 'antd'

const FormItem = Form.Item
const mimeWhiteList = ['image/jpg', 'image/jpeg']

// 将图片转换为base64格式
function getBase64(img) {
  return new Promise((resolve, reject) => {
    try {
      const reader = new FileReader()
      reader.addEventListener('load', () => resolve(reader.result))
      if (img) {
        reader.readAsDataURL(img)
      } else {
        resolve('')
      }
    } catch (err) {
      reject(err)
    }
  })
}

// 检查上传文件是否符合要求
function checkFileStream(file) {
  const isJPG = !!~mimeWhiteList.indexOf(file.type)
  if (!isJPG) {
    message.error(i18n.get('请选择.jpg格式的图片上传'))
    return false
  }

  const isLt800K = file.size / 1024 / 1024 < 0.8
  if (!isLt800K) {
    message.error(i18n.get('上传图片不能超过800KB'))
    return false
  }

  return true
}

// 图片上传组件，用于上传和预览图片

export default class ImageUpload extends Component {
  // 构造函数，初始化组件状态
  constructor(props) {
    super(props)
    this.state = {}
  }

  // 处理文件上传变化事件
  handleChange = async info => {
    const {
      name,
      form: { setFieldsValue }
    } = this.props
    let checked = checkFileStream(info.file)
    if (checked) {
      let imageUrl = await getBase64(info.file)
      this.setState({ imageUrl: imageUrl })
      setFieldsValue({
        [name]: imageUrl
      })
    } else {
      let imageUrl = await getBase64('')
      this.setState({ imageUrl: imageUrl })
      setFieldsValue({
        [name]: imageUrl
      })
    }
  }

  // 上传前处理函数，阻止自动上传
  beforeUpload() {
    return false
  }

  // 渲染图片上传组件
  render() {
    const {
      name,
      help,
      require,
      uploadText,
      form: { getFieldDecorator }
    } = this.props
    return (
      <FormItem>
        {getFieldDecorator(name, {
          rules: [{ required: require, message: help }]
        })(
          <Upload
            className="avatar-uploader"
            name={name}
            showUploadList={false}
            beforeUpload={this.beforeUpload}
            onChange={this.handleChange}
          >
            {this.state.imageUrl ? (
              <img 
                src={this.state.imageUrl} 
                style={{ borderRadius: 6 }} 
                alt="上传图片"
                className="avatar"
                data-testid={`pay-custom-payment-image-preview-${name}`}
              />
            ) : (
              <div data-testid={`pay-custom-payment-image-uploader-${name}`}>
                <Icon type="plus" className="avatar-uploader-trigger" data-testid={`pay-custom-payment-image-upload-icon-${name}`} />
                <div className="upload-text">{uploadText}</div>
              </div>
            )}
          </Upload>
        )}
      </FormItem>
    )
  }
}
