import './account-yeepay-wrapper.less'
import React, { Component } from 'react'
import MessageCenter from '@ekuaibao/messagecenter'
import { app as api } from '@ekuaibao/whispered'
import actions from '../custom-payment-action'
import { EnhanceConnect } from '@ekuaibao/store'
import { showModal } from '@ekuaibao/show-util'
@EnhanceConnect(state => ({
  YeepayInfo: state['@custom-payment'].YeepayInfo
}))
// 易宝支付账户页面组件，用于展示和管理易宝支付账户

export default class AccountYeepayPage extends Component {
  constructor(props) {
    super(props)
    this.bus = props.bus || new MessageCenter()
    this.state = {
      isbind: false
    }
  }

  // 组件挂载后刷新账户信息
  componentDidMount() {
    this.refresh(null)
  }

  // 处理账户绑定点击事件
  handleActionClick = async () => {
    const result = await api.open('@custom-payment:BindingYeepayFrom', { ...this.props })
    api.dispatch(actions.postYeepayBinding(result)).then(() => {
      this.refresh()
    })
  }
  
  // 刷新账户信息
  refresh = () => {
    api.dispatch(actions.getYeepayInfo()).then(res => {
      this.setState({
        isbind: !!res.value
      })
    })
  }
  
  // 渲染未绑定状态
  binding = () => {
    return (
      <div className="left-part">
        <div className="account-infoBox">
          <div className="account-name">
            <span className={'fs-14 fw-500'}>{i18n.get('账户余额')}</span>
            <div className="color-gray">-- --</div>
          </div>
        </div>
        <div>
          <a 
            onClick={this.handleActionClick}
            data-testid="pay-custom-payment-yeepay-bind-button"
          >{i18n.get('账户绑定')}</a>
        </div>
      </div>
    )
  }
  
  // 下载账户对账单
  downAccountStatement = () => {
    api.emit('@vendor:open:link', 'https://www.yeepay.com/selfservice/login.action')
  }
  
  // 处理余额充值
  Recharge = () => {
    api.emit('@vendor:open:link', 'https://www.yeepay.com/selfservice/login.action')
    showModal.success({
      title: i18n.get('请在新打开的页面上完成充值'),
      content: () => {
        return <div>{i18n.get('充值完成前请不要关闭此窗口')}</div>
      },
      onOk: () => {
        this.refresh()
      }
    })
  }
  
  // 渲染已绑定状态
  refreshView = () => {
    const { YeepayInfo } = this.props
    return (
      <div className="left-part">
        <div className="account-infoBox">
          <div className="account-name">
            <span className={'fs-14 fw-500'}>{i18n.get('账户余额')}</span>
            <div className="color-gray">{YeepayInfo.value.amount}</div>
          </div>
        </div>
        <div className="handle-view">
          <a 
            onClick={this.refresh}
            data-testid="pay-custom-payment-yeepay-refresh-button"
          >{i18n.get('刷新')}</a>
          <span>|</span>
          <a 
            onClick={this.downAccountStatement}
            data-testid="pay-custom-payment-yeepay-download-button"
          >{i18n.get('对账单下载')}</a>
          <span>|</span>
          <a 
            onClick={this.Recharge}
            data-testid="pay-custom-payment-yeepay-recharge-button"
          >{i18n.get('余额充值')}</a>
        </div>
      </div>
    )
  }
  
  // 渲染组件主界面
  render() {
    return (
      <div className="body">
        <div className={'account-wrapper-yeepay'}>{this.state.isbind ? this.refreshView() : this.binding()}</div>
      </div>
    )
  }
}
