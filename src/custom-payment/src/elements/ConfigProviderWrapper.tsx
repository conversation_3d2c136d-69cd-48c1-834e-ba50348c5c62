import React from 'react'
import { ConfigProvider } from '@hose/eui'
import { Fetch } from '@ekuaibao/fetch'
import enUS from '@hose/eui/es/locale/en_US'
import zhCN from '@hose/eui/es/locale/zh_CN'
import { ConfigTheme } from '@hose/eui-theme'

// 确定当前语言环境
const euiLocalValue = Fetch.lang === 'en-US' ? enUS : zhCN

// 组件属性接口
interface IProps {
  children: React.ReactNode
}

// 从本地存储获取主题颜色配置
const initTheme = JSON.parse(localStorage.getItem('theme-web') || '{}').color

// 初始化EUI主题配置
const euiTheme = new ConfigTheme()

// 配置Hose主题EUI样式
const initHoseThemeEUI = (color?: string) => {
  euiTheme.config({ prefix: 'eui', platform: 'eui', brandColor: color })
}

// 应用初始化主题配置
initHoseThemeEUI(initTheme)

// 配置提供者包装组件，用于为整个应用提供EUI配置和国际化支持
const ConfigProviderWrapper: React.FC<IProps> = (props) => {
  // 渲染配置提供者组件，传递当前语言环境和子组件
  return (
    <ConfigProvider locale={euiLocalValue}>
      {props.children}
    </ConfigProvider>
  )
}

export default ConfigProviderWrapper
