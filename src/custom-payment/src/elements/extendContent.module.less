.content-wrapper {
  margin-top: 8px;

  :global {
    .common {
      color: var(--eui-text-placeholder, rgba(29, 33, 41, 0.5));
      font: var(--eui-font-body-r1);
    }
    .content-wrapper {
      border-radius: 6px;
      margin-top: 8px;
      background: var(--eui-bg-float-base, #f2f3f5);
      padding: 8px;

      .content-item {
        margin-top: 8px;
        display: flex;
        flex-direction: row;
        align-items: center;
        .item-label {
          color: var(--eui-text-title);
        }
        .item-view {
          flex: 1;
        }
        &:first-child {
          margin-top: 0;
        }
      }

      .option-wrapper {
        display: flex;
        flex-direction: row;
        align-items: center;

        .option-value {
          flex: 1;
          display: flex;

          div {
            flex: 1;
          }

          div:nth-child(2) {
            margin-left: 8px;
          }

          &.has-error {
            border-color: var(--eui-function-danger-500);
          }
        }
      }

      .hide {
        display: none;
      }

      .err-show {
        color: var(--eui-function-danger-500);
        line-height: 1.5;
      }
    }
  }
}
