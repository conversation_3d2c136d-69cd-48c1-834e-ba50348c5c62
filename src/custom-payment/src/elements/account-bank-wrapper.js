import { app } from '@ekuaibao/whispered'
import './account-bank-wrapper.less'
import React, { Component } from 'react'
import MessageCenter from '@ekuaibao/messagecenter'
import { EnhanceConnect } from '@ekuaibao/store'
import { searchPayment } from '../lib/paymentUtils'
import { ID } from '../key'
import EMPTY_ICON from '../images/empty-icon.svg'
const AccountListItem = app.require('@elements/payee-account/account-list-item')
const EmptyBody = app.require('@bills/elements/EmptyBody')

@EnhanceConnect(state => ({
  payAccountList: state['@common'].newPayAccount.list,
  searchText: state[ID].storage && state[ID].storage.searchText,
  staffs: state['@common'].staffs,
  roles: state['@common'].roleList,
  WalletPower: state['@common'].powers.Wallet,
  departmentTree: state['@common'].department.data,
  dynamicChannelMap: state['@audit'].dynamicChannelMap
}))
// 银行账户页面组件，用于展示和管理银行账户列表
export default class AccountBankPage extends Component {
  constructor(props) {
    super(props)
    this.bus = props.bus || new MessageCenter()
    this.state = { list: this.fnSortList(props.payAccountList) }
  }

  // 对账户列表进行排序，钱包账户优先显示
  fnSortList = list => {
    const { WalletPower } = this.props
    if (!WalletPower) return list
    const walletAccounts = list.filter(el => el.sort === 'WALLET')
    if (walletAccounts.length) {
      const otherAccounts = list.filter(el => el.sort !== 'WALLET')
      return walletAccounts.concat(otherAccounts)
    }
    return list
  }

  // 处理props更新，重新排序账户列表
  componentWillReceiveProps(nextProps) {
    if (nextProps.payAccountList !== this.props.payAccountList) {
      this.setState({ list: this.fnSortList(nextProps.payAccountList) })
    }
  }

  // 处理编辑账户
  handleEdit = item => {
    this.bus.emit('payment:handle:edit:account', item)
  }

  // 创建钱包账户
  createWallet = () => {
    this.bus.emit('payment:handle:create:wallet')
  }

  // 处理编辑钱包账户
  handleEditWallet(item) {
    this.bus.emit('payment:handle:edit:wallet', item)
  }

  // 处理点击查看账户详情
  handleClick(v) {
    this.bus.emit('payment:push:stacker', 'WalletManage', { accountInfo: v })
  }

  // 处理钱包账户点击事件
  handleWalletClick = v => {
    const { clickWalletHideFooter } = this.props
    const state = v.state || ''
    switch (state) {
      case 'PENDING':
        this.handleEditWallet(v)
        break
      case 'PROCESSED':
        break
      case 'SUCCESS':
        this.handleClick(v)
        clickWalletHideFooter && clickWalletHideFooter(true)
        break
      default:
        this.createWallet()
        break
    }
  }

  // 处理卡片点击事件，阻止冒泡
  handleCardClick = e => {
    e.stopPropagation()
    e.preventDefault()
  }

  // 渲染空状态
  renderEmpty() {
    return (
      <EmptyBody label = {i18n.get('没有找到相应的结果')}/>
    )
  }

  // 渲染组件主界面
  render() {
    let { searchText, isWalletManager, dynamicChannelMap } = this.props
    let { list: payAccountList } = this.state
    if (searchText) {
      payAccountList = searchPayment(payAccountList, searchText)
    }
    if (!payAccountList.length) {
      return <div className="body">{this.renderEmpty()}</div>
    }
    payAccountList = payAccountList.filter((v) => v.sort !== 'WALLET')
    
    return (
      <div className={`body pb-80`}>
        {payAccountList.map((v, idx) => {
          return (
            <AccountListItem
              onClick={e => {
                this.handleCardClick(e)
                this.handleEdit(v)
              }}
              handleWalletClick={e => {
                this.handleCardClick(e)
                this.handleWalletClick(v)
              }}
              key={v.id + idx}
              isManangePage
              formChannel="pay"
              data={v}
              isWalletManager={isWalletManager}
              dynamicChannelMap={dynamicChannelMap}
              data-testid={`pay-custom-payment-account-item-${v.id}`}
            />
          )
        })}
      </div>
    )
  }
}
