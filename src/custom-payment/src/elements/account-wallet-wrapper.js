import { app as api } from '@ekuaibao/whispered'
import React, { Component } from 'react'
const EkbHighLighter = api.require('@elements/EkbHighLighter')
import classnames from 'classnames'
import MessageCenter from '@ekuaibao/messagecenter'
import { EnhanceConnect } from '@ekuaibao/store'
import { ID } from '../key'
import { searchPayment } from '../lib/paymentUtils'
const EmptyBody = app.require('@bills/elements/EmptyBody')
import EMPTY_ICON from '../images/empty-icon.svg'

@EnhanceConnect(state => ({
  payAccountList: state['@common'].payAccount.list,
  searchText: state[ID].storage.searchText
}))
// 钱包账户页面组件，用于展示和管理企业钱包账户

export default class AccountWalletPage extends Component {
  constructor(props) {
    super(props)
    this.bus = props.bus || new MessageCenter()
    this.state = {
      isManager: false
    }
  }

  // 组件挂载时检查用户是否有管理权限
  componentWillMount() {
    let data = api.getState()['@common'].userinfo.data
    let isManager = data.permissions.filter(o => o === 'CORP_WALLET_MANAGE')
    if (isManager.length > 0) {
      this.setState({ isManager: true })
    }
  }

  // 创建钱包账户
  createWallet = () => {
    this.bus.emit('payment:handle:create:wallet')
  }

  // 处理编辑钱包账户
  handleEditWallet(item) {
    this.bus.emit('payment:handle:edit:wallet', item)
  }

  // 处理点击查看账户详情
  handleClick(v) {
    this.bus.emit('payment:push:stacker', 'WalletManage', { accountInfo: v })
  }

  // 处理操作按钮点击事件
  handleActionClick = (v, e) => {
    e.preventDefault()
    e.stopPropagation()
    if (!this.state.isManager) {
      return
    }
    let state = v.detail ? v.detail.state : ''
    switch (state) {
      case 'PENDING':
        this.handleEditWallet(v)
        break
      case 'PROCESSED':
        break
      case 'SUCCESS':
        this.handleClick(v)
        break
      default:
        this.createWallet()
        break
    }
  }

  // 获取操作按钮状态文本
  getActionState = state => {
    switch (state) {
      case 'PENDING':
        return i18n.get('开通账户')
      case 'PROCESSED':
        return i18n.get('查看详情')
      case 'SUCCESS':
        return i18n.get('管理')
      default:
        return i18n.get('开通账户')
    }
  }

  // 渲染钱包账户项
  renderWalletAccount = v => {
    let { isManager } = this.state
    let { searchText } = this.props
    let state = v.detail.state
    let action = this.getActionState(state)
    return (
      <div
        key={v.id}
        className={classnames('account-wrapper', { disabled: !isManager })}
        onClick={this.handleActionClick.bind(this, v)}
        data-testid={`pay-custom-payment-wallet-item-${v.id}`}
      >
        <div className="left-part">
          <div className="account-infoBox">
            <div className="account-name">
              <span
                className={(!v.active && state === 'PROCESSED') || state === 'PENDING' ? 'color-gray' : 'fs-14 fw-500'}
              >
                <EkbHighLighter highlightClassName="highlight" searchWords={[searchText]} textToHighlight={v.name} />
              </span>
              <span className="color-gray fs-12">
                {i18n.get('（编码：')}
                {<EkbHighLighter highlightClassName="highlight" searchWords={[searchText]} textToHighlight={v.code} />}
                {i18n.get('）')}
              </span>
              {state === 'PENDING' ||
                (state === 'FAILURE' && <span className="color-gray"> {i18n.get('(未开通)')}</span>)}
              {state === 'PROCESSED' && <span className="color-gray"> {i18n.get('(开通中)')}</span>}
            </div>
          </div>
          {state !== 'PROCESSED' && (
            <a
              style={
                isManager
                  ? {}
                  : {
                      cursor: 'not-allowed',
                      color: 'gray',
                      textDecoration: 'none',
                      userSelect: 'none'
                    }
              }
              onClick={this.handleActionClick.bind(this, v)}
              data-testid={`pay-custom-payment-wallet-action-${v.id}`}
            >
              {action}
            </a>
          )}
        </div>
      </div>
    )
  }

  // 渲染开通钱包项
  renderOpenWalletItem = () => {
    let { isManager } = this.state
    return (
      <div
        onClick={this.handleActionClick.bind(this, { state: '' })}
        className={classnames('account-wrapper', { disabled: !isManager })}
        data-testid="pay-custom-payment-wallet-create"
      >
        <div className="left-part">
          <div className="account-infoBox">
            <div className="account-name">
              <span className={'fs-14 fw-500'}>{i18n.get('企业钱包')}</span>
              <span className="color-gray"> {i18n.get('(未开通)')}</span>
            </div>
          </div>
          <div>
            {isManager && (
              <a 
                onClick={this.handleActionClick.bind(this, { state: '' })}
                data-testid="pay-custom-payment-wallet-create-button"
              >{i18n.get('开通账户')}</a>
            )}
          </div>
        </div>
      </div>
    )
  }

  // 渲染空状态
  renderEmpty() {
    return (
      <EmptyBody label = {i18n.get('没有找到相应的结果')}/>
    )
  }

  // 渲染主体内容
  renderBody = () => {
    const { payAccountList, searchText } = this.props
    let list = payAccountList.filter((o) => o.sort === 'WALLET')
    const isOpened = list.length > 0
    if (searchText) {
      list = searchPayment(list, searchText)
    }

    if (isOpened) {
      return list.length > 0
        ? list.map((account) => this.renderWalletAccount(account))
        : this.renderEmpty()
    } else {
      return this.renderOpenWalletItem()
    }
  }

  // 渲染组件主界面
  render() {
    return <div className="body">{this.renderBody()}</div>
  }
}
