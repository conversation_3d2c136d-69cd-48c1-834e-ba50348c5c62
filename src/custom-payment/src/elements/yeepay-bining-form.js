import { app } from '@ekuaibao/whispered'
import React, { Component } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import styles from '../employee/elements/WalletOpenAgreementView.module.less'
import { Form, Button, Icon, Input } from 'antd'
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')
const { Item } = Form
@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer',
  maskClosable: false
})
@EnhanceFormCreate()
// 易宝支付绑定表单组件，用于绑定易宝支付商户信息

export default class YeepayBiningForm extends Component {
  // 构造函数，初始化组件并设置获取结果方法
  constructor(props) {
    super(props)
    props.overrideGetResult(this.getResult)
  }

  // 获取表单结果数据
  getResult = () => {
    const { form } = this.props
    return form.getFieldsValue()
  }

  // 处理确定按钮点击事件，验证表单并提交
  handleOk = () => {
    this.props.form.validateFieldsAndScroll((errors, _values) => {
      if (!errors) {
        return this.props.layer.emitOk()
      }
    })
  }

  // 处理取消按钮点击事件
  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  // 验证商户编码格式
  checkCode = (_rule, value, callback) => {
    if (/^BM\d{14}$/.test(value)) {
      return callback()
    }
    callback(i18n.get('请输入正确的商户编码'))
  }

  // 验证商户名称是否与支付账户名称重复
  checkName = (_rule, value, callback) => {
    if (!this.props.payAccountList.find(val => val.name === value)) {
      return callback()
    }
    callback(i18n.get('商户名称不能和支付账户名称相同'))
  }

  // 渲染组件主界面
  render() {
    const { getFieldDecorator } = this.props.form
    return (
      <div>
        <div className="modal-header">
          <div className="title flex">{i18n.get('绑定易宝支付')}</div>
          <Icon className="cross-icon" type="cross" onClick={() => this.props.layer.emitCancel()} data-testid="pay-custom-payment-yeepay-bind-close" />
        </div>
        <div className="content" style={{ margin: '20px' }}>
          <Form>
            <Item label={i18n.get('商户编码')} {...layout}>
              {getFieldDecorator('code', {
                rules: [
                  { required: true, whitespace: true, message: i18n.get('商户编码不能为空') },
                  { validator: this.checkCode }
                ]
              })(<Input placeholder={i18n.get('请输入商户编码')} data-testid="pay-custom-payment-yeepay-code" />)}
            </Item>
            <Item label={i18n.get('商户名称')} {...layout}>
              {getFieldDecorator('name', {
                rules: [
                  { required: true, whitespace: true, message: i18n.get('商户名称不能为空') },
                  { validator: this.checkName }
                ]
              })(<Input placeholder={i18n.get('请输入商户名称')} data-testid="pay-custom-payment-yeepay-name" />)}
            </Item>
          </Form>
        </div>
        <div className={styles.footerflex}>
          <Button key="cancel" size="large" className="mr-20" onClick={this.handleCancel} data-testid="pay-custom-payment-yeepay-cancel">
            {i18n.get('取消')}
          </Button>
          <Button type="primary" onClick={this.handleOk} data-testid="pay-custom-payment-yeepay-confirm">
            {i18n.get('确定')}
          </Button>
        </div>
      </div>
    )
  }
}
