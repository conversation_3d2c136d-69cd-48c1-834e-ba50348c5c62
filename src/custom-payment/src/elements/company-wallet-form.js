import { app as api } from '@ekuaibao/whispered'
import styles from './company-wallet-form.module.less'
import { Form, Input, Button, DatePicker, Icon, Tooltip, Row, Col } from 'antd'

import { EnhanceConnect } from '@ekuaibao/store'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
const EnhanceFormCreate = api.require('@elements/enhance/enhance-form-create')
const SelectBankField = api.require('@elements/payment-form-field/select-bank-field')
const SelectAreaField = api.require('@elements/payment-form-field/select-area-field')
const SelectBranchField = api.require('@elements/payment-form-field/select-branch-field')
import ImageUpload from './image-upload'
import React from 'react'
import { fnCompareProps } from '@ekuaibao/lib/lib/lib-util'
import { debounce } from 'lodash'

const FormItem = Form.Item

const formItemLayout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 18 }
}

@EnhanceConnect(state => ({
  bankList: state['@common'].bankList,
  provinceList: state['@common'].provinceList,
  cityList: state['@common'].cityList,
  branchList: state['@common'].branchList
}))
@EnhanceModal({
  title: '',
  footer: [],
  keyboard: false,
  className: 'ekb-custom-modal'
})
@EnhanceFormCreate()
// 企业钱包表单组件，用于创建和编辑企业钱包账户

export default class CompanyWalletForm extends React.PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      data: {},
      values: {},
      bankList: [],
      loading: false
    }
    this.province = undefined
    this.city = undefined
    this.currentBank = undefined
    this.getResult = this.getResult.bind(this)
    props.overrideGetResult(this.getResult)
  }

  // 组件挂载后初始化数据和获取银行列表
  componentDidMount() {
    let { data } = this.props
    let item = (data && data.item) || {}
    item && item.id && delete item.id
    this.setInitData(item)
    api.invokeService('@common:get:banks').then(bankList => {
      this.setState({ bankList })
    })
    api.invokeService('@common:get:bank:provinces')
    api.invokeService('@common:get:powers') //刷新当前企业已购买的服务
  }

  // 处理props更新，重新设置初始数据
  componentWillReceiveProps(nextProps) {
    const fn = fnCompareProps(this.props, nextProps)
    fn('data', data => {
      this.setInitData(data)
    })
  }

  // 设置初始表单数据
  setInitData(data) {
    if (data) {
      if (data.detail) {
        this.getInitDataList(data.detail)
        let area = {
          province: data.detail.province,
          city: data.detail.city
        }
        data.detail.area = JSON.stringify(area)
      }
      this.setState({
        data
      })
    } else {
      this.setState({
        data: {}
      })
    }
  }

  // 获取初始数据列表
  getInitDataList(detail) {
    return api
      .invokeService('@common:get:banks', {
        filter: `name.contains("${detail.bank}")`
      })
      .then(bank => {
        this.currentBank = bank[0] || {}
        let obj = {}
        obj['detail.branch'] = ''
        this.props.form.setFieldsValue(obj)
        api.invokeService('@common:get:bank:provinces').then(province => {
          this.province = province.find(v => v.name === detail.province)
          api
            .invokeService('@common:get:bank:cites', {
              provId: this.province.id
            })
            .then(city => {
              this.city = city.find(v => v.name === detail.city)
              api
                .invokeService('@common:get:bank:branches', {
                  bankCode: this.currentBank.code,
                  provId: this.province.id,
                  cityId: this.city.cityId
                })
                .then(_branchList => {
                  const result = {}
                  result['detail.branch'] = detail.branch
                  this.currentBranch = detail.branch
                  this.props.form.setFieldsValue(result)
                })
            })
        })
      })
      .catch(_error => {
        return null
      })
  }

  // 获取表单结果数据
  getResult() {
    return this.state.values
  }

  // 处理提交按钮点击事件
  handleOK = () => {
    this.props.form.validateFieldsAndScroll((errors, values) => {
      if (!!errors) return
      let { detail } = values
      values.expiresEnd = values.expiresEnd.format('YYYY-MM-DD')
      const val = JSON.parse(JSON.stringify(values))
      if (detail) {
        let areaObj = JSON.parse(detail.area)
        val.bankCardProvince = areaObj.province
        val.bankCardCity = areaObj.city
        val.branch = detail.branch
        delete detail.area
        delete val.detail
      }
      this.setState({ values: val, loading: true })
      api.invokeService('@custom-payment:create:wallet', val).then(
        () => {
          this.setState({ loading: false })
          this.props.layer.emitOk()
        },
        () => {
          this.setState({ loading: false })
        }
      )
    })
  }

  // 处理取消按钮点击事件
  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  // 渲染法人姓名标签
  renderUserName() {
    return (
      <div className="inl-block">
        <span>{i18n.get('法人姓名')}</span>
        <Tooltip
          title={i18n.get(
            '按照营业执照上填写，如果属于分公司则填写工商营业执照上明确的负责人，个体工商户请填写经营者姓名，合伙企业请填写合伙人姓名，个人独资企业请填写投资人姓名，企业法人的非法人分支机构填写负责人姓名。'
          )}
        >
          <Icon style={{ fontSize: '14px', color: '#9e9e9e' }} className="ml-5" type="question-circle-o" />
        </Tooltip>
      </div>
    )
  }

  // 处理银行搜索
  handleSearch = debounce((value) => {
    api
      .invokeService('@common:get:banks', {
        filter: `name.contains("${value}")`,
        start: 0,
        count: 50,
      })
      .then((bankList) => {
        this.setState({ bankList: bankList })
      })
  }, 300)

  // 处理银行选择变化
  handleBankChange = value => {
    this.currentBank = this.state.bankList.find(o => o.name == value) || {}
    this.getBranchList()
  }

  // 处理地区选择变化
  handleAreaChange = (area, flg) => {
    let areaObj = JSON.parse(area)
    let { provinceList, cityList } = this.props
    if (flg === 'province') {
      this.province = provinceList.find(o => o.name === areaObj.province)
      this.getCityList()
      this.getBranchList()
    } else {
      this.city = cityList.find(o => o.name === areaObj.city)
      this.getBranchList()
    }
  }

  // 获取城市列表
  getCityList() {
    this.province && api.invokeService('@common:get:bank:cites', { provId: this.province.id })
  }

  // 获取银行网点列表
  getBranchList() {
    let obj = {}
    obj['detail.branch'] = ''
    this.currentBranch && this.props.form.setFieldsValue(obj)
    if (this.province && this.city && this.currentBank) {
      api.invokeService('@common:get:bank:branches', {
        provId: this.province.id,
        cityId: this.city.cityId,
        bankCode: this.currentBank.code
      })
    }
  }

  // 处理银行网点选择变化
  handleBranchChange(value) {
    this.currentBranch = value
  }

  // 处理协议重定向点击事件
  handleRedirectProtocol(e) {
    e.preventDefault()
    e.stopPropagation()
    api.emit('@vendor:open:link', 'http://cn.mikecrm.com/UI3uaSo')
  }

  // 验证身份证号码
  checkIdCard = (_rule, value, callback) => {
    let regString = '^(\\d{18}|\\d{15}|\\d{17}[x|X])$'
    let reg = new RegExp(regString)
    if (!reg.test(value)) return callback(i18n.get('身份证号长度必须为15位或18位'))
    callback()
  }

  // 渲染钱包表单内容
  renderWalletForm = () => {
    let doc = this.state.data
    let detail = doc.detail || {}
    let area = this.props.form.getFieldValue('detail.area') || detail.area
    let areaObj = area ? JSON.parse(area) : {}
    let city = areaObj.city
    const {
      form,
      form: { getFieldDecorator, getFieldValue }
    } = this.props
    return (
      <div className="steps-content">
        <Form horizontal>
          <div className={'group-name'}>{i18n.get('企业信息')}</div>
          <FormItem
            {...formItemLayout}
            label={i18n.get('企业全称')}
            extra={i18n.get('只支持中国大陆工商局或市场监督管理局登记的企业')}
          >
            {getFieldDecorator('companyName', {
              initialValue: detail.companyName,
              rules: [
                { required: true, whitespace: true, message: i18n.get('请输入企业全称') },
                { max: 50, message: i18n.get('企业名称不能超过50个字符') }
              ]
            })(<Input disabled={false} placeholder={i18n.get('请输入工商营业执照上的企业全称')} data-testid="pay-custom-payment-company-name" />)}
          </FormItem>
          <FormItem {...formItemLayout} label={this.renderUserName()}>
            {getFieldDecorator('egalPerson', {
              initialValue: detail.egalPerson,
              rules: [
                { required: true, whitespace: true, message: i18n.get('请输入姓名') },
                { max: 50, message: i18n.get('姓名不能超过50个字符') }
              ]
            })(<Input placeholder={i18n.get('请输入法定代表人/企业负责人姓名')} data-testid="pay-custom-payment-legal-person" />)}
          </FormItem>
          <FormItem {...formItemLayout} label={i18n.get('法人身份证号')}>
            {getFieldDecorator('egalPersonCertNo', {
              initialValue: detail.egalPersonCertNo,
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: i18n.get('请输入法人身份证号')
                },
                { validator: this.checkIdCard }
              ]
            })(<Input placeholder={i18n.get('请输入法人身份证号')} data-testid="pay-custom-payment-legal-person-id" />)}
          </FormItem>
          <FormItem
            {...formItemLayout}
            label={i18n.get('统一社会信用号')}
            extra={i18n.get('请输入9位组织机构代码，如12345678-9；或三证合一后18位的统一社会信用代码')}
          >
            {getFieldDecorator('socialCreditCode', {
              initialValue: detail.socialCreditCode,
              rules: [
                { required: true, whitespace: true, message: i18n.get('请输入机构代码') },
                { max: 50, message: i18n.get('机构代码不能超过50个字符') }
              ]
            })(<Input placeholder={i18n.get('请输入组织机构代码/统一社会信用代码')} data-testid="pay-custom-payment-social-credit-code" />)}
          </FormItem>
          <FormItem {...formItemLayout} label={i18n.get('证件有效期')}>
            {getFieldDecorator('expiresEnd', {
              initialValue: detail.expiresEnd,
              rules: [{ required: true, message: i18n.get('请输入证件有效期(营业执照)') }]
            })(<DatePicker style={{ width: '100%' }} placeholder={i18n.get('请选择证件有效期(营业执照)')} data-testid="pay-custom-payment-cert-expiry-date" />)}
          </FormItem>
          <FormItem {...formItemLayout} label={i18n.get('发证机关')}>
            {getFieldDecorator('certOrganization', {
              initialValue: detail.certOrganization,
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: i18n.get('请输入发证机关名称')
                },
                { max: 50, message: i18n.get('姓名不能超过50个字符') }
              ]
            })(<Input placeholder={i18n.get('请输入发证机关名称')} data-testid="pay-custom-payment-cert-organization" />)}
          </FormItem>
          <FormItem {...formItemLayout} label={i18n.get('营业执照号')}>
            {getFieldDecorator('businessLicenseNo', {
              initialValue: detail.businessLicenseNo,
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: i18n.get('请输入营业执照号')
                },
                { max: 50, message: i18n.get('营业执照号长度不能超过50个字符') }
              ]
            })(<Input placeholder={i18n.get('请输入营业执照号')} data-testid="pay-custom-payment-business-license" />)}
          </FormItem>
          <FormItem {...formItemLayout} label={i18n.get('税务登记号')}>
            {getFieldDecorator('taxRegisterNo', {
              initialValue: detail.taxRegisterNo,
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: i18n.get('请输入税务登记号')
                },
                { max: 50, message: i18n.get('税务登记号长度不能超过50个字符') }
              ]
            })(<Input placeholder={i18n.get('请输入税务登记号')} data-testid="pay-custom-payment-tax-register" />)}
          </FormItem>
          <div className={'group-name border-top-line'}>{i18n.get('账户信息')}</div>
          <FormItem {...formItemLayout} label={i18n.get('企业银行账号')}>
            {getFieldDecorator('cardNo', {
              initialValue: detail.cardNo,
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: i18n.get('请输入企业银行账号')
                },
                { max: 50, message: i18n.get('对公银行卡号长度不能超过50个字符') }
              ]
            })(<Input placeholder={i18n.get('请输入对公账号银行卡号')} data-testid="pay-custom-payment-bank-account" />)}
          </FormItem>
          <FormItem {...formItemLayout} label={i18n.get('银行预留手机号')}>
            {getFieldDecorator('bankCardPhoneNO', {
              initialValue: detail.bankCardPhoneNO,
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: i18n.get('请输入银行预留手机号')
                },
                { max: 11, message: i18n.get('银行预留手机号长度不能超过11位') }
              ]
            })(<Input placeholder={i18n.get('请输入银行预留手机号')} data-testid="pay-custom-payment-bank-phone" />)}
          </FormItem>
          <FormItem {...formItemLayout} label={i18n.get('开户行')}>
            {getFieldDecorator('bankCardName', {
              initialValue: detail.bank,
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: i18n.get('请输入银行开户行名称')
                }
              ],
              onChange: this.handleBankChange
            })(
              <SelectBankField
                dataSource={this.state.bankList}
                placeholder={i18n.get('请输入银行开户行名称')}
                onSearch={this.handleSearch}
                data-testid="pay-custom-payment-bank-select"
              />
            )}
          </FormItem>
          <FormItem {...formItemLayout} label={i18n.get('省市地区')}>
            {getFieldDecorator('detail.area', {
              initialValue: detail.area,
              rules: [{ required: true, whitespace: true, message: i18n.get('请选择省市地区') }],
              onChange: this.handleAreaChange
            })(
              <SelectAreaField
                bank={getFieldValue('bankCardName')}
                provinceList={this.props.provinceList}
                cityList={this.props.cityList}
                data-testid="pay-custom-payment-area-select"
              />
            )}
          </FormItem>
          <FormItem label={i18n.get('开户网点')} {...formItemLayout}>
            {getFieldDecorator('detail.branch', {
              initialValue: detail.branch,
              rules: [
                { required: true, whitespace: true, message: i18n.get('请选择开户网点') },
                { max: 50, message: i18n.get('开户网点不能超过50个字') }
              ],
              onChange: this.handleBranchChange
            })(
              <SelectBranchField
                city={city}
                dataSource={this.props.branchList}
                placeholder={i18n.get('请输入开户网点')}
                data-testid="pay-custom-payment-branch-select"
              />
            )}
          </FormItem>
          <div className={'group-name border-top-line'}>{i18n.get('联系人信息')}</div>
          <FormItem {...formItemLayout} label={i18n.get('联系人')}>
            {getFieldDecorator('contactor', {
              initialValue: detail.contactor,
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: i18n.get('请输入联系人姓名')
                },
                { max: 50, message: i18n.get('姓名不能超过50个字符') }
              ]
            })(<Input placeholder={i18n.get('请输入联系人姓名')} data-testid="pay-custom-payment-contactor" />)}
          </FormItem>
          <FormItem {...formItemLayout} label={i18n.get('联系人手机号')}>
            {getFieldDecorator('contactorMobile', {
              initialValue: detail.contactorMobile,
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: i18n.get('请输入联系人手机号')
                },
                { max: 11, message: i18n.get('手机号长度不能超过11位') }
              ]
            })(<Input placeholder={i18n.get('请输入联系人手机号')} data-testid="pay-custom-payment-contactor-mobile" />)}
          </FormItem>
          <FormItem {...formItemLayout} label={i18n.get('联系人邮箱')}>
            {getFieldDecorator('email', {
              initialValue: detail.email,
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: i18n.get('请输入联系人邮箱')
                },
                { max: 50, message: i18n.get('邮箱长度不能超过50个字符') }
              ]
            })(<Input placeholder={i18n.get('请输入联系人邮箱')} data-testid="pay-custom-payment-contactor-email" />)}
          </FormItem>
          <FormItem {...formItemLayout} label={i18n.get('联系人地址')}>
            {getFieldDecorator('contactorAddress', {
              initialValue: detail.contactorAddress,
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: i18n.get('请输入联系人地址')
                },
                { max: 100, message: i18n.get('联系人地址长度不能超过100个字符') }
              ]
            })(<Input placeholder={i18n.get('请输入联系人地址')} data-testid="pay-custom-payment-contactor-address" />)}
          </FormItem>
          <div className={'group-name border-top-line'}>{i18n.get('上传企业影印件')}</div>
          <FormItem {...formItemLayout} label={i18n.get('上传证件')} className="required-label">
            <Row gutter={16} className="mb-25">
              <Col span={8}>
                <ImageUpload
                  form={form}
                  name="businessLicenceImg"
                  action="//jsonplaceholder.typicode.com/posts/"
                  uploadText={i18n.get('三证扫描件（需盖章）')}
                  require={true}
                  help={i18n.get('请上传三证扫描件')}
                  data-testid="pay-custom-payment-upload-business-licence"
                />
              </Col>
              <Col span={8}>
                <ImageUpload
                  form={form}
                  name="identityImg"
                  action="//jsonplaceholder.typicode.com/posts/"
                  uploadText={i18n.get('法人身份证')}
                  require={true}
                  help={i18n.get('请上传法人身份证')}
                  data-testid="pay-custom-payment-upload-identity"
                />
              </Col>
              <Col span={8}>
                <ImageUpload
                  form={form}
                  name="openingPermitsImg"
                  action="//jsonplaceholder.typicode.com/posts/"
                  uploadText={i18n.get('开户许可证')}
                  require={true}
                  help={i18n.get('请上传开户许可证')}
                  data-testid="pay-custom-payment-upload-permit"
                />
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <ImageUpload
                  form={form}
                  name="letterOfAuthorization"
                  action="//jsonplaceholder.typicode.com/posts/"
                  uploadText={i18n.get('合作企业开户授权委托书')}
                  require={true}
                  help={i18n.get('请上传合作企业开户授权委托书')}
                  data-testid="pay-custom-payment-upload-authorization"
                />
              </Col>
              <Col span={8}>
                <ImageUpload
                  form={form}
                  name="proxyIdentityImg"
                  action="//jsonplaceholder.typicode.com/posts/"
                  uploadText={i18n.get('被授权人身份证')}
                  require={true}
                  help={i18n.get('请上传被授权人身份证')}
                  data-testid="pay-custom-payment-upload-proxy-identity"
                />
              </Col>
            </Row>
            <div className={'upload-tips'}>{i18n.get('上传图片只支持.jpg/.jpeg格式，且文件大小需小于800KB')}</div>
          </FormItem>
        </Form>
      </div>
    )
  }

  // 渲染组件主界面
  render() {
    const { loading } = this.state
    const docName = './合作企业开户授权委托书.docx' //@i18n-ignore
    return (
      <div id={'custom-payment_companyWalletForm'} className={styles['company-wallet-modal']}>
        <div className="modal-header">
          <div className="title flex">{i18n.get('开通企业钱包')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleCancel} data-testid="pay-custom-payment-wallet-form-close" />
        </div>
        <div className="wallet-form-wrapper">{this.renderWalletForm()}</div>
        <div className="modal-footer">
          <div>{i18n.get('点击下载')}</div>
          <a href={docName} data-testid="pay-custom-payment-wallet-form-download-link">
            {i18n.get('《')}
            {i18n.get('合作企业开户授权委托书')}
            {i18n.get('》')}
          </a>
          <div className="grow flex" />
          <Button key="cancel" size="large" className="mr-20" onClick={this.handleCancel} data-testid="pay-custom-payment-wallet-form-cancel">
            {i18n.get('取消')}
          </Button>
          <Button key="ok" type="primary" size="large" className={'mr-10'} loading={loading} onClick={this.handleOK} data-testid="pay-custom-payment-wallet-form-submit">
            {i18n.get('提交')}
          </Button>
        </div>
      </div>
    )
  }
}
