// @i18n-ignore-all
import actions, { getBranchByCardNo, postParsing } from './custom-payment-action'
import { app as api } from '@ekuaibao/whispered'
import { fnShowOpenLinkModal } from './custom-payment-fetch-util'
export const Universal_Unique_Key = 'customPayment.pc'
export default [
  {
    id: '@custom-payment',
    reducer: () => Promise.resolve(actions.getReducer()),
    path: '/custom-payment',
    ref: '/',
    onload: () => {
      const { getBoolVariation } = api.require('@lib/featbit')
      if (getBoolVariation('Payment_Account_Table')) {
        return import('./CustomPaymentTable')
      } else {
        return import('./custom-payment-view')
      }
    },
    'create:wallet'(data) {
      return api.dispatch(actions.postWalletOpen(data))
    },
    'get:CountryList'(data) {
      return api.dispatch(actions.getCountries(data))
    },
    'get:CityList'(data) {
      return api.dispatch(actions.getCities(data))
    },
    'open:link:modal': (url, fn) => {
      return fnShowOpenLinkModal(url, fn)
    },
    'get:BranchByCardNo': (cardNo) => {
      return getBranchByCardNo(cardNo)
    },
    'post:Parsing': (data) => {
      return postParsing(data)
    },
  },

  {
    point: '@@layers',
    prefix: '@custom-payment',
    onload: () => require('./layers').default,
  },

  {
    point: '@@menus',
    onload: () => [
      {
        id: 'custom-payment',
        pId: 'enterprise-manage',
        permissions: ['BANK_ACCOUNT_MANAGE', 'SYS_ADMIN'],
        weight: 9,
        label: '付款账户',
        href: '/custom-payment',
        icon: 'custom-payment-icon',
      },
    ],
  },
  {
    id: '@custom-pay-way-config',
    path: '/pay-way-config',
    ref: '/',
    onload: () => import('./PayWayConfig'),
  },

  {
    id: '@e-card-pay-control',
    path: '/e-card-pay-control',
    ref: '/',
    onload: () => import('./e-card-pay-control-view'),
  },
]
