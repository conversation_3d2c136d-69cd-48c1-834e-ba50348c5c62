import { Resource } from '@ekuaibao/fetch'
const datalink = new Resource('/api/v2/datalink')
const extendDetailsByPlatformId = new Resource('/api/v1/datalink/appExtend')
const control = new Resource('/api/ecard/control/config/v1')

export function getEBussCardEntityList() {
  const join = {
    join: `platformId,platformId,/v2/datalink/platform`,
    join$1: 'parentId,parentId,/v2/datalink/entity',
  }
  return datalink.GET('/entity/autoExpense/getEBussCardEntityList', { ...join })
}

export function getAppExtendDetailsByPlatformId(platformId: string) {
  return extendDetailsByPlatformId.GET('/getAppExtendDetailsByPlatformId', { platformId })
}

export function getControlList(params: any, join: any) {
  return control.POST('/search', params, join)
}

export function saveControlConfig(params: any) {
  return control.POST('/save', params)
}

export function delControlConfig(id: string) {
  return control.DELETE('/del', { id })
}

export function copyControlConfig(id: string) {
  return control.GET('/copy', { id })
}

export function activeControlConfig(params: { id: string; active: boolean }) {
  return control.GET('/active', params)
}

export function getUniversalityConfig() {
  return control.GET('/universality')
}

export function saveUniversalityConfig(params: any) {
  return control.POST('/universality/save', params)
}

export function getDefaultControlRuleList() {
  return control.GET('/defaultControlRuleList')
}

export function setRuleListOrder(ids: string[]) {
  return control.POST('/order', ids)
}
