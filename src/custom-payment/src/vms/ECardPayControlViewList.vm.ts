import { TableVm } from '@ekuaibao/collection-definition'
import { app } from '@ekuaibao/whispered'
import { observable, reaction, IReactionPublic } from 'mobx'
import { getControlList } from '../e-card-pay-control-action'
import parseQuery2Select from '@ekuaibao/lib/lib/parseQuery2Select'
const fetchFixer = app.require<any>('@elements/data-grid/fetchFixer').default
export class ECardPayControlViewListVm extends TableVm<any> {
  static NAME = Symbol('ECardPayControlViewListVm')
  // @ts-ignore

  reaction: IReactionPublic
  @observable loading: boolean = false
  init() {
    reaction(
      () => [this.currentPage, this.pageSize, this.sorters, this.filters],
      (_data, reaction) => {
        this.reaction = reaction
        this.fetchControlListData()
      },
      { fireImmediately: true },
    )
  }

  buildParams = () => {
    const p = this.params()
    const param = fetchFixer(p)
    const query = parseQuery2Select(param)
    return query.value()
  }

  fetchControlListData = async () => {
    const join = {
      join: 'conditions.feeTypeIds,feeTypeList,/v1/form/feeTypes',
      join$1: 'conditions.staffs,staffList,/v1/organization/staffs',
      join$2: 'conditions.departments,departmentList,/v1/organization/departments',
    }
    this.loading = true
    const { count, items } = await getControlList(undefined, join)
    this.loading = false
    this.dataTotal = count
    this.dataSource = items
  }
}
