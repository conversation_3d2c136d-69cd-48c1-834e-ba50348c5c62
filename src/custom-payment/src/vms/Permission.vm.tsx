/*
 * @Author: Onein
 * @Date: 2020-09-11 11:08:38
 * @Last Modified by: Onein
 * @Last Modified time: 2020-09-15 22:58:52
 */

import { computed, observable } from 'mobx'
import IPermission, { IPermissionMap } from '../models/PermissionModel'
import { getV } from '@ekuaibao/lib/lib/help'

export class PermissionVm {
  static NAME = Symbol('CustomPaymentPermissionVm')

  @observable createAuth = false
  @observable deleteAuth = false
  @observable editAuth = false
  @observable moveAuth = false
  @observable importAuth = false
  @observable exportAuth = false
  @observable addAuth = false
  @observable extendAuth = false
  @observable stopAuth = false
  @observable enableAuth = false

  @computed get create() {
    return this.createAuth
  }
  @computed get delete() {
    return this.deleteAuth
  }
  @computed get edit() {
    return this.editAuth
  }
  @computed get import() {
    return this.importAuth
  }
  @computed get export() {
    return this.exportAuth
  }
  @computed get extend() {
    return this.extendAuth
  }
  @computed get add() {
    return this.addAuth
  }
  @computed get stop() {
    return this.stopAuth
  }
  @computed get enable() {
    return this.enableAuth
  }
  @computed get move() {
    return this.moveAuth
  }

  private setCreate(createAuth: boolean) {
    if (this.createAuth != createAuth) {
      this.createAuth = createAuth
    }
  }
  private setDelete(deleteAuth: boolean) {
    if (this.deleteAuth != deleteAuth) {
      this.deleteAuth = deleteAuth
    }
  }
  private setEdit(editAuth: boolean) {
    if (this.editAuth != editAuth) {
      this.editAuth = editAuth
    }
  }
  private setImport(importAuth: boolean) {
    if (this.importAuth != importAuth) {
      this.importAuth = importAuth
    }
  }
  private setExport(exportAuth: boolean) {
    if (this.exportAuth != exportAuth) {
      this.exportAuth = exportAuth
    }
  }
  private setExtend(extendAuth: boolean) {
    if (this.extendAuth != extendAuth) {
      this.extendAuth = extendAuth
    }
  }
  private setAdd(addAuth: boolean) {
    if (this.addAuth != addAuth) {
      this.addAuth = addAuth
    }
  }
  private setStop(stopAuth: boolean) {
    if (this.stopAuth != stopAuth) {
      this.stopAuth = stopAuth
    }
  }
  private setEnable(enableAuth: boolean) {
    if (this.enableAuth != enableAuth) {
      this.enableAuth = enableAuth
    }
  }
  private setMove(moveAuth: boolean) {
    if (this.moveAuth != moveAuth) {
      this.moveAuth = moveAuth
    }
  }

  private permissionMap: IPermissionMap = {
    CREATE: (auth: boolean) => this.setCreate(auth),
    DELETE: (auth: boolean) => this.setDelete(auth),
    EDIT: (auth: boolean) => this.setEdit(auth),
    IMPORT: (auth: boolean) => this.setImport(auth),
    EXPORT: (auth: boolean) => this.setExport(auth),
    ADD: (auth: boolean) => this.setAdd(auth),
    EXTEND: (auth: boolean) => this.setExtend(auth),
    STOP: (auth: boolean) => this.setStop(auth),
    ENABLE: (auth: boolean) => this.setEnable(auth),
    MOVE: (auth: boolean) => this.setMove(auth),
  }

  //初始化数据
  public initData(data: any = {}) {
    const type: string = getV(data, 'type', '')
    const permissions: Array<IPermission> = getV(data, 'permissions', [])
    if (data.hasOwnProperty('type') && type === 'FREEDOM') {
      this.setAllPermissions(true)
    } else {
      this.setPermission(permissions)
    }
  }

  public initPermissions(data: any = {}) {
    //重置
    this.setAllPermissions(false)
    const permissions: Array<IPermission> = getV(data, 'permissions', [])
    const allIndex = permissions.findIndex((item: IPermission) => item.name === 'ALL')
    if (allIndex > -1 || (!permissions.length && !data.id)) {
      this.setAllPermissions(true)
    } else {
      this.setPermission(permissions)
    }
  }

  // 设置下发权限
  private setPermission(permission: Array<IPermission> = []) {
    permission.forEach((item: IPermission) => {
      this.permissionMap[item.name](item.auth)
    })
  }

  //设置所有权限
  private setAllPermissions(newAuth: boolean) {
    Object.values(this.permissionMap).forEach((item: (auth: boolean) => void) => {
      item && item(newAuth)
    })
  }
}
