import { app } from '@ekuaibao/whispered'
import { observable, toJS } from 'mobx'
import {
  getAppExtendDetailsByPlatformId,
  getDefaultControlRuleList,
  getEBussCardEntityList,
} from '../e-card-pay-control-action'
import {
  ConfigValue,
  ControlConfigType,
  DefaultControlIds,
  GlobalGroupType,
  controlConfig,
} from '../pay-control/types'
import { GlobalFieldIF } from '@ekuaibao/ekuaibao_types'
//@ts-ignore
import { QuerySelect } from 'ekbc-query-builder'
export const fieldBlackList = [
  'apportions',
  'detailId',
  'expenseLinks',
  'feeDetailPayeeId',
  'feeTypeForm',
  'linkDetailEntities',
  'linkDetailEntityId',
  'multiplePayeesMode',
  'order',
  'tripForm',
  'tripId',
  'trips',
  'apportionId',
  'feeDetailPayeeId',
  'dataLinkForm',
  'invoiceType',
  'dataLinkId',
  'payPlan',
  'preApprovedNodeName',
  'rejectionNum',
  'u_行程规划',
  'travelPlanning',
  'u_行程订单',
  'tmcOrder',
]

export class ECardPayControlViewSettingVm {
  static NAME = Symbol('ECardPayControlViewSettingVm')
  @observable data: any[] = []
  @observable globalFields: GlobalGroupType[] = []
  @observable list: any[] = []
  @observable specificationGroups: any[] = []
  @observable allSpecificationGroups: any[] = []
  @observable defaultControlIds: DefaultControlIds[] = []
  @observable map: Record<string, any> = {}
  @observable configValue: ConfigValue = {
    name: '',
    conditionConfig: {
      conditionType: 'payScene',
      conditions: [{ type: 'payScene', staffs: [], departments: [], feeTypeIds: [] }],
    },
    controlIds: [],
  }

  formatFeeTypes(data: any[]) {
    const tree = data || []
    const list: any[] = []
    const map: Record<string, any> = {}
    if (tree.length) {
      const fn = (children: any) =>
        children.forEach((line: any) => {
          line.title = line.name
          line.value = line.id
          if (line.children && line.children.length) {
            line.disabled = true
          }
          list.push(line)
          line.children && line.children.length && fn(line.children)
        })
      fn(tree)
      list.forEach((o) => (map[o.id] = o))
    }
    return { data, list, map }
  }

  fetchFeeTypeByECardConfig() {
    getEBussCardEntityList().then((res) => {
      const data = res?.items || []
      const entityData = data.filter((value: any) => value?.active)
      const platformId = entityData?.[0]?.platformId?.id
      getAppExtendDetailsByPlatformId(platformId).then((result) => {
        const syncFeeTypeIds: string[] = result?.value?.syncFeeTypeIds || []
        app.invokeService('@custom-feetype:getActiveFeeTypes').then((res: any) => {
          if (res.items.length) {
            const { data, list, map } = this.formatFeeTypes(res.items)
            this.list = list
            this.map = map
            if (syncFeeTypeIds.length === 0) {
              this.data = data
            } else {
              this.data = syncFeeTypeIds.map((it) => map[it])
            }
          }
        })
      })
    })
  }

  formatControlDefaultConfig(config: DefaultControlIds[]) {
    config.forEach((item) => {
      item.rules.forEach((it) => {
        it.control = controlConfig[it.type] as ControlConfigType
      })
    })
    return config
  }

  async fetchConfigValue(record: any) {
    if (record) {
      this.configValue = {
        ...record,
        conditionConfig: {
          conditionType: record.conditionType,
          conditions: record.conditions,
        },
      }
    } else {
      const { items = [] } = await getDefaultControlRuleList()
      const controlIds = this.formatControlDefaultConfig(items)
      this.configValue = {
        name: '',
        conditionConfig: {
          conditionType: 'payScene',
          conditions: [{ type: 'payScene', staffs: [], departments: [], feeTypeIds: [] }],
        },
        controlIds,
      }
      console.log('[ toJs ] >', toJS(this.configValue))
    }
  }

  fetchGlobalFields() {
    const whiteList = [
      { label: '文本', key: 'text' },
      { label: '数字', key: 'number' },
      { label: '金额', key: 'money' },
      { label: '时间', key: 'date' },
      { label: '时间范围', key: 'dateRange' },
    ]
    app
      .dataLoader('@common.globalFields')
      .reload()
      .then((res: any) => {
        const data = res.data as GlobalFieldIF[]
        const formatGlobal = whiteList.map((item) => {
          const { key, label } = item
          const options = data
            .filter(
              (field) =>
                field.dataType.type === key &&
                field.active &&
                !fieldBlackList.includes(field.name) &&
                !field.ability,
            )
            .map((it) => {
              return { ...it, value: it.name }
            })
          return {
            label,
            options,
          }
        }) as GlobalGroupType[]
        this.globalFields = formatGlobal
      })
  }

  async fetchAllSpecificationGroups() {
    const res = await app.invokeService('@custom-specification:get:specificationGroups')
    this.allSpecificationGroups = (res?.items || [])
      .filter((group: any) => group?.specifications?.length)
      .map((group: any) => {
        const { name, specifications } = group
        const options = specifications.map(({ id, name }: any) => ({
          value: id,
          label: name,
        }))
        return { ...group, label: name, options }
      })

    const requisitionList = (res.items || []).map((item: any) => ({
      ...item,
      specifications: item?.specifications?.filter((spec: any) => spec.type === 'requisition'),
    }))

    this.specificationGroups = (requisitionList || [])
      .filter((group: any) => group?.specifications?.length)
      .map((group: any) => {
        const label = group.name
        const options = group.specifications.map((it: any) => {
          return {
            value: it.id,
            label: it.name,
          }
        })
        return { ...group, label, options }
      })
  }
}
