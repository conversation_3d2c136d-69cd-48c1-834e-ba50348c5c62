@import "~@ekuaibao/web-theme-variables/styles/default";
@import "~@ekuaibao/eui-styles/less/token.less";

.create-account-modal {
  width: 100%;
  .select {
    width: 100%;
  }

  .form-content {
    padding: 16px;
    overflow: auto;
    height: calc(100% - 64px);
    .form-content-group-title {
      color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
      font: var(--eui-font-body-b1);
    }
    .eui-form-item-control-input {
      min-height: auto;
    }
    .mb-16 {
      margin-bottom: 16px;
    }
    .mt-24 {
      margin-top: 24px;
    }
    .mt-8 {
      margin-top: 8px;
    }
    .mr-8 {
      margin-right: 8px;
    }
    .mt-16 {
      margin-top: 16px;
    }
    .account-type {
      display: flex;
      flex-direction: column;
    }

    .ant-input,
    .select {
      .font-size-2;
      height: 32px;

      .ant-select-selection--single {
        height: 100% !important;
        display: flex;
        align-items: center;

        .ant-select-selection__rendered {
          width: 100%;
        }
      }
    }

    .multiple {
      height: auto;
    }

    .disabled-radio-animate {
      label[ant-click-animating-without-extra-node]:after {
        border: 0 none;
        opacity: 0;
        animation: none 0 ease 0 1 normal;
      }
    }

    .channels {
      > .ant-form-item-label {
        margin-bottom: @space-4;
      }
    }

    .visible {
      display: flex;
      align-items: flex-start;

      .subject-select {
        width: 300px;
      }

      .radio-left-text {
        text-align: left;
      }

      .ant-radio-wrapper {
        line-height: 3;

        &:hover {
          border-color: @color-inform-2;

          .ant-radio-inner {
            border-color: @color-inform-2;
          }
        }
      }

      .selectPerson {
        display: inline-flex;
        flex-wrap: wrap;
        overflow-x: hidden;
        overflow-y: auto;
        background-color: @color-white-1;
        border: 1px solid @color-line-1;
        border-radius: @radius-2;
        min-width: 300px;
        min-height: 28px;
        line-height: 28px;
        max-height: 100px;
        width: 234px;
        text-indent: 6px;
        align-items: center;

        .ant-tag {
          margin: @space-3;
        }

        .placeholder {
          color: @color-black-4;
        }

        &:hover {
          border-color: @color-brand-4;
        }
      }
    }

    .branch {
      margin-bottom: @space-5;

      .dis-f {
        color: @color-black-3;
        .font-size-2;

        .filter-text {
          color: @color-brand-2;
          margin-left: @space-3;
          cursor: pointer;
        }
      }
    }

    .ant-form-item {
      margin-bottom: @space-5;
    }

    .sortSpace {
      width: 100%;

      .eui-space-item:nth-child(1) {
        flex: 1;
      }
      .eui-space-item:nth-child(2) {
        margin-top: -2px;
      }
    }
  }

  .account-footer {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 16px;
    justify-content: flex-start;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    box-shadow: var(--eui-shadow-up-2);
  }

  .select-bank {
    position: relative;

    .line-bank {
      display: flex;
    }

    .tool-tips {
      position: absolute;
      top: 0;
      right: -25px;
    }
  }

  .currency-item {
    .currency-tips {
      margin-top: 8px;
      color: var(--eui-text-placeholder, rgba(29, 33, 41, 0.5));
      font: var(--eui-font-body-r1);
    }
  }

  .currency-select {
    width: 100%;
    border-radius: 6px;
    background: var(--eui-bg-float-base, #f2f3f5);
    padding: 8px;

    .currency-group {
      width: 100%;

      .currency-radio {
        .eui-radio-wrapper {
          color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
          width: 100%;
        }
      }
    }
  }

  .red-color-border {
    .eui-select-selector {
      border: 1px solid var(--eui-function-danger-500, #f53f3f);
    }
  }

  .red-color-tips {
    color: var(--eui-function-danger-500, #f53f3f);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }
}

.option-wrap {
  display: flex;
  align-items: center;

  .currency-option {
    display: flex;
    align-items: center;

    .left {
      .currency-img {
        width: 32px;
        height: 32px;
        border-radius: 100%;
        margin-right: 12px;
      }
    }

    .right {
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;

      .name {
        color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
      }

      .code {
        color: var(--eui-text-placeholder, rgba(29, 33, 41, 0.5));
      }
    }
  }
}

.tag-item {
  margin: 2px 3px 2px 0;
  background: var(--eui-transparent-n900-10);
  color: var(--eui-text-title);

  .eui-icon-OutlinedTipsClose {
    color: rgba(0, 0, 0, 0.45);
  }
}
