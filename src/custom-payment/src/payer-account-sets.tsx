import React, { PureComponent } from 'react'
//@ts-ignore
import styles from './payer-account-sets.module.less'
import { remove } from 'lodash'
import { EnhanceConnect } from '@ekuaibao/store'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Alert, Button, Space } from '@hose/eui'
import PayerRemarkConfig from './payer-remark-config'
import { trackCustomPayment } from './lib/fetchUtil'

interface PayProps {
  id: string
  byHand: boolean
  useSpecial: boolean
  dimensionId: any
  dimensionField: string
  autoSummary: boolean
  fieldId: string
  fieldLabel: string
  layer: any
  useLegalEntityAccount: boolean
}

@EnhanceConnect((state: any) => ({
  CHANGJIEPay: state['@common'].powers.CHANGJIEPay,
  staffs: state['@common'].staffs,
  roles: state['@common'].roleList,
  departmentTree: state['@common'].department.data,
}))
@EnhanceModal({
  title: i18n.get('设置'),
  footer: null,
})
export default class PayAccoutSets extends PureComponent<PayProps, any> {
  constructor(props: PayProps) {
    super(props)
    this.state = {
      id: props.id,
      byHand: props.byHand,
      useSpecial: props.useSpecial,
      dimensionId: props.dimensionId,
      dimensionField: props.dimensionField,
      autoSummary: props.autoSummary,
      fieldId: props.fieldId,
      fieldLabel: props.fieldLabel,
      useLegalEntityAccount: props.useLegalEntityAccount,
    }
  }

  protected handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleDeleteItem = (deleteItem: { id: string }, visibility: any = {}) => {
    const staffs = visibility.staffs
    remove(staffs, (id) => id === deleteItem.id)
    const departments = visibility.departments
    remove(departments, (id) => id === deleteItem.id)
    const roles = visibility.roles
    remove(roles, (id) => id === deleteItem.id)
    return { staffs, departments, roles }
  }

  handleClose = (name: string, data: any, deleteItem: { id: string }) => {
    const { visibility, publicVisibility } = this.state
    const visibilityData = name === 'isShowPrivacy' ? visibility : publicVisibility
    let cVisibility = this.handleDeleteItem(deleteItem, visibilityData)
    if (name === 'isShowPrivacy') {
      this.setState({ visibility: cVisibility, visibilityList: data })
    } else {
      this.setState({ publicVisibility: cVisibility, PublicVisibilityList: data })
    }
  }

  private handleModalSave = () => {
    let flag = true
    if (this.state.useSpecial) {
      if (
        this.state.dimensionId === null ||
        this.state.dimensionId === undefined ||
        this.state.dimensionId === '' ||
        this.state.dimensionField === null ||
        this.state.dimensionField === undefined ||
        this.state.dimensionField === ''
      ) {
        this.refs.remarkConfig.refs.userSpecialWarn.style.display = 'inline'
        flag = false
      }
    }
    if (this.state.autoSummary) {
      if (
        this.state.fieldId === null ||
        this.state.fieldId === undefined ||
        this.state.fieldId === ''
      ) {
        this.refs.remarkConfig.refs.fieldWarn.style.display = 'inline'
        flag = false
      }
    }
    if (!flag) {
      return
    }
    if (!this.state.useSpecial) {
      this.setState({ dimensionId: '', dimensionField: '' })
    }
    if (!this.state.autoSummary) {
      this.setState({ fieldId: '', fieldLabel: '' })
    }
    const {
      id,
      byHand,
      useSpecial,
      dimensionId,
      dimensionField,
      autoSummary,
      fieldId,
      fieldLabel,
      useLegalEntityAccount,
    } = this.state
    const emitObj = {
      id,
      useSpecial,
      byHand,
      dimensionId,
      dimensionField,
      autoSummary,
      fieldId,
      fieldLabel,
      useLegalEntityAccount,
    }
    this.props.layer.emitOk(emitObj)
  }

  handleModalClose = () => {
    this.props.layer.emitCancel()
  }
  handleCheck = (name: string, e: any) => {
    if (name == 'useSpecial') {
      this.setState({ byHand: true })
    }
    if (name == 'byHand' && this.state.byHand) {
      this.setState({ useSpecial: false })
    }

    if (['byHand', 'useSpecial', 'autoSummary', 'useLegalEntityAccount'].includes(name)) {
      this.setState((prevState) => {
        return {
          [name]: !prevState[name],
        }
      })
    } else {
      if (name == 'fieldId') {
        this.setState({
          fieldLabel: this.refs.remarkConfig.state.fieldList.find((v) => v.name === e).label,
        })
        this.refs.remarkConfig.refs.fieldWarn.style.display = 'none'
      }
      if (
        (name === 'dimensionId' && this.state.dimensionField != '') ||
        (name === 'dimensionField' && this.state.dimensionId != '')
      ) {
        this.refs.remarkConfig.refs.userSpecialWarn.style.display = 'none'
      }
      this.setState({ [name]: e })
    }

    if (name === 'useLegalEntityAccount') {
      trackCustomPayment(
        e.target.checked ? 'use_legal_enitiy_account' : 'cancel_legal_enitiy_account',
      )
    }
  }

  render() {
    return (
      <div id={'payerAccountSets'} className={styles['payer-account-sets']}>
        <div className="account-sets-content">
          <Alert
            message={i18n.get(
              '超出银行摘要的限定长度，将自动截取，工商银行20个汉字以内；民生银行26个汉字以内；杭州银行30个汉字以内；建设银行50个汉字以内；中国银行200个汉字以内；长沙银行300个汉字以内；其它28个汉字以内',
            )}
            type="info"
          />
          <div className="account-sets-content-title">{i18n.get('支付摘要配置')}</div>
          <PayerRemarkConfig
            from="globalSetting"
            ref="remarkConfig"
            byHand={this.state.byHand}
            useSpecial={this.state.useSpecial}
            dimensionField={this.state.dimensionField}
            dimensionId={this.state.dimensionId}
            autoSummary={this.state.autoSummary}
            fieldId={this.state.fieldId}
            handleCheck={this.handleCheck}
            useLegalEntityAccount={this.state.useLegalEntityAccount}
          />
        </div>
        <div className="modal-footer-button">
          <Space>
            <Button category="secondary" onClick={this.handleModalClose} data-testid="pay-payerAccountSets-cancel-button">
              {i18n.get('取消')}
            </Button>
            <Button onClick={this.handleModalSave} data-testid="pay-payerAccountSets-save-button">{i18n.get('保存')}</Button>
          </Space>
        </div>
      </div>
    )
  }
}
