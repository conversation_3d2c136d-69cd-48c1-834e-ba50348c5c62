import { app as api } from '@ekuaibao/whispered'
import styles from './payment-manage.module.less'
import React, { PureComponent } from 'react'
import EmployeeWrapper from './employee/employee-wrapper'
import AccountInfoWrapper from './account-info/account-info-wrapper'
const ETabs = api.require('@elements/ETabs')
const SearchInput = api.require('@elements/search-input')
import { EnhanceConnect } from '@ekuaibao/store'
import { ID } from './key'

import actions from './custom-payment-action'
import { connect } from '@ekuaibao/mobx-store'

@connect(store => ({ size: store.states['@layout'].size }))
@EnhanceConnect(
  state => ({
    staffs: state['@common'].staffs
  }),
  null,
  `${ID}/storage`
)
export default class CustomPaymentView extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      activeKey: 'account-info'
    }
  }

  componentWillMount() {
    api.dataLoader('@common.staffs').load()
    // 获取企业钱包的权限
    api
      .dataLoader('@common.powers')
      .load()
      .then(powers => powers.Wallet)
      .then(value => {
        this.setState({ walletPower: value })
      })
  }

  renderEmployeeWrapper() {
    return <EmployeeWrapper {...this.props} />
  }

  renderAccountInfoWrapper() {
    return <AccountInfoWrapper {...this.props} />
  }

  handlePwd = () => {
    const walletId = this.props.accountInfo.id
    api.dispatch(actions.getPwdUrl(walletId)).then(result => {
      api.emit('@vendor:open:link', result && result.value.bankUrl)
    })
  }

  handleTabChange = activeKey => {
    this.setState({ activeKey })
  }

  handleSearch = text => {
    this.props.setState({ searchStaffText: text })
  }

  render() {
    const { activeKey } = this.state
    const dataSource = [
      {
        tab: i18n.get('基本信息'),
        children: this.renderAccountInfoWrapper(),
        key: 'account-info'
      },
      {
        tab: i18n.get('员工开通管理'),
        children: this.renderEmployeeWrapper(),
        key: 'employee'
      }
    ]
    const operations = (
      <div className="mr-20">
        {activeKey === 'employee' ? (
          <SearchInput
            className="search"
            placeholder={i18n.get('搜索员工姓名或手机号码')}
            onSearch={this.handleSearch}
            style={{ width: 220 }}
            data-testid="pay-paymentManage-search-input"
          />
        ) : (
          <a className="enable-switch" onClick={this.handlePwd} data-testid="pay-paymentManage-setPwd-link">
            {i18n.get('设置支付密码')}
          </a>
        )}
      </div>
    )
    return (
      <div className={styles['custom-payment']}>
        <ETabs
          onChange={this.handleTabChange}
          className="ekb-tab-line-left"
          defaultActiveKey="account-info"
          tabBarStyle={{ paddingTop: 6, height: 40, width: '100%' }}
          tabBarExtraContent={operations}
          dataSource={dataSource}
          data-testid="pay-paymentManage-tabs"
        />
      </div>
    )
  }
}
