@import '~@ekuaibao/web-theme-variables/styles/default';
@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/eui-styles/less/token.less';

.custom-payment {
  display: flex;
  flex: 1;
  flex-direction: column;
  color: @text-color;
  overflow: hidden;
  .stand-20-icon{
    margin-right: 20px;
  }
  .inl-block {
    display: inline-block;
  }
  .text-red {
    color: @red-6;
  }
  .ekb-tab-line-left{
    flex: none;
  }
  .account-manage-wrapper {
    padding: @space-7 @space-7 0 @space-7;
    overflow-y: auto;
    .right-buttons {
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      margin-bottom: @space-7;
      .search {
        flex: none;
        width: 300px;
        .ant-input {
          background-color: @color-bg-2;
          height: 32px;
          border: none;
          border-radius: @radius-2;
        }
        .ant-input-search-icon {
          color: @color-black-2;
        }
      }
      .checkbox-group {
        .ant-checkbox-wrapper {
          .font-size-2;
          .font-weight-2;
          color: @color-black-2;
          .ant-checkbox {
            .ant-checkbox-inner {
              border: 1px solid @color-black-4;
            }
          }
        }
      }
    }
  }
  .header {
    display: flex;
    align-items: center;
    padding: 0 24px;
    height: 61px;
    font-size: 13px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    background: #ffffff;
    justify-content: space-between;
    flex-shrink: 0;

    .menu {
      flex: 1;
      display: flex;
      align-items: center;
    }
  }
  .body {
    position: relative;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
    overflow: visible;
    flex: 1;
    .account-wrapper {
      display: flex;
      justify-content: space-between;
      height: 80px;
      padding: 0 24px;
      margin-top: 10px;
      margin-left: 16px;
      margin-right: 16px;
      border-radius: 4px;
      background-color: #ffffff;
      border: solid 1px #e9e9e9;
      box-sizing: border-box;
      &:hover {
        box-shadow: 0 1px 6px 0 rgba(204, 204, 204, 0.65);
        cursor: pointer;
        .disable-switch {
          display: inline-block !important;
        }
      }
      .account-infoBox {
        margin: auto 0;
        font-size: 13px;
        user-select: none;
        .op {
          img {
            opacity: 0.5;
          }
        }
        .account-name {
          margin-bottom: 10px;
        }
        .highlight {
          color: var(--brand-base);
          background: none;
        }
      }
      .left-part {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex: 1;
      }

      .right-part {
        display: flex;
        justify-content: center;
        align-items: center;
        .visible-range-wrap {
          color: var(--brand-base);
          display: flex;
          align-items: center;
          height: 100%;
          .box-line {
            height: 20px;
            border-right: 1px solid #e9e9e9;
            margin-right: 16px;
            padding-right: 16px;
          }
        }
        .payment-action {
          display: flex;
          align-items: center;
          margin: auto 0;
          .box-line {
            height: 20px;
            border-right: 1px solid #e9e9e9;
            margin-right: 16px;
            padding-right: 16px;
          }
          a {
            display: flex;
            align-items: center;
          }
          .action-wrapper {
            width: 75px;
            text-align: right;
          }
          .enable-switch {
            min-width: 32px;
            height: 18px;
            line-height: 16px;
            border-radius: 16px;
            margin-left: 5px;
            &::after {
              width: 16px;
              height: 16px;
              left: 0;
              top: 0;
              border-radius: 16px;
            }
          }
          .disable-switch {
            .enable-switch;
            display: none;
          }

          .ant-switch-checked {
            &::after {
              left: 33px !important;
            }
          }
        }
      }
    }
    .account-wrapper.disabled {
      cursor: not-allowed;
    }
  }
  .empty-view {
    margin-top: @space-7;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
    background-color: #f9fafc;
    overflow: auto;
    padding-bottom: 20px;
    .empty-view-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #b1b9bd;
      font-size: 14px;
      img {
        width: 200px;
        height: 133px;
        margin-bottom: 10px;
      }
    }
  }
  .footer {
    z-index: 1;
    flex-shrink: 0;
    width: 100%;
    background-color: @color-white-1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 56px;
    padding: @space-6 @space-7;
    box-shadow: 0px 6px 24px 0px rgba(29, 43, 61, 0.2);
    > button {
      border-radius: @radius-2;
    }
  }

  .hideFooter {
    display: none;
  }
  .footer-wrapper {
    flex-shrink: 0;
    background-color: @color-white-1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 56px;
    padding: 0 @space-7;
    box-shadow: 0px 6px 24px 0px rgba(29, 43, 61, 0.2);
    .btn-wrapper {
      display: flex;
      align-items: center;
      .btn-group {
        > .ant-btn {
          line-height: normal;
          &:first-child {
            border-radius: 4px 0px 0px 4px;
          }
          &:last-child {
            border-radius: 0px 4px 4px 0px;
          }
        }
      }
    }
    .ant-pagination-next:hover a,.ant-pagination-prev:hover a {
      text-decoration: none;
    }
  }
}

.custom-payment-layout5{
  .header {
    padding: 0 16px 0 0;
  }
}
