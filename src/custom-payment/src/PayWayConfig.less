@import '~@ekuaibao/eui-styles/less/token.less';

.pay-way-config-wrapper{
  padding: @space-7;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f9;
  flex: 1;
  .pay-way-config-content{
    display: flex;
    flex: 1;
    background-color: @color-white-1;
    border-radius: 10px;
    flex-direction: column;
    .title-wrapper{
      height: 40px;
      padding: @space-7;
      .title-wrapper-title{
        .TypographyBase(16px,24px);
        color: rgba(20, 34, 52, 0.92);
      }
    }
    .pay-way-config-list{
      padding: @space-7;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      flex: 1;
      .pay-way-card-item{
        padding: @space-7;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border: 1px solid #E6E6E6;
        position: relative;
        .bank-bg{
          position:absolute;
          left: 0;
          top: 0;
          height: 100%;
          z-index: 0;
        }
        .left-wrapper{
          display: flex;
          align-items: center;
          z-index: 1;
          .bank-img{
            width: 40px;
            height: 40px;
          }
          .bank-name{
            .TypographyBase(16px,24px);
            margin-left: 20px;
            font-weight: 500;
            color: rgba(20, 34, 52, 0.92);
          }
        }
        .right-action{
          display: flex;
          .action-setting{
            .font-size-2;
            padding: @space-3 @space-6;
            color: @color-white-1;
            cursor: pointer;
          }
        }
      }
    }
  }
}
