import React, { FC, useEffect, useState } from 'react'
import { LayerDef } from '@ekuaibao/enhance-layer-manager/types/EnhanceLayerBase'
// @ts-ignore
import styles from './ECardPayControlCommonConfigDrawer.module.less'
import { Button, Form } from '@hose/eui'
import CommonWhiteListStaffMoney from '../components/CommonWhiteListStaffMoney'
import { whiteListType } from '../../pay-control/types'
import { getUniversalityConfig, saveUniversalityConfig } from '../../e-card-pay-control-action'
import { showMessage } from '@ekuaibao/show-util'

const FormItem = Form.Item

interface IProps {
  layer: LayerDef
}

const ECardPayControlCommonConfigDrawer: FC<IProps> = (props) => {
  const { layer } = props
  const [form] = Form.useForm()
  const [commonConfig, setCommonConfig] = useState<whiteListType>()
  const handleOk = () => {
    form.validateFields().then(async (res: whiteListType) => {
      const params = {
        ...commonConfig,
        ...res,
      }
      saveUniversalityConfig(params)
        .then((_) => {
          layer.emitCancel()
        })
        .catch((e) => {
          showMessage.error(e.errorMessage || e.message)
        })
    })
  }
  const handleClose = () => {
    layer.emitCancel()
  }
  const fetchCommonConfigData = async () => {
    const res = await getUniversalityConfig()
    const commonConfigValue: whiteListType = res?.value || {
      whiteList: [{ staffs: [], singleTimeLimit: 0 }],
    }
    setCommonConfig(commonConfigValue)
    form.setFieldsValue(commonConfigValue)
  }

  useEffect(() => {
    fetchCommonConfigData()
  }, [])

  return (
    <div className={styles.eCardPayControlCommonConfigDrawerWrapper}>
      <div className={styles.content}>
        <Form layout="vertical" style={{ width: '100%' }} form={form}>
          <FormItem
            name="whiteList"
            label={
              <>
                白名单
                <span>{'（以下人员可以跳过所有规则，且消费金额不受管控）'}</span>
              </>
            }>
            <CommonWhiteListStaffMoney />
          </FormItem>
        </Form>
        <div className={styles.footer}>
          <Button category="primary" onClick={handleOk} style={{ marginRight: 8 }}>
            {i18n.get('确定')}
          </Button>
          <Button category="secondary" onClick={handleClose}>
            {i18n.get('取消')}
          </Button>
        </div>
      </div>
    </div>
  )
}

export default ECardPayControlCommonConfigDrawer
