import React, { FC } from 'react'
// @ts-ignore
import styles from './FeeStandardConfigDrawer.module.less'
import { LayerDef } from '@ekuaibao/enhance-layer-manager/types/EnhanceLayerBase'
import { Button, Form, Select } from '@hose/eui'
import { ControlConfigType } from '../../pay-control/types'

const FormItem = Form.Item

interface IProps {
  layer: LayerDef
  control: ControlConfigType
  allSpecificationGroups: any[]
}

const FeeStandardConfigDrawer: FC<IProps> = (props) => {
  const { layer, control, allSpecificationGroups } = props
  const [form] = Form.useForm()

  const handleOk = () => {
    form.validateFields().then((result) => {
      console.log('%c [ result ]-22', 'font-size:13px; background:pink; color:#bf2c9f;', result)
      const { type } = control
      const params = {
        ...result,
        type,
      }
      layer.emitOk(params)
    })
  }

  const handleClose = () => {
    layer.emitCancel()
  }

  return (
    <div className={styles.FeeStandardConfigDrawerWrapper}>
      <div className={styles.content}>
        <Form
          name="vertical"
          layout="vertical"
          style={{ width: '100%' }}
          form={form}
          initialValues={control}>
          <FormItem
            name="specificationId"
            label={i18n.get('可选择的单据范围')}
            rules={[{ required: true, message: '请选择单据范围' }]}>
            <Select
              placeholder={i18n.get('请选择单据范围')}
              options={allSpecificationGroups}
              style={{ width: '100%' }}
            />
          </FormItem>
        </Form>
      </div>
      <div className={styles.footer}>
        <Button category="primary" onClick={handleOk} style={{ marginRight: 8 }}>
          {i18n.get('确定')}
        </Button>
        <Button category="secondary" onClick={handleClose}>
          {i18n.get('取消')}
        </Button>
      </div>
    </div>
  )
}

export default FeeStandardConfigDrawer
