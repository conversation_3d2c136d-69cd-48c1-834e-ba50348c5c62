import React, { FC } from 'react'
// @ts-ignore
import styles from './FeeIncFieldConfigDrawer.module.less'
import { Button, Form } from '@hose/eui'
import { LayerDef } from '@ekuaibao/enhance-layer-manager/types/EnhanceLayerBase'
import { ControlConfigType, GlobalGroupType } from '../../pay-control/types'
import FeeIncFieldConfigWidget from '../components/FeeIncFieldConfigWidget'
import FeeIncFieldSpecialConfigWidget from '../components/FeeIncFieldSpecialConfigWidget'
const FormItem = Form.Item
interface IProps {
  layer: LayerDef
  control: ControlConfigType
  globalFields: GlobalGroupType[]
  staffs: string[]
  feeTypeList: string[]
  staffList: string[]
}
const FeeIncFieldConfigDrawer: FC<IProps> = (props) => {
  const { layer, control, globalFields, feeTypeList, staffList } = props
  const [form] = Form.useForm()

  const handleOk = () => {
    form.validateFields().then((result) => {
      const { type } = control
      const params = {
        ...result,
        type,
      }
      layer.emitOk(params)
    })
  }
  const handleClose = () => {
    layer.emitCancel()
  }

  return (
    <div className={styles.feeIncFieldConfigDrawerWrapper}>
      <div className={styles.content}>
        <Form
          form={form}
          name="vertical"
          layout="vertical"
          style={{ width: '100%' }}
          initialValues={control}>
          <FormItem
            name="feeIncField"
            label={i18n.get('费用类型范围')}
            rules={[{ required: true, message: '请配置' }]}>
            <FeeIncFieldConfigWidget globalFields={globalFields} feeTypeList={feeTypeList} />
          </FormItem>
          <FeeIncFieldSpecialConfigWidget
            globalFields={globalFields}
            feeTypeList={feeTypeList}
            staffList={staffList}
          />
        </Form>
      </div>
      <div className={styles.footer}>
        <Button category="primary" onClick={handleOk} style={{ marginRight: 8 }}>
          {i18n.get('确定')}
        </Button>
        <Button category="secondary" onClick={handleClose}>
          {i18n.get('取消')}
        </Button>
      </div>
    </div>
  )
}

export default FeeIncFieldConfigDrawer
