import React, { FC } from 'react'
// @ts-ignore
import styles from './SelectRequisitionConfigDrawer.module.less'
import { LayerDef } from '@ekuaibao/enhance-layer-manager/types/EnhanceLayerBase'
import { Button, Form, Select } from '@hose/eui'
import { ControlConfigType } from '../../pay-control/types'
import RequisitionConfigWidget from '../components/RequisitionConfigWidget'

const FormItem = Form.Item

interface IProps {
  layer: LayerDef
  control: ControlConfigType
  specificationGroups: any[]
  staffs: string[]
  staffList: any[]
}

const SelectRequisitionConfigDrawer: FC<IProps> = (props) => {
  const { layer, control, specificationGroups, staffs, staffList } = props
  const [form] = Form.useForm()

  const handleOk = () => {
    form.validateFields().then((result) => {
      const { type } = control
      const params = {
        ...result,
        type,
      }
      layer.emitOk(params)
    })
  }

  const handleClose = () => {
    layer.emitCancel()
  }

  return (
    <div className={styles.selectRequisitionConfigDrawerWrapper}>
      <div className={styles.content}>
        <Form
          name="vertical"
          layout="vertical"
          style={{ width: '100%' }}
          form={form}
          initialValues={control}>
          <FormItem label={i18n.get('适用人员')}>
            <Select
              mode="multiple"
              disabled
              style={{ width: '100%' }}
              placeholder="适用人员"
              defaultValue={staffs.filter(item=>item)}
              options={staffList}
            />
          </FormItem>
          <FormItem
            name="specificationIds"
            label={i18n.get('可选择的申请单范围')}
            rules={[{ required: true, message: '请选择申请单范围' }]}>
            <Select
              mode="multiple"
              placeholder={i18n.get('请选择申请单范围')}
              options={specificationGroups}
              style={{ width: '100%' }}
            />
          </FormItem>
          <RequisitionConfigWidget
            specificationGroups={specificationGroups}
            staffList={staffList}
          />
        </Form>
      </div>
      <div className={styles.footer}>
        <Button category="primary" onClick={handleOk} style={{ marginRight: 8 }}>
          {i18n.get('确定')}
        </Button>
        <Button category="secondary" onClick={handleClose}>
          {i18n.get('取消')}
        </Button>
      </div>
    </div>
  )
}

export default SelectRequisitionConfigDrawer
