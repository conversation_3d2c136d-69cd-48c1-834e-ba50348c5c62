import React, { FC, useState } from 'react'
// @ts-ignore
import styles from './CardLimitConfigDrawer.module.less'
import { Button, Form, InputNumber, Space, Select } from '@hose/eui'
import { LayerDef } from '@ekuaibao/enhance-layer-manager/types/EnhanceLayerBase'
import { ControlConfigType } from '../../pay-control/types'
import CardLimitConfigWidget from '../components/CardLimitConfigWidget'

const FormItem = Form.Item

interface IProps {
  layer: LayerDef
  control: ControlConfigType
  staffs: string[]
  staffList: any[]
}
const CardLimitConfigDrawer: FC<IProps> = (props) => {
  const { layer, control, staffs, staffList } = props
  const [form] = Form.useForm()
  const [singleDayUnit, setSingleDayUnit] = useState(control?.singleDayUnit ?? 'D')
  const handleOk = () => {
    form.validateFields().then((result) => {
      const { type } = control
      const params = {
        ...result,
        singleDayUnit,
        type,
      }
      layer.emitOk(params)
    })
  }
  const handleClose = () => {
    layer.emitCancel()
  }
  const validateSingleTimeLimit = (_rule: any, value: number, callback: any) => {
    if (value === 0 || value < 0) {
      callback(new Error(i18n.get('请输入大于0的数值')))
    } else {
      callback()
    }
  }
  const selectBefore = () => {
    return (
      <Select
        defaultValue={singleDayUnit}
        onChange={(selected: string) => {
          setSingleDayUnit(selected)
        }}
        options={[
          {
            value: 'D',
            label: '单日最高',
          },
          {
            value: 'W',
            label: '每周最高',
          },
          {
            value: 'M',
            label: '每月最高',
          },
          {
            value: 'Q',
            label: '季度最高',
          },
        ]}
      />
    )
  }
  return (
    <div className={styles.cardLimitConfigDrawerWrapper}>
      <div className={styles.content}>
        <Form
          name="vertical"
          layout="vertical"
          style={{ width: '100%' }}
          form={form}
          initialValues={control}>
          <FormItem label={i18n.get('适用人员')}>
            <Select
              mode="multiple"
              disabled
              style={{ width: '100%' }}
              placeholder="适用人员"
              defaultValue={staffs.filter((item) => item)}
              options={staffList}
            />
          </FormItem>
          <FormItem
            required
            name="singleTimeDayLimitConfig"
            label={i18n.get('统一限额')}
            dependencies={['singleTimeLimit']}
            className={styles.singleTimeDayLimitConfigWrapper}>
            <Space direction="vertical">
              <FormItem
                name="singleTimeLimit"
                rules={[
                  { required: true, message: '单笔最高为必填项' },
                  { validator: validateSingleTimeLimit },
                ]}>
                <InputNumber addonBefore={'单笔最高'} addonAfter={'元'} />
              </FormItem>
              <FormItem
                name="singleDayLimit"
                rules={[
                  { required: true, message: '单日最高为必填项' },
                  { validator: validateSingleTimeLimit },
                ]}>
                <InputNumber addonBefore={selectBefore()} addonAfter={'元'} />
              </FormItem>
            </Space>
          </FormItem>
          <CardLimitConfigWidget staffList={staffList} />
        </Form>
      </div>
      <div className={styles.footer}>
        <Button category="primary" onClick={handleOk} style={{ marginRight: 8 }}>
          {i18n.get('确定')}
        </Button>
        <Button category="secondary" onClick={handleClose}>
          {i18n.get('取消')}
        </Button>
      </div>
    </div>
  )
}

export default CardLimitConfigDrawer
