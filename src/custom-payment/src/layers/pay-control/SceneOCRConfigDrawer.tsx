import React, { <PERSON> } from 'react'
// @ts-ignore
import styles from './SceneOCRConfigDrawer.module.less'
import { LayerDef } from '@ekuaibao/enhance-layer-manager/types/EnhanceLayerBase'
import { Button, Form, Select } from '@hose/eui'
import { ControlConfigType } from '../../pay-control/types'

const FormItem = Form.Item

interface IProps {
  layer: LayerDef
  control: ControlConfigType
  staffs: string[]
  staffList: any[]
}

const SceneOCRConfigDrawer: FC<IProps> = (props) => {
  const { layer, control, staffs, staffList } = props
  const [form] = Form.useForm()

  const handleOk = () => {
    form.validateFields().then((result) => {
      const { type } = control
      const params = {
        ...result,
        type,
      }
      layer.emitOk(params)
    })
  }

  const handleClose = () => {
    layer.emitCancel()
  }

  return (
    <div className={styles.sceneOCRConfigDrawerWrapper}>
      <div className={styles.content}>
        <Form
          name="vertical"
          layout="vertical"
          style={{ width: '100%' }}
          form={form}
          initialValues={control}>
          <FormItem label={i18n.get('适用人员')}>
            <Select
              mode="multiple"
              disabled
              style={{ width: '100%' }}
              placeholder="选择人员"
              defaultValue={staffs.filter(item=>item)}
              options={staffList}
            />
          </FormItem>
          <FormItem
            name="staffs"
            label={i18n.get('适用人员中哪些人员不需要提供场景照片')}>
            <Select
              mode="multiple"
              style={{ width: '100%' }}
              placeholder="选择人员"
              options={staffList}
            />
          </FormItem>
        </Form>
      </div>
      <div className={styles.footer}>
        <Button category="primary" onClick={handleOk} style={{ marginRight: 8 }}>
          {i18n.get('确定')}
        </Button>
        <Button category="secondary" onClick={handleClose}>
          {i18n.get('取消')}
        </Button>
      </div>
    </div>
  )
}

export default SceneOCRConfigDrawer
