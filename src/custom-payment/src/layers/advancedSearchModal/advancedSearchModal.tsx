import React, { PureComponent } from 'react'
import { Button, Icon, Table, Form, Pagination } from 'antd'
import { RowSelectionType } from 'antd/lib/Table/interface'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import styles from './advancedSearchModal.module.less'
import { app } from '@ekuaibao/whispered'
import { fetchResult, trackApproved } from '../../lib/fetchUtil'
import { showMessage } from '@ekuaibao/show-util'
const SearchInput = app.require<any>('@elements/search-input')


interface Props {
  layer: any
}

interface States {
  loading: boolean
  dataSource: any[]
  pageSize: number
  current: number
  total: number
  emptyText?: string
  bankName?: string
  branchName?: string
  place?: string
  bankLinkNo?: string
  selectedRow: any
}

const PageSize = 10
const CurrentPage = 1

const defaultSearchValue = {
  bankName: '',
  branchName: '',
  place: '',
  bankLinkNo: ''
}

@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer'
})
class AdvancedSearchModal extends PureComponent<Props,States> {
  constructor(props: Props) {
    super(props)
    this.state = {
      loading: false,
      dataSource: [],
      pageSize: PageSize,
      current: CurrentPage,
      total: 0,
      selectedRow: null,
      emptyText: i18n.get('暂无数据'),
      ...defaultSearchValue
    }
  }

  private columns: any[] = [
    {
      title: i18n.get('网点名称'),
      dataIndex: 'name',
      dataType: 'text',
      filterType: 'text',
      key: 'name',
      width: '200px',
      ellipsis: true,
    },
    {
      title: i18n.get('银行名称'),
      dataIndex: 'bankName',
      key: 'bankName',
      dataType: 'text',
      filterType: 'text',
      width: '200px',
      ellipsis: true,
    },
    {
      title: i18n.get('联行号'),
      dataIndex: 'code',
      filterType: 'text',
      dataType: 'text',
      key: 'code',
      width: '200px',
      ellipsis: true,
    }
  ]

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleClear = () => {
    this.setState(defaultSearchValue)
  }

  handleSave = () => {
    const { selectedRow, dataSource } = this.state
    this.props.layer.emitOk(selectedRow ? selectedRow : dataSource[0])
  }

  handleChange = e => {
    const value = e.target.value.trim()
    const state = { }
    if (!e.target.category) {
      state[e.target.getAttribute('category')] = value
    } else {
      state[e.target.category] = value
    }
    this.setState(state)
  }

  getDataInfo = () => {
    const { bankLinkNo } = this.state
      const start = new Date().getTime();
    fetchResult(this.filtersForStates()).then(data => {
      const end = new Date().getTime();
      const t = end - start;
      trackApproved(t, 'branchTime', '网点查询时间')
      if (bankLinkNo && data.items.length === 0) {
        this.setState({ emptyText: i18n.get('未能找到对应的开户网点，请与发卡行确认网点信息或联行号是否正确。') })
      } else if(!bankLinkNo && data.items.length === 0) {
        this.setState({ emptyText: i18n.get('未能找到对应的开户网点。请与发卡行确认网点信息是否正确，或通过发卡行提供的联行号进行查询。') })
      }
      this.setState({ dataSource: data.items, total: data.count,loading: false, selectedRow: data.items.length ? data.items[0] : null })
    }).catch(err => {
      showMessage.error(err)
    })
  }

  handleSearch = () => {
    this.setState({ loading: true, current: 1 }, () => {
      this.getDataInfo()
    })
  }

  filtersForStates = () => {
    const { place, bankLinkNo, bankName, branchName, current, pageSize } = this.state
    return { place, bankLinkNo, bankName, branchName, start: (current - 1) * pageSize, count: pageSize }
  }

  handlePageChange = (page: number, pageSize: number) => {
    const { total } = this.state
    if (page > Math.ceil(total / pageSize)) return
    this.setState({ current: page, pageSize, loading: true }, () => {
      this.getDataInfo()
    })
  }

  enterSearch = (e) => {
    const { bankName, branchName, place, bankLinkNo } = this.state
    const isDisableBtn = bankName || branchName || place || bankLinkNo

    if (e.keyCode === 13) {
      //搜索的方法
      isDisableBtn ? this.handleSearch() : showMessage.warning(i18n.get('请输入查询条件'))
    }
  }

  handleRowClick = (_selectedRowKeys: string[] | number[], selectedRows: any) => {
    this.setState({ selectedRow: selectedRows[0] })
  }

  render() {
    const { dataSource, current, total, bankName, branchName, place, bankLinkNo, selectedRow, emptyText } = this.state
    const rowSelection = {
      type: 'radio' as RowSelectionType,
      onChange: this.handleRowClick,
      selectedRowKeys: selectedRow ? [selectedRow.id] : (dataSource.length && [dataSource[0].id] || [])
    }
    const isDisableBtn = bankName || branchName || place || bankLinkNo
    return (
      <div id="AdvancedSearchModal" className={styles.advancedSearchModal}>
        <div className="modal-header">
          <div className="flex">{i18n.get('选择开户网点')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleCancel} data-testid="pay-advanced-search-close" />
        </div>
        <div className="modal-content">
          <div className="left-search">
            <span className="">
              {i18n.get('选择银行:')}
              <SearchInput
                className="search-input"
                placeholder={i18n.get('请输入银行名称')}
                onChange={this.handleChange}
                category="bankName"
                value={bankName}
                onKeyUp={this.enterSearch}
                data-testid="pay-advanced-search-bankName"
              />
            </span>
            <span className="mt-24 dis-b">
              {i18n.get('网点名称:')}
              <SearchInput
                className="search-input"
                placeholder={i18n.get('请输入网点名称')}
                onChange={this.handleChange}
                category="branchName"
                value={branchName}
                onKeyUp={this.enterSearch}
                data-testid="pay-advanced-search-branchName"
              />
            </span>
            <span className="mt-24 dis-b">
              {i18n.get('联行号:')}
              <SearchInput
                className="search-input"
                placeholder={i18n.get('请输入联行号')}
                onChange={this.handleChange}
                category="bankLinkNo"
                value={bankLinkNo}
                onKeyUp={this.enterSearch}
                data-testid="pay-advanced-search-bankLinkNo"
              />
            </span>
            <span className="mt-24 dis-b">
              {i18n.get('开户地:')}
              <SearchInput
                className="search-input"
                placeholder={i18n.get('请输入省份或城市')}
                onChange={this.handleChange}
                category="place"
                value={place}
                onKeyUp={this.enterSearch}
                data-testid="pay-advanced-search-place"
              />
            </span>
            <span className="mt-24 dis-f jc-sa">
              <Button key="ok" type="primary" disabled={!isDisableBtn} className="btn" onClick={this.handleSearch} data-testid="pay-advanced-search-button">
                {i18n.get('搜索')}
              </Button>
              <Button key="cancel" className="btn ml-8" onClick={this.handleClear} data-testid="pay-advanced-search-clear">
                {i18n.get('清空')}
              </Button>
            </span>
          </div>
          <div className="right-box">
            <div className="right-result">
              <Table
                className="table"
                rowSelection={rowSelection}
                rowKey={(record: any) => record.id}
                columns={this.columns}
                dataSource={dataSource}
                locale={{emptyText}}
                bordered
                useFixedHeader={true}
                scroll={{ y: 300 }}
                pagination={false}
              />
            </div>
            <Pagination onChange={this.handlePageChange} className="pagination" simple current={current} total={total} />
          </div>
        </div>
        <div className="modal-footer">
          <Button className="btn-ml" onClick={this.handleCancel} data-testid="pay-advanced-search-cancel">
            {i18n.get('取消')}
          </Button>
          <Button type="primary" className="btn-ml" disabled={this.state.loading || !selectedRow} onClick={this.handleSave} data-testid="pay-advanced-search-save">
            {i18n.get('保存')}
          </Button>
        </div>
      </div>
    )
  }
}
export default Form.create()(AdvancedSearchModal as any)
