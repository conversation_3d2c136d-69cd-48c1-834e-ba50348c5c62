@import '~@ekuaibao/eui-styles/less/token.less';
.advancedSearchModal {
  :global {
    .modal-content {
      display: flex;
      padding-bottom: @space-9;
      padding-left: @space-7;
      padding-right: @space-7;
      .left-search {
        .font-size-2;
        width: 250px;
        flex-shrink: 0;
        .jc-sa > .btn {
          .font-size-2;
        }
      }
      .right-box {
        position: relative;
        width: 548px;

        .right-result {
          margin-left: @space-7;
          padding-top:  @space-2;
          overflow-x: auto;
          overflow-y: hidden;

          .table {
            width: 663px;
            overflow: hidden;
            height: 304px;
            .ant-table-header {
              height: 60px;
              overflow: hidden;
            }
            .ant-table-body {
              overflow: hidden;
              max-height: 250px !important;
              padding-bottom: 0 !important;
            }
            .ant-table-thead > tr > th {
              .font-size-2;
              .font-weight-3;
              padding: @space-4;
              padding-left: @space-7;
            }
          }
        }
        .pagination {
          margin-right: -@space-5;
          text-align: right;
          position: absolute;
          right: 24px;
          margin-top: 10px;
        }
      }
    }
  }
}