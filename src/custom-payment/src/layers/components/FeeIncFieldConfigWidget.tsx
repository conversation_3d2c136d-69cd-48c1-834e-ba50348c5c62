import React, { FC } from 'react'
import { GlobalGroupType } from '../../pay-control/types'
// @ts-ignore
import styles from './FeeIncFieldConfigWidget.module.less'
import { Button, Select, Space, TreeSelect, Form } from '@hose/eui'
import { OutlinedEditDeleteTrash, OutlinedTipsAdd } from '@hose/eui-icons'

const FormItem = Form.Item
const FormList = Form.List

interface IProps {
  globalFields: GlobalGroupType[]
  feeTypeList: any[]
}
const FeeIncFieldConfigWidget: FC<IProps> = (props) => {
  const { globalFields, feeTypeList } = props
  return (
    <div className={styles.feeIncFieldConfigWidgetWrapper}>
      <FormList name="feeIncField">
        {(fields, { add, remove }) => (
          <>
            {fields?.map(({ key, name, ...restField }) => {
              return (
                <Space key={key}>
                  <FormItem
                    {...restField}
                    name={[name, 'feeTypeIds']}
                    rules={[{ required: true, message: '请选择费用' }]}>
                    <TreeSelect
                      treeNodeFilterProp={'title'}
                      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                      placeholder={i18n.get('请选择费用')}
                      treeData={feeTypeList}
                      allowClear
                      showSearch
                      multiple
                      data-testid={`pay-fee-field-fee-type-${key}`}
                    />
                  </FormItem>
                  <FormItem
                    {...restField}
                    name={[name, 'fields']}
                    rules={[{ required: true, message: '需要补充的字段' }]}>
                    <Select
                      mode="multiple"
                      placeholder={i18n.get('需要补充的字段')}
                      options={globalFields}
                      data-testid={`pay-fee-field-select-${key}`}
                    />
                  </FormItem>
                  {fields.length > 1 ? (
                    <OutlinedEditDeleteTrash onClick={() => remove(name)} data-testid={`pay-fee-field-delete-${key}`} />
                  ) : (
                    <span />
                  )}
                </Space>
              )
            })}            <FormItem>
              <Space>
                <Button
                  icon={<OutlinedTipsAdd />}
                  theme="highlight"
                  size="small"
                  category="text"
                  onClick={() => add()}
                  data-testid="pay-fee-field-add"
                >
                  {i18n.get('添加范围')}
                </Button>
              </Space>
            </FormItem>
          </>
        )}
      </FormList>
    </div>
  )
}

export default FeeIncFieldConfigWidget
