import React, { FC, useState } from 'react'
// @ts-ignore
import styles from './CardLimitConfigWidget.module.less'
import { specialCaseGroup } from '../../pay-control/types'
import { Button, InputNumber, Radio, Select, Space, Form } from '@hose/eui'
import { OutlinedEditDeleteTrash, OutlinedTipsAdd } from '@hose/eui-icons'

const RadioGroup = Radio.Group
const FormItem = Form.Item

interface IProps {
  staffList: any[]
}

const CardLimitConfigWidget: FC<IProps> = (props) => {
  const { staffList } = props

  const getStaffList = (list: any[], index: number) => {
    const disabledIds = list.reduce((pre: string[], cur, currentIndex: number) => {
      if (currentIndex !== index && cur.staffs?.length) {
        pre = pre.concat(cur.staffs)
      }
      return pre
    }, [])
    return staffList.map((item) => {
      return {
        ...item,
        disabled: disabledIds.includes(item.id),
      }
    })
  }
  const validateSingleTimeLimit = (_rule: any, value: number, callback: any) => {
    if (value === 0 || value < 0) {
      callback(new Error(i18n.get('请输入大于0的数值')))
    } else {
      callback()
    }
  }
  const selectBefore = (name: any, restField: any) => {
    return (
      <FormItem
        className={styles['form-item-singleDayUnit']}
        {...restField}
        name={[name, 'singleDayUnit']}>
        <Select
          style={{ width: 100 }}
          options={[
            {
              value: 'D',
              label: '单日最高',
            },
            {
              value: 'W',
              label: '每周最高',
            },
            {
              value: 'M',
              label: '每月最高',
            },
            {
              value: 'Q',
              label: '季度最高',
            },
          ]}
        />
      </FormItem>
    )
  }
  return (
    <div className={styles.cardLimitConfigWidget}>
      <FormItem
        name="isSpecialCase"
        label={i18n.get('需要特殊处理的人员')}
        rules={[{ required: true, message: '请配置' }]}>
        <RadioGroup>
          <Space direction="vertical">
            {specialCaseGroup.map((item) => (
              <Radio key={item.value} value={item.value} data-testid={`pay-card-limit-radio-${item.value}`}>
                {item.label}
              </Radio>
            ))}
          </Space>
        </RadioGroup>
      </FormItem>
      <FormItem
        noStyle
        shouldUpdate={(prevValues, curValues) =>
          prevValues.isSpecialCase !== curValues.isSpecialCase ||
          prevValues.specialCase !== curValues.specialCase
        }>
        {({ getFieldValue }) => {
          let flag = getFieldValue('isSpecialCase')
          return (
            flag === 'TRUE' && (
              <div className={styles.specialCaseWrapper}>
                <Form.List name="specialCase">
                  {(fields, { add, remove }) => (
                    <div className={styles.conditionWrapper}>
                      {fields.map(({ key, name, ...restField }) => {
                        return (
                          <>
                            <FormItem {...restField} name={[name, 'type']} noStyle />
                            <Space key={key} style={{ alignItems: 'center' }}>
                              <FormItem
                                {...restField}
                                name={[name, 'staffs']}
                                rules={[{ required: true, message: '请选择人员' }]}>
                                <Select
                                  placeholder={i18n.get('请选择人员')}
                                  mode="multiple"
                                  options={getStaffList(getFieldValue('specialCase'), name)}
                                  data-testid={`pay-card-limit-staff-select-${key}`}
                                />
                              </FormItem>
                              <FormItem
                                {...restField}
                                name={[name, 'singleTimeLimit']}
                                rules={[
                                  { required: true, message: '请输入金额' },
                                  { validator: validateSingleTimeLimit },
                                ]}>
                                <InputNumber
                                  size="middle"
                                  addonBefore={'单笔最高'}
                                  addonAfter={'元'}
                                  data-testid={`pay-card-limit-single-time-${key}`}
                                />
                              </FormItem>
                              <FormItem
                                {...restField}
                                name={[name, 'singleDayLimit']}
                                rules={[
                                  { required: true, message: '请输入金额' },
                                  { validator: validateSingleTimeLimit },
                                ]}>
                                <InputNumber
                                  size="middle"
                                  addonBefore={selectBefore(name, restField)}
                                  addonAfter={'元'}
                                  data-testid={`pay-card-limit-single-day-${key}`}
                                />
                              </FormItem>
                              {fields.length > 1 ? (
                                <OutlinedEditDeleteTrash onClick={() => remove(name)} data-testid={`pay-card-limit-delete-${key}`} />
                              ) : (
                                <span />
                              )}
                            </Space>
                          </>
                        )
                      })}
                      <FormItem>
                        <Space>
                          <Button
                            icon={<OutlinedTipsAdd />}
                            theme="highlight"
                            size="small"
                            category="text"
                            onClick={() => add({ type: 'card_limit', singleDayUnit: 'D' })}
                            data-testid="pay-card-limit-add-staff"
                          >
                            {i18n.get('添加人员')}
                          </Button>
                        </Space>
                      </FormItem>
                    </div>
                  )}
                </Form.List>
              </div>
            )
          )
        }}
      </FormItem>
    </div>
  )
}

export default CardLimitConfigWidget
