.feeIncFieldSpecialConfigWidget {
    padding-bottom: 56px;

    :global {
        .eui-form-item {
            margin-bottom: 0;
        }
    }

    .specialCaseWrapper {
        width: 100%;
        padding: 16px;
        border-radius: 6px;
        background: var(--eui-bg-float-base, #F2F3F5);
        margin-top: 8px;

        .conditionWrapper {
            display: flex;
            flex-direction: column;

            :global {

                .eui-space {
                    margin-top: 10px;
                    align-items: baseline;

                    .feeIncFieldSpecialList {
                        display: flex;
                        flex: 1;
                        gap: 8px;

                        .eui-form-item {
                            flex: 1;

                        }
                    }

                    .eui-space-item:nth-of-type(1) {
                        flex: .5 !important;
                    }

                    .eui-space-item:not(:last-of-type) {
                        flex: 1;

                        &:last-of-type {
                            flex: 0;
                        }
                    }

                    .eui-form-item-explain {
                        margin-bottom: 0;
                    }
                }
            }
        }
    }
}