.cardLimitConfigWidget {
    padding-bottom: 56px;

    :global {
        .eui-form-item {
            margin-bottom: 0;
        }
    }

    .specialCaseWrapper {
        width: 100%;
        padding: 16px;
        border-radius: 6px;
        background: var(--eui-bg-float-base, #F2F3F5);
        margin-top: 8px;

        .conditionWrapper {
            display: flex;
            flex-direction: column;

            :global {
                .eui-space {
                    margin-top: 10px;
                    align-items: baseline;

                    .eui-space-item:not(:last-of-type) {
                        flex: 1;

                        &:last-of-type {
                            flex: 0;
                        }
                    }

                    .eui-form-item-explain {
                        margin-bottom: 0;
                    }
                }
            }
        }
    }
}
.form-item-singleDayUnit{
    :global {
    .eui-form-item-control-input{
        min-height: 0;
    }
}
}