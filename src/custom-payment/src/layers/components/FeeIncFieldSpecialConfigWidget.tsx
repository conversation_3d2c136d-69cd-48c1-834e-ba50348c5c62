import React, { FC } from 'react'
// @ts-ignore
import styles from './FeeIncFieldSpecialConfigWidget.module.less'
import { Button, Radio, Select, Space, TreeSelect, Form } from '@hose/eui'
import { FeeIncFieldSpecialGroup, GlobalGroupType, specialCaseGroup } from '../../pay-control/types'
import { OutlinedEditDeleteTrash, OutlinedTipsAdd } from '@hose/eui-icons'

const RadioGroup = Radio.Group
const FormItem = Form.Item

interface IProps {
  globalFields: GlobalGroupType[]
  feeTypeList: any[]
  staffList: any[]
}

const FeeIncFieldSpecialConfigWidget: FC<IProps> = (props) => {
  const { globalFields, feeTypeList, staffList } = props
  const getStaffList = (list: any[], index: number) => {
    const disabledIds = list.reduce((pre: string[], cur, currentIndex: number) => {
      if (currentIndex !== index && cur.staffs?.length) {
        pre = pre.concat(cur.staffs)
      }
      return pre
    }, [])
    return staffList.map((item) => {
      return {
        ...item,
        disabled: disabledIds.includes(item.id),
      }
    })
  }
  return (
    <div className={styles.feeIncFieldSpecialConfigWidget}>
      <FormItem
        name="isSpecialCase"
        label={i18n.get('需要特殊处理的人员')}
        rules={[{ required: true, message: '请配置' }]}>
        <RadioGroup>
          <Space direction="vertical">
            {specialCaseGroup.map((item) => (
              <Radio key={item.value} value={item.value} data-testid={`pay-fee-special-case-radio-${item.value}`}>
                {item.label}
              </Radio>
            ))}
          </Space>
        </RadioGroup>
      </FormItem>
      <FormItem
        noStyle
        shouldUpdate={(prevValues, curValues) =>
          prevValues.isSpecialCase !== curValues.isSpecialCase ||
          prevValues.specialCase !== curValues.specialCase
        }>
        {({ getFieldValue }) => {
          const flag = getFieldValue('isSpecialCase')
          return (
            flag === 'TRUE' && (
              <div className={styles.specialCaseWrapper}>
                <FormItem name="isStandard">
                  <RadioGroup>
                    <Space direction="vertical">
                      {FeeIncFieldSpecialGroup.map((item) => (
                        <Radio key={item.value} value={item.value} data-testid={`pay-fee-special-standard-radio-${item.value}`}>
                          {item.label}
                        </Radio>
                      ))}
                    </Space>
                  </RadioGroup>
                </FormItem>
                <Form.List name="specialCase">
                  {(specialCaseFields, { add, remove }) => (
                    <div className={styles.conditionWrapper}>
                      {specialCaseFields.map(({ key, name, ...restField }) => {
                        return (
                          <>
                            <FormItem {...restField} name={[name, 'type']} noStyle />
                            <Space key={key}>
                              <FormItem
                                {...restField}
                                name={[name, 'staffs']}
                                rules={[{ required: true, message: '请选择人员' }]}>
                                <Select
                                  placeholder={i18n.get('请选择人员')}
                                  mode="multiple"
                                  options={getStaffList(getFieldValue('specialCase'), name)}
                                  data-testid={`pay-fee-special-staff-select-${key}`}
                                />
                              </FormItem>
                              <Form.List name={[name, 'feeIncField']}>
                                {(feeIncFields) =>
                                  feeIncFields.map(
                                    ({ key: innerKey, name: innerName, ...innerRestField }) => {
                                      return (
                                        <div className={'feeIncFieldSpecialList'} key={innerKey}>
                                          <FormItem
                                            {...innerRestField}
                                            name={[innerName, 'feeTypeIds']}
                                            rules={[{ required: true, message: '请选择选择费用' }]}>
                                            <TreeSelect
                                              treeNodeFilterProp={'title'}
                                              dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                                              placeholder={i18n.get('请选择费用')}
                                              allowClear
                                              showSearch
                                              multiple
                                              treeData={feeTypeList}
                                              data-testid={`pay-fee-special-fee-type-${key}-${innerKey}`}
                                            />
                                          </FormItem>
                                          <FormItem
                                            {...innerRestField}
                                            name={[innerName, 'fields']}
                                            rules={[
                                              { required: true, message: '请选择需要补充的字段' },
                                            ]}>
                                            <Select
                                              mode="multiple"
                                              placeholder={i18n.get('需要补充的字段')}
                                              options={globalFields}
                                              data-testid={`pay-fee-special-fields-select-${key}-${innerKey}`}
                                            />
                                          </FormItem>
                                        </div>
                                      )
                                    },
                                  )
                                }
                              </Form.List>
                              {specialCaseFields.length > 1 ? (
                                <OutlinedEditDeleteTrash onClick={() => remove(name)} data-testid={`pay-fee-special-delete-${key}`} />
                              ) : (
                                <span />
                              )}
                            </Space>
                          </>
                        )
                      })}
                      <FormItem>
                        <Button
                          icon={<OutlinedTipsAdd />}
                          theme="highlight"
                          size="small"
                          category="text"
                          onClick={() => {
                            add({
                              type: 'fee_inc_field',
                              staffs: [],
                              feeIncField: [
                                {
                                  feeTypeIds: [],
                                  fields: [],
                                },
                              ],
                            })
                          }}
                          data-testid="pay-fee-special-add-staff"
                        >
                          {i18n.get('添加人员')}
                        </Button>
                      </FormItem>
                    </div>
                  )}
                </Form.List>
              </div>
            )
          )
        }}
      </FormItem>
    </div>
  )
}

export default FeeIncFieldSpecialConfigWidget
