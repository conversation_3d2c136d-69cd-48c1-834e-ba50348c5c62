import React, { FC } from 'react'
// @ts-ignore
import styles from './CommonWhiteListStaffMoney.module.less'
import { InputNumber, Space, Button } from '@hose/eui'
import { OutlinedEditDeleteTrash, OutlinedTipsAdd } from '@hose/eui-icons'
import SelectStaffDepartmentWidget from '../../pay-control/components/SelectStaffDepartmentWidget'
import { cloneDeep } from 'lodash'
import { showMessage } from '@ekuaibao/show-util'
type Value = { staffs: string[]; departments: string[]; singleTimeLimit: number }[]

interface IProps {
  value?: Value
  onChange?: (value: Value | any) => void
}

const CommonWhiteListStaffMoney: FC<IProps> = (props) => {
  const { value, onChange } = props

  const handleStaffChange = (val: { staffs: string[] }, index: number) => {
    const newValue = cloneDeep(value)
    // @ts-ignore
    newValue?.[index]?.staffs = val.staffs
    onChange?.(newValue)
  }

  const handleSingleTimeLimitChange = (singleTimeLimit: number | null, index: number) => {
    const newValue = cloneDeep(value)
    // @ts-ignore
    newValue?.[index]?.singleTimeLimit = singleTimeLimit
    onChange?.(newValue)
  }

  const handleDeleteItem = (index: number) => {
    if (value?.length === 1) {
      showMessage.warning('至少保留一个')
      return
    }
    const newValue = cloneDeep(value)
    newValue?.splice(index, 1)
    onChange?.(newValue)
  }

  const handleAddItem = () => {
    const newValue = cloneDeep(value)
    newValue?.push({ staffs: [], departments: [], singleTimeLimit: 0 })
    onChange?.(newValue)
  }

  return (
    <div className={styles.commonWhiteListStaffMoneyWrapper}>
      {value?.map((item, index) => {
        return (
          <Space key={index}>
            <span>{i18n.get('人员')}</span>
            <SelectStaffDepartmentWidget
              needDepartment
              value={{ staffs: item.staffs, departments: item.departments }}
              onChange={(v) => handleStaffChange(v, index)}
              data-testid={`pay-white-list-staff-select-${index}`}
            />
            <span>{i18n.get('单笔额度')}</span>
            <InputNumber
              addonAfter="元"
              placeholder="输入消费金额"
              value={item.singleTimeLimit}
              onChange={(v) => handleSingleTimeLimitChange(v, index)}
              data-testid={`pay-white-list-amount-input-${index}`}
            />
            <OutlinedEditDeleteTrash onClick={() => handleDeleteItem(index)} data-testid={`pay-white-list-delete-${index}`} />
          </Space>
        )
      })}
      <Space>
        <Button
          icon={<OutlinedTipsAdd />}
          theme="highlight"
          size="small"
          category="text"
          onClick={handleAddItem}
          data-testid="pay-white-list-add"
        >
          {i18n.get('添加名单')}
        </Button>
      </Space>
    </div>
  )
}

export default CommonWhiteListStaffMoney
