import React, { FC } from 'react'
// @ts-ignore
import styles from './RequisitionConfigWidget.module.less'
import { Button, Radio, Select, Space, Form } from '@hose/eui'
import { specialCaseGroup, specialStandardGroup } from '../../pay-control/types'
import { OutlinedEditDeleteTrash, OutlinedTipsAdd } from '@hose/eui-icons'

const RadioGroup = Radio.Group
const FormItem = Form.Item

interface IProps {
  specificationGroups: any[]
  staffList: any[]
}

const RequisitionConfigWidget: FC<IProps> = (props) => {
  const { specificationGroups, staffList } = props
  const getStaffList = (list: any[], index: number) => {
    const disabledIds = list.reduce((pre: string[], cur, currentIndex: number) => {
      if (currentIndex !== index && cur.staffs?.length) {
        pre = pre.concat(cur.staffs)
      }
      return pre
    }, [])
    return staffList.map((item) => {
      return {
        ...item,
        disabled: disabledIds.includes(item.id),
      }
    })
  }
  return (
    <div className={styles.requisitionConfigWidget}>
      <FormItem
        name="isSpecialCase"
        label={i18n.get('需要特殊处理的人员')}
        rules={[{ required: true, message: '请配置' }]}>
        <RadioGroup>
          <Space direction="vertical">
            {specialCaseGroup.map((item) => (
              <Radio key={item.value} value={item.value} data-testid={`pay-requisition-case-radio-${item.value}`}>
                {item.label}
              </Radio>
            ))}
          </Space>
        </RadioGroup>
      </FormItem>
      <FormItem
        noStyle
        shouldUpdate={(prevValues, curValues) =>
          prevValues.isSpecialCase !== curValues.isSpecialCase ||
          prevValues.specialCase !== curValues.specialCase
        }>
        {({ getFieldValue }) => {
          const flag = getFieldValue('isSpecialCase')
          return (
            flag === 'TRUE' && (
              <div className={styles.specialCaseWrapper}>
                <FormItem name="isStandard">
                  <RadioGroup defaultValue={'true'}>
                    <Space direction="vertical">
                      {specialStandardGroup.map((item) => (
                        <Radio key={item.value} value={item.value} data-testid={`pay-requisition-standard-radio-${item.value}`}>
                          {item.label}
                        </Radio>
                      ))}
                    </Space>
                  </RadioGroup>
                </FormItem>
                <Form.List name="specialCase">
                  {(fields, { add, remove }) => (
                    <div className={styles.conditionWrapper}>
                      {fields.map(({ key, name, ...restField }) => {
                        return (
                          <>
                            <FormItem {...restField} name={[name, 'type']} noStyle />
                            <Space key={key}>
                              <FormItem
                                {...restField}
                                name={[name, 'staffs']}
                                rules={[{ required: true, message: '请选择人员' }]}>
                                <Select
                                  placeholder={i18n.get('请选择人员')}
                                  mode="multiple"
                                  options={getStaffList(getFieldValue('specialCase'), name)}
                                  data-testid={`pay-requisition-staff-select-${key}`}
                                />
                              </FormItem>
                              <FormItem
                                {...restField}
                                name={[name, 'specificationIds']}
                                rules={[{ required: true, message: '请选择申请单范围' }]}>
                                <Select
                                  placeholder={i18n.get('请选择申请单范围')}
                                  mode="multiple"
                                  options={specificationGroups}
                                  data-testid={`pay-requisition-scope-select-${key}`}
                                />
                              </FormItem>
                              {fields.length > 1 ? (
                                <OutlinedEditDeleteTrash onClick={() => remove(name)} data-testid={`pay-requisition-delete-${key}`} />
                              ) : (
                                <span />
                              )}
                            </Space>
                          </>
                        )
                      })}
                      <FormItem>
                        <Space>
                          <Button
                            icon={<OutlinedTipsAdd />}
                            theme="highlight"
                            size="small"
                            category="text"
                            onClick={() => add({ type: 'select_requisition' })}
                            data-testid="pay-requisition-add-staff"
                          >
                            {i18n.get('添加人员')}
                          </Button>
                        </Space>
                      </FormItem>
                    </div>
                  )}
                </Form.List>
              </div>
            )
          )
        }}
      </FormItem>
    </div>
  )
}

export default RequisitionConfigWidget
