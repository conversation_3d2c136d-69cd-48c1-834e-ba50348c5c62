import './index.less'
export default [
  {
    key: 'WalletOpenAgreementView',
    getComponent: () => import('../employee/elements/WalletOpenAgreementView'),
  },
  {
    key: 'WalletOpenView',
    getComponent: () => import('../employee/elements/WalletOpenView'),
  },
  {
    key: 'CompanyAccountForm',
    getComponent: () => import('../create-account-form'),
    width: 800,
    style: { minHeight: '432px' },
    maskClosable: false,
  },
  {
    key: 'CompanyWalletForm',
    getComponent: () => import('../elements/company-wallet-form'),
    width: 700,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal',
  },
  {
    key: 'CreateAccountFormPopup',
    getComponent: () => import('../PopupPage/CreateAccountFormViewPopup'),
    title: '',
    width: 700,
    timeout: 500,
    className: '',
    isHoseEUI: true,
    wrapClassName: 'payyee-account-drawer-wrapper',
  },
  {
    key: 'AdvancedSearch',
    getComponent: () => import('./advancedSearchModal/advancedSearchModal'),
    width: 850,
    style: { minHeight: '432px' },
    maskClosable: false,
  },
  {
    key: 'PayerAccountSets',
    getComponent: () => import('./../payer-account-sets'),
    width: 520,
    isHoseEUI: true,
    wrapClassName: 'payee-account-modal',
  },
  {
    key: 'ECardPayControlCommonConfigDrawer',
    getComponent: () => import('./pay-control/ECardPayControlCommonConfigDrawer'),
    enhancer: 'drawer',
    enhancerOptions: {
      title: i18n.get('通用配置'),
      width: 510,
      footer: [],
    },
    maskClosable: false,
  },
  {
    key: 'SelectRequisitionConfigDrawer',
    getComponent: () => import('./pay-control/SelectRequisitionConfigDrawer'),
    enhancer: 'drawer',
    enhancerOptions: {
      title: i18n.get('编辑'),
      width: 560,
      footer: [],
    },
    maskClosable: false,
  },
  {
    key: 'FeeIncFieldConfigDrawer',
    getComponent: () => import('./pay-control/FeeIncFieldConfigDrawer'),
    enhancer: 'drawer',
    enhancerOptions: {
      title: i18n.get('编辑'),
      width: 560,
      footer: [],
    },
    maskClosable: false,
  },
  {
    key: 'CardLimitConfigDrawer',
    getComponent: () => import('./pay-control/CardLimitConfigDrawer'),
    enhancer: 'drawer',
    enhancerOptions: {
      title: i18n.get('编辑'),
      width: 560,
      footer: [],
    },
    maskClosable: false,
  },
  {
    key: 'FeeStandardConfigDrawer',
    getComponent: () => import('./pay-control/FeeStandardConfigDrawer'),
    enhancer: 'drawer',
    enhancerOptions: {
      title: i18n.get('编辑'),
      width: 560,
      footer: [],
    },
    maskClosable: false,
  },
  {
    key: 'SceneOCRConfigDrawer',
    getComponent: () => import('./pay-control/SceneOCRConfigDrawer'),
    enhancer: 'drawer',
    enhancerOptions: {
      title: i18n.get('编辑'),
      width: 560,
      footer: [],
    },
    maskClosable: false,
  },
]
