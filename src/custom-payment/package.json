{"name": "@ekuaibao/plugin-web-custom-payment", "version": "1.2.3-release.0", "author": "---none--->", "scripts": {"fix-jszip-issue": "node ./scripts/fix-jszip-issue.js", "lint": "eslint --ext .tsx,.ts --fix ./src", "start": "npm run dev", "dev": "cross-env NODE_ENV=development webpack-dev-server --progress --color --config webpack.config.dev.js", "clean": "<PERSON><PERSON><PERSON> build", "build": "run-s clean build:src", "upload_plugin_to_cdn": "upload_plugin_to_cdn build", "build:src": "cross-env NODE_ENV=production webpack --progress --color -p --config webpack.config.pro.js", "test": "jest"}, "devDependencies": {"@types/jest": "^24.0.17", "@types/node": "^12.7.1", "@types/react": "^16.9.1", "@typescript-eslint/eslint-plugin": "^3.7.1", "@typescript-eslint/parser": "^3.7.1", "babel-eslint": "^10.1.0", "cross-env": "^7.0.3", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-jsx-control-statements": "^2.2.1", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.20.5", "husky": "^4.2.5", "jest": "^24.8.0", "lint-staged": "^10.2.11", "npm-run-all": "^4.1.5", "prettier": "^2.0.5", "rimraf": "^3.0.2", "ts-jest": "^24.0.2", "upload_to_cdn": "^1.2.8", "whispered-build": "^3.2.0"}, "dependencies": {"@ekuaibao/enhance-layer-manager": "^5.1.3", "@ekuaibao/enhance-stacker-manager": "^3.1.3", "@ekuaibao/eui-styles": "^2.1.0", "@ekuaibao/lib": "^1.2.8", "@ekuaibao/loading": "^4.0.1", "@ekuaibao/vendor-antd": "^3.8.407", "@ekuaibao/vendor-common": "^1.1.0", "@ekuaibao/vendor-lodash": "^4.17.2", "@ekuaibao/vendor-whispered": "^2.2.2", "@ekuaibao/web-theme-variables": "^1.1.1", "ekbc-datagrid": "^5.2.0", "mobx": "^5.15.6", "mobx-react": "^6.3.0", "react": "^16.12.0", "react-dom": "^16.12.0", "tslib": "^1"}, "publishConfig": {"registry": "https://npm.ekuaibao.com/"}, "license": "UNLICENSED", "xhusky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"linters": {"*.{js,jsx,ts,tsx,json,css,less,scss,md}": ["prettier --write", "git add"]}, "ignore": ["**/assets/**/*"]}, "jest": {"moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node", "mjs"], "collectCoverage": true, "collectCoverageFrom": ["<rootDir>/src/**/*.{ts,tsx}", "!**/*.d.ts"], "coverageDirectory": "temp/coverage", "testMatch": ["<rootDir>/src/**/*.spec.{ts,tsx}"], "transform": {"^.+\\.tsx?$": "ts-jest"}}}