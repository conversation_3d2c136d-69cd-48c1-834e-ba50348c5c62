{"compilerOptions": {"target": "es5", "module": "esnext", "lib": ["es2018", "es2015", "es2016", "es2017", "es2019", "dom"], "allowJs": true, "jsx": "react", "declaration": true, "declarationMap": false, "sourceMap": true, "rootDir": "src", "outDir": "build", "removeComments": false, "noEmit": false, "importHelpers": true, "downlevelIteration": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictPropertyInitialization": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": true, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "resolveJsonModule": false, "experimentalDecorators": true, "charset": "utf-8", "emitBOM": false, "newLine": "LF", "pretty": true, "allowUnreachableCode": false, "stripInternal": true, "allowUnusedLabels": false}, "include": ["src", "index.d.ts"]}