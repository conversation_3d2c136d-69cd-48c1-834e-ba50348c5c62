/**************************************
 * Created By LinK On 2020/1/6 15:05.
 **************************************/

export const getMapCheckList = () => [
  {
    title: i18n.get('允许员工创建自有收款账户'),
    name: 'isAllowCreate',
  },
  {
    title: i18n.get('允许员工创建公共收款账户'),
    name: 'isAllowCreatePublic'
  },
  {
    title: i18n.get('允许员工设置账户可见性(共享账户信息)'),
    name: 'allowShared',
    isCommon: true
  },
  {
    title: i18n.get('开启个人账户保护'),
    subTitle: i18n.get('仅员工本人和白名单可见身份证号、完整卡号等隐私信息'),
    name: 'protectPrivacy',
    isPrivacy: true
  },
  {
    title: i18n.get('开启对公账户保护'),
    subTitle: i18n.get('仅员工本人和白名单可见身份证号、完整卡号等隐私信息'),
    name: 'protectPrivacy',
    isPublic: true
  },
  {
    title: i18n.get('禁止员工修改收款账户信息'),
    name: 'forbiddenModify',
    isCommon: true
  },
  {
    title: i18n.get('禁止员工添加非本人银行卡'),
    name: 'forbiddenAdd',
    isPrivacy: true
  },
  {
    title: i18n.get('允许开户网点为非必填项'),
    name: 'networkNotRequired',
    isCommon: true
  },
  {
    title: i18n.get('将备注信息显示在卡片上'),
    name: 'remarkDisplay',
    isCommon: true
  },
  {
    title: i18n.get('开启对公账户专属底色'),
    name: 'publicBackgroundCls',
    isPublic: true
  }
]
