import { app } from '@ekuaibao/whispered'
/**
 * Created by Liyang on 2019/7/30.
 */
import React, { PureComponent } from 'react'
import { SetItemProps } from './types'
import { Checkbox } from '@hose/eui'
const StaffSelect = app.require<any>('@components/select-staff-heavy/staff-select-range')

export default class PayAccoutSets extends PureComponent<SetItemProps<any>> {
  state = {
    isChecked: this.props.isChecked,
  }
  componentWillReceiveProps(nextProps: SetItemProps<string>) {
    this.setState({
      isChecked: nextProps.isChecked,
    })
  }
  render() {
    const { title, subTitle, handleChecked, name, handleAreaClick, privacyVisibility, toolTips } = this.props
    const { isChecked } = this.state
    return (
      <div>
        <Checkbox
          data-testid="pay-payeeAccountSetItem-checkbox"
          style={{ marginRight: 16 }}
          checked={isChecked}
          onChange={(e) => handleChecked(e, name)}>
          <div>
            {title}
            {toolTips}
          </div>
          {!!subTitle && (
            <div
              style={{
                marginTop: 4,
                color: 'var(--eui-text-placeholder, rgba(29, 33, 41, 0.50))',
                font: 'var(--eui-font-body-r1)',
              }}>
              {subTitle}
            </div>
          )}
        </Checkbox>
        {isChecked && name == 'protectPrivacy' && (
          <div
            style={{
              padding: 8,
              borderRadius: '6px',
              marginTop: 8,
              marginLeft: 24,
              background: 'var(--eui-bg-float-base, #F2F3F5)',
            }}>
            <StaffSelect
              multiple
              includeSubWrapperStyle={{ marginTop: '8px' }}
              showIncludeChildren={true}
              className="subject-select"
              placeholder={i18n.get('请选择人员、角色(职级)或部门')}
              value={privacyVisibility}
              tagShowStyle={{ width: '100%' }}
              onValueChange={handleAreaClick}
              data-testid="pay-payeeAccountSetItem-staff-select"
            />
          </div>
        )}
      </div>
    )
  }
}
