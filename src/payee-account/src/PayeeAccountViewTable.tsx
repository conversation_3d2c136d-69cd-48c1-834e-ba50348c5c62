//@ts-ignore
import styles from './PayeeAccountViewTable.module.less'
import React, { FC, useState, useEffect, useCallback } from 'react'
import {
  Button,
  Space,
  Input,
  message,
  Divider,
  Tabs,
  Tag,
  TreeSelect,
  Checkbox,
  Modal,
  Tooltip,
  Ellipsis,
} from '@hose/eui'
import { app } from '@ekuaibao/whispered'
import {
  OutlinedGeneralSetting,
  OutlinedTipsAdd,
  OutlinedGeneralMember,
  OutlinedGeneralGroup,
} from '@hose/eui-icons'
import { PayeeInfoIF } from '@ekuaibao/ekuaibao_types'
const { payFromChannelMap } = app.require('@elements/payee-account/account-list-consts') as any
//@ts-ignore
import CustomBreadcrumb, { PathItem } from '../../custom-payment/src/elements/CustomBreadcrumb'
const { Search } = Input
//@ts-ignore
import { ProTable } from '@hose/pro-table'
import { EnhanceConnect } from '@ekuaibao/store'
//@ts-ignore
import ConfigProviderWrapper from '../../custom-payment/src/elements/ConfigProviderWrapper'
import { useObserver } from 'mobx-react-lite'
import { provider, useInstance } from '@ekuaibao/react-ioc'
import { PermissionVm } from './vms/Permission.vm'
const { UniversalComponent } = app.require('@elements/UniversalComponent') as any
import MessageCenter from '@ekuaibao/messagecenter'
//@ts-ignore
import { Universal_Unique_Key } from './index'
//@ts-ignore
import * as actions from './payee-account-action'
import { Fetch } from '@ekuaibao/fetch'
//@ts-ignore
import key from './key'
import Person from './elements/FieldSelect/Person'
import { find, concat, remove } from 'lodash'
const TreeNode = TreeSelect.TreeNode

export type RuleType = {
  staffId: string
  payerIds: string[]
  configId?: string
}

export type RuleItem = Partial<{
  id: string
  staffId: PayeeInfoIF
  payerIds: string[]
  timestamp: number
  createTime: number
  updateTime: number
  operatorId: string
  operatorName: string
}>

interface SearchInfo {
  current: number
  pageSize: number
  searchText: string
  type: 'ALL' | 'DISABLED' | 'PERSONAL' | 'PUBLIC'
  departmentValue: string[]
  isFilterPayment: boolean // 筛选「银企联支付」不可用账户
}
const bus = new MessageCenter()
const PayeeAccountViewTable: FC = (props: any) => {
  const vm = useInstance<PermissionVm>(PermissionVm.NAME)
  const [loading, setLoading] = useState<boolean>(false)
  const [searchInfo, setSearchInfo] = useState<SearchInfo>({
    current: 1,
    pageSize: 10,
    type: 'ALL',
    searchText: '',
    departmentValue: [],
    isFilterPayment: false,
  })
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [total, setTotal] = useState<number>(0)
  const path: PathItem[] = [
    { name: i18n.get('资金管理') },
    { name: i18n.get(`资金账户管理`) },
    { name: i18n.get('银行收款账户') },
  ]

  const handleSaveAccount = useCallback(
    (data: any) => {
      const type = data.isCreate ? '新建' : '修改' // @i18n-ignore
      actions.savePayee(data).then(() => {
        message.success(i18n.get('保存成功'))
        if (data.isCreate) {
          setSearchInfo({ ...searchInfo, current: 1 })
        } else {
          getPayeeList()
        }
        handleInsertAssist(`${type}${data.accountName}收款账户`) // @i18n-ignore
      })
    },
    [searchInfo],
  )

  useEffect(() => {
    app.invokeService('@common:get:mc:permission:byName', 'ACCOUNT_PEE').then((result: any) => {
      if (result?.value) {
        vm?.setEnableMC(result?.value)
      }
    })
    app.invokeService('@common:get:payee:shared')
    app.invokeService('@common:get:staffs:roleList:department')
  }, [])

  useEffect(() => {
    bus.watch('payee:save:click', handleSaveAccount)
    return () => {
      bus.un('payee:save:click', handleSaveAccount)
    }
  }, [handleSaveAccount])

  useEffect(() => {
    getPayeeList()
  }, [searchInfo])

  const handleInsertAssist = (title: string) => {
    app.invokeService('@common:insert:assist:record', {
      title,
    })
  }
  const fnPayeeListFilter = ({ menu, searchValue, isShowDisabledOnly, isFilterPayment }: any) => {
    const hasSearchValue = `${searchValue === '企业' ? ' owner=="CORPORATION"' : null}` // @i18n-ignore
    return [
      `${menu !== 'ALL' && menu ? 'state==' + menu : null}`,
      `${searchValue ? hasSearchValue : null}`,
      `${isShowDisabledOnly ? 'active == 0' : 'active == 1'}`,
      `(asPayee==true) && sort != "WALLET"`,
      `${isFilterPayment ? '(bankLinkNo=="null" || bankLinkNo=="")' : null}`,
    ]
      .filter((o) => {
        return o !== 'null'
      })
      .join('&&')
  }
  const getPayeeList = () => {
    setLoading(true)
    app.dispatch(actions.getPayeeList(getSearchParams())).then((result: any) => {
      setTotal(result?.count ?? 0)
      setLoading(false)
    })
  }

  const handleOnSearch = (value: string) => {
    setSearchInfo({ ...searchInfo, searchText: value, current: 1 })
  }

  const handleEdit = (record: any, isCreate: boolean) => {
    app.open('@bills:PayeeAccountCreatePopup', {
      payee: !isCreate
        ? {
            ...record,
            owner: record.owner,
          }
        : {
            ...record,
            owner: 'CORPORATION',
          },
      isCreate,
      bus: bus,
      disableType: 'MCDISABLE',
      mcDisable: isCreate || vm.getEnabledEdit(record),
      handleActiveCheck: handleActiveCheck,
      payFromChannel: payFromChannelMap.manage,
    })
    handleInsertAssist(`查看${record.accountName}收款账户`) // @i18n-ignore
  }
  const handleActiveCheck = (value: any, card: any) => {
    let data = {
      value,
      id: card.id,
    }
    if (value) {
      Modal.confirm({
        title: i18n.get('停用：') + card.accountName,
        content: i18n.get('停用后该账户在收款信息列表中不可见'),
        onOk: () => {
          app.dispatch(actions.setActivePayee(data)).then(() => {
            setTimeout(() => {
              setSearchInfo({ ...searchInfo, current: 1 })
            }, 200)
          })
          app.invokeService('@common:get:payeeinfos')
          handleInsertAssist(`停用${card.accountName}收款账户`) // @i18n-ignore
        },
      })
    } else {
      app.dispatch(actions.setActivePayee(data)).then(() => {
        setTimeout(() => {
          setSearchInfo({ ...searchInfo, current: 1 })
        }, 200)
      })
      app.invokeService('@common:get:payeeinfos')
      handleInsertAssist(`启用${card.accountName}收款账户`) // @i18n-ignore
    }
  }

  const columns = [
    {
      title: i18n.get('户名'),
      dataIndex: 'accountName',
      key: 'accountName',
      width: 232,
      render: (accountName: any) => {
        if (accountName.length < 4) {
          return <span className="bankInfo_table_remark">{accountName}</span>
        }
        return (
          <Tooltip
            getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
            title={accountName}
            placement="topLeft">
            <span className="bankInfo_table_remark">{accountName}</span>
          </Tooltip>
        )
      },
    },
    {
      title: i18n.get('账户与账号'),
      dataIndex: 'bankName',
      key: 'bankName',
      width: 323,
      render: (_: string, payerInfo: PayeeInfoIF) => {
        const accountNo = payerInfo.accountNo || payerInfo.cardNo
        const name =
          payerInfo.branch ||
          payerInfo.bankName ||
          payerInfo.bank ||
          payerInfo.name ||
          payerInfo.unionBank
        const iconUrl = payerInfo.icon || payerInfo.unionIcon
        return (
          <div className="bankInfo_table_item">
            <img src={iconUrl ? `${iconUrl}?type=new` : ''} />
            <div>
              <Ellipsis className="bankInfo_table_item_name" content={name} />
              {accountNo && (
                <div className="bankInfo_table_item_num">
                  {accountNo?.replace(/\s/g, '')?.replace(/(.{4})/g, '$1 ')}
                </div>
              )}
            </div>
          </div>
        )
      },
    },
    {
      title: i18n.get('账户类型'),
      dataIndex: 'type',
      width: 136,
      key: 'type',
      render: (type: string, record: any) => {
        const fullVisible = record.visibility?.fullVisible
        return (
          <Space size={4}>
            <Tag
              color={type === 'PERSONAL' ? 'pri' : 'pur'}
              icon={
                type === 'PERSONAL' ? (
                  <OutlinedGeneralMember fontSize={14} />
                ) : (
                  <OutlinedGeneralGroup fontSize={14} />
                )
              }>
              {type === 'PERSONAL' ? i18n.get('个人') : i18n.get('对公')}
            </Tag>
            {fullVisible && <Tag color="neu">{i18n.get('共享')}</Tag>}
          </Space>
        )
      },
    },
    {
      title: i18n.get('所有者'),
      dataIndex: 'owner',
      width: 150,
      key: 'owner',
      render: (owner: string, record: any) => {
        const label = owner === 'CORPORATION' ? i18n.get('企业') : record.staffId?.name
        return (
          <Person src={record?.staffId?.avatar} isCorp={owner === 'CORPORATION'} label={label} />
        )
      },
    },
    {
      title: i18n.get('状态'),
      dataIndex: 'active',
      width: 100,
      key: 'active',
      valueEnum: {
        true: {
          text: i18n.get('启用'),
          status: 'Success',
        },
        false: {
          text: i18n.get('停用'),
          status: 'Error',
        },
      },
    },
    {
      title: i18n.get('备注信息'),
      dataIndex: 'remark',
      key: 'remark',
      width: 162,
      render: (remark: any) => {
        if (remark.length < 4) {
          return <span className="bankInfo_table_remark">{remark}</span>
        }
        return (
          <Tooltip
            getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
            title={remark}
            placement="topLeft">
            <span className="bankInfo_table_remark">{remark}</span>
          </Tooltip>
        )
      },
    },
    {
      title: i18n.get('操作'),
      dataIndex: 'action',
      key: 'action',
      width: 100,
      fixed: 'right',
      render: (_: any, record: any) => {
        return (
          <Space>
            <Button
              category="text"
              size="small"
              onClick={() => handleEdit(record, false)}
              theme="highlight"
              data-testid={`pay-payeeAccountViewTable-edit-button-${record.id}`}
            >
              {i18n.get('编辑')}
            </Button>
          </Space>
        )
      },
    },
  ]
  const handlePageChange = (page: number, pageSize: number) => {
    setSearchInfo({ ...searchInfo, current: page, pageSize: pageSize })
  }
  const handleImport = () => {
    app
      .open('@bills:ImportDetailByExcel', {
        type: 'payee',
      })
      .then((_) => {
        setSearchInfo({ ...searchInfo, current: 1 })
        handleInsertAssist(`导入收款账户`) // @i18n-ignore
      })
  }
  const handleOnTabsChange = (key: string) => {
    setSearchInfo({ ...searchInfo, type: key as any, current: 1 })
  }

  const getSearchParams = () => {
    const {
      searchText: searchValue,
      isFilterPayment,
      type: bankTypeValue,
      departmentValue,
    } = searchInfo
    const dValue = departmentValue.join(',')
    const filter = fnPayeeListFilter({
      menu: 'ALL',
      searchValue,
      isShowDisabledOnly: bankTypeValue === 'DISABLED',
      isFilterPayment,
    })
    const nameLike = searchValue && searchValue !== '企业' ? searchValue : '' // @i18n-ignore
    const start = (searchInfo.current - 1) * searchInfo.pageSize
    const count = searchInfo.pageSize
    const params = filter
      ? {
          filter,
          start,
          count,
          nameLike,
          departmentLike: dValue,
          type: bankTypeValue === 'ALL' || bankTypeValue === 'DISABLED' ? '' : bankTypeValue,
        }
      : {
          start,
          count,
          nameLike,
          departmentLike: dValue,
          type: bankTypeValue === 'ALL' || bankTypeValue === 'DISABLED' ? '' : bankTypeValue,
        }
    return params
  }
  const handleExportAll = () => {
    const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
    const {
      filter,
      encodeNameLike: nameLike,
      encodeDepartmentLike: departmentLike,
      type,
    } = fnGetUrlParams()
    Fetch.GET(`/api/pay/v2/accounts/exportWay?${filter}${nameLike}${departmentLike}${type}`).then(
      (v: any) => {
        if (v.value.exportWay === 'async') {
          app.open('@layout:AsyncExportModal').then((res: any) => {
            Fetch.GET(
              `/api/pay/v2/accounts/export/excel/async?${filter}${nameLike}${departmentLike}`,
              {
                taskName: res.taskName,
              },
            )
          })
        } else {
          const exportUrl = `${Fetch.fixOrigin(
            location.origin,
          )}/api/pay/v2/accounts/export/excel?corpId=${ekbCorpId}&${filter}${nameLike}${departmentLike}`
          message.success(i18n.get(`导出成功`))
          app.emit('@vendor:download', exportUrl)
        }
      },
    )
    handleInsertAssist(`导出收款账户`) // @i18n-ignore
  }

  const fnGetUrlParams = () => {
    const params = getSearchParams()
    const filterStr = params.type ? params.filter + `&&type == "${params.type}"` : params.filter
    const _filterStr = encodeURIComponent(filterStr)
    const filter = _filterStr ? `filter=${_filterStr}` : ''
    const encodeNameLike = params.nameLike ? `&nameLike=${encodeURIComponent(params.nameLike)}` : '' // @i18n-ignore
    const encodeDepartmentLike = params.departmentLike
      ? `&departmentLike=${encodeURIComponent(params.departmentLike)}`
      : ''
    const type = params.type ? `&type=${encodeURIComponent(params.type)}` : ''
    return {
      type,
      filter,
      filterStr,
      encodeNameLike,
      encodeDepartmentLike,
      nameLike: params.nameLike,
      departmentLike: params.departmentLike,
    }
  }

  const handleExportBySelected = async (selectedRowKeys: string[], onCleanSelected: any) => {
    const { filterStr, nameLike, departmentLike } = fnGetUrlParams()
    const result = await actions.postExportExcelAccounts({
      filter: filterStr,
      nameLike,
      departmentLike,
      ids: selectedRowKeys,
    })
    if (result?.url) {
      app.emit('@vendor:download', result?.url, result?.fileName)
      message.success('导出成功')
      onCleanSelected()
    } else {
      message.error('导出失败')
    }
  }

  const getvisibilityList = (visibility: any) => {
    const { staffs, roles, departmentTree } = props
    const staffsState = visibility && visibility.staffs
    const rolesState = visibility && visibility.roles
    const departments = visibility && visibility.departments
    const visibilityList: any[] = []
    let depChild: any[] = []
    function deep(departmentsTree: any[]) {
      departmentsTree.forEach((item) => {
        if (!!item.children) {
          depChild = concat(item.children, depChild)
        }
        {
          deep(item.children)
        }
      })
    }
    deep(departmentTree)

    departments &&
      departments.forEach((item: any) => {
        let dep = find(departmentTree, (line) => line.id === item)
        dep && visibilityList.push(dep)
        let depfind = find(depChild, (line) => line.id === item)
        depfind && visibilityList.push(depfind)
      })
    rolesState &&
      rolesState.forEach((item: any) => {
        visibilityList.push(find(roles, (line) => line.id === item))
      })
    staffsState &&
      staffsState.forEach((item: any) => {
        visibilityList.push(find(staffs, (line) => line.id === item))
      })
    return visibilityList
  }
  const getPayeeConfig = async () => {
    const config = await app.invokeService('@common:get:payee:shared')
    return config.value
  }
  const handleSetBtn = async () => {
    const config = await getPayeeConfig()
    const { publicAccountConfig, personalAccountConfig } = config
    publicAccountConfig.name = 'publicAccountConfig'
    personalAccountConfig.name = 'personalAccountConfig'
    app
      .open('@payeeAccount:PayeeAccountSets', {
        isEnableMC: vm.isEnableMC,
        publicAccountConfig,
        personalAccountConfig,
        getvisibilityList: getvisibilityList,
      })
      .then((data: any) => {
        app.dispatch(actions.setConfigAttr(data))
        setSearchInfo({ ...searchInfo, current: 1 })
      })
  }
  const handleSetVisibilityClick = () => {
    app.open('@payeeAccount:PayeeVisibilitySetModal').then(() => {
      getPayeeList()
    })
  }
  const renderTreeNode = () => {
    const { departmentTree = [] } = props
    const loop = (arr: any) =>
      arr.length > 0 &&
      arr.map((child: any) => {
        const { id, name, children = [], parentId } = child
        return (
          <TreeNode disabled={!parentId} key={id} name={name} title={name} value={id}>
            {children && loop(children)}
          </TreeNode>
        )
      })

    return loop(departmentTree)
  }

  const handleTreeSelectChange = (value: string[]) => {
    setSearchInfo({
      ...searchInfo,
      current: 1,
      departmentValue: value,
    })
  }
  const MaxTagPlaceholder = ({ omittedValues, titleKey = 'label' }: any) => {
    const title = omittedValues.map((item: any) => item[titleKey]).join('，')
    return <Tooltip title={title}>{`+${omittedValues.length}..`}</Tooltip>
  }
  const handleOnCheck = (e: any) => {
    setSearchInfo({
      ...searchInfo,
      current: 1,
      isFilterPayment: e.target.checked,
    })
  }
  return useObserver(() => {
    return (
      <div className={styles['commonWrapper']}>
        <CustomBreadcrumb path={path} />
        <div className="tableWrapper">
          <div className="tableTop">
            <span style={{ flexShrink: 0 }}>{i18n.get('银行收款账户')}</span>
            {vm.isEnableMC && (
              <div style={{ flexShrink: 0, display: 'flex', alignItems: 'center' }}>
                <Space>
                  <UniversalComponent uniqueKey={`${Universal_Unique_Key}.settingVisibility`}>
                    <Button category="secondary" onClick={handleSetVisibilityClick} data-testid="pay-payeeAccountViewTable-batch-visibility-button">
                      {i18n.get('批量修改可见性')}
                    </Button>
                  </UniversalComponent>
                  <Button category="secondary" onClick={handleImport} data-testid="pay-payeeAccountViewTable-import-button">
                    {i18n.get('导入')}
                  </Button>
                  <Button disabled={total === 0} category="secondary" onClick={handleExportAll} data-testid="pay-payeeAccountViewTable-export-all-button">
                    {i18n.get('导出全部')}
                  </Button>
                  <Button
                    data-testid="pay-payeeAccountAdd-button"
                    onClick={() => handleEdit({}, true)}
                    icon={<OutlinedTipsAdd />}>
                    {i18n.get('新建')}
                  </Button>
                </Space>
                <Divider type="vertical" style={{ height: 24 }} />
                <Button
                  category="secondary"
                  onClick={handleSetBtn}
                  icon={<OutlinedGeneralSetting />}
                  data-testid="pay-payeeAccountViewTable-setting-button"
                />
              </div>
            )}
          </div>
          <div className="tableContent">
            <ConfigProviderWrapper>
              <Tabs
                data-testid="pay-payeeAccountViewTabs-tabs"
                onChange={handleOnTabsChange}
                defaultActiveKey="all"
                items={[
                  { label: i18n.get('全部'), key: 'ALL' },
                  { label: i18n.get('个人账户'), key: 'PERSONAL' },
                  { label: i18n.get('对公账户'), key: 'PUBLIC' },
                  { label: i18n.get('已停用'), key: 'DISABLED' },
                ]}
              />
              <div className="tableContentTop">
                <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                  <Search
                    data-testid="pay-payeeAccountSearch-input"
                    allowClear
                    style={{ flex: 1, maxWidth: 334, marginRight: 12 }}
                    onSearch={handleOnSearch}
                    placeholder={i18n.get(`搜索账户名、卡号、证件号、所有者、备注`)}
                  />
                  <TreeSelect
                    data-testid="pay-payeeAccountViewTable-department-select"
                    style={{ maxWidth: 320, flex: 1 }}
                    multiple
                    allowClear
                    showArrow
                    maxTagCount="responsive"
                    maxTagPlaceholder={(omittedValues) => {
                      return <MaxTagPlaceholder omittedValues={omittedValues} />
                    }}
                    treeNodeFilterProp="name"
                    onChange={handleTreeSelectChange}
                    placeholder={i18n.get('请选择可见性部门')}
                    dropdownStyle={{ maxHeight: 260, overflow: 'auto' }}
                    getPopupContainer={(triggerNode: any) => triggerNode.parentNode}>
                    {renderTreeNode()}
                  </TreeSelect>
                </div>

                {props.CHANGJIEPay && (
                  <Checkbox data-testid="pay-payeeAccountViewTable-filter-checkbox" style={{ flexShrink: 0, marginLeft: 12 }} onChange={handleOnCheck}>
                    {i18n.get('筛选「银企联支付」不可用账户')}
                  </Checkbox>
                )}
              </div>
              <ProTable
                data-testid="pay-payeeAccountViewTable-protable"
                loading={loading}
                search={false}
                toolbar={{ style: { display: 'none' } }}
                columns={columns}
                dataSource={props.payeeList}
                scroll={{
                  y:
                    selectedRowKeys.length > 0
                      ? 'calc(100vh - 356px - 102px)'
                      : 'calc(100vh - 292px - 102px)',
                  x: 1203,
                }}
                rowKey="id"
                rowSelection={{
                  selectedRowKeys: selectedRowKeys,
                  preserveSelectedRowKeys: true,
                  onChange: (selectedRowKeys: React.Key[]) => {
                    setSelectedRowKeys(selectedRowKeys)
                  },
                }}
                tableAlertRender={({ selectedRowKeys, onCleanSelected }: any) => (
                  <Space size={32}>
                    <Space size={8}>
                      <span>已选 {selectedRowKeys.length} 项</span>
                      <Button
                        onClick={onCleanSelected}
                        data-testid="pay-payeeAccountViewTable-cancel-selection-button"
                        category="text"
                        size="small"
                        theme="highlight">
                        {i18n.get('取消选择')}
                      </Button>
                    </Space>
                  </Space>
                )}
                tableAlertOptionRender={({ selectedRowKeys, onCleanSelected }: any) => {
                  return (
                    <Space size={8}>
                      <Button
                        category="text"
                        onClick={() => handleExportBySelected(selectedRowKeys, onCleanSelected)}
                        theme="highlight"
                        size="small"
                        data-testid="pay-payeeAccountViewTable-export-selected-button">
                        {i18n.get('导出所选')}
                      </Button>
                    </Space>
                  )
                }}
                pagination={{
                  total,
                  current: searchInfo.current,
                  pageSize: searchInfo.pageSize,
                  showTotal: (total: number) => i18n.get('共 {__k0} 条', { __k0: total }),
                  onChange: handlePageChange,
                  showSizeChanger: true,
                  showQuickJumper: true,
                }}
              />
            </ConfigProviderWrapper>
          </div>
        </div>
      </div>
    )
  })
}
export default provider([PermissionVm.NAME, PermissionVm])(
  EnhanceConnect((state: any) => ({
    payeeList: state[key.ID].payeeList,
    count: state[key.ID].count,
    staffs: state['@common'].staffs,
    roles: state['@common'].roleList,
    departmentTree: state['@common'].department.data,
    userinfo: state['@common'].userinfo && state['@common'].userinfo.staff,
    CHANGJIEPay: state['@common'].powers.CHANGJIEPay,
  }))(PayeeAccountViewTable as any),
)
