/**
 * Created by <PERSON><PERSON><PERSON> on 2017/7/12.
 */

import './payee-account-view.less'
import { app as api } from '@ekuaibao/whispered'
import React, { Component } from 'react'
import { Button, Checkbox, Pagination, TreeSelect, Input, Select, Icon } from 'antd'
const EKBIcon = api.require('@elements/ekbIcon')
import { EnhanceConnect } from '@ekuaibao/store'
import key from './key'
import * as actions from './payee-account-action'
import MessageCenter from '@ekuaibao/messagecenter'
const SVG_SEARCH_EMPTY = api.require('@images/search-empty.svg')
import { Fetch } from '@ekuaibao/fetch'
import { showMessage, showModal } from '@ekuaibao/show-util'
const { fnCheckedList, fnSetVisibleList } = api.require('@elements/payee-account/utils')
import classNames from 'classnames'
import { find, concat, remove } from 'lodash'
import { observer } from 'mobx-react'
import { inject, provider } from '@ekuaibao/react-ioc'
import { PermissionVm } from './vms/Permission.vm'
import { Universal_Unique_Key } from './index'
import { newTrack } from './trackPayeeAccount'
const AccountListItem = api.require('@elements/payee-account/account-list-item')
const { payFromChannelMap } = api.require('@elements/payee-account/account-list-consts')
const ButtonGroup = Button.Group
const TreeNode = TreeSelect.TreeNode
const { Option } = Select
const PAGE_SIZE = 10
const { UniversalComponent } = api.require('@elements/UniversalComponent')
const EmptyBody = api.require('@bills/elements/EmptyBody')
@EnhanceConnect((state) => ({
  payeeList: state[key.ID].payeeList,
  count: state[key.ID].count,
  staffs: state['@common'].staffs,
  roles: state['@common'].roleList,
  departmentTree: state['@common'].department.data,
  userinfo: state['@common'].userinfo && state['@common'].userinfo.staff,
  CHANGJIEPay: state['@common'].powers.CHANGJIEPay,
}))
@provider(['permission', PermissionVm])
@observer
export default class PayeeAccount extends Component {
  @inject('permission') permission: any

  constructor(props) {
    super(props)
    this.bus = props.bus || new MessageCenter()
    this.state = {
      menu: 'ALL',
      isShowActive: false,
      isFilterPayment: false,
      searchValue: '',
      pagination: {
        pageCurrent: 1,
        pageSize: PAGE_SIZE,
      },
      payeeList: props.payeeList,
      total: props.count,
      visibilityList: [],
      departmentValue: [],
      bankTypeValue: 'ALL',
    }
  }

  componentWillMount() {
    api.invokeService('@common:get:mc:permission:byName', 'ACCOUNT_PEE').then((result) => {
      if (result?.value) {
        this.permission?.setEnableMC(result?.value)
      }
    })
  }

  componentDidMount() {
    newTrack('payee_account_view')
    api.invokeService('@common:get:payee:shared')
    api.invokeService('@common:get:staffs:roleList:department')
    api.dispatch(
      actions.getPayeeList({
        filter: '(asPayee==true) && active == 1 && sort != "WALLET"',
        start: 0,
        count: PAGE_SIZE,
      }),
    )
    this.bus.watch('payee:save:click', this.handleSaveAccount) //保存收款账户
  }

  componentWillUnmount() {
    this.bus.un('payee:save:click', this.handleSaveAccount) //保存收款账户
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.payeeList !== nextProps.payeeList) {
      this.setState({
        payeeList: nextProps.payeeList,
        total: nextProps.count,
      })
    }
  }

  fnPayeeListFilter({ menu, searchValue, isShowActive, isFilterPayment }) {
    const hasSearchValue = `${searchValue === '企业' ? ' owner=="CORPORATION"' : null}` // @i18n-ignore
    return [
      `${menu !== 'ALL' && menu ? 'state==' + menu : null}`,
      `${searchValue ? hasSearchValue : null}`,
      `${isShowActive ? null : 'active == 1'}`,
      `(asPayee==true) && sort != "WALLET"`,
      `${isFilterPayment ? '(bankLinkNo=="null" || bankLinkNo=="")' : null}`,
    ]
      .filter((o) => {
        return o !== 'null'
      })
      .join('&&')
  }

  getPayeeList(start = 0, count = PAGE_SIZE) {
    const { menu, searchValue, isShowActive, isFilterPayment, bankTypeValue, departmentValue } =
      this.state
    const dValue = departmentValue.join(',')
    const filter = this.fnPayeeListFilter({
      menu,
      searchValue,
      isShowActive,
      isFilterPayment,
    })
    const nameLike = searchValue && searchValue !== '企业' ? searchValue : '' // @i18n-ignore
    const params = filter
      ? {
          filter,
          start,
          count,
          nameLike,
          departmentLike: dValue,
          type: bankTypeValue === 'ALL' ? '' : bankTypeValue,
        }
      : {
          start,
          count,
          nameLike,
          departmentLike: dValue,
          type: bankTypeValue === 'ALL' ? '' : bankTypeValue,
        }
    return api.dispatch(actions.getPayeeList(params))
  }

  getvisibilityList = (visibility) => {
    const { staffs, roles, departmentTree } = this.props
    const staffsState = visibility && visibility.staffs
    const rolesState = visibility && visibility.roles
    const departments = visibility && visibility.departments
    const visibilityList = []
    let depChild = []
    function deep(departmentsTree) {
      departmentsTree.forEach((item) => {
        if (!!item.children) {
          depChild = concat(item.children, depChild)
        }
        {
          deep(item.children)
        }
      })
    }
    deep(departmentTree)
    deep = null

    departments &&
      departments.forEach((item) => {
        let dep = find(departmentTree, (line) => line.id === item)
        dep && visibilityList.push(dep)
        let depfind = find(depChild, (line) => line.id === item)
        depfind && visibilityList.push(depfind)
      })
    rolesState &&
      rolesState.forEach((item) => {
        visibilityList.push(find(roles, (line) => line.id === item))
      })
    staffsState &&
      staffsState.forEach((item) => {
        visibilityList.push(find(staffs, (line) => line.id === item))
      })
    return visibilityList
  }

  getPayeeConfig = async (_) => {
    let config = await api.invokeService('@common:get:payee:shared')
    return config.value
  }

  handleSetBtn = async () => {
    let config = await this.getPayeeConfig()
    let { publicAccountConfig, personalAccountConfig } = config
    publicAccountConfig.name = 'publicAccountConfig'
    personalAccountConfig.name = 'personalAccountConfig'
    api
      .open('@payeeAccount:PayeeAccountSets', {
        isEnableMC: this.permission.isEnableMC,
        publicAccountConfig,
        personalAccountConfig,
        getvisibilityList: this.getvisibilityList,
      })
      .then((data) => {
        console.log(data)
        api.dispatch(actions.setConfigAttr(data))
        this.getPayeeList()
      })
  }

  handleActiveCheck = (value, card) => {
    let data = {
      value,
      id: card.id,
    }
    if (value) {
      showModal.confirm({
        title: i18n.get('停用：') + card.accountName,
        content: i18n.get('停用后该账户在收款信息列表中不可见'),
        onOk: () => {
          api
            .dispatch(actions.setActivePayee(data))
            .then((_) => this.handleRefreshView(value, card.id, 'active'))
          api.invokeService('@common:get:payeeinfos')
          this.__handleInsertAssist(`停用${card.accountName}收款账户`) // @i18n-ignore
        },
      })
    } else {
      api
        .dispatch(actions.setActivePayee(data))
        .then((_) => this.handleRefreshView(value, card.id, 'active'))
      api.invokeService('@common:get:payeeinfos')
      this.__handleInsertAssist(`启用${card.accountName}收款账户`) // @i18n-ignore
    }
  }

  handleRefreshView = (value, id, _key) => {
    let { payeeList, isShowActive } = this.state
    if (value && !isShowActive) {
      remove(payeeList, function (n) {
        return n.id === id
      })
    } else {
      payeeList.forEach((line) => {
        if (line.id === id) {
          line[_key] = !line[_key]
        }
      })
    }
    this.setState({
      payeeList,
    })
  }

  handleSaveAccount = (data) => {
    const type = data.isCreate ? '新建' : '修改' // @i18n-ignore
    actions.savePayee(data).then((_) => {
      showMessage.success(i18n.get('保存成功'))
      this.handlePageCtrlChange(this.state.pagination.pageCurrent)
      this.__handleInsertAssist(`${type}${data.accountName}收款账户`) // @i18n-ignore
    })
  }

  handleEditAccount = (payee, isCreate) => {
    api.open('@bills:PayeeAccountCreatePopup', {
      payee: !isCreate
        ? {
            ...payee,
            owner: payee.owner,
          }
        : {
            ...payee,
            owner: 'CORPORATION',
          },
      isCreate,
      bus: this.bus,
      disableType: 'MCDISABLE',
      mcDisable: isCreate || this.permission.getEnabledEdit(payee),
      handleActiveCheck: this.handleActiveCheck,
      payFromChannel: payFromChannelMap.manage,
    })
    this.__handleInsertAssist(`查看${payee.accountName}收款账户`) // @i18n-ignore
  }

  handleSearchOnChange = (e) => {
    const { value } = e.target
    ;(!value || (value && value.trim().length)) && this.fnValueChange(e.target.value)
  }

  fnValueChange = (value) => {
    this.setState({
      searchValue: value,
    })
  }

  handlePageCtrlChange = (page, pageSize = PAGE_SIZE) => {
    let { pagination } = this.state
    let start = (page - 1) * pageSize
    let count = pageSize
    pagination.pageCurrent = page
    pagination.pageSize = pageSize
    this.getPayeeList(start, count)
    this.setState(
      {
        pagination,
      },
      () => (document.getElementById('payee-account-scroll').scrollTop = 0),
    )
  }

  handleSetShareAccount = (line) => {
    let { payeeList } = this.state
    let value = !line.visibility.fullVisible // true:取消共享 false:共享
    let refreshList = (_this, _payeeList) => {
      _payeeList.forEach((o) => {
        if (o.id === line.id) {
          o.visibility = {
            fullVisible: value,
          }
        }
      })
      _this.setState({
        payeeList: _payeeList,
      })
    }
    let data = {
      fullVisible: value,
      id: line.id,
    }
    api.dispatch(actions.setShare(data)).then((_result) => {
      refreshList(this, payeeList)
    })
  }

  handleSetShareSelectAccount = (line) => {
    let { payeeList } = this.state
    let refreshList = (_this, _payeeList, _line, _strInfo, visibility) => {
      _payeeList.forEach((o) => {
        if (o.id === _line.id) {
          o.visibility = visibility
        }
      })
      _this.setState({
        payeeList: _payeeList,
        visibility,
      })
    }
    let checkedList = fnCheckedList(line.visibility)
    api
      .open('@layout:SelectStaffsModal', {
        checkedList,
        multiple: true,
        isNeedSubDept: false,
      })
      .then((data) => {
        const { checkedList: checkedListResult } = data
        let { visibility, strInfo } = fnSetVisibleList(checkedListResult)
        visibility = {
          ...visibility,
          fullVisible: false,
        }
        let params = {
          id: line.id,
          ...visibility,
        }
        api.dispatch(actions.setShare(params)).then((_) => {
          refreshList(this, payeeList, line, strInfo, visibility)
        })
      })
  }

  handleExport = () => {
    let ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
    let { menu, searchValue, isShowActive, isFilterPayment } = this.state
    let filterStr = encodeURIComponent(
      this.fnPayeeListFilter({
        menu,
        searchValue,
        isShowActive,
        isFilterPayment,
      }),
    )
    let filter = filterStr ? `filter=${filterStr}` : ''
    const nameLike =
      searchValue && searchValue !== '企业' ? `&nameLike=${encodeURIComponent(searchValue)}` : '' // @i18n-ignore
    let exportUrl = ''
    Fetch.GET(`/api/pay/v2/accounts/exportWay?${filter}${nameLike}`).then((v) => {
      if (v.value.exportWay === 'async') {
        api.open('@layout:AsyncExportModal').then((res: any) => {
          Fetch.GET(`/api/pay/v2/accounts/export/excel/async?${filter}${nameLike}`, {
            taskName: res.taskName,
          })
        })
      } else {
        exportUrl = `${Fetch.fixOrigin(
          location.origin,
        )}/api/pay/v2/accounts/export/excel?corpId=${ekbCorpId}&${filter}${nameLike}`
      }
      api.emit('@vendor:download', exportUrl)
    })
    this.__handleInsertAssist(`导出收款账户`) // @i18n-ignore
  }

  handleImport = () => {
    api
      .open('@bills:ImportDetailByExcel', {
        type: 'payee',
      })
      .then((_) => {
        this.handlePageCtrlChange(1)
        this.__handleInsertAssist(`导入收款账户`) // @i18n-ignore
      })
  }

  renderReminder = () => {
    const { CHANGJIEPay } = this.props
    return CHANGJIEPay ? (
      <div className="concise-reminder"> {i18n.get('Excel导入暂不支持收款信息的简洁录入')} </div>
    ) : null
  }

  __handleInsertAssist = (title) => {
    api.invokeService('@common:insert:assist:record', {
      title,
    })
  }

  renderFooter() {
    const { pagination, total } = this.state
    const cls = this.permission.isEnableMC ? 'footer-wrapper' : 'footer-wrapper disableMC'
    return (
      <div className={cls}>
        {this.permission.isEnableMC && (
          <div className="btn-wrapper">
            <Button
              type="primary"
              className="mr-8"
              data-testid="pay-payeeAccountView-new-button"
              onClick={this.handleEditAccount.bind(this, {}, true)}>
              {i18n.get('新建')}
            </Button>
            <ButtonGroup className="btn-group">
              <Button
                data-testid="pay-payeeAccountView-export-button"
                onClick={this.handleExport.bind(this)}>
                {i18n.get('导出')}
              </Button>
              <Button
                data-testid="pay-payeeAccountView-import-button"
                onClick={this.handleImport.bind(this)}>
                {i18n.get('导入')}
              </Button>
            </ButtonGroup>
          </div>
        )}
        <div>
          <Pagination
            simple
            className="pagination-style"
            current={pagination.pageCurrent}
            onChange={this.handlePageCtrlChange}
            pageSize={PAGE_SIZE}
            total={total}
            data-testid="pay-payeeAccountView-pagination"
          />
        </div>
      </div>
    )
  }

  renderPayeeList() {
    const { payeeList } = this.state
    if (!payeeList.length) {
      return <EmptyBody label={i18n.get('没有找到您想要的信息')} />
    }
    let payeeListWrapper = payeeList.map((line) => {
      return (
        <AccountListItem
          onClick={(e) => {
            e.stopPropagation()
            e.preventDefault()
            this.handleEditAccount(line, false)
          }}
          key={line.id}
          formChannel="payee"
          isManangePage
          payFromChannel={payFromChannelMap.manage}
          className="payee-account-list-item-wrap"
          data={line}
          data-testid={`pay-payeeAccountView-item-${line.id}`}
        />
      )
    })
    return <div className="payee-list"> {payeeListWrapper} </div>
  }

  renderEmpty() {
    return <EmptyBody label={i18n.get('您目前没有收款信息')}></EmptyBody>
  }

  renderSetBtn = () => {
    return (
      <>
        <EKBIcon
          className="cur-p stand-20-icon"
          name="#EDico-setting"
          onClick={this.handleSetBtn}
        />
      </>
    )
  }

  checkboxGroupChange = (e = []) => {
    // 显示已停用
    if (e.length === 1 && e.includes('disabled')) {
      this.setState(
        {
          isFilterPayment: false,
          isShowActive: true,
        },
        () => {
          this.handlePageCtrlChange(1)
        },
      )
      //筛选「银企联支付」不可用账户
    } else if (e.length === 1 && e.includes('filter')) {
      this.setState(
        {
          isFilterPayment: true,
          isShowActive: false,
        },
        () => {
          this.handlePageCtrlChange(1)
        },
      )
    } else if (e.length === 2) {
      this.setState(
        {
          isFilterPayment: true,
          isShowActive: true,
        },
        () => {
          this.handlePageCtrlChange(1)
        },
      )
    } else {
      this.setState(
        {
          isFilterPayment: false,
          isShowActive: false,
        },
        () => {
          this.handlePageCtrlChange(1)
        },
      )
    }
  }

  handleTreeSelectChange = (value) => {
    this.setState({ departmentValue: value })
  }

  handleOptionChange = (value) => {
    this.setState({ bankTypeValue: value })
  }

  handleSearchClick = () => {
    this.handlePageCtrlChange(1)
  }

  handleIconClick = () => {
    this.setState({ searchValue: '' })
  }

  handleSetVisibilityClick = () => {
    newTrack('payee_visibility_modal_click')
    api.open('@payeeAccount:PayeeVisibilitySetModal').then(() => {
      this.getPayeeList()
    })
  }

  renderTreeNode = () => {
    const { departmentTree = [] } = this.props
    const loop = (arr) =>
      arr.length > 0 &&
      arr.map((child) => {
        let { id, name, children = [], parentId } = child

        return (
          <TreeNode disabled={!parentId} key={id} name={name} title={name} value={id}>
            {children && loop(children)}
          </TreeNode>
        )
      })

    return loop(departmentTree)
  }

  renderFilterBtn = () => {
    const { CHANGJIEPay } = this.props
    const options = [
      {
        label: i18n.get('显示已停用'),
        value: 'disabled',
      },
    ]
    if (CHANGJIEPay) {
      options.push({
        label: i18n.get('筛选「银企联支付」不可用账户'),
        value: 'filter',
      })
    }
    return (
      <div className="filter-btn-wrapper mt-20">
        <Checkbox.Group
          className="checkbox-group"
          options={options}
          onChange={this.checkboxGroupChange}
          data-testid="pay-payeeAccountView-filter-checkbox"
        />
        <UniversalComponent uniqueKey={`${Universal_Unique_Key}.settingVisibility`}>
          <Button
            onClick={this.handleSetVisibilityClick}
            data-testid="pay-payeeAccountView-batch-visibility-btn">
            {i18n.get('批量修改可见性')}
          </Button>
        </UniversalComponent>
      </div>
    )
  }

  render() {
    let { isShowActive, menu, payeeList, bankTypeValue, searchValue } = this.state
    let isEmpty = isShowActive && menu === 'ALL' && !payeeList.length
    return (
      <div id={'payeeAccount'} className="payee-account">
        <div
          className={classNames('payee-account-setting', {
            'r-32': !window.isNewHome,
          })}>
          {this.renderSetBtn()}
        </div>
        <div className="content-wrapper" id="payee-account-scroll">
          <div className="content-header">
            {!window.isNewHome && <div className="content-title"> {i18n.get('收款账户')} </div>}
            <div className="right-buttons">
              <Select
                defaultValue={bankTypeValue}
                className="type-select"
                onChange={this.handleOptionChange}
                data-testid="pay-payeeAccountView-bankType-select">
                <Option data-testid="pay-payeeAccountView-bankType-select-all" value="ALL">{i18n.get('全部')}</Option>
                <Option data-testid="pay-payeeAccountView-bankType-select-personal" value="PERSONAL">{i18n.get('个人账户')}</Option>
                <Option data-testid="pay-payeeAccountView-bankType-select-public" value="PUBLIC">{i18n.get('对公账户')}</Option>
              </Select>
              <TreeSelect
                className="department-select"
                multiple
                allowClear
                onChange={this.handleTreeSelectChange}
                placeholder={i18n.get('请选择可见性部门')}
                dropdownStyle={{ maxHeight: 260, overflow: 'auto' }}
                getPopupContainer={() => document.getElementById('payee-account-scroll')}
                data-testid="pay-payeeAccountView-department-select">
                {this.renderTreeNode()}
              </TreeSelect>
              <Input
                className="search"
                suffix={
                  searchValue ? (
                    <Icon
                      type="close-circle"
                      style={{ color: '#9c9c9c' }}
                      onClick={this.handleIconClick}
                    />
                  ) : null
                }
                placeholder={i18n.get('输入账户名称、银行卡号、证件号码、所有者、备注')}
                value={this.state.searchValue}
                onChange={this.handleSearchOnChange}
                data-testid="pay-payeeAccountView-search-input"
              />
              <div
                className="search-btn"
                onClick={this.handleSearchClick}
                data-testid="pay-payeeAccountView-search-button">
                {i18n.get('搜索')}
              </div>
            </div>
            {this.renderFilterBtn()}
          </div>
          {isEmpty ? this.renderEmpty() : this.renderPayeeList()}
        </div>
        {isEmpty ? null : this.renderFooter()}
      </div>
    )
  }
}
