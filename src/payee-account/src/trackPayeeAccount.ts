import { app } from '@ekuaibao/whispered'

export function newTrack(key: string, options: any = {}) {
  const staff = app.getState()['@common'].userinfo?.staff
  window.TRACK &&
    window.TRACK(key, {
      source: window.__PLANTFORM__,
      companyId: staff?.corporation?.id,
      corName: staff?.corporation?.name,
      userId: staff?.userId,
      userName: staff?.name,
      time: new Date().getTime(),
      ...options,
    })
}
