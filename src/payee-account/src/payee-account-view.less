/**
 * Created by <PERSON><PERSON><PERSON> on 2017/7/12.
 */
@import '~@ekuaibao/web-theme-variables/styles/default';
@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/eui-styles/less/token.less';

.payee-account {
  height: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  background: @color-white-1;
  border-radius: 5px;

  .payee-account-setting {
    position: fixed;
    right: @space-6;
    top: 65px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: @space-2;
    &:hover {
      background: rgba(0,0,0,0.1);
      border-radius: @radius-2;
    }
  }
  .r-32 {
    right: @space-8;
  }
  .header {
    display: flex;
    align-items: center;
    padding: 0 24px;
    height: 51px;
    font-size: 13px;
    background: #ffffff;
    border-bottom: solid 1px #dcdcdc;
    justify-content: space-between;
    flex-shrink: 0;
  }
  .content-wrapper {
    display: flex;
    flex: 1;
    flex-direction: column;
    padding: 0 10px;
    overflow: hidden;
    .content-header {
      display: flex;
      justify-content: flex-start;
      flex-direction: column;
      padding: 12px 10px;
      flex-shrink: 0;
      flex-wrap: wrap;
      overflow-y: auto;
      .content-title {
        padding: @space-2 0 @space-5 0;
        .font-size-5;
        color:var(--eui-text-title);
        font-weight: 600;
      }
      .right-buttons {
        display: flex;
        flex-direction: row;
        position: relative;
        .department-select {
          width: 264px;
          margin-right: 18px;
          border-radius: 5px;
          .ant-select-selection--multiple {
            height: 28px;
            border-radius: 5px;
            overflow-y: auto;
          }
        }
        .type-select {
          width: 120px;
          margin-right: 18px;
          .ant-select-selection {
            border-radius: 5px;
          }
        }
        .search {
          flex: none;
          width: 292px;
          height: 28px;
          .ant-input {
            height: 28px;
            border: none;
            border: 1px solid rgba(29,43,61,0.15);
            border-radius: 5px;
          }
          .ant-input-search-icon {
            color: @color-black-2;
          }
        }
        .search-btn {
          position: absolute;
          right: 0;
          width: 68px;
          height: 32px;
          font-size: 14px;
          text-align: center;
          line-height: 30px;
          background: @color-brand;
          color: #fff;
          border: 1px solid @color-brand;
          border-radius: 4px;
          cursor: pointer;
        }
      }
      .filter-btn-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .checkbox-group {
          .ant-checkbox-wrapper {
            .font-size-2;
            .font-weight-2;
            color: @color-black-2;
            .ant-checkbox {
              .ant-checkbox-inner {
                border: 1px solid @color-black-4;
              }
            }
          }
        }
      }
    }
    .payee-list {
      flex: 1;
      padding: 0 10px 0 10px;
      margin-bottom: @space-6;
      overflow-y: auto;
    }
    .empty-wrapper {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100%;
      width: 100%;
      margin-bottom: @space-6;
    }
  }
  .footer-wrapper {
    flex-shrink: 0;
    background-color: @color-white-1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 56px;
    padding: 0 @space-7;
    box-shadow: 0px 6px 24px 0px rgba(29, 43, 61, 0.2);
    .btn-wrapper {
      display: flex;
      align-items: center;
      .btn-group {
        > .ant-btn {
          line-height: normal;
          &:first-child {
            border-radius: 4px 0px 0px 4px;
          }
          &:last-child {
            border-radius: 0px 4px 4px 0px;
          }
        }
      }
    }
    .ant-pagination-next:hover a,.ant-pagination-prev:hover a {
      text-decoration: none;
    }
  }
  .disableMC {
    justify-content: flex-end;
  }
}
