/*
 *
 * 　　┏┓　　　┏┓+ +
 * 　┏┛┻━━━┛┻┓ + +
 * 　┃　　　　　　　┃
 * 　┃　　　━　　　┃ ++ + + +
 *  ████━████ ┃+
 * 　┃　　　　　　　┃ +
 * 　┃　　　┻　　　┃
 * 　┃　　　　　　　┃ + +
 * 　┗━┓　　　┏━┛
 * 　　　┃　　　┃
 * 　　　┃　　　┃ + + + +
 * 　　　┃　　　┃
 * 　　　┃　　　┃ +  神兽保佑
 * 　　　┃　　　┃    代码无bug
 * 　　　┃　　　┃　　+
 * 　　　┃　 　　┗━━━┓ + +
 * 　　　┃ 　　　　　　　┣┓
 * 　　　┃ 　　　　　　　┏┛
 * 　　　┗┓┓┏━┳┓┏┛ + + + +
 * 　　　　┃┫┫　┃┫┫
 * 　　　　┗┻┛　┗┻┛+ + + +
 *
 */

import React, { useState, useContext, useCallback } from 'react'
import PayeeAccountSetItem from './payee-account-setItem'
import { Select, Checkbox, message, Space } from '@hose/eui'
import { app } from '@ekuaibao/whispered'
import styles from './payee-account-tab-details.module.less'
import PayeeContext from './payee-account-context'
import { getV } from '@ekuaibao/lib/lib/help'
import { showModal } from '@ekuaibao/show-util'
import { FieldSelect } from './elements/FieldSelect/fieldSelect'
import { useForceRender } from './hooks'
const StaffSelect = app.require<any>('@components/select-staff-heavy/staff-select-range')

interface IDataSource {
  type: string
  indexKey: string
  mapCheckList: Array<any>
  defaultAccountConfig: any
  defaultAccountTypes: Array<any>
}

export default function PayeeAccountTabDetails(dataSource: IDataSource) {
  //------------------props 数据源------------------------
  let {
    mapCheckList = [],
    defaultAccountTypes = [],
    defaultAccountConfig,
    type,
    indexKey,
  } = dataSource
  const { publicAccountConfig, personalAccountConfig } = useContext(PayeeContext) as any
  const personalAccountString = 'personalAccountConfig'
  mapCheckList = mapCheckList.filter(
    (ele: any) => ele.isCommon || (type === personalAccountString ? ele.isPrivacy : ele.isPublic),
  )

  //------------------init 数据------------------------
  let accountConfig: any =
    type === personalAccountString ? personalAccountConfig : publicAccountConfig
  accountConfig = accountConfig ?? defaultAccountConfig
  const createAccount: any = getV(accountConfig, 'createAccount', {})
  const accountSortValue: any = getV(accountConfig, 'accountSort', [])
  const creator: any = getV(createAccount, 'creator', {})
  const allowShared = getV(accountConfig, 'allowShared', false)
  const forbiddenModify = getV(accountConfig, 'forbiddenModify', false)
  const forbiddenAdd = getV(accountConfig, 'forbiddenAdd', false)
  const networkNotRequired = getV(accountConfig, 'networkNotRequired', false)
  const conciseInput = getV(accountConfig, 'conciseInput', false)
  const protectPrivacy = getV(accountConfig, 'privacy.protectPrivacy', false)
  const visibility: any = getV(accountConfig, 'privacy.visibility')
  const remarkDisplay = getV(accountConfig, 'remarkDisplay', false)
  const customFields = getV(accountConfig, 'customFields', [])
  const showCardCustomFields = getV(accountConfig, 'showCardCustomFields', [])
  const publicBackgroundCls = getV(accountConfig, 'publicBackgroundCls', '')

  //------------------hooks state------------------------
  const [checked, handleCreateChecked] = useState(createAccount.checked)
  const [fullVisible, changeCreator] = useState(creator.fullVisible ? 'ALL' : 'PART')
  const [visible, changeVisible] = useState(createAccount.visible ? 'ALL' : 'OWN')
  const [accountSort, changeAccountType] = useState(
    accountSortValue ?? ['BANK', 'ALIPAY', 'OVERSEABANK', 'OTHER'],
  )
  const [othersCheckedMap, changeOthersCheckedChange] = useState({
    allowShared,
    conciseInput,
    protectPrivacy,
    forbiddenModify,
    forbiddenAdd,
    networkNotRequired,
    remarkDisplay,
    customFields,
    showCardCustomFields,
    publicBackgroundCls,
  } as any)
  const [privacyVisibility, setPrivacyVisibility] = useState(visibility)
  const [errText, handleAccountError] = useState('')
  const [customField, setCustomField] = useState(customFields)

  const forceRender = useForceRender()

  //允许 全部/部分 创建
  function handleCreatorSelect(value: any) {
    if (creator) {
      creator.fullVisible = value === 'ALL'
      //选中全部 清空部分值
      if (creator.fullVisible) {
        creator.departments = []
        creator.roles = []
        creator.staffs = []
      }
    }
    return changeCreator(value)
  }

  //默认 全部/自己 可见
  function handleVisibleSelect(value: any) {
    const createAccountConfig: any = getV(accountConfig, 'createAccount')
    if (createAccountConfig) {
      createAccountConfig.visible = value === 'ALL'
    }
    return changeVisible(value)
  }

  // 全部/部分 选的人 角色 部门
  async function handleSelectStaffs(value: any) {
    const objValue: any = {
      ...value,
      fullVisible: creator.fullVisible,
    }
    createAccount.creator = objValue
    changeCreator(objValue)
  }
  //点击选人组件区域事件
  const handleSelectStaff = async (value: any) => {
    accountConfig.privacy.visibility = value
    setPrivacyVisibility(value)
  }

  //勾选 创建账户
  function handleAllowChecked(e: any) {
    const createAccountConfig: any = getV(accountConfig, 'createAccount')
    if (createAccountConfig) {
      createAccountConfig.checked = e.target.checked
    }
    return handleCreateChecked(e.target.checked)
  }
  /**
   * 自定义账户
   */
  const handleCustomFieldChange = useCallback(
    (value: string[]) => {
      const conFirm = () => {
        accountConfig.customFields = value ?? accountConfig.customFields
        if (accountConfig.showCardCustomFields?.length) {
          // 展示字段依赖于当前字段的值，所以过滤掉被取消的值
          accountConfig.showCardCustomFields = accountConfig.showCardCustomFields.filter(
            (item: string) => value.includes(item),
          )
        }
        setCustomField(value)
      }
      const limit = 10

      // 当前数据小于之前选择的值为删除字段
      if (value.length < customField.length) {
        return showModal.confirm({
          title: i18n.get('你确定删除该字段吗？'),
          content: i18n.get('删除字段会影响历史数据，请确认'),
          okText: i18n.get('确认'),
          cancelText: i18n.get('取消'),
          onOk: conFirm,
        })
      }

      if (value.length > limit) {
        message.error(i18n.get(`最多可选择${limit}个标签自定义`))
        return
      }
      conFirm()
    },
    [accountConfig, customField],
  )

  /**
   * 展示卡片账户
   */
  const handleCardCustomFieldChange = useCallback(
    (value: string[]) => {
      const limit = 3
      if (value.length > limit) {
        message.error(i18n.get(`最多可选择${limit}个字段`))
        return
      }
      accountConfig.showCardCustomFields = value ?? accountConfig.showCardCustomFields
      forceRender()
    },
    [accountConfig],
  )

  //账号类别切换
  function handleAccountTypeChange(value: any) {
    if (value.length === 0) {
      handleAccountError(i18n.get('至少保留一个选项'))
      return
    }
    accountConfig.accountSort = value
    handleAccountError('')
    return changeAccountType(value)
  }

  //勾选 "其它" 的事件
  function handleCheckedChange(e: any, name: string) {
    console.log(name)
    if (name === 'networkNotRequired' && e.target.checked) {
      showModal.confirm({
        content: i18n.get(
          '开户网点为空时，可能会导致支付失败。请确认未使用线上支付渠道，并谨慎选择。',
        ),
        okText: i18n.get('继续'),
        cancelText: i18n.get('取消'),
        onCancel() {
          handleCheckedChange({ target: { checked: false } }, 'networkNotRequired') // 再次触发子组件change
        },
      })
    }
    if (name === 'protectPrivacy') {
      const privacy: any = getV(accountConfig, 'privacy')
      privacy[name] = e.target.checked
    } else {
      accountConfig[name] = e.target.checked
    }
    othersCheckedMap[name] = e.target.checked //用于刷新
    return changeOthersCheckedChange({ ...othersCheckedMap })
  }

  //获取数据
  function getCheckedList(visibilityData: any) {
    const departments = getV(visibilityData, 'departments', [])
    const staffs = getV(visibilityData, 'staffs', [])
    const roles = getV(visibilityData, 'roles', [])
    return [
      { type: 'department-member', multiple: true, checkIds: staffs },
      { type: 'department', multiple: true, checkIds: departments },
      { type: 'role', multiple: true, checkIds: roles },
    ]
  }

  //------------------select JSX------------------------

  const createSelect = (
    <Select
      className="item-input"
      getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
      value={fullVisible}
      onSelect={handleCreatorSelect}>
      <Select.Option value="ALL">{i18n.get('所有人')}</Select.Option>
      <Select.Option value="PART">{i18n.get('部分人')}</Select.Option>
    </Select>
  )

  const visibleSelect = (
    <Select
      data-testid="pay-payeeAccountTabDetails-visible-select"
      className="item-input"
      getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
      value={visible}
      onSelect={handleVisibleSelect}>
      <Select.Option value="ALL">{i18n.get('所有人')}</Select.Option>
      <Select.Option value="OWN">{i18n.get('自己')}</Select.Option>
    </Select>
  )

  const partSelect = (
    <div
      style={{
        width: '100%',
        padding: 8,
        borderRadius: '6px',
        marginTop: 8,
        background: ' var(--eui-bg-float-base, #F2F3F5)',
      }}>
      <StaffSelect
        data-testid="pay-payeeAccountTabDetails-staff-select"
        multiple
        includeSubWrapperStyle={{ marginTop: '8px' }}
        showIncludeChildren={true}
        className="subject-select"
        placeholder={i18n.get('请选择人员、角色(职级)或部门')}
        value={creator}
        tagShowStyle={{ width: '100%' }}
        onValueChange={handleSelectStaffs}
      />
    </div>
  ) as any
  const accountType = (
    <Select
      data-testid="pay-payeeAccountTabDetails-accountType-select"
      showArrow={true}
      value={accountSort}
      style={{ width: '100%' }}
      fieldNames={{ label: 'name', value: 'id' }}
      onChange={handleAccountTypeChange}
      placeholder={i18n.get('选择相关的账号类别')}
      options={defaultAccountTypes}
      getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
      mode="multiple"
    />
  ) as any

  //------------------render------------------------

  return (
    <div
      id={`payee-account-tab-details${indexKey}`}
      className={styles['payee-account-tab-details']}>
      <div>
        <div className="item-label">{i18n.get('创建账户')}</div>
        <div className="item-conetent">
          <Checkbox className="mr-8" checked={checked} onChange={handleAllowChecked} data-testid="pay-payeeAccountTabDetails-create-checkbox" />
          <span style={{ marginRight: '8px' }}>{i18n.get('允许')}</span>
          {createSelect}
          <span style={{ marginLeft: '8px', marginRight: '8px' }}>{`${i18n.get('创建')}，${i18n.get(
            '默认',
          )}`}</span>
          {visibleSelect}
          <span style={{ marginLeft: '8px' }}>{i18n.get('可见')}</span>
        </div>
        {!creator.fullVisible && partSelect}
        <div className="item-label mt-24">{i18n.get('账号类别')}</div>
        <div className="">
          {accountType}
          {errText && <span className="error-tips">{errText}</span>}
        </div>

        <div className="item-label mt-24">{i18n.get('自定义字段')}</div>
        <FieldSelect
          mode="multiple"
          className="w-100b"
          type="text"
          showSearch={true}
          placeholder={i18n.get('请选择自定义字段')}
          value={accountConfig?.customFields}
          onChange={handleCustomFieldChange}
          data-testid="pay-payeeAccountTabDetails-customField-select"
        />
        <div className="item-label mt-24">{i18n.get('以下字段展示到卡片')}</div>
        <FieldSelect
          mode="multiple"
          className="w-100b"
          type="text"
          placeholder={i18n.get('请先选择自定义字段，数据来源于自定义字段')}
          dataSource={customField}
          value={accountConfig?.showCardCustomFields}
          onChange={handleCardCustomFieldChange}
          data-testid="pay-payeeAccountTabDetails-showCardField-select"
        />
        <div className="item-label mt-32">{i18n.get('其它')}</div>
        <Space direction="vertical" size={12} style={{ width: '100%' }}>
          {mapCheckList.map((line: any) => {
            return (
              <PayeeAccountSetItem
                key={line.name}
                title={line.title}
                name={line.name}
                toolTips={line.toolTips}
                subTitle={line.subTitle}
                isChecked={othersCheckedMap[line.name]}
                handleChecked={handleCheckedChange}
                handleAreaClick={handleSelectStaff}
                privacyVisibility={privacyVisibility}
              />
            )
          })}
        </Space>
      </div>
    </div>
  )
}
