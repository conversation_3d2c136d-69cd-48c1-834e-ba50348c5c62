/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/9/10 14:12.
 */
import { action, observable } from 'mobx'

export class PermissionVm {
  static NAME = Symbol('PayeeAccountPermissionVm')

  @observable isEnableMC: boolean = false
  @observable isCorpMC: boolean = false

  @action
  setEnableMC(value: any) {
    this.isCorpMC = value?.type === 'MC'
    if (!this.isCorpMC) {
      this.isEnableMC = true
      return
    }
    const permissions: any = value?.permissions
    if (permissions && permissions[0] && permissions[0].auth) {
      this.isEnableMC = true
    } else {
      this.isEnableMC = false
    }
  }

  getEnabledEdit(value: any) {
    if (!this.isCorpMC) {
      return true
    }
    const nodePermissions: any = value?.permissions
    if (Array.isArray(nodePermissions)) {
      return !!nodePermissions.find((el: any) => el.name === 'ALL' && el.auth)
    }
    return false
  }
}
