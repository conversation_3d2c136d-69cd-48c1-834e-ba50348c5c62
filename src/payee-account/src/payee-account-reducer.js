/**
 * Created by <PERSON><PERSON><PERSON> on 2017/7/12.
 */

import { Reducer } from '@ekuaibao/store'
import key from './key'
import { catchError } from '@ekuaibao/lib/lib/lib-util'
import { showMessage, showModal } from '@ekuaibao/show-util'

const reducer = new Reducer(key.ID, {
  payeeList: []
})

reducer.handle(key.GET_PAYEE_LIST)(
  catchError((state, action) => {
    let { payload } = action
    return { ...state, count: payload?.count, payeeList: payload?.items }
  })
)

reducer.handle(key.SET_ACTIVE_PAYEE)(
  catchError((state, _action) => {
    showMessage.success(i18n.get('设置成功'))
    return state
  })
)

reducer.handle(key.SET_SHARE)(
  catchError((state, _action) => {
    showMessage.success(i18n.get('设置成功'))
    return state
  })
)

reducer.handle(key.SET_ALLOW_SHARE)(
  catchError((state, _action) => {
    return state
  })
)

reducer.handle(key.IMPORT_EXCEL)((state, action) => {
  let { error, payload } = action
  if (payload.items && payload.items.length > 0) {
    showModal.error({
      content: action.payload.items.map(line => {
        return <div className="mb-5">{line}</div>
      })
    })
  } else {
    error ? showMessage.error(payload.msg) : showMessage.info(i18n.get('导入成功'))
  }

  return state
})

reducer.handle(key.IS_ALLOW_CREATE_ACCOUNT)(
  catchError((state, _actions) => {
    return state
  })
)
reducer.handle(key.SET_CONFIG_ATTR)(
  catchError((state, _actions) => {
    showMessage.success(i18n.get('设置成功'))
    return state
  })
)

reducer.handle(key.SAVE_PAYEE)(
  catchError((state, _action) => {
    return state
  })
)

reducer.handle(key.GET_BANKS)(
  catchError((state, _actions) => {
    return state
  })
)
export default reducer
