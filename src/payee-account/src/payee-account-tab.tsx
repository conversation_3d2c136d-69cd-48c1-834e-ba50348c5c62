/*
 *
 * 　　┏┓　　　┏┓+ +
 * 　┏┛┻━━━┛┻┓ + +
 * 　┃　　　　　　　┃
 * 　┃　　　━　　　┃ ++ + + +
 *  ████━████ ┃+
 * 　┃　　　　　　　┃ +
 * 　┃　　　┻　　　┃
 * 　┃　　　　　　　┃ + +
 * 　┗━┓　　　┏━┛
 * 　　　┃　　　┃
 * 　　　┃　　　┃ + + + +
 * 　　　┃　　　┃
 * 　　　┃　　　┃ +  神兽保佑
 * 　　　┃　　　┃    代码无bug
 * 　　　┃　　　┃　　+
 * 　　　┃　 　　┗━━━┓ + +
 * 　　　┃ 　　　　　　　┣┓
 * 　　　┃ 　　　　　　　┏┛
 * 　　　┗┓┓┏━┳┓┏┛ + + + +
 * 　　　　┃┫┫　┃┫┫
 * 　　　　┗┻┛　┗┻┛+ + + +
 *
 */

import React, { useState, useContext } from 'react'
import PayeeAccountTabDetails from './payee-account-tab-details'
import PayeeContext from './payee-account-context'
import { Tabs } from '@hose/eui'

interface IDataSource {
  mapCheckList: Array<any>
}

const defaultAccountTypes = () => {
  return [
    { name: i18n.get('银行卡'), id: 'BANK' },
    { name: i18n.get('支付宝'), id: 'ALIPAY' },
    { name: i18n.get('海外账号'), id: 'OVERSEABANK' },
    { name: i18n.get('微信'), id: 'WEIXIN' },
    { name: i18n.get('承兑汇票'), id: 'ACCEPTANCEBILL' },
    { name: i18n.get('其它'), id: 'OTHER' },
  ]
}

//默认值
const defaultAccountConfig = {
  createAccount: {
    checked: false,
    creator: {
      fullVisible: false,
      staffs: [],
      roles: [],
      departments: [],
    },
    visible: false,
  },
  accountSort: [],
  privacy: {
    protectPrivacy: false,
  },
  allowShared: false,
  forbiddenModify: false,
  conciseInput: false,
}

export default function PayeeAccountTab(dataSource: IDataSource = { mapCheckList: [] }) {
  const [activeTabKey, changeTabs] = useState('personalAccountConfig')
  const { parentState } = useContext(PayeeContext) as any

  const handleOnTabsChange = (activeKey: string) => {
    parentState.activeTabKey = activeKey
    changeTabs(activeKey)
  }

  return (
    <div>
      <Tabs
        data-testid="pay-payeeAccountTab-tabs"
        onChange={handleOnTabsChange}
        defaultActiveKey="all"
        items={[
          {
            label: i18n.get('个人账户'),
            key: 'personalAccountConfig',
            children: (
              <PayeeAccountTabDetails
                type={activeTabKey}
                indexKey={'personalAccountConfig'}
                defaultAccountTypes={defaultAccountTypes()}
                defaultAccountConfig={defaultAccountConfig}
                mapCheckList={dataSource.mapCheckList}
              />
            ),
          },
          {
            label: i18n.get('对公账户'),
            key: 'publicAccountConfig',
            children: (
              <PayeeAccountTabDetails
                type={activeTabKey}
                indexKey={'publicAccountConfig'}
                defaultAccountTypes={defaultAccountTypes()}
                defaultAccountConfig={defaultAccountConfig}
                mapCheckList={dataSource.mapCheckList}
              />
            ),
          },
        ]}></Tabs>
    </div>
  )
}
