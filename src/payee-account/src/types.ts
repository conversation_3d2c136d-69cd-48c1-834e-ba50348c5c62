/*!
 * Copyright 2019 liyang. All rights reserved.
 * @since 2019-07-30 15:42:25
 */

export interface IsAllowAttr<T = boolean> {
  isAllowCreate: T
  isFilterPayment: T
  isAllowCreatePublic: T
  isAllowConcisionType: T
  isAllowShare: T
  isShowActive: T
  isShowPrivacy: T
  isForbiddenModify: T
  isShowPublicPrivacy?: boolean
  PublicVisibilityList?: any[]
  publicVisibility?: InterfaceVisibility
  visibility: InterfaceVisibility
}

interface InterfaceVisibility {
  fullVisible: boolean
  roles: string[]
  staffs: string[]
  departs: string[]
  departmentsIncludeChildren?: boolean
}
export interface IsAllowProps<T = any> extends IsAllowAttr {
  handleFilterPayment(e: any): void
  handleAllowAccountCreate(e: any): void
  handleSetAllowShare(e: any): void
  handleShowActive(e: any): void
  handleAllowCreatePublic(e: any): void
  handleAllowConcisionType(e: any): void
}

export interface SetItemProps<T> {
  title: T
  name: T
  toolTips: T
  isChecked: boolean
  subTitle?: T
  handleChecked: (e: any, name: string) => void
  handleAreaClick?: (e: any, name: string) => void
  handleClose?: Function
  privacyVisibility?: any
}
