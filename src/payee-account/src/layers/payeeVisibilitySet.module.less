@import '~@ekuaibao/web-theme-variables/styles/default';
@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/eui-styles/less/token.less';

.payee-visibility-set-details {
  display: flex;
  flex-direction: column;
  padding: 0 24px;
  :global {
    .step-wrapper {
      padding: 0 190px;
    }
    .content {
      flex: 1;
    }
  }
}

.payee-visibility-set-wrapper {
  :global{
    .ant-modal-body {
      padding: 0;
    }

    .ant-modal-footer {
      border: none;
      padding: 0;
    }

    .footer {
      display: flex;
      height: 56px;
      padding: 12px 24px;
      box-shadow: 0px -8px 16px -13px rgba(0, 0, 0, 0.14);
      
      .next, .prev, .success {
        width: 68px;
        height: 32px;
        text-align: center;
        line-height: 30px;
        background: @color-brand;
        color: #fff;
        border: 1px solid @color-brand;
        border-radius: 4px;
        cursor: pointer;
      }

      .prev {
        background: #fff;
        border: 1px solid rgba(29,43,61,0.15);
        color: #1d2b3d;
        margin-right: 10px;
      }
    }
  }
}