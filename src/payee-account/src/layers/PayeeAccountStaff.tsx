/*
 * @Author: zhangkai
 * @Date: 2021-05-10 11:50:33
 */
import React, { Component } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { remove } from 'lodash'
// @ts-ignore
import styles from './payeeAccountStaff.module.less'

const TagSelector: any = api.require('@elements/tag-selector-edit')
const { getDeptItemsByIds, getItemByIds, getCheckedKeys } = api.require('@lib/lib-util')

interface Props {
  staffs?: any
  roles?: any
  departmentTree?: any
  externalList?: any
  selectedStaffValues: any
  visibility: any
  setStaffValues: (selectedStaffValues: any, visibility: any) => void
}

interface State {}

@EnhanceConnect((state: any) => ({
  staffs: state['@common'].staffs,
  roles: state['@common'].roleList,
  departmentTree: state['@common'].department.data,
  externalList: state['@common'].externalList,
}))
export default class PayeeAccountStaff extends Component<Props, State> {
  valueParse = (value: any) => {
    if (!value) {
      return []
    }
    return [
      ...getDeptItemsByIds(this.props.departmentTree, value.departments || []),
      ...getItemByIds(this.props.staffs, value.staffs || []),
      ...getItemByIds(this.props.roles, value.roles || []),
      ...getItemByIds(this.props.externalList, value.staffs || []),
    ]
  }

  handleTagChange = (deleteItem: any) => {
    const { visibility, setStaffValues } = this.props
    const staffKeys = visibility.staffs || []
    const departments = visibility.departments || []
    const roleKeys = visibility.roles || []
    remove(staffKeys, (id) => id === deleteItem.id)
    remove(departments, (id) => id === deleteItem.id)
    remove(roleKeys, (id) => id === deleteItem.id)
    const params = {
      ...visibility,
      roles: roleKeys,
      staffs: staffKeys,
      departments: departments,
    }
    const selectedStaffValues = this.valueParse(params)
    setStaffValues && setStaffValues(selectedStaffValues, params)
  }

  handleSelectStaffs = (data: any = {}) => {
    const { staffs = [], roles = [], departments = [], departmentsIncludeChildren = true } = data
    const { setStaffValues } = this.props
    api
      .open('@layout:SelectStaffsModal', {
        checkedList: [
          { type: 'department-member', multiple: true, checkedKeys: staffs },
          { type: 'department', multiple: true, checkedKeys: departments },
          { type: 'role', multiple: true, checkedKeys: roles },
        ],
        departmentsIncludeChildren,
      })
      .then((result: any) => {
        const { checkedList, departmentsIncludeChildren: departmentsIncludeChildrenResult } = result
        const staffsResult = getCheckedKeys(checkedList, 'department-member')
        const departs = getCheckedKeys(checkedList, 'department')
        const rolesResult = getCheckedKeys(checkedList, 'role')
        const visibility: any = {
          roles: rolesResult,
          staffs: staffsResult,
          departments: departs,
          departmentsIncludeChildren: departmentsIncludeChildrenResult,
        }
        if (!staffsResult.length && !departs.length && !rolesResult.length) {
          visibility.fullVisible = true
        } else {
          visibility.fullVisible = false
        }
        const selectedStaffValues = this.valueParse(visibility)
        setStaffValues && setStaffValues(selectedStaffValues, visibility)
      })
  }

  render() {
    const { selectedStaffValues, visibility } = this.props
    return (
      <div className={styles['payee-account-staff-wrapper']}>
        <TagSelector
          data-testid="pay-payeeAccountStaff-tag-selector"
          value={selectedStaffValues}
          className="select-person"
          onClick={() => this.handleSelectStaffs(visibility)}
          onChange={(_: any, deleteItem: any) => this.handleTagChange(deleteItem)}
          placeholder={i18n.get('选择适用人员、角色(职级)或部门')}
        />
      </div>
    )
  }
}
