@import '~@ekuaibao/web-theme-variables/styles/default';
@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/eui-styles/less/token.less';

.list-wrapper {
  :global {
    .search-header {
      display: flex;
      flex-direction: row;
      position: relative;
      margin: 30px 0 18px;
      .department-select {
        width: 240px;
        margin-right: 10px;
        border-radius: 5px;
        .ant-select-selection--multiple {
          height: 32px;
          border-radius: 5px;
          overflow-y: auto;
        }
      }
      .type-select {
        width: 120px;
        margin-right: 10px;
        .ant-select-selection {
          border-radius: 5px;
          height: 32px;
          .ant-select-selection__rendered {
            line-height: 32px;
          }
        }
      }
      .search {
        flex: none;
        width: 270px;
        height: 32px;
        .ant-input {
          height: 32px;
          border: none;
          border: 1px solid rgba(29,43,61,0.15);
          border-radius: 5px;
        }
        .ant-input-search-icon {
          color: @color-black-2;
        }
      }
      .search-btn {
        position: absolute;
        right: 0;
        width: 68px;
        height: 32px;
        text-align: center;
        line-height: 30px;
        background: @color-brand;
        color: #fff;
        border: 1px solid @color-brand;
        border-radius: 4px;
        cursor: pointer;
      }
    }

    .table-wrapper {
      height: 405px;
      table {
        padding: 0 !important;
        .ant-table-thead {
          >tr {
            >th {
              background: rgba(29,43,61,0.06) !important;
            }
          }
        }
      }
    }

    .payee-pagination {
      display: flex;
      justify-content: flex-end;
      margin: 10px 0 16px;
    }
  }
}
