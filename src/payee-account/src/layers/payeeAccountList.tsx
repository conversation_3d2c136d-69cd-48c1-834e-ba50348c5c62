/*
 * @Author: z<PERSON><PERSON>
 * @Date: 2021-04-29 16:48:45
 */
import React, { Component } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import { TreeSelect, Input, Select, Icon, Table, Pagination } from 'antd'
// @ts-ignore
import styles from './payeeAccountList.module.less'
import * as actions from '../payee-account-action'

const TreeNode = TreeSelect.TreeNode
const { Option } = Select
const PAGE_SIZE = 10

const columns: any = [
  {
    width: '35%',
    title: i18n.get('开户名称'),
    dataIndex: 'accountName',
    align: 'center',
  },
  {
    width: '10%',
    title: i18n.get('账户类型'),
    dataIndex: 'type',
    align: 'center',
    render: (_: any, line: any) => {
      return line.type === 'PERSONAL' ? i18n.get('个人账户') : i18n.get('对公账户')
    },
  },
  {
    width: '25%',
    title: i18n.get('银行卡号'),
    dataIndex: 'accountNo',
    align: 'center',
  },
  {
    width: '20%',
    title: i18n.get('开户行'),
    dataIndex: 'bank',
    align: 'center',
  },
  {
    width: '10%',
    title: i18n.get('所有者'),
    dataIndex: 'name',
    align: 'center',
    render: (_: any, line: any) => {
      return line.owner === 'CORPORATION' ? i18n.get('企业') : line.staffId && line.staffId.name
    },
  },
]

interface Props {
  departmentTree?: any[]
  selectedRowKeys: any[]
  setSelectedRowKeys: (key: any) => void
}

interface State {
  searchValue: string
  departmentValue: any[]
  bankTypeValue: string
  bankList: any[]
  count: number
  current: number
}

@EnhanceConnect((state: any) => ({
  departmentTree: state['@common'].department.data,
}))
export default class PayeeAccountList extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      searchValue: '',
      departmentValue: [],
      bankTypeValue: 'ALL',
      bankList: [],
      count: 0,
      current: 1,
    }
  }

  componentDidMount() {
    this.getResult()
  }

  getResult = async () => {
    const res: any = await actions.getPayeeAccountList({
      filter: '(asPayee==true) && active == 1 && sort != "WALLET"',
      start: 0,
      count: PAGE_SIZE,
    })
    this.setState({ bankList: res.items || [], count: res.count || 0 })
  }

  handleTreeSelectChange = (value: any) => {
    this.setState({ departmentValue: value })
  }

  handleOptionChange = (value: any) => {
    this.setState({ bankTypeValue: value })
  }

  handleSearchClick = () => {
    this.setState({ current: 1 })
    this.getPayeeList(0)
  }

  handleIconClick = () => {
    this.setState({ searchValue: '' })
  }

  handleSearchOnChange = (e: any) => {
    const { value } = e.target
    const searchValue = value.trim()
    this.setState({
      searchValue,
    })
  }

  onSelectChange = (selectedRowKeys: any, _b: any) => {
    const { setSelectedRowKeys } = this.props
    setSelectedRowKeys && setSelectedRowKeys(selectedRowKeys)
  }

  handlePaginationChange = (current: any, pageSize: any) => {
    this.setState({ current })
    const start = (current - 1) * pageSize
    this.getPayeeList(start)
  }

  getPayeeList = async (start: number) => {
    const { bankTypeValue, departmentValue, searchValue } = this.state
    const dValue = departmentValue.join(',')
    const filter = this.fnGetFilter()
    const params = {
      filter,
      start,
      count: PAGE_SIZE,
      nameLike: searchValue && searchValue !== '企业' ? searchValue : '',
      departmentLike: dValue,
      type: bankTypeValue === 'ALL' ? '' : bankTypeValue,
    }
    const res: any = await actions.getPayeeAccountList(params)
    this.setState({ bankList: res.items || [], count: res.count || 0 })
  }

  fnGetFilter = () => {
    const { searchValue } = this.state
    const hasSearchValue = `${searchValue === '企业' ? ' owner=="CORPORATION"' : null}` // @i18n-ignore
    return [
      `${searchValue ? hasSearchValue : null}`,
      `(asPayee==true) && active == 1 && sort != "WALLET"`,
    ]
      .filter((o) => {
        return o !== 'null'
      })
      .join('&&')
  }

  renderTreeNode = () => {
    const { departmentTree = [] } = this.props
    const loop = (arr: any) =>
      arr.length > 0 &&
      arr.map((child: any) => {
        let { id, name, children = [], parentId } = child

        return (
          <TreeNode disabled={!parentId} key={id} name={name} title={name} value={id}>
            {children && loop(children)}
          </TreeNode>
        )
      })

    return loop(departmentTree)
  }
  render() {
    const { searchValue, bankTypeValue, bankList, count, current } = this.state
    const { selectedRowKeys } = this.props
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange,
    }
    return (
      <div className={styles['list-wrapper']}>
        <div className="search-header">
          <Select
            data-testid="pay-payeeAccountList-type-select"
            defaultValue={bankTypeValue}
            className="type-select"
            onChange={this.handleOptionChange}>
            <Option value="ALL">{i18n.get('全部')}</Option>
            <Option value="PERSONAL">{i18n.get('个人账户')}</Option>
            <Option value="PUBLIC">{i18n.get('对公账户')}</Option>
          </Select>
          <TreeSelect
            data-testid="pay-payeeAccountList-department-select"
            className="department-select"
            multiple
            allowClear
            onChange={this.handleTreeSelectChange}
            placeholder={i18n.get('请选择部门')}
            dropdownStyle={{ maxHeight: 260, overflow: 'auto' }}
            getPopupContainer={(triggerNode) => triggerNode.parentNode}>
            {this.renderTreeNode()}
          </TreeSelect>
          <Input
            data-testid="pay-payeeAccountList-search-input"
            className="search"
            suffix={
              searchValue ? (
                <Icon
                  type="close-circle"
                  style={{ color: '#9c9c9c' }}
                  onClick={this.handleIconClick}
                />
              ) : null
            }
            placeholder={i18n.get('输入账户名称、银行卡号、证件号码、所有者')}
            value={searchValue}
            onChange={this.handleSearchOnChange}
          />
          <div data-testid="pay-payeeAccountList-search-button" className="search-btn" onClick={this.handleSearchClick}>
            {i18n.get('搜索')}
          </div>
        </div>
        <Table
          data-testid="pay-payeeAccountList-table"
          className="table-wrapper"
          rowClassName={() => 'col-line'}
          size="small"
          bordered
          rowKey="id"
          pagination={false}
          rowSelection={rowSelection}
          columns={columns}
          dataSource={bankList}
          scroll={{ y: 350 }}
        />
        <Pagination
          data-testid="pay-payeeAccountList-pagination"
          className="payee-pagination"
          simple
          current={current}
          total={count}
          onChange={this.handlePaginationChange}
        />
      </div>
    )
  }
}
