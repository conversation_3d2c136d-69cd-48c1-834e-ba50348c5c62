/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/12/12 下午8:05.
 */
import { app } from '@ekuaibao/whispered'
import './index.less'

export default [
  {
    key: 'PayeeChangeLogs',
    getComponent: () => Promise.resolve(app.require('@elements/payee-account/account-change-log')),
    width: 600,
    wrapClassName: 'vertical-center-modal',
  },
  {
    key: 'PayeeAccountSets',
    getComponent: () => import('./../payee-account-sets'),
    width: 600,
    isHoseEUI: true,
    wrapClassName: 'payee-account-modal',
  },
  {
    key: 'PayeeVisibilitySetModal',
    getComponent: () => import('./payeeVisibilitySetModal'),
    width: 787,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal',
  },
]
