/*
 * @Author: zhangkai
 * @Date: 2021-04-29 15:18:22
 */
import React, { Component } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Steps } from 'antd'
// @ts-ignore
import styles from './payeeVisibilitySet.module.less'
import PayeeAccountList from './payeeAccountList'
import PayeeAccountStaff from './PayeeAccountStaff'
import { showMessage } from '@ekuaibao/show-util'
import { putPayeeAccountVisibility } from '../payee-account-action'

const Step = Steps.Step

interface Props {
  layer?: any
}

interface State {
  step: number
  selectedRowKeys: any
  selectedStaffValues: any
  visibility: any
}

//@ts-ignore
@EnhanceModal({
  footer: [],
  title: i18n.get('批量新增'),
  className: styles['payee-visibility-set-wrapper'],
})
export default class PayeeVisibilitySetModal extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      step: 0,
      selectedRowKeys: [],
      selectedStaffValues: [],
      visibility: {},
    }
  }

  handleNext = () => {
    const { selectedRowKeys } = this.state
    if (!selectedRowKeys.length) {
      showMessage.error(i18n.get('请选择账户！'))
      return
    }
    this.setState({ step: 1 })
  }

  handlePrev = () => {
    this.setState({ step: 0 })
  }

  setSelectedRowKeys = (selectedRowKeys: any) => {
    this.setState({ selectedRowKeys })
  }

  setStaffValues = (selectedStaffValues: any, visibility: any) => {
    this.setState({ selectedStaffValues, visibility })
  }

  handleSuccess = () => {
    const { selectedStaffValues, selectedRowKeys, visibility } = this.state
    if (!selectedStaffValues.length) {
      showMessage.error(i18n.get('请选择可见人员！'))
      return
    }
    const params = {
      ids: selectedRowKeys,
      visibility,
      asPayee: true,
    }
    putPayeeAccountVisibility(params).then((res: any) => {
      if (res?.value) {
        showMessage.success(i18n.get('修改成功！'))
      } else {
        showMessage.error(i18n.get(res.errorMessage) || i18n.get('修改失败！'))
      }
      this.props.layer.emitOk({})
    })
  }

  getSteps = () => {
    const { selectedRowKeys, selectedStaffValues, visibility } = this.state
    return [
      {
        index: 0,
        title: i18n.get('选择账户'),
        component: (
          <PayeeAccountList
            selectedRowKeys={selectedRowKeys}
            setSelectedRowKeys={this.setSelectedRowKeys}
          />
        ),
      },
      {
        index: 1,
        title: i18n.get('选择人员'),
        component: (
          <PayeeAccountStaff
            selectedStaffValues={selectedStaffValues}
            visibility={visibility}
            setStaffValues={this.setStaffValues}
          />
        ),
      },
    ]
  }

  render() {
    const { step } = this.state
    if (step < 0) {
      return null
    }
    const steps = this.getSteps()
    const { component } = steps[step]
    return (
      <>
        <div className={styles['payee-visibility-set-details']}>
          <div className="step-wrapper">
            <Steps current={step}>
              {steps.map((item: any) => {
                return item.title && <Step key={item.index} title={item.title} />
              })}
            </Steps>
          </div>
          <div className="content">{component}</div>
        </div>
        <div className="footer">
          {step === 0 ? (
            <div data-testid="pay-payeeVisibilitySetModal-next-button" className="next" onClick={this.handleNext}>
              {i18n.get('下一步')}
            </div>
          ) : (
            <>
              <div data-testid="pay-payeeVisibilitySetModal-prev-button" className="prev" onClick={this.handlePrev}>
                {i18n.get('上一步')}
              </div>
              <div data-testid="pay-payeeVisibilitySetModal-complete-button" className="success" onClick={this.handleSuccess}>
                {i18n.get('完成')}
              </div>
            </>
          )}
        </div>
      </>
    )
  }
}
