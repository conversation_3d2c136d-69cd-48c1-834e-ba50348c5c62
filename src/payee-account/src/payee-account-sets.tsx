import { app } from '@ekuaibao/whispered'
/**
 * Created by Liyang on 2019/7/30.
 */
import React, { PureComponent } from 'react'
import { IsAllowProps } from './types'
import styles from './payee-account-sets.module.less'
import { EnhanceConnect } from '@ekuaibao/store'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { getV } from '@ekuaibao/lib/lib/help'
import { Button, Space, Tooltip } from '@hose/eui'
import { getMapCheckList } from './payee-enums'
import PayeeAccountTab from './payee-account-tab'
import PayeeContext from './payee-account-context'
import { showMessage } from '@ekuaibao/show-util'
import { Universal_Unique_Key } from './index'
const { showFunctionByKey } = app.require('@elements/UniversalComponent')
import { OutlinedTipsInfo } from '@hose/eui-icons'

interface PayProps extends IsAllowProps {
  CHANGJIEPay: any
  layer: any
  placeholder: string
  isEnableMC: any
  staffs: any
  roles: any
  departmentTree: any
  visibilityList: any
  personalAccountConfig: any
  publicAccountConfig: any
  getvisibilityList: Function
}

interface StateConnect<T> {
  [x: string]: {
    powers: T
    staffs: T
    roleList: T
    department: T
  }
}

@EnhanceConnect((state: StateConnect<any>) => ({
  CHANGJIEPay: state['@common'].powers.CHANGJIEPay,
  staffs: state['@common'].staffs,
  roles: state['@common'].roleList,
  departmentTree: state['@common'].department.data,
}))
@EnhanceModal({
  title: i18n.get('设置'),
  footer: null,
})
export default class PayAccoutSets extends PureComponent<PayProps, any> {
  private mapCheckList: any[]

  constructor(props: PayProps) {
    super(props)
    this.getResult = this.getResult.bind(this)
    this.state = {
      fullVisibleErrMsg: '',
    }
    const mapCheckList = getMapCheckList()
    if (props.CHANGJIEPay)
      mapCheckList.push({
        title: i18n.get('开启银企联简洁录入'),
        //@ts-ignore
        toolTips: (
          <Tooltip
            title={i18n.get(
              '银行卡类型的收款账户只需输入开户行名称即可。注意：若为对公付款或使用其他支付渠道，该收款信息可能会导致支付失败！',
            )}>
            <OutlinedTipsInfo
              fontSize={14}
              style={{ marginLeft: 4, color: 'var(--eui-icon-n2)' }}
            />
          </Tooltip>
        ),
        name: 'conciseInput',
        isCommon: true,
      })
    this.mapCheckList = mapCheckList.filter(
      (item) => !!showFunctionByKey(`${Universal_Unique_Key}.${item.name}`),
    )
  }

  protected handleCancel = () => {
    this.props.layer.emitCancel()
  }

  getResult = () => {
    return {}
  }

  private handleModalSave = () => {
    let { publicAccountConfig, personalAccountConfig } = this.props
    publicAccountConfig.hasOwnProperty('name') && delete publicAccountConfig.name
    personalAccountConfig.hasOwnProperty('name') && delete personalAccountConfig.name
    const personalFullVisible = getV(
      personalAccountConfig,
      'createAccount.creator.fullVisible',
      false,
    )
    const publicFullVisible = getV(publicAccountConfig, 'createAccount.creator.fullVisible', false)
    const personalDepartments = getV(personalAccountConfig, 'createAccount.creator.departments', [])
    const personalRoles = getV(personalAccountConfig, 'createAccount.creator.roles', [])
    const personalStaffs = getV(personalAccountConfig, 'createAccount.creator.staffs', [])
    const publicDepartments = getV(publicAccountConfig, 'createAccount.creator.departments', [])
    const publicRoles = getV(publicAccountConfig, 'createAccount.creator.roles', [])
    const publicStaffs = getV(publicAccountConfig, 'createAccount.creator.staffs', [])
    const personalErr =
      !personalDepartments.length && !personalRoles.length && !personalStaffs.length
    const publicErr = !publicDepartments.length && !publicRoles.length && !publicStaffs.length
    publicAccountConfig.publicBackgroundCls = publicAccountConfig.publicBackgroundCls
      ? 'mod-green-bg'
      : null // publicBackgroundCls className, 目前写死，后续可扩展
    //个人部分人创建
    if (!personalFullVisible && !publicFullVisible && publicErr && personalErr) {
      showMessage.error(i18n.get('请选择可以创建个人/对公账户的人员、角色(职级)或部门'))
      return
    } else if (!personalFullVisible && personalErr) {
      showMessage.error(i18n.get('请选择可以创建个人账户的人员、角色(职级)或部门'))
      return
    } else if (!publicFullVisible && publicErr) {
      showMessage.error(i18n.get('请选择可以创建对公账户的人员、角色(职级)或部门'))
      return
    }
    this.setState({ fullVisibleErrMsg: '' }, () => {
      this.props.layer.emitOk({ publicAccountConfig, personalAccountConfig })
    })
  }

  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  render() {
    const {
      isEnableMC = true,
      publicAccountConfig,
      personalAccountConfig,
      getvisibilityList,
    } = this.props

    return (
      <div id={'payeeAccountSets'} className={styles['payee-account-sets']}>
        <div className="account-sets-content">
          <PayeeContext.Provider
            value={{
              publicAccountConfig,
              personalAccountConfig,
              getvisibilityList,
              parentState: this.state,
            }}>
            <PayeeAccountTab mapCheckList={this.mapCheckList} />
          </PayeeContext.Provider>
        </div>
        <div className="modal-footer-button">
          <Space>
            <Button
              category="secondary"
              data-testid="pay-payeeAccountSets-cancel-button"
              onClick={this.handleModalClose}
            >
              {i18n.get('取消')}
            </Button>
            <Button
              data-testid="pay-payeeAccountSets-save-button"
              onClick={this.handleModalSave}
            >
              {i18n.get('保存')}
            </Button>
          </Space>
        </div>
      </div>
    )
  }
}
