@import "~@ekuaibao/eui-styles/less/token";

.payee-account-tab-details {
  max-height: calc(100vh - 200px - 56px - 64px - 54px);
  overflow-x: hidden;
  overflow-y: auto;
  margin: 0 -16px;
  padding: 0 16px;
  :global {
    .select_staffs {
      border: 1px solid @color-line-2;
      border-radius: @radius-2;
      padding: @space-2;
      min-height: 40px;
    }
    .item-label {
      margin-bottom: 8px;
      color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
      font: var(--eui-font-body-b1);
    }
    .mt-32 {
      margin-top: 32px;
    }
    .item-conetent {
      display: flex;
      flex-direction: row;
      align-items: center;
      .item-input {
        flex: 1;
      }
    }
    .error-tips {
      color: @color-error;
    }

    .m-tree-select-dropdown {
      max-height: 280px;
      overflow-y: auto;
      width: 100%;
      border: 1px solid @color-line-2;
    }
  }
}
