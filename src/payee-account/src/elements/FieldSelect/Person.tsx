import React from 'react'
import { FilledGeneralMember, FilledGeneralRoom } from '@hose/eui-icons'
import './Person.less'
interface IProps {
  label?: string //人员名称
  src?: string //头像的地址,可以不传，不传的话用默认头像
  closable?: boolean //是否可以关闭
  onClose?: () => void //关闭的回调
  person?: React.ReactNode //人员的组件
  vallue?: string //人员的id
  isCorp?: boolean //是否是企业
}

const Person: React.FC<IProps> = (options: IProps) => {
  const { label, src, isCorp } = options
  return (
    <div className="__person-wrapper">
      {src ? (
        <img className="member-font" src={src}></img>
      ) : (
        <div className="member-font">
          {isCorp ? (
            <FilledGeneralRoom fontSize={8} style={{ color: '#fff' }} />
          ) : (
            <FilledGeneralMember fontSize={8} style={{ color: '#fff' }} />
          )}
        </div>
      )}
      <div>{label ? label : '人员名'}</div>
    </div>
  )
}

export default Person
