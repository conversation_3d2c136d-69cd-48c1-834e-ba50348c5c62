import { FieldSelectProps } from './types'
import React, { useEffect, useState } from 'react'
import { Select } from '@hose/eui'
import { app } from '@ekuaibao/whispered'

/**
 * 配置字段选择组件
 * @param props
 * @returns
 */
export const FieldSelect: React.FC<FieldSelectProps> = (props) => {
  const { onChange, value, type, dataSource, ...otherProps } = props
  const [options, setOptions] = useState<{ label: string; value: any }[]>([])
  const globalFields = app.getState()['@common'].globalFields.data

  useEffect(() => {
    let fields = globalFields
    if (type) {
      // 数据源为未停用&以及非系统预置字段
      fields = fields.filter((item) => item.dataType.type === type && item.active && !item.ability)
    }
    if (dataSource) {
      fields = dataSource.map((name: string) =>
        globalFields.find((item: any) => item.name === name),
      )
    }

    setOptions(
      fields.map((item) => ({
        label: item.label,
        value: item.name,
      })),
    )
  }, [type, dataSource])

  console.log(options)
  return (
    <Select
      data-testid="pay-fieldSelect-select"
      {...otherProps}
      showArrow={true}
      value={value}
      getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
      onChange={onChange}
      filterOption={(value: string, option: any) => {
        return (option?.props?.children || '').toLowerCase().includes(value)
      }}>
      {options.map((item) => (
        <Select.Option value={item.value} key={item.value}>
          {item.label}
        </Select.Option>
      ))}
    </Select>
  )
}
