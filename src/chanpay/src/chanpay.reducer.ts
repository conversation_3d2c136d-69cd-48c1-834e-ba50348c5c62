import { Reducer } from '@ekuaibao/store'
import key from './key'
import { catchError } from '@ekuaibao/lib/lib/lib-util'
import { showMessage } from '@ekuaibao/show-util'
interface Props {
  [key: string]: any
}
const reducer = new Reducer(key.ID, {})

reducer.handle(
  key.GET_CHANPAY_LIST,
  catchError((state: Props, action: Props) => {
    if (action.error) {
      showMessage.error(action.error.message)
    }
    return { ...state }
  })
)

reducer.handle(
  key.PUT_ACCOUNT_PERSON,
  catchError((state: Props, action: Props) => {
    if (action.error) {
      showMessage.error(action.error.message)
    }
    return { ...state }
  })
)

reducer.handle(
  key.GET_REFRESH_LIST,
  catchError((state: Props, action: Props) => {
    if (action.error) {
      showMessage.error(action.error.message)
    }
    return { ...state }
  })
)

reducer.handle(
  key.GET_BANK_LIST,
  catchError((state: Props, action: Props) => {
    if (action.error) {
      showMessage.error(action.error.message)
    }
    return { ...state }
  })
)

reducer.handle(
  key.GET_PROVINCES_LIST,
  catchError((state: Props, action: Props) => {
    if (action.error) {
      showMessage.error(action.error.message)
    }
    return { ...state }
  })
)

reducer.handle(
  key.GET_CITIES_LIST,
  catchError((state: Props, action: Props) => {
    if (action.error) {
      showMessage.error(action.error.message)
    }
    return { ...state }
  })
)

reducer.handle(
  key.GET_BRANCH_LIST,
  catchError((state: Props, action: Props) => {
    if (action.error) {
      showMessage.error(action.error.message)
    }
    return { ...state }
  })
)

reducer.handle(
  key.POST_CHANPAY_CARD,
  catchError((state: Props, action: Props) => {
    if (action.error) {
      showMessage.error(action.error.message)
    }
    return { ...state }
  })
)

reducer.handle(
  key.POST_PAYMENT,
  catchError((state: Props, action: Props) => {
    if (action.error) {
      showMessage.error(action.error.message)
    }
    return { ...state }
  })
)

export default reducer
