import React, { PureComponent } from 'react'
import { Button } from 'antd'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import styles from './chanpay-success-modal.module.less'
import SVG_SUCCESS from '../images/check-success.svg'
import MessageCenter from '@ekuaibao/messagecenter'
import { app as api } from '@ekuaibao/whispered'
import * as actions from '../chanpay.action'
import { showMessage } from '@ekuaibao/show-util'

interface Props {
  layer: any
  handleRefresh: Function
  param: any
  bus?: any
}

interface State {}

// 畅捷支付开户成功弹窗组件，用于提示用户开户成功并询问是否创建系统支付账户
@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer'
})
export default class ChanpaySuccessModal extends PureComponent<Props, State> {
  private bus: MessageCenter = this.props.bus || new MessageCenter()
  constructor(props: Props) {
    super(props)
    this.state = {}
  }

  // 组件挂载前监听保存付款账户事件
  componentWillMount() {
    this.bus.watch('payment:save:click', this.handleSaveAccount)//保存付款账户
  }

  // 组件卸载时移除事件监听
  componentWillUnmount() {
    this.bus.un('payment:save:click', this.handleSaveAccount)//保存付款账户
  }

  // 处理保存账户信息事件
  handleSaveAccount = (result) => {
    const { handleRefresh } = this.props
    const { data } = result
    api.dispatch(actions.postPayment(data)).then(
      (res: any) => {
        if (res.id) {
          showMessage.success(i18n.get('创建成功'))
          handleRefresh(false)
        }
      },
      err => {
        showMessage.error(err.message)
      }
    )
  }

  // 处理创建系统账户事件
  handleOk = () => {
    const { param } = this.props
    const { acctName, acctNo, branch } = param
    const params = {
      channels: ['CHANPAY', 'CHANPAYV2', 'OFFLINE'],
      visibility: { fullVisible: true },
      accountNo: acctNo,
      accountName: acctName,
      branch,
      state: 'edit',
      title: i18n.get('新建企业付款账户'),
      formChanpaySuccessModal: true
    }
    this.props.layer.emitOk({})
    api.open('@custom-payment:CreateAccountFormPopup', { bus: this.bus, ...params })
  }

  // 处理暂不创建事件
  handleCancel = () => {
    const { handleRefresh } = this.props
    this.props.layer.emitCancel()
    handleRefresh(false)
  }

  // 渲染组件主界面
  render() {
    return (
      <div className={styles['success-modal-wrapper']}>
        <div className="success-modal-container">
          <div className="title-wrapper">
            <img className="img" src={SVG_SUCCESS} />
            <div className="title">
              <span>{i18n.get('开户成功！')}</span>
              <span>{i18n.get('是否同步创建系统支付账户？')}</span>
            </div>
          </div>
          <div className="container">
            {i18n.get('该账户已成功添加至贵司银企联云账户，即日起您将可使用此账户进行支付。')}
          </div>
        </div>
        <div className="modal-footer">
          <Button className="btn-ml" onClick={this.handleCancel} data-testid="pay-chanpay-success-modal-cancel">
            {i18n.get('暂不创建')}
          </Button>
          <Button type="primary" className="btn-ml" onClick={this.handleOk} data-testid="pay-chanpay-success-modal-confirm">
            {i18n.get('创建系统账户')}
          </Button>
        </div>
      </div>
    )
  }
}
