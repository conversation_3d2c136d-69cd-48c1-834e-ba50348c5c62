.query-bank-statement-wrapper {
  :global {
    .modal-header {
      border: none;

      .flex {
        font-size: 20px;
      }

      .cross-icon {
        cursor: pointer;
      }
    }

    .title {
      height: 22px;
      font-size: 14px;
      font-weight: 400;
      color: rgba(29, 43, 61, 0.5);
      line-height: 22px;
      margin: 0 0 16px 16px;
    }

    .line {
      width: 100%;
      height: 8px;
      background: rgba(29, 43, 61, 0.06);
    }

    .addAccount-modal-container {
      padding: 24px 24px 0 24px;
      .item-placeholder {
        height: 22px;
        font-size: 14px;
        color: rgba(29, 43, 61, 0.5);
        line-height: 22px;
      }
      #payment-account-inquire {
        .ant-select-tree {
          .ant-select-tree-checkbox {
            padding-top: 8px;
            vertical-align: top;
          }
        }
        .legal-record {
          .name-code {
            display: flex;
            margin-bottom: 2px;

            .name {
              flex: 1;
              height: 22px;
              font-size: 14px;
              font-weight: 600;
              line-height: 22px;
              color: #03223b;
            }

            .code {
              height: 22px;
              font-size: 14px;
              line-height: 22px;
              color: rgba(29, 43, 61, 0.3);
            }
          }

          .bank-name-number {
            display: flex;
            align-items: center;

            .bank-icon {
              width: 16px;
              height: 16px;
              display: flex;
              align-items: center;
              margin-right: 4px;

              .bank-icon-img {
                width: 100%;
                height: 100%;
              }
            }

            .bank-name {
              height: 22px;
              font-size: 14px;
              line-height: 22px;
              margin-right: 16px;
              color: rgba(29, 43, 61, 0.75);
            }

            .bank-number {
              height: 22px;
              font-size: 14px;
              line-height: 22px;
              margin-right: 16px;
              color: rgba(29, 43, 61, 0.75);
            }
          }
        }
      }
    }

    .agree-item {
      display: flex;
      align-items: center;
      padding: 0 24px;
      margin-bottom: 48px;
      .text {
        font-size: 14px;
        margin-left: 8px;
      }
    }

    .modal-footer {
      border: none;
      box-shadow: 0 4px 24px 0 rgba(29, 43, 61, 0.2);
    }
  }
}
