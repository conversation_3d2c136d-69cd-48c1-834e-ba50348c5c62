import React, { PureComponent } from 'react'
import { Button, Icon, Form } from 'antd'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { connect } from '@ekuaibao/mobx-store'
import { Dynamic } from '@ekuaibao/template'
import Loading from '@ekuaibao/loading'
import MessageCenter from '@ekuaibao/messagecenter'
import { addAccountField } from '../util/config'
import { addAccountElements as elements } from '../dynamic'
import styles from './add-account-modal.module.less'
import { app as api } from '@ekuaibao/whispered'
import * as actions from '../chanpay.action'

interface Props {
  layer: any
  handleRefresh: Function
  customerCode: string
  size: any
}

interface State {}

@connect((store: any) => ({ size: store.states['@layout'].size }))
@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer'
})
// 新增账户弹窗组件，用于添加畅捷支付银行账户
export default class AddAccountModal extends PureComponent<Props, State> {
  bus: MessageCenter = new MessageCenter()
  constructor(props: Props) {
    super(props)
    this.state = {}
  }

  // 处理关闭弹窗事件
  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  // 处理保存账户信息事件
  handleModalSave = () => {
    const { handleRefresh, customerCode } = this.props
    // @ts-ignore
    this.bus.getValueWithValidate().then((values: any) => {
      const {
        acctKind,
        acctName,
        acctNo,
        bank: { bankNo, brchCode, brchName },
        bankArea: { city, province },
        branch,
        branchCode
      } = values
      const param = {
        customerCode,
        acctName,
        acctNo,
        province,
        city,
        bank: brchName,
        branch,
        bankNo,
        brchType: brchCode,
        branchCode,
        acctKind
      }
      api.dispatch(actions.postChanpayCard(param)).then((res: any) => {
        if (res.value) {
          const { state } = res.value
          if (state === '1') {
            this.props.layer.emitOk({})
            api.dataLoader('@common.channelList').load()
            api.open('@chanpay:ChanpaySuccessModal', { handleRefresh, param })
          } else {
            this.props.layer.emitOk({})
            handleRefresh(true)
          }
        }
      })
    })
  }

  render() {
    const { size } = this.props
    const h = size.y - 130 > 585 ? 585 : size.y - 130
    return (
      <div className={styles['addAccount-modal-wrapper']}>
        <div className="modal-header">
          <div className="flex">{i18n.get('新增账户')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose} data-testid="pay-chanpay-add-account-close-icon" />
        </div>
        <div className="title">{i18n.get('仅部分银行支持线上新增账户，如有特殊需求请联系易快报工作人员')}</div>
        <div className="title second-line">
          {i18n.get('请准确录入以下信息，系统将根据您输入的账户信息，为您申请账户入网')}
        </div>
        <div className="line" />
        <div className="addAccount-modal-container" style={{ height: h + 'px' }}>
          <Dynamic
            bus={this.bus as any}
            create={T => Form.create()(T)}
            template={addAccountField}
            elements={elements as any}
            loading={Loading}
          />
        </div>
        <div className="modal-footer">
          <Button className="btn-ml" onClick={this.handleModalClose} data-testid="pay-chanpay-add-account-cancel">
            {i18n.get('取消')}
          </Button>
          <Button type="primary" className="btn-ml" onClick={this.handleModalSave} data-testid="pay-chanpay-add-account-confirm">
            {i18n.get('确定')}
          </Button>
        </div>
      </div>
    )
  }
}
