import React, { PureComponent } from 'react'
import { Button, Icon, Checkbox, Form } from 'antd'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Dynamic } from '@ekuaibao/template'
import Loading from '@ekuaibao/loading'
import MessageCenter from '@ekuaibao/messagecenter'
import { queryBankStatmentFild as config } from '../util/config'
import { queryBankStatmentElements as elements } from '../dynamic'
import styles from './query-bank-statement-modal.module.less'
import moment from 'moment'

interface Props {
  layer: any
  form: any
  customerCode: string
}

interface State {
  agree: boolean
}

// 交易流水查询弹窗组件，用于查询畅捷支付账户的银行流水
@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer'
})
export default class QueryBankStatementModal extends PureComponent<Props, State> {
  bus: any = new MessageCenter()
  constructor(props: Props) {
    super(props)
    this.state = {
      agree: false
    }
  }

  // 处理关闭弹窗事件
  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  // 格式化日期
  fnFormatMoment = (value: any) => {
    return moment(value)
      .format('YYYY-MM-DD')
      .replace(/-/g, '')
  }

  // 处理查询银行流水事件
  handleModalSave = () => {
    if (!this.state.agree) {
      return
    }
    this.bus.getValueWithValidate().then((res: any) => {
      const { paymentAccount, dateRange } = res
      const acctList = paymentAccount.map((v: any) => {
        const { value, label } = v
        return { acctNo: value, acctName: label }
      })
      const [beg_date, end_date] = dateRange.map((v: any) => this.fnFormatMoment(v))
      this.props.layer.emitOk({ acctList, beg_date, end_date, customerCode: this.props.customerCode })
    })
  }

  // 处理同意协议复选框变化
  handleAgree = (e: any) => {
    this.setState({ agree: e.target.checked })
  }

  // 渲染组件主界面
  render() {
    return (
      <div className={styles['query-bank-statement-wrapper']}>
        <div className="modal-header">
          <div className="flex">{i18n.get('交易流水查询')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose} data-testid="pay-chanpay-query-bank-statement-close-icon" />
        </div>
        <div className="addAccount-modal-container">
          <Dynamic
            bus={this.bus}
            create={T => Form.create()(T)}
            template={config}
            elements={elements}
            loading={Loading}
            customerCode={this.props.customerCode}
          />
        </div>
        <div className="agree-item">
          <Checkbox onChange={this.handleAgree} data-testid="pay-chanpay-query-bank-statement-checkbox" />
          <div className="text">{i18n.get('我允许易快报获取上述账户的银行流水信息')}</div>
        </div>
        <div className="modal-footer">
          <Button className="btn-ml" onClick={this.handleModalClose} data-testid="pay-chanpay-query-bank-statement-cancel">
            {i18n.get('取消')}
          </Button>
          <Button type="primary" className="btn-ml" disabled={!this.state.agree} onClick={this.handleModalSave} data-testid="pay-chanpay-query-bank-statement-confirm">
            {i18n.get('确定')}
          </Button>
        </div>
      </div>
    )
  }
}
