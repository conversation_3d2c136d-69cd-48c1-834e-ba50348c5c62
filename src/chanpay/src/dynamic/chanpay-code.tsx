import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Input } from 'antd'
const { wrapper } = app.require('@components/layout/FormWrapper')

interface Props {
  value: any
  field: any
  onChange: Function
  bus: any
}
interface State {
  disabled: boolean
}

// 畅捷支付代码组件，用于显示和处理畅捷支付相关代码
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'chanpay-code'
  },
  validator: (field: any) => (_rule: any, value: any, callback: any) => {
    const { label, optional } = field
    if (!optional && (!value || value.length === 0)) {
      return callback(i18n.get('not-empty', { label: i18n.get(label) }))
    }
    return callback(undefined)
  },
  wrapper: wrapper()
})
export default class ChanpayCode extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      disabled: true
    }
  }

  componentWillMount() {
    const { bus } = this.props
    bus.on('get:chanpay:code', this.getChanpayCode)
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('get:chanpay:code', this.getChanpayCode)
  }

  // 处理获取畅捷支付代码
  getChanpayCode = (value: any) => {
    this.setState({ disabled: !!value })
    const { onChange } = this.props
    onChange && onChange(value)
  }

  // 处理输入变化事件
  onChange = (e: any) => {
    const value = e.target.value
    const { onChange } = this.props
    onChange && onChange(value)
  }

  render() {
    const { field, value } = this.props
    const { placeholder } = field
    const { disabled } = this.state
    return <Input value={value} size="large" disabled={disabled} placeholder={placeholder} onChange={this.onChange} data-testid="pay-chanpay-code-input" />
  }
}
