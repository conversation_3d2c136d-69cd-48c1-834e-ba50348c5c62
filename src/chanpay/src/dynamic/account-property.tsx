import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Select } from 'antd'
const { wrapper } = app.require('@components/layout/FormWrapper')

interface Props {
  value: any
  field: any
  onChange: Function
  bus: any
}
interface State {
  disabled: boolean
}

// 账户属性选择组件，用于选择账户属性类型
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'account-property'
  },
  validator: (field: any) => (_rule: any, value: any, callback: any) => {
    const { label, optional } = field
    if (!optional && (!value || value.length === 0)) {
      return callback(i18n.get('not-empty', { label: i18n.get(label) }))
    }
    return callback(undefined)
  },
  wrapper: wrapper()
})
export default class AccountProperty extends PureComponent<Props, State> {
  // 处理属性选择变化事件
  onChange = (value: any) => {
    const { onChange } = this.props
    onChange && onChange(value)
  }

  // 渲染属性选项列表
  renderChildren = () => {
    const { field } = this.props
    const { tag } = field
    const children: any = []
    tag.forEach((line: any) => {
      const { key, name } = line
      children.push(<Select.Option key={key}>{name}</Select.Option>)
    })
    return children
  }

  render() {
    const { field } = this.props
    const { placeholder } = field
    return (
      <Select
        style={{ width: '100%' }}
        placeholder={placeholder}
        onChange={this.onChange}
        size="large"
        getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
        data-testid="pay-chanpay-account-property-select"
      >
        {this.renderChildren()}
      </Select>
    )
  }
}
