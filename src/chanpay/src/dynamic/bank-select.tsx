import { app, app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Select } from 'antd'
const { wrapper } = app.require('@components/layout/FormWrapper')
import * as actions from '../chanpay.action'

interface Props {
  value: any
  field: any
  onChange: Function
  bus: any
}
interface State {
  tag: any
}

// 银行选择组件，用于选择畅捷支付支持的银行
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'bank-select'
  },
  validator: (field: any) => (_rule: any, value: any, callback: any) => {
    const { label, optional } = field
    if (!optional && (!value || value.length === 0)) {
      return callback(i18n.get('not-empty', { label: i18n.get(label) }))
    }
    return callback(undefined)
  },
  wrapper: wrapper()
})
export default class BankSelect extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      tag: []
    }
  }

  componentDidMount() {
    api.dispatch(actions.getBankList()).then((res: any) => {
      if (res) {
        this.setState({
          tag: res.items
        })
      }
    })
  }

  // 处理银行选择变化事件
  onChange = (value: any) => {
    const data = JSON.parse(value)
    const { bus } = this.props
    bus.emit('get:branchList:by:bankId', data)
    const { onChange } = this.props
    onChange && onChange(data)
  }

  // 渲染银行选项列表
  renderChildren = () => {
    const { tag } = this.state
    const children: any = []
    tag.forEach((line: any) => {
      const key = JSON.stringify(line)
      const { brchName } = line
      children.push(<Select.Option key={key}>{brchName}</Select.Option>)
    })
    return children
  }

  render() {
    const { field } = this.props
    const { placeholder } = field
    return (
      <Select
        style={{ width: '100%' }}
        placeholder={placeholder}
        onChange={this.onChange}
        size="large"
        getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
        data-testid="pay-chanpay-bank-select"
      >
        {this.renderChildren()}
      </Select>
    )
  }
}
