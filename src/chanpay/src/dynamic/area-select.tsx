import { app, app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Select, Col, Row } from 'antd'
const { wrapper } = app.require('@components/layout/FormWrapper')
import * as actions from '../chanpay.action'

interface Props {
  value: any
  field: any
  onChange: Function
  bus: any
}
interface State {
  provinceTag: any
  cityTag: any
  showCity: boolean
  data: any
}

// 地区选择组件，用于选择省市区信息
@EnhanceField({
  descriptor: {
    type: 'area-select'
  },
  validator: (field: any) => (_rule: any, value: any, callback: any) => {
    const { label, optional } = field
    if (!optional && (!value || value.length === 0)) {
      return callback(i18n.get('not-empty', { label: i18n.get(label) }))
    }
    return callback(undefined)
  },
  wrapper: wrapper()
})
export default class AreaSelect extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      provinceTag: [],
      cityTag: [],
      showCity: false,
      data: {
        province: '',
        city: ''
      }
    }
  }

  // 组件挂载时获取省份列表
  componentDidMount() {
    api.dispatch(actions.getProvincesList()).then((res: any) => {
      if (res) {
        this.setState({
          provinceTag: res.items
        })
      }
    })
  }

  // 处理省份选择变化
  handleProvinceChange = (value: any) => {
    const cData = JSON.parse(value)
    const { name, id } = cData
    if (id) {
      this.setState({
        showCity: true,
        data: {
          province: name,
          city: ''
        }
      })
      api.dispatch(actions.getCitiesList(id)).then((res: any) => {
        if (res) {
          this.setState({
            cityTag: res.items
          })
        }
      })
    }
  }

  // 处理城市选择变化
  onSecondCityChange = (value: any) => {
    const cData = JSON.parse(value)
    const { name, cityId } = cData
    const { bus } = this.props
    bus.emit('get:branchList:by:cityId', cityId)
    const { data } = this.state
    const params = { ...data, city: name }
    this.setState({
      data: params
    })
    const { onChange } = this.props
    onChange && onChange(params)
  }

  // 渲染下拉选项列表
  renderItems = (tags: any[]) => {
    return tags?.map((line) => {
      const key = JSON.stringify(line)
      return <Select.Option key={key}>{line.name}</Select.Option>
    })
  }

  render() {
    const { field } = this.props
    const {
      placeholderArea: { provincePlaceholder, cityPlaceholder }
    } = field
    const { showCity, data, provinceTag, cityTag } = this.state
    const provinceValue = data.province ? data.province : undefined
    const cityValue = data.city ? data.city : undefined
    return (
      <Row span={24}>
        <Col span={16}>
          <Select
            style={{ width: '100%' }}
            showSearch
            placeholder={provincePlaceholder}
            onChange={this.handleProvinceChange}
            value={provinceValue}
            getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
            data-testid="pay-chanpay-area-province-select"
          >
            {this.renderItems(provinceTag)}
          </Select>
        </Col>
        <Col span={7} offset={1}>
          <Select
            showSearch
            style={{ width: '100%' }}
            disabled={!showCity}
            placeholder={cityPlaceholder}
            onChange={this.onSecondCityChange.bind(this)}
            value={cityValue}
            getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
            data-testid="pay-chanpay-area-city-select"
          >
            {this.renderItems(cityTag)}
          </Select>
        </Col>
      </Row>
    )
  }
}
