import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { TreeSelect } from 'antd'
const { wrapper } = app.require('@components/layout/FormWrapper')
import * as actions from '../chanpay.action'

interface Props {
  value: any
  field: any
  onChange: Function
  bus: any
  customerCode: string
}
interface State {
  paymentAccountList: any[]
}

// 付款账户选择组件，用于选择畅捷支付的付款账户
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'payment-account'
  },
  validator: (field: any) => (rule: any, value: any, callback: any) => {
    const { label } = field
    if (!value || value.length === 0) {
      return callback(i18n.get('not-empty', { label: i18n.get(label) }))
    }
    return callback(undefined)
  },
  wrapper: wrapper()
})
export default class PaymentAccount extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      paymentAccountList: []
    }
  }

  componentDidMount() {
    this.getPaymentAccountByPermission()
  }

  // 获取有权限的付款账户列表
  getPaymentAccountByPermission = () => {
    const { customerCode } = this.props
    actions.getPaymentAccountByPermission({ customerCode }).then((res: any) => {
      this.setState({ paymentAccountList: res.items })
    })
  }

  // 处理选择变化事件
  onChange = (value: any) => {
    const { onChange } = this.props
    onChange && onChange(value)
  }

  // 渲染账户标题信息
  renderPaymentAccountTitle = (item: any) => {
    const { acctName, acctNo, branch, icon } = item
    return (
      <div className="legal-record">
        <div className="name-code">{acctName && <div className="name">{acctName}</div>}</div>
        <div className="bank-name-number">{acctNo && <div className="bank-number">{acctNo}</div>}</div>
        <div className="bank-name-number">
          {icon && (
            <div className="bank-icon">
              <img className="bank-icon-img" src={icon} alt="" />
            </div>
          )}
          {branch && <div className="bank-name">{`${branch}`}</div>}
        </div>
      </div>
    )
  }

  // 获取树形结构数据
  getTreeData = () => {
    const { paymentAccountList } = this.state
    return paymentAccountList.map(v => {
      const { id, acctNo, acctName } = v
      return {
        title: this.renderPaymentAccountTitle(v),
        value: acctNo,
        key: id,
        filterKey: `${acctName}${acctNo}`,
        label: acctName
      }
    })
  }

  render() {
    const { value } = this.props
    return (
      <div id="payment-account-inquire">
        <TreeSelect
          value={value}
          allowClear
          showSearch
          labelInValue
          treeData={this.getTreeData()}
          placeholder={i18n.get('请选择付款账户')}
          treeCheckable={true}
          onChange={this.onChange}
          notFoundContent={i18n.get('没有匹配结果')}
          treeNodeFilterProp="filterKey"
          showCheckedStrategy={TreeSelect.SHOW_PARENT}
          treeNodeLabelProp="label"
          style={{ width: '100%' }}
          dropdownStyle={{ maxHeight: 200, overflow: 'auto' }}
          getPopupContainer={triggerNode => triggerNode.parentElement}
          data-testid="pay-chanpay-payment-account-select"
        />
        <div className="item-placeholder">{i18n.get('部分银行暂未接通流水查询服务，可能无法获取流水数据')}</div>
      </div>
    )
  }
}
