import { app } from '@ekuaibao/whispered'
import AccountNumber from './account-number'
import BankSelect from './bank-select'
import AreaSelect from './area-select'
import BranchSelect from './branch-select'
import ChanpayCode from './chanpay-code'
import AccountProperty from './account-property'
import DateRange from './date-range'
import PaymentAccount from './payment-account'
import loadable, { LoadableComponent } from '@loadable/component'

function loadableWithDescriptor<T>(fn: () => Promise<any>, descriptor: any): LoadableComponent<T> {
    const oo: any = loadable(fn)
    oo.descriptor = descriptor
    return oo
}
// import InterconInput from '../../../components/interconnectal/InterconInput'
const InterconInput = loadableWithDescriptor(
    () => Promise.resolve(app.require('@components/interconnectal/InterconInput')),
    {
        type: 'interconnectal-text'
    }
)

const addAccountElements = [
  InterconInput,
  AccountNumber,
  BankSelect,
  AreaSelect,
  BranchSelect,
  ChanpayCode,
  AccountProperty
]
const queryBankStatmentElements = [DateRange, PaymentAccount]
export { addAccountElements, queryBankStatmentElements }
