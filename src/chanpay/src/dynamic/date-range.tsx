import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
const { wrapper } = app.require('@components/layout/FormWrapper')
import moment from 'moment'
import { DatePicker } from 'antd'

const { RangePicker } = DatePicker

interface Props {
  value: any
  field: any
  onChange: Function
}

interface State {
  selectedValue: string
}

// 日期范围选择组件，用于选择交易流水查询的日期范围
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'date-range'
  },
  validator: (field: any) => (_rule: any, value: any, callback: any) => {
    const { label } = field
    if (!value || value.length === 0) {
      return callback(i18n.get('not-empty', { label: i18n.get(label) }))
    }
    return callback()
  },
  wrapper: wrapper()
})
export default class DateRange extends PureComponent<Props, State> {
  state = {
    selectedValue: ''
  }

  // 处理日期范围变化事件
  onChange = (value: any[]) => {
    const { onChange } = this.props
    onChange && onChange(value)
  }

  // 禁用超出范围的日期
  disabledDate = (current: any) => {
    const { selectedValue } = this.state
    return (
      current &&
      (current.isBefore(moment(selectedValue).add(-90, 'day')) ||
        current.isAfter(moment(selectedValue).add(90, 'day')) ||
        current > moment().endOf('day'))
    )
  }

  // 处理日历选择变化
  onCalendarChange = (date: any[]) => {
    date && this.setState({ selectedValue: date[0] })
  }

  render() {
    const { value } = this.props
    return (
      <div>
        <RangePicker
          style={{ width: '100%' }}
          value={value}
          disabledDate={this.disabledDate}
          onChange={this.onChange}
          onCalendarChange={this.onCalendarChange}
          data-testid="pay-chanpay-date-range-select"
        />
        <div className="item-placeholder">{i18n.get('仅可查询跨度90天的交易流水')}</div>
      </div>
    )
  }
}
