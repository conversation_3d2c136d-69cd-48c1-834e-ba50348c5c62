import { app, app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Select, Input } from 'antd'
const { wrapper } = app.require('@components/layout/FormWrapper')
import * as actions from '../chanpay.action'

interface Props {
  value: any
  field: any
  onChange: Function
  bus: any
}
interface State {
  tag: any
  cityId: any
  bankCode: any
  isOther: boolean
}

const otherText = { name: i18n.get('其他'), code: '' }

// 银行网点选择组件，用于选择银行网点信息
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'branch-select'
  },
  validator: (field: any) => (_rule: any, value: any, callback: any) => {
    const { label, optional } = field
    if (!optional && (!value || value.length === 0)) {
      return callback(i18n.get('not-empty', { label: i18n.get(label) }))
    }
    return callback(undefined)
  },
  wrapper: wrapper()
})
export default class BranchSelect extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      tag: [],
      cityId: '',
      bankCode: '',
      isOther: false
    }
  }

  componentWillMount() {
    const { bus } = this.props
    bus.on('get:branchList:by:cityId', this.handleCityGetBranch)
    bus.on('get:branchList:by:bankId', this.handleCityGetBankId)
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('get:branchList:by:cityId', this.handleCityGetBranch)
    bus.un('get:branchList:by:bankId', this.handleCityGetBankId)
  }

  // 处理银行ID变化事件
  handleCityGetBankId = (param: any) => {
    if (param) {
      const { bankNo } = param
      const { bankCode } = this.state
      if (bankCode !== bankNo) {
        const { value, onChange } = this.props
        value && onChange && onChange(undefined)
        this.setState({ bankCode: bankNo }, () => this.getBranchData())
      }
    }
  }

  // 处理城市ID变化事件
  handleCityGetBranch = (cityId: any) => {
    const oCityId = this.state.cityId
    if (cityId !== oCityId) {
      const { value, onChange } = this.props
      value && onChange && onChange(undefined)
      this.setState({ cityId }, () => this.getBranchData())
    }
  }

  // 获取银行网点数据
  getBranchData = () => {
    const { bankCode, cityId } = this.state
    const data = {
      cityId,
      bankCode
    }
    api.dispatch(actions.getBranchList(data)).then((res: any) => {
      if (res) {
        const data = res.items
        data.push(otherText)
        this.setState({ tag: data })
      }
    })
  }

  // 处理网点选择变化事件
  onChange = (value: any) => {
    const data = JSON.parse(value)
    const { name, code } = data
    const isOther = name === otherText.name
    if (!isOther) {
      const { bus } = this.props
      bus.emit('get:chanpay:code', code)
    }
    this.setState({ isOther }, () => {
      !isOther ? this.props.onChange(name) : this.props.onChange(undefined)
    })
  }

  // 处理输入框变化事件
  handleInputChange = (e: any) => {
    const code = e.target.value
    const { bus } = this.props
    bus.emit('get:chanpay:code', '')
    this.props.onChange && this.props.onChange(code)
  }

  // 渲染网点选项列表
  renderChildren = () => {
    const { tag } = this.state
    const children: any = []
    tag.forEach((line: any) => {
      const key = JSON.stringify(line)
      const { name } = line
      children.push(<Select.Option key={key}>{name}</Select.Option>)
    })
    return children
  }

  render() {
    const { field, value } = this.props
    const { placeholder } = field
    const { bankCode, cityId, isOther } = this.state
    const disabled = !cityId || !bankCode
    const cValue = isOther ? otherText.name : value
    return (
      <div>
        <Select
          style={{ width: '100%' }}
          showSearch
          placeholder={placeholder}
          onChange={this.onChange}
          size="large"
          disabled={disabled}
          value={cValue}
          getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
          data-testid="pay-chanpay-branch-select"
        >
          {this.renderChildren()}
        </Select>
        {isOther && (
          <Input
            placeholder={i18n.get('请输入开户网点')}
            className="mt-20"
            onChange={this.handleInputChange}
            value={value}
            data-testid="pay-chanpay-branch-other-input"
          />
        )}
      </div>
    )
  }
}
