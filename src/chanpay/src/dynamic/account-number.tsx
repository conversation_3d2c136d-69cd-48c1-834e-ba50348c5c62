import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Input } from 'antd'
const { wrapper } = app.require('@components/layout/FormWrapper')

interface Props {
  value: any
  field: any
  onChange: Function
}

interface State {}

// 账号输入组件，用于输入银行账号信息
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'account-number'
  },
  validator: (field: any) => (_rule: any, value: any, callback: any) => {
    const { label, optional, maxLength } = field
    if (!optional && (!value || value.length === 0)) {
      return callback(i18n.get('not-empty', { label: i18n.get(label) }))
    }
    if (!/^[0-9a-zA-Z-]*$/.test(value)) {
      return callback(i18n.get('银行账号格式不正确'))
    }
    if (value && value.length > maxLength) {
      return callback(i18n.get('cannot-exceed-words', { label, maxLength }))
    }
    return callback(undefined)
  },
  wrapper: wrapper()
})
export default class AccountNumber extends PureComponent<Props, State> {
  // 处理账号输入变化
  onChange = (value: any) => {
    const { onChange } = this.props
    onChange && onChange(value)
  }

  render() {
    const { field } = this.props
    const { placeholder } = field
    return <Input size="large" placeholder={placeholder} onChange={this.onChange} data-testid="pay-chanpay-account-number-input" />
  }
}
