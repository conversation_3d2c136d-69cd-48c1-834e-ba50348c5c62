<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 56.3 (81716) - https://sketch.com -->
    <title>icon/check-fill</title>
    <desc>Created with <PERSON>ket<PERSON>.</desc>
    <defs>
        <path d="M15,27.5 C8.09644063,27.5 2.5,21.9035594 2.5,15 C2.5,8.09644063 8.09644063,2.5 15,2.5 C21.9035594,2.5 27.5,8.09644063 27.5,15 C27.5,21.9035594 21.9035594,27.5 15,27.5 Z M13.2383289,17.2791176 L9.70279503,13.7435837 L7.93502808,15.5113506 L13.2383289,20.8146515 L15.0060959,19.0468845 L22.0771637,11.9758167 L20.3093967,10.2080498 L13.2383289,17.2791176 Z" id="path-1"></path>
        <filter x="-50.0%" y="-46.0%" width="200.0%" height="200.0%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="4" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.0941176471   0 0 0 0 0.71372549   0 0 0 0 0.580392157  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="银企直联" transform="translate(-59.000000, -802.000000)">
            <g id="编组" transform="translate(40.000000, 766.000000)">
                <g id="icon/check-fill" transform="translate(24.000000, 40.000000)">
                    <g id="Combined-Shape">
                        <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                        <use fill="#18B694" fill-rule="evenodd" xlink:href="#path-1"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>