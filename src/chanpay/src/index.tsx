import key from './key'
import loadable from '@loadable/component'
export default [
  {
    id: key.ID,
    reducer: () => Promise.resolve(require('./chanpay.reducer')).then(m => m.default || m),
  },
  {
    point: '@@components',
    namespace: key.ID,
    onload: () => [{ key: 'ChanpayView', component: () => import('./chanpay-view') }]
  },
  {
    point: '@@layers',
    prefix: '@chanpay',
    onload: () => require('./layers').default
  },
  {
    resource: '@chanpay',
    value: {
      ['elements/account-management-person']: loadable(() => import('./elements/account-management-person'))
    }
  }
]
