import key from './key'
import { Fetch, Resource } from '@ekuaibao/fetch'
import { app as api } from '@ekuaibao/whispered'
const isWebchat = window.isWebchat
import { Modal } from 'antd'
import { QuerySelect } from 'ekbc-query-builder'
const { renderOpenLinkDesc } = api.require('@audit/service-renderOpenLinkDesc')
const chanpay = new Resource('/api/pay/v2/chanpay')
const banks = new Resource('/api/pay/v1/banks')
const accounts = new Resource('/api/pay/v2/accounts')

export function getChanpayList(params: any) {
  return {
    type: key.GET_CHANPAY_LIST,
    payload: chanpay.GET('/account', params)
  }
}

export function putAccountPerson(params: any) {
  return {
    type: key.PUT_ACCOUNT_PERSON,
    payload: chanpay.PUT('/account/visibility/$accountId', { ...params })
  }
}

export function getRefreshList(params: any) {
  return {
    type: key.GET_REFRESH_LIST,
    payload: chanpay.GET('/account/refresh', { ...params })
  }
}

export function getBankList() {
  return {
    type: key.GET_BANK_LIST,
    payload: chanpay.GET('/account/banks')
  }
}

export function getProvincesList() {
  return {
    type: key.GET_PROVINCES_LIST,
    payload: banks.GET('/provinces')
  }
}

export function getCitiesList(provId: any) {
  return {
    type: key.GET_CITIES_LIST,
    payload: banks.GET('/cities', { provId })
  }
}

export function getBranchList(data: any) {
  return {
    type: key.GET_BRANCH_LIST,
    payload: banks.GET('/branches', data)
  }
}

export function checkHasPermission() {
  return chanpay.GET('/extension/permission')
}

export function postChanpayCard(params: any) {
  return {
    type: key.POST_CHANPAY_CARD,
    payload: chanpay.POST('/account', { ...params })
  }
}

export function postPayment(data: any) {
  return {
    type: key.POST_PAYMENT,
    payload: accounts.POST(data)
  }
}

export function certification({ isBind, fn }: { isBind?: boolean; fn?: Function }) {
  let params = `platform=${window.__PLANTFORM__}&ekbCorpId=${Fetch.ekbCorpId}&corpId=${Fetch.corpId}&token=${Fetch.accessToken}&pluginType=certification`
  const title =
    window.__PLANTFORM__ === 'WEIXIN'
      ? i18n.get('请复制链接到浏览器上进行认证')
      : i18n.get('请在新打开的页面上进行认证')
  const desc =
    window.__PLANTFORM__ === 'WEIXIN'
      ? i18n.get('由于企业微信限制，请复制此链接到浏览器上进行相关操作')
      : i18n.get('认证完成前请不要关闭此窗口')
  const okText = i18n.get('已完成认证')
  isBind && (params += '&isBind=true')
  params = encodeURIComponent(params)
  let url = `/web/payment.html?${params}`
  url = `${location.origin}${url}`
  /**
   * bbsazdLsGM0g00 是私有化客户任买的企业id
   * DTj4_Yucjk_Eyw 是460mix环境中可测试银企联支付的合思信息的企业id
   * PRP-15316 工单03#4339
   * 简要描述遇到的问题：银企直连无法直接跳转到支付界面进行付款，需要复制链接才可以
   * 问题影响：客户无法接受这种方式
   * 个人认为现有的方案是合理的，因企业微信中不能识别U盾，不能在应用内弹出的银企联弹窗内完成支付，所以提供了复制弹窗
   * 但因客户强烈要求，现根据corpid判断，不再为此用户弹出用于复制链接的窗口，直接跳转至支付页
   * 2020年3月23日晚上线，次日上午，coe反馈：客户认同此方案
   * 此用户场景：企业微信用户，在进入应用后，点击右上角的浏览器按钮，跳转至默认浏览器使用
   */
  if (window.__PLANTFORM__ === 'WEIXIN' && isWebchat && Fetch.ekbCorpId !== 'bbsazdLsGM0g00') {
    Modal.info({
      content: renderOpenLinkDesc(title, desc, url),
      okText: okText,
      onOk() {
        fn && fn()
      }
    })
  } else {
    api.emit('@vendor:open:link', url)
    Modal.info({
      content: renderOpenLinkDesc(title, desc),
      okText: okText,
      onOk() {
        fn && fn()
      }
    })
  }
}

export function getBankStatementVisible() {
  return chanpay.GET('/extension/account/visible')
}

export function getPaymentAccountByPermission(params: any) {
  return chanpay.GET('/account/visible', params)
}

export function updateStatement(params: any) {
  return chanpay.POST('/paymentFlow/add/task', params)
}

export function checkUpdateStatementResult() {
  return chanpay.GET('/paymentFlow/get/task')
}

export function queryPaymentFlow(params?: any) {
  const { start, count, filterBy, entityId, type, category, orderBy } = params
  let query = new QuerySelect()
  if (count) {
    query.limit(start, count)
  }
  if (filterBy) {
    query.filterBy(filterBy)
  }
  query = query.value()
  return chanpay.POST('/paymentFlow/query', { ...query, orderBy }, { entityId, type, category })
}

export function checkExistPlatform() {
  return chanpay.GET('/paymentFlow/checkPlatform')
}

export function handleExport(params: any) {
  const { id, account } = params
  const qs: any = {}
  const filterAccount = account.map((v: string) => `"${encodeURIComponent(v)}"`)
  qs.filter = `form.E_${id}_acctNo.in(${filterAccount})`
  const entityId = encodeURIComponent(id)
  const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
  const InquireUrl = `${Fetch.fixOrigin(location.origin)}/api/v1/datalink/excel/exportWay/$${entityId}`
  const syncUrl = `${Fetch.fixOrigin(location.origin)}/api/v1/datalink/excel/export/all/$${entityId}?filter=${
    qs.filter
  }&corpId=${ekbCorpId}`
  Fetch.GET(InquireUrl).then(v => {
    if (v.value.exportWay === 'async') {
      api.open('@layout:AsyncExportModal').then((r: any) => {
        const asyncUrl = `${Fetch.fixOrigin(location.origin)}/api/v1/datalink/excel/export/async/$${entityId}`
        Fetch.GET(asyncUrl, { taskName: r.taskName, ...qs })
      })
    } else {
      api.emit('@vendor:download', syncUrl)
    }
  })
}

export function getVerify() {
  return chanpay.GET('/extension/verify').then((res: any) => {
    return res.value
  })
}

export function deleteVerify() {
  return chanpay.DELETE('/extension/verify')
}
