const acctName = {
  name: 'acctName',
  label: i18n.get('账户名称'),
  placeholder: i18n.get('请输入账户名称'),
  maxLength: 30,
  type: 'interconnectal-text',
  optional: false,
  editable: true
}

const acctNo = {
  name: 'acctNo',
  label: i18n.get('银行账号'),
  placeholder: i18n.get('请输入银行账号'),
  maxLength: 32,
  type: 'account-number',
  optional: false,
  editable: true
}

const bank = {
  name: 'bank',
  label: i18n.get('开户行'),
  placeholder: i18n.get('请选择开户行'),
  type: 'bank-select',
  optional: false,
  editable: true
}

const bankArea = {
  name: 'bankArea',
  label: i18n.get('开户行所在地'),
  placeholder: i18n.get('请选择开户行所在地'),
  placeholderArea: {
    provincePlaceholder: i18n.get('省'),
    cityPlaceholder: i18n.get('市')
  },
  type: 'area-select',
  optional: false,
  editable: true
}

const branch = {
  name: 'branch',
  label: i18n.get('开户网点'),
  placeholder: i18n.get('请选择开户网点'),
  type: 'branch-select',
  optional: false,
  editable: true
}

const branchCode = {
  name: 'branchCode',
  label: i18n.get('银行联行号'),
  placeholder: i18n.get('请输入银行联行号'),
  type: 'chanpay-code',
  optional: false,
  editable: true
}

const acctKind = {
  name: 'acctKind',
  label: i18n.get('账户性质'),
  placeholder: i18n.get('请选择账户性质'),
  tag: [
    { key: 1, name: i18n.get('基本户') },
    { key: 2, name: i18n.get('一般户') },
    { key: 3, name: i18n.get('其他') }
  ],
  type: 'account-property',
  optional: false,
  editable: true
}

const paymentAccount = {
  name: 'paymentAccount',
  label: i18n.get('付款账户'),
  placeholder: i18n.get('请选择银行账号'),
  type: 'payment-account',
  optional: false,
  editable: true
}

const dateRange = {
  name: 'dateRange',
  label: i18n.get('请选择日期'),
  type: 'date-range',
  optional: false,
  editable: true
}

const addAccountField = [acctName, acctNo, bank, bankArea, branch, branchCode, acctKind]
const queryBankStatmentFild = [paymentAccount, dateRange]

export { addAccountField, queryBankStatmentFild }
