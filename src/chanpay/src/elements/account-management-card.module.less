.card-wrapper {
  width: calc(100% + 20px);
  margin-left: -26px;
  padding: 16px 24px 0 24px;
  &:hover {
    background: rgba(29, 43, 61, 0.03);
  }
  :global {
    .card {
      position: relative;
      &:hover .permission-wrapper .edit {
        display: block;
      }
      .icon,
      .bank,
      .branch {
        display: inline-block;
      }
      .icon {
        width: 16px;
        height: 16px;
        > img {
          width: 100%;
          height: 100%;
        }
      }
      .bank {
        font-size: 14px;
        font-weight: 400;
        color: rgba(29, 43, 61, 0.75);
        line-height: 22px;
        margin: 0 8px 0 4px;
      }
      .branch {
        font-size: 14px;
        font-weight: 400;
        color: rgba(29, 43, 61, 0.5);
        line-height: 22px;
      }
      .acctName {
        font-size: 14px;
        font-weight: 500;
        color: rgba(29, 43, 61, 1);
        line-height: 22px;
        margin: 12px 0 2px 0;
        padding-left: 20px;
      }
      .acctNo {
        font-size: 20px;
        font-weight: bold;
        color: rgba(29, 43, 61, 1);
        line-height: 28px;
        margin-bottom: 12px;
        padding-left: 20px;
      }
      .card-type {
        position: absolute;
        top: 0;
        right: 24px;
        .enable,
        .stop,
        .application {
          height: 22px;
          text-align: center;
          line-height: 22px;
          font-size: 12px;
          font-weight: 400;
          border-radius: 4px;
          padding: 0 8px;
        }
        .stop {
          background: rgba(29, 43, 61, 0.09);
          color: rgba(29, 43, 61, 0.75);
        }
        .application {
          background: rgba(24, 144, 255, 0.1);
          color: rgba(24, 144, 255, 1);
        }
      }
      .gray {
        color: rgba(29, 43, 61, 0.3);
      }
      .permission-wrapper {
        display: flex;
        .permission-bank {
          margin-left: 20px;
          font-size: 14px;
          font-weight: 400;
          color: rgba(29, 43, 61, 0.75);
          line-height: 26px;
        }
        .empty {
          font-size: 12px;
          font-weight: 400;
          color: rgba(29, 43, 61, 0.75);
          line-height: 26px;
        }
        .edit {
          display: none;
          margin-left: 12px;
          font-size: 14px;
          font-weight: 400;
          color: var(--brand-base);
          line-height: 26px;
          cursor: pointer;
        }
      }
    }
    .line {
      margin: 6px 24px 0 20px;
      height: 1px;
      background-color: rgba(29, 43, 61, 0.15);
    }
  }
}
