import { app, app as api } from '@ekuaibao/whispered'
// 交易流水组件，用于展示和管理银行流水记录
import { Icon, Modal } from 'antd'
import React, { PureComponent } from 'react'
import styles from './deal-flow.module.less'
import MessageCenter from '@ekuaibao/messagecenter'
const MongolianLayer = app.require('@aikeCRM/elements/MongolianLayer')
import * as actions from '../chanpay.action'
const DataLinkTableWrapper = app.require('@elements/DataLinkTable/DataLinkTableWrapper')
import { cloneDeep, get } from 'lodash'
import { parseFields, parseFilter, parseSorters } from '@ekuaibao/lib/lib/entityUtil/entityTableUtil'
const { parseFieldMap, parseOptions } = app.require('@elements/DataLinkTable/tableUtil')

interface Props {
  layer?: any
  customer: string[]
  account: string[]
  editFlag: boolean
}

interface State {
  noActivated: boolean
  customerCode: string
  inLoading: boolean
  needCreatePlatform: boolean
  entityInfo: any
  entityId: string
}

export default class DealFlow extends PureComponent<Props, State> {
  bus = new MessageCenter()
  pathMap: any = {}
  fieldMap: any = {}
  timer: any
  constructor(props: Props) {
    super(props)
    this.state = {
      noActivated: true,
      customerCode: '',
      inLoading: false,
      needCreatePlatform: false,
      entityInfo: null,
      entityId: ''
    }
  }

  componentDidMount() {
    this.checkExistPlatform()
  }

  componentWillUnmount() {
    clearInterval(this.timer)
    actions.deleteVerify()
  }

  // 检查平台是否已创建
  checkExistPlatform = () => {
    // 检查平台是否已创建存在，存在则获取entityId
    actions.checkExistPlatform().then((res: any) => {
      const { hasPlatform, entityId } = res.value
      if (hasPlatform) {
        api.invokeService('@third-party-manage:get:entity:info', { id: entityId }).then((value: any) => {
          this.setState({ entityInfo: value, entityId })
        })
      } else {
        // 如未创建则引导客户手动更新流水（流水平台的创建时机在第一次更新流水时）
        this.setState({ needCreatePlatform: true })
      }
    })
  }

  // 获取交易流水数据
  fetch = (options = { page: { currentPage: 0, pageSize: 10 }, sorters: {}, filters: {} }) => {
    const {
      entityInfo,
      entityInfo: { type }
    } = this.state

    // @ts-ignore
    const __options = cloneDeep(options)
    __options.sorters = parseSorters(options.sorters, this.pathMap)
    __options.filters = parseFilter(options.filters, this.pathMap)
    const query = parseOptions({ options: __options, entityInfo, fieldMap: this.fieldMap })
    const { limit, filterBy, orderBy } = query
    return actions
      .queryPaymentFlow({
        ...limit,
        filterBy,
        orderBy,
        type: 'TABLE',
        entityId: entityInfo.id,
        category: 'all'
      })
      .then((rep: any) => {
        const { data, template, total, path } = rep.items
        this.pathMap = path
        if (!Object.keys(this.fieldMap).length) {
          const fields = parseFields({ res: template, type, entityInfo })
          this.fieldMap = parseFieldMap(fields, path)
        }
        return { dataSource: data, template, total: total ? total : 1, path }
      })
  }

  // 处理更新流水事件
  handleUpdateStatement = () => {
    // 更新流水
    api.open('@chanpay:QueryBankStatementModal', { customerCode: this.state.customerCode }).then(async (value: any) => {
      const needCreatePlatform = this.state.needCreatePlatform
      needCreatePlatform
        ? this.setState({ inLoading: true, needCreatePlatform: false })
        : this.setState({ inLoading: true })
      await actions.updateStatement(value) // 先创建更新任务
      localStorage.setItem('in:add:task', 'xxx') // 本地添加一个标识，用于客户更新流水过程中退出应用再进来
      localStorage.setItem('need:create:platform', 'yyy') // 本地添加一个标识，首次更新流水，创建流水平台过程中退出应用再进来
      this.checkUpdateStatementResult(needCreatePlatform) // 查询更新是否完成
    })
  }

  // 检查流水更新结果
  checkUpdateStatementResult = (needCreatePlatform: boolean) => {
    this.timer = setInterval(() => {
      actions
        .checkUpdateStatementResult()
        .then((res: any) => {
          const state = get(res, 'value.state')
          const comments = get(res, 'value.comments')
          if (state !== 'START' && state !== 'SYNCING') {
            this.fnAfterCheckUpdateStatement({ needCreatePlatform, state, comments })
          }
        })
        .catch((e: any) => {
          this.fnAfterCheckUpdateStatement({ needCreatePlatform, state: 'EXCEPTION', comments: e.msg })
        })
    }, 3000)
  }

  // 处理流水更新后的逻辑
  fnAfterCheckUpdateStatement = (params: { needCreatePlatform: boolean; state: string; comments: string }) => {
    const { needCreatePlatform, state, comments } = params

    clearInterval(this.timer)
    localStorage.removeItem('in:add:task')
    localStorage.removeItem('need:create:platform')

    if (state === 'EXCEPTION') {
      // 系统出错，直接弹出错误信息
      this.setState({
        inLoading: false,
        needCreatePlatform: true
      })
      Modal.error({
        title: i18n.get('更新流水失败'),
        content: comments
      })
    } else {
      this.setState({ inLoading: false })
      needCreatePlatform ? this.checkExistPlatform() : this.fetch()
      state === 'SUCCESS'
        ? Modal.success({
            title: i18n.get('更新流水成功')
          })
        : Modal.warning({
            title: i18n.get('更新流水失败'),
            content: comments
          })
    }
  }

  // 处理导出事件
  handleExport = () => {
    const { entityId } = this.state
    const { account } = this.props
    actions.handleExport({ id: entityId, account })
  }

  // 处理身份认证事件
  handleAuthentication = () => {
    // 身份认证逻辑
    actions.certification({ fn: this.checkHasPermission })
  }

  // 检查权限
  checkHasPermission = async () => {
    const customerCode = await actions.getVerify()
    if (customerCode) {
      const needGetTask = !!localStorage.getItem('in:add:task')
      if (needGetTask) {
        // 对应客户更新流水过程中离开应用的逻辑
        this.setState({ inLoading: true })
        const needCreatePlatform = !!localStorage.getItem('need:create:platform')
        this.checkUpdateStatementResult(needCreatePlatform)
      }
    }
    this.setState({ noActivated: !customerCode, customerCode })
  }

  // 渲染前置页面
  renderPrePage = (params: { contentText: string; actionText: string; actionClick: Function }) => {
    return (
      <div className={styles['deal-flow-wrapper']}>
        <div className="not-active">
          <div className="explain">
            <Icon type="info-circle" style={{ fontSize: '16px', color: '#1890ff' }} />
            <div className="text">{params.contentText} </div>
          </div>
          <div
            className="action"
            onClick={() => {
              params.actionClick()
            }}
            data-testid="pay-chanpay-deal-flow-prepage-action"
          >
            {params.actionText}
          </div>
        </div>
      </div>
    )
  }

  // 渲染组件主界面
  render() {
    const { noActivated, inLoading, needCreatePlatform, entityInfo } = this.state

    if (noActivated) {
      const contentText = i18n.get('为保障流水数据安全，请先进行身份认证')
      const actionText = i18n.get('前往认证')
      const actionClick = this.handleAuthentication
      return this.renderPrePage({ contentText, actionText, actionClick })
    }

    if (needCreatePlatform) {
      const contentText = i18n.get('您尚未更新过流水记录，如需查看，请先进行更新')
      const actionText = i18n.get('确认更新')
      const actionClick = this.handleUpdateStatement
      return this.renderPrePage({ contentText, actionText, actionClick })
    }

    if (inLoading) {
      return (
        <div className={styles['deal-flow-wrapper']}>
          <MongolianLayer text={i18n.get('交易流水更新中...')} />
        </div>
      )
    }

    if (!entityInfo) {
      return null
    }

    return (
      <div className={styles['deal-flow-wrapper']}>
        <div className="deal-flow-header">
          <div className="header-btn first-btn" onClick={this.handleUpdateStatement} data-testid="pay-chanpay-deal-flow-update">
            {i18n.get('更新流水记录')}
          </div>
          <div className="header-btn" onClick={this.handleExport} data-testid="pay-chanpay-deal-flow-export">
            {i18n.get('导出')}
          </div>
        </div>
        <DataLinkTableWrapper
          linkType="CHANPAY"
          entityInfo={entityInfo}
          bus={this.bus}
          getLocal={() => {}}
          fetch={this.fetch}
          isShowSearch={false}
          editFlag={this.props.editFlag}
        />
      </div>
    )
  }
}
