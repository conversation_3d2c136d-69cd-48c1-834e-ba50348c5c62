// 账户管理卡片组件，用于展示和管理银行账户信息
import React, { PureComponent } from 'react'
import styles from './account-management-card.module.less'
import { EnhanceConnect } from '@ekuaibao/store'
import classNames from 'classnames'
import { Tooltip } from 'antd'
import { app as api } from '@ekuaibao/whispered'
import AccountManagementPersonnel from './account-management-person'
import * as actions from '../chanpay.action'

interface Props {
  data: {
    id: string
    icon: any
    bank: string
    branch: string
    state: any
    acctNo: string
    acctName: string
    visibility: any
  }
  key: number
  getList: Function
  departmentTree?: any
  staffs?: any
  roles?: any
  changeEditFlag?: Function
}

interface State {}

const typeMap: any = {
  '0': { cls: 'application', title: i18n.get('待审核') },
  '1': { cls: 'enable', title: '' },
  '2': { cls: 'stop', title: i18n.get('审核拒绝') }
}

const description = i18n.get('以下员工 (或部门、角色) 可查看此账户的银行流水，并实时更新流水信息')
@EnhanceConnect((state: any) => ({
  staffs: state['@common'].staffs,
  roles: state['@common'].roleList,
  departmentTree: state['@common'].department.data
}))
// 账户管理卡片组件，用于展示单个银行账户的详细信息
export default class AccountManagementCard extends PureComponent<Props, State> {
  // 组件挂载前获取员工、角色和部门信息
  componentWillMount() {
    api.invokeService('@common:get:staffs:roleList:department')
  }

  // 根据ID获取部门项
  getDeptItemsByIds = (list: any = [], ids: any = []) => {
    const items: any = []
    const fn = (item: any) => {
      if (ids && ids.indexOf(item.id) > -1) {
        items.push(item)
      }
      item.children = item.children || []
      if (item.children.length) {
        item.children.forEach((c: any) => {
          fn(c)
        })
      }
    }
    list.forEach((item: any) => {
      fn(item)
    })
    return items
  }

  // 根据ID获取数据项
  getItemByIds = (data: any = [], ids: any = []) => {
    return data.filter((line: any) => {
      return ids && ids.indexOf(line.id) > -1
    })
  }

  // 解析值为标签格式
  valueParse = (value: any) => {
    if (!value) {
      return { tags: [] }
    }
    return {
      tags: [
        ...this.getDeptItemsByIds(this.props.departmentTree, value.departments),
        ...this.getItemByIds(this.props.staffs, value.staffs),
        ...this.getItemByIds(this.props.roles, value.roles)
      ]
    }
  }

  // 处理编辑点击事件
  handleEditClick = () => {
    const { data: { visibility, id }, changeEditFlag, getList } = this.props
    api
      .open('@layout:SelectStaffsModal', {
        checkedList: [
          { type: 'department-member', multiple: true, checkedKeys: visibility.staffs || [] },
          { type: 'department', multiple: true, checkedKeys: visibility.departments || [] },
          { type: 'role', multiple: true, checkedKeys: visibility.roles || [] }
        ],
        departmentsIncludeChildren: visibility.departmentsIncludeChildren ?? true
      })
      .then((data: any) => {
        const { checkedList, departmentsIncludeChildren } = data
        const staffs = checkedList.find((o: any) => o.type === 'department-member').checkedKeys || []
        const departments = checkedList.find((o: any) => o.type === 'department').checkedKeys || []
        const roles = checkedList.find((o: any) => o.type === 'role').checkedKeys || []
        const params = {
          accountId: id,
          staffs,
          departments,
          roles,
          departmentsIncludeChildren
        }
        api.dispatch(actions.putAccountPerson(params)).then((res: any) => {
          if (res.value) {
            changeEditFlag(true)
            getList()
          }
        })
      })
  }

  // 渲染权限信息
  renderPermission = () => {
    const { data } = this.props
    const { state, visibility } = data
    const IS_ENABLE = state === '1'
    const { tags } = this.valueParse(visibility)
    return (
      <div className="permission-wrapper">
        <Tooltip placement="topLeft" title={description}>
          <div className={classNames('permission-bank', { gray: !IS_ENABLE })}>{i18n.get('银行流水权限：')}</div>
        </Tooltip>
        {!!tags.length ? (
          <AccountManagementPersonnel dataSource={tags} />
        ) : (
          <div className="empty">{i18n.get('暂未设置')}</div>
        )}
        <div className="edit" onClick={this.handleEditClick} data-testid="pay-chanpay-account-management-edit">
          {i18n.get('编辑')}
        </div>
      </div>
    )
  }

  // 渲染账户类型
  renderType = () => {
    const {
      data: { state }
    } = this.props
    const { cls, title } = typeMap[state]
    return (
      title && (
        <div className="card-type">
          <div className={cls}>{title}</div>
        </div>
      )
    )
  }

  render() {
    const { data, key } = this.props
    const { icon, bank, branch, state, acctNo, acctName } = data
    const IS_ENABLE = state === '1'
    return (
      <div className={styles['card-wrapper']} key={key}>
        <div className="card">
          <div className="icon">
            <img src={icon} />
          </div>
          <div className={classNames('bank', { gray: !IS_ENABLE })}>{bank}</div>
          <div className={classNames('branch', { gray: !IS_ENABLE })}>{branch}</div>
          <div className={classNames('acctName', { gray: !IS_ENABLE })}>{acctName}</div>
          <div className={classNames('acctNo', { gray: !IS_ENABLE })}>{acctNo}</div>
          {IS_ENABLE && this.renderPermission()}
          {this.renderType()}
        </div>
        <div className="line" />
      </div>
    )
  }
}
