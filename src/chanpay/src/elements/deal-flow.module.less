@import "~@ekuaibao/eui-styles/less/token";
.deal-flow-wrapper {
  width: 100%;
  height: 100%;
  padding-bottom: 50px;
  overflow: auto;
  display: flex;
  flex-direction: column;
  :global {
    .deal-flow-header {
      margin: @space-6 0;
      padding-left: @space-6;
      .header-btn {
        display: inline-block;
        margin-right: 8px;
        padding: 5px 16px;
        border-radius: 4px;
        border: 1px solid rgba(29, 43, 61, 0.15);
        font-size: 14px;
        font-weight: 400;
        color: rgba(29, 43, 61, 1);
        line-height: 22px;
        cursor: pointer;
      }
      .first-btn {
        background-color: var(--brand-base);
        color: #fff;
      }
    }
    .not-active {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      .explain {
        align-items: center;
        .text {
          display: inline-block;
          flex: 1;
          font-size: 16px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: 500;
          line-height: 22px;
          margin-left: 16px;
        }
      }
      .action {
        float: right;
        color: #fff;
        font-size: 14px;
        padding: 0 15px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        cursor: pointer;
        margin-top: 24px;
        border-radius: 4px;
        background-color:  var(--brand-base);
      }
    }
  }
}
