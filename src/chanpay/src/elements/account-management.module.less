.account-management-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 0 12px;
  :global {
    .account-management-header {
      position: relative;
      height: 64px;
      padding-top: 16px;
      .header-left {
        .header-btn {
          display: inline-block;
          margin-right: 8px;
          padding: 5px 16px;
          border-radius: 4px;
          border: 1px solid rgba(29, 43, 61, 0.15);
          font-size: 14px;
          font-weight: 400;
          color: rgba(29, 43, 61, 1);
          line-height: 22px;
          cursor: pointer;
        }
        .first-btn {
          background-color: var(--brand-base);
          color: #fff;
        }
      }
      .header-right {
        position: absolute;
        right: 0;
        top: 16px;
        width: 347px;
        .header-search {
          border-radius: 16px;
          flex: none;
          .ant-input {
            border-radius: 16px;
            background: rgba(29, 43, 61, 0.03);
          }
        }
      }
    }
    .account-management-list {
      flex: 1;
      margin: 24px 0 0 24px;
      overflow-y: auto;
      .more {
        margin-top: 24px;
        text-align: center;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.5);
        cursor: pointer;
      }
    }
  }
}

.content-wrapper {
  max-height: 300px;
  overflow-y: auto;
  margin-top: 24px;
  :global {
    .content-title {
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      margin-bottom: 6px;
    }
    .account-reject {
      margin-top: 6px;
    }
    .content-item {
      font-size: 12px;
      font-weight: 400;
      color: rgba(29, 43, 61, 0.75);
      line-height: 20px;
      margin-left: 8px;
    }
  }
}
