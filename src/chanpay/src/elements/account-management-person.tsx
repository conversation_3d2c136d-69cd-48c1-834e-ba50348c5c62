import React from 'react'
import { Popover } from 'antd'
import styles from './account-management-person.module.less'
import SVG_DEFAULT from '../images/avatar.svg'

interface Props {
  dataSource: Array<{
    name: string
    avatar: string
  }>
  type?: string
}

const ENGLISHNUM = 6 // 保留最多字母个数
const CHINESENUM = 3 // 保留最多中文个数

// 账户管理人员组件，用于展示有银行流水查看权限的人员信息
export default function AccountManagementPersonnel(props: Props) {
  const { dataSource, type } = props
  const CINDEX = type === 'MANAGEMENT' ? 3 : 5
  const count = dataSource.length
  const oDataSource = count > CINDEX ? dataSource.slice(0, CINDEX) : dataSource
  return (
    <div className={styles['personnel-wrapper']}>
      <div className="personnel-wrapper-oneLine">
        <div className="personnel-items">
          {oDataSource &&
            oDataSource.map((line: any, index: number) => {
              return <StaffItem key={index} line={line} i={index} />
            })}
          {count > CINDEX && renderMoreView(dataSource)}
        </div>
      </div>
    </div>
  )
}

// 渲染更多人员的弹出框
function renderMoreView(dataSource: any) {
  const count = dataSource.length
  const counStr = i18n.get(`共{__k0}人`, { __k0: count })
  return (
    <Popover
      content={popoverCardContent(dataSource)}
      title={counStr}
      placement="top"
      overlayClassName={styles['popoverCard']}
      overlayStyle={{ maxWidth: 408, maxHeight: 300, overflowY: 'auto' }}
      data-testid="pay-chanpay-account-management-more-popover"
    >
      <div className="countNumber">{counStr}</div>
    </Popover>
  )
}

// 渲染弹出框内容
function popoverCardContent(dataSource: any) {
  return dataSource.map((item: any, index: number) => {
    const avatar = item && item.avatar ? item.avatar : SVG_DEFAULT
    return (
      <div className="personnel-item" key={index}>
        {item && <img className="head-portrait" src={`${avatar}`} alt="" />}
        {item && <div className="person-name">{personName(item.name)}</div>}
      </div>
    )
  })
}

// 渲染单个人员项
function StaffItem(props: { line: any; i: number }) {
  const { line, i } = props
  const oName = line && line.name
  const cImg = line && line.avatar ? line.avatar : SVG_DEFAULT
  const cName = personName(oName)
  return (
    <div className="staff-item" key={i}>
      <div className="staff-avatar">
        <img className="head-portrait" src={`${cImg}`} alt="" />
      </div>
      <div className="staff-name">
        <div>{cName}</div>
      </div>
    </div>
  )
}

// 处理人员姓名显示，超出长度时显示省略号
function personName(name: any) {
  const isChina = /.*[\u4e00-\u9fa5]+.*$/.test(name)
  const index = isChina ? CHINESENUM : ENGLISHNUM
  if (name && name.length > index) {
    return name.substring(0, index) + '...'
  }
  return name
}
