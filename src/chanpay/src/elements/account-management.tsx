import { app, app as api } from '@ekuaibao/whispered'
import React, { PureComponent, Fragment } from 'react'
import { Modal } from 'antd'
import styles from './account-management.module.less'
import * as actions from '../chanpay.action'
const SearchInput = app.require('@elements/search-input')
import AccountManagementCard from './account-management-card'
const EmptyBody = app.require('@bills/elements/EmptyBody')
import { showMessage } from '@ekuaibao/show-util'

interface Props {
  customer: any
  changeEditFlag: Function
}

interface State {
  data: any
  isShowMore: boolean
  value: string
}

const placeholder = i18n.get('搜索账户名称、银行卡号')

// 账户管理组件，用于管理畅捷支付的银行账户列表
export default class AccountManagement extends PureComponent<Props, State> {
  start: number = 20
  count: number = 20
  constructor(props: Props) {
    super(props)
    this.state = {
      data: [],
      isShowMore: false,
      value: ''
    }
  }

  componentDidMount() {
    this.handleRefreshClick(true)
  }

  // 获取账户列表数据
  getList = (params: any) => {
    api.dispatch(actions.getChanpayList(params)).then((res: any) => {
      if (res) {
        this.start = this.count
        const { count, items } = res
        if (count < this.count) {
          this.setState({
            data: items,
            isShowMore: false
          })
        } else {
          this.setState({
            data: items,
            isShowMore: true
          })
        }
      }
    })
  }

  // 处理加载更多数据
  handleMoreClick = () => {
    const { value, data } = this.state
    const params = {
      filter: value,
      start: this.start,
      count: this.count
    }
    api.dispatch(actions.getChanpayList(params)).then((res: any) => {
      if (res) {
        this.start += this.count
        const { count, items } = res
        const cData = data.concat(items)
        if (count <= this.start) {
          this.start = this.count
          this.setState({
            data: cData,
            isShowMore: false
          })
        } else {
          this.setState({
            data: cData,
            isShowMore: true
          })
        }
      }
    })
  }

  // 处理搜索事件
  onSearch = (value: any) => {
    this.setState({ value })
    const params = {
      filter: value,
      start: 0,
      count: this.count
    }
    this.getList(params)
  }

  // 处理输入框变化事件
  getDataList = (e: any) => {
    if (!e.target.value) {
      this.setState({ value: '' })
      const params = {
        filter: '',
        start: 0,
        count: this.count
      }
      this.getList(params)
    }
  }

  // 渲染弹窗内容
  renderContent = (res: any) => {
    const { accountToPass, accountToReject } = res.value
    const passLength = accountToPass.length
    const rejectLength = accountToReject.length
    return (
      <div className={styles['content-wrapper']}>
        {!!passLength && (
          <Fragment>
            <div className="content-title">
              {i18n.get('以下{passLength}个账户审核通过，在易快报中创建支付账户后可使用', { passLength })}
            </div>
            {accountToPass.map((item: any, index: any) => {
              return (
                <div className="content-item" key={index}>
                  {item}
                </div>
              )
            })}
          </Fragment>
        )}
        {!!rejectLength && (
          <Fragment>
            <div className="content-title account-reject">
              {i18n.get('以下{rejectLength}个账户审核被拒绝，请联系银企联工作人员', { rejectLength })}
            </div>
            {accountToReject.map((item: any, index: any) => {
              return (
                <div className="content-item" key={index}>
                  {item}
                </div>
              )
            })}
          </Fragment>
        )}
      </div>
    )
  }

  // 处理刷新列表事件
  handleRefreshClick = (isRefresh: boolean) => {
    const { customer } = this.props
    const customerCode = customer[0]
    const successMessage = isRefresh ? i18n.get('刷新列表成功') : i18n.get('数据更新成功')
    const errorMessage = isRefresh ? i18n.get('刷新列表失败') : i18n.get('数据更新失败')
    api.dispatch(actions.getRefreshList({ customerCode })).then((res: any) => {
      const { status, accountToPass, accountToReject } = res.value
      const passLength = accountToPass.length
      const rejectLength = accountToReject.length
      if (status) {
        if (!passLength && !rejectLength) {
          showMessage.success(successMessage)
          this.handleOk()
        } else {
          Modal.success({
            title: successMessage,
            content: this.renderContent(res),
            onOk: this.handleOk,
            width: 600
          })
        }
      } else {
        showMessage.error(errorMessage)
      }
    })
  }

  // 处理确认事件
  handleOk = () => {
    this.setState({ value: '' })
    const params = {
      filter: '',
      start: 0,
      count: this.count
    }
    this.getList(params)
  }

  // 处理新增账户事件
  handleAddAccountClick = () => {
    const { customer } = this.props
    const customerCode = customer[0]
    api.open('@chanpay:AddAccountModal', { handleRefresh: this.handleRefreshClick, customerCode })
  }

  // 处理批量编辑权限事件
  handleBatchEditPermission = () => {
    api.open('@bills:ImportDetailByExcel', { type: 'editPermission' }).then(_ => {
      this.handleOk()
    })
  }

  render() {
    const { data, isShowMore, value } = this.state
    return (
      <div className={styles['account-management-wrapper']}>
        <div className="account-management-header">
          <div className="header-left">
            <div className="header-btn first-btn" onClick={this.handleAddAccountClick} data-testid="pay-chanpay-account-management-add">
              {i18n.get('新增账户')}
            </div>
            <div className="header-btn" onClick={this.handleBatchEditPermission} data-testid="pay-chanpay-account-management-batch-edit">
              {i18n.get('批量设置银行流水权限')}
            </div>
            <div className="header-btn" onClick={this.handleRefreshClick.bind(this, true)} data-testid="pay-chanpay-account-management-refresh">
              {i18n.get('刷新账户列表')}
            </div>
          </div>
          <div className="header-right">
            <SearchInput
              className="header-search"
              onSearch={this.onSearch}
              placeholder={placeholder}
              onChange={this.getDataList}
              value={value}
              data-testid="pay-chanpay-account-management-search"
            />
          </div>
        </div>
        {data.length ? (
          <div className="account-management-list">
            {data.map((item: any, index: number) => {
              return (
                <AccountManagementCard
                  data={item}
                  key={index}
                  getList={this.handleOk}
                  changeEditFlag={this.props.changeEditFlag}
                />
              )
            })}
            {isShowMore && (
              <div className="more" onClick={this.handleMoreClick} data-testid="pay-chanpay-account-management-load-more">
                {i18n.get('点击加载')}
              </div>
            )}
          </div>
        ) : (
          <EmptyBody label={i18n.get(`暂无数据`)} />
        )}
      </div>
    )
  }
}
