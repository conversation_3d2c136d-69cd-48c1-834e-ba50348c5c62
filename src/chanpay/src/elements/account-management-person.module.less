.personnel-wrapper {
  :global {
    .personnel-wrapper-oneLine {
      position: relative;
      height: 36px;
      overflow: hidden;
      .personnel-items {
        display: flex;
        position: relative;
        .countNumber {
          position: relative;
          display: inline-block;
          height: 26px;
          font-size: 14px;
          padding: 2px 10px 2px 10px;
          font-weight: 400;
          color: rgba(29, 43, 61, 0.5);
        }
        .staff-item {
          display: flex;
          height: 28px;
          align-content: center;
          border-radius: 32px;
          padding: 2px 10px 2px 2px;
          background-color: rgba(29, 43, 61, 0.09);
          margin-right: 8px;
          .staff-avatar {
            width: 24px;
            height: 24px;
            border-radius: 11px;
            img {
              width: 100%;
              height: 100%;
              border-radius: 11px;
            }
          }
          .staff-name {
            margin-left: 6px;
            font-size: 14px;
            color: #262626;
          }
        }
      }
    }
  }
}

.popoverCard {
  :global{
    .personnel-item {
      position: relative;
      display: inline-block;
      margin-right: 8px;
      margin-bottom: 10px;
      height: 26px;
      border-radius: 13px;
      padding-right: 10px;
      background-color: rgba(29, 43, 61, 0.09);
      .head-portrait {
        display: inline-block;
        width: 24px;
        height:24px;
        border-radius: 11px;
        margin: -1px 0 2px 2px;
      }
      .person-name {
        display: inline-block;
        height: 22px;
        font-size: 14px;
        margin-left: 6px;
        font-weight: normal;
        font-style: normal;
        font-stretch: normal;
        line-height: 1.8;
        letter-spacing: normal;
        text-align: justify;
        color: #262626;
      }
    }
  }
}
