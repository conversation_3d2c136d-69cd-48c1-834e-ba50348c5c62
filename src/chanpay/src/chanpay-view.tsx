import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { Tabs } from 'antd'
import styles from './chanpay-view.module.less'
import AccountManagement from './elements/account-management'
import DealFlow from './elements/deal-flow'
const Detail = app.require('@expansion-center/elements/ChargeCardDetail')
import * as actions from './chanpay.action'
import { EnhanceConnect } from '@ekuaibao/store'
import { get } from 'lodash'

const { TabPane } = Tabs

interface Props {
  value: any
  onRenewClick: Function
  userInfo: any
  needPermissionData?: any
}

interface State {
  customer: string[]
  bankStatementVisible: boolean
  account: string[]
  editFlag: boolean
}

// 畅捷支付视图组件，用于展示畅捷支付的主界面
@EnhanceConnect((state: any) => ({
  userInfo: state['@common'].userinfo.data
}))
export default class ChanPayView extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      customer: get(props, 'needPermissionData.customer', []),
      bankStatementVisible: false,
      account: [],
      editFlag: false
    }
  }

  // 组件挂载后获取银行流水可见性
  componentDidMount() {
    this.state.customer.length && this.getBankStatementVisible()
  }

  // 获取权限信息
  getPermission = () => {
    actions.checkHasPermission().then((res: any) => {
      this.setState({
        ...get(res, 'value', {})
      })
    })
  }

  // 获取银行流水可见性状态
  getBankStatementVisible = () => {
    actions.getBankStatementVisible().then((res: any) => {
      const bankStatementVisible = get(res, 'value.visible', false)
      const account = get(res, 'value.account', [])
      this.setState({ bankStatementVisible, account })
    })
  }

  // 认证处理
  certification = () => {
    actions.certification({ isBind: true, fn: this.getPermission })
  }

  // 处理标签页切换
  handleChange = (activeType: any) => {
    const { editFlag } = this.state
    if (activeType === 'dealFlow' && editFlag) {
      this.changeEditFlag(false)
      this.getBankStatementVisible()
    }
  }

  // 更改编辑标志状态
  changeEditFlag = (editFlag: boolean) => {
    this.setState({ editFlag })
  }

  // 渲染组件主界面
  render() {
    const { bankStatementVisible, customer, account, editFlag } = this.state
    const {
      userInfo: { permissions }
    } = this.props
    const isAdministrator = permissions && permissions.includes('SYS_ADMIN')

    if (customer.length) {
      return (
        <div className={styles['chanpay-wrapper']}>
          <Tabs animated={false} onChange={this.handleChange} data-testid="pay-chanpay-tabs">
            {isAdministrator && (
              <TabPane tab={i18n.get('账户管理')} key="accountManagement" data-testid="pay-chanpay-tab-account-management">
                <div className="account-management">
                  <AccountManagement customer={customer} changeEditFlag={this.changeEditFlag} />
                </div>
              </TabPane>
            )}
            {bankStatementVisible && (
              <TabPane tab={i18n.get('交易流水')} key="dealFlow" data-testid="pay-chanpay-tab-deal-flow">
                <DealFlow customer={customer} account={account} editFlag={editFlag} />
              </TabPane>
            )}
          </Tabs>
        </div>
      )
    }

    return (
      <div className={styles['chanpay-transparent']}>
        <Detail {...this.props} permissionCustomer={customer} certification={this.certification} />
      </div>
    )
  }
}
