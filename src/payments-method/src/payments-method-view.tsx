import { app } from '@ekuaibao/whispered'
/**
 *  Created by gym on 2018/3/14 上午11:27.
 */

import React, { Component } from 'react'
import { Switch } from 'antd'
import { EnhanceConnect } from '@ekuaibao/store'
import styles from './payments-method-view.module.less'
import ActionFooterBar from './elements/ActionFooterBar'
import PaytypeForm from './payments-type-form'
import { getPayments, getPaymentsOpportunity } from './payments-method-action'
import { app as api } from '@ekuaibao/whispered'
import key from './key'
import { MessageCenter } from '@ekuaibao/messagecenter'
import classnames from 'classnames'
import { showModal } from '@ekuaibao/show-util'
import { observer } from 'mobx-react';
import { inject, provider } from '@ekuaibao/react-ioc';
import { PermissionVm } from './vms/Permission.vm'
const ImmersiveToolBar = app.require('@elements/ImmersiveToolBar')
const SearchBarForTitle = app.require('@elements/SearchBarForTitle/SearchBarForTitle')
const EkbHighLighter = app.require('@elements/EkbHighLighter')
const NoResultViewForSearch = app.require('@elements/SearchBarForTitle/NoResultViewForSearch')

const confirm = showModal.confirm
import decorators from '@ekuaibao/lib/lib/decorators'
import EMPTY_DETAIL from './resources/empty-detail.svg'

@EnhanceConnect(state => ({
  payments: state[key.ID].payments,
  payOpportunityList: state[key.ID].payOpportunityList
}))
@provider(['permission', PermissionVm])
@observer
export default class PaymentsMethodView extends Component {
  @inject('permission') permission: any

  constructor(props) {
    super(props)
    this.bus = new MessageCenter()
    this.state = {
      isActive: true,
      selected: undefined,
      filterList: [],
      searchValue: '',
      isAdd: false,
      listData: [],
      inSearch: false
    }
    this.checkModify = this.checkModify.bind(this)
    decorators.layoutMenuWillLeave('financial')(this.checkModify)
    decorators.layoutMenuWillLeave('payments-method')(this.checkModify)
  }

  componentWillMount() {
    api.dispatch(getPaymentsOpportunity())
    this.getPaymentList()
    this.bus.on('list:change', this.getPaymentList)
    this.bus.on('recompose:finish', this.getRecompose)
    api.invokeService('@common:get:mc:permission:byName', 'SETTLEMENT').then(result => {
      if (result?.value) {
        this.permission?.setEnableMC(result?.value);
      }
    })
  }

  componentWillUnmount() {
    decorators.removeListener('financial')
    decorators.removeListener('payments-method')
    this.bus.un('list:change', this.getPaymentList)
    this.bus.un('recompose:finish', this.getRecompose)
  }

  checkModify() {
    return new Promise((resolve, reject) => {
      if (this.refs['payTypeForm']) {
        this.refs['payTypeForm'].getDiff().then(diff => {
          if (diff) {
            resolve()
          } else {
            confirm({
              title: i18n.get('提示'),
              content: i18n.get('您的结算方式尚未保存，是否放弃编辑当前结算方式？'),
              onOk() {
                resolve()
              },
              onCancel() {
                reject()
              }
            })
          }
        })
      } else {
        resolve()
      }
    })
  }

  getPaymentList = isAddInfo => {
    const { isActive, isAdd, inSearch, searchValue } = this.state
    api.dispatch(getPayments(isActive)).then(payload => {
      const stateParam = {}
      if (inSearch) {
        stateParam.filterList = this.fnGetFilterList(searchValue)
      }
      let { items } = payload
      if (isAdd && !isAddInfo) {
        stateParam.selected = items.length ? items[items.length - 1] : {}
      }
      this.setState(stateParam)
    })
  }

  getRecompose = data => {
    this.setState({
      selected: data.value
    })
  }

  handleSearch = e => {
    let { value } = e.target
    if (value.trim().length) {
      let filterList = this.fnGetFilterList(value)
      this.setState({ inSearch: true, filterList, searchValue: value })
    } else {
      this.setState({ inSearch: true, filterList: [], searchValue: '' })
    }
  }

  fnGetFilterList = searchValue => {
    if (searchValue.trim().length) {
      let { payments } = this.props
      return payments.filter(v => {
        return !!~v.name.indexOf(searchValue) || !!~v.nameSpell.indexOf(searchValue)
      })
    }
    return []
  }

  handleAddClick = () => {
    this.checkModify().then(_ => {
      this.setState({ isAdd: true, selected: undefined }, () => {
        this.bus.emit('payment:add:click')
      })
    })
  }

  handleSelectItem(item) {
    this.checkModify().then(_ => {
      this.setState({ selected: item, isAdd: false }, () => {
        this.bus.emit('payment:select:change')
      })
    })
  }

  switchOnChange(value) {
    let isAddInfo = true
    this.setState({ isActive: !value }, _ => {
      this.getPaymentList(isAddInfo)
    })
  }

  renderNull = () => {
    const { searchValue } = this.state
    return <NoResultViewForSearch searchKey={searchValue} />
  }

  renderPaymentList() {
    let { filterList, selected, searchValue, inSearch } = this.state
    let { payments = [] } = this.props
    const items = inSearch ? filterList : payments
    if (inSearch && (searchValue === '' || !items.length)) return this.renderNull()

    return (
      <ul className="payment-way">
        {items.length > 0 ? (
          items.map(value => {
            return (
              <li
                className={classnames('payment-list', { selected: selected && selected.id === value.id })}
                style={{ color: value.active ? '' : '#9e9e9e' }}
                key={value.id}
                onClick={this.handleSelectItem.bind(this, value)}
                data-testid={`pay-paymentsMethodView-list-item-${value.id}`}
              >
                <EkbHighLighter
                  highlightClassName="highlight"
                  searchWords={[searchValue]}
                  textToHighlight={value.name}
                />
                {!value.active && i18n.get('(已停用)')}
              </li>
            )
          })
        ) : (
          <div className="empty-wrapper">
            <img className="empty-image" src={EMPTY_DETAIL} />
            <p className="empty-tip">{i18n.get('您的结算方式将显示在这里')}</p>
          </div>
        )}
      </ul>
    )
  }

  renderDetail() {
    const { selected } = this.state
    const title = selected ? selected.name : i18n.get('新建结算方式')
    return (
      <div className="triptype-detail">
        <div className="detail-header horizontal">
          <div className="title">{title}</div>
        </div>
        <div className="type-children vertical">
          <PaytypeForm
            ref="payTypeForm"
            bus={this.bus}
            payOpportunityList={this.props.payOpportunityList}
            selectedInfo={selected}
          />
        </div>
      </div>
    )
  }

  handleClickSearchBtn = () => {
    this.setState({ inSearch: true })
  }

  handleClickBackBtn = () => {
    this.setState({ inSearch: false, searchValue: '' })
  }

  renderTitle = () => {
    return (
      <>
        <div className="grow immersive-title">{i18n.get('结算方式')}</div>
        <svg className="icon stand-icon mr-8 cur-p flex-s-0" aria-hidden="true" onClick={this.handleClickSearchBtn} data-testid="pay-paymentsMethodView-search-button">
          <use xlinkHref="#EDico-search" />
        </svg>
        {
          this.permission.isEnableMC && (
            <svg className="icon stand-icon cur-p" aria-hidden="true" onClick={this.handleAddClick} data-testid="pay-paymentsMethodView-add-button">
              <use xlinkHref="#EDico-plus-default" />
            </svg>
          )
        }
      </>
    )
  }

  render() {
    const { inSearch } = this.state
    return (
      <div id={'payments-method'} className={styles['payment-wrapper']}>
        <div className="payment-content-left">
          <ImmersiveToolBar hide={inSearch} titleType={'main'} otherView={() => this.renderTitle()} />

          {inSearch && (
            <SearchBarForTitle
              fnOnChange={this.handleSearch}
              placeholder={i18n.get('请输入结算方式名称')}
              fnBack={this.handleClickBackBtn}
              data-testid="pay-paymentsMethodView-search-bar"
            />
          )}
          <div className="custom-triptype-content-wrapper">
            {this.renderPaymentList()}

          </div>
          <div className="triptype-footer">
            <span>{i18n.get('显示已停用')}</span>
            <Switch
              className="triptype-sw-last"
              checked={!this.state.isActive}
              onChange={this.switchOnChange.bind(this)}
              data-testid="pay-paymentsMethodView-show-disabled-switch"
            />
          </div>
        </div>

        {this.state.isAdd || this.state.selected ? (
          <div className="layout-content-right">
            {this.renderDetail.call(this)}
            <ActionFooterBar
              bus={this.bus}
              refresh={selected => this.getPaymentList(selected)}
              doc={this.state.selected}
            />
          </div>
        ) : (
          <div className="empty-body">
            <img className="no-flow-img" src={EMPTY_DETAIL} />
          </div>
        )}
      </div>
    )
  }
}
