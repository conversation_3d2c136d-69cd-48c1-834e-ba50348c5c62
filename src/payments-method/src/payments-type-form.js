import { app } from '@ekuaibao/whispered'
/**
 *  Created by gym on 2018/3/14 下午4:08.
 */
import React, { PureComponent } from 'react'
import styles from './payments-type-form.module.less'

import { Form, Input, Select, Row, Col } from 'antd'
import { app as api } from '@ekuaibao/whispered'
import { savePayment, recompose } from './payments-method-action'

const FormItem = Form.Item
const Option = Select.Option
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')

const formItemLayout = { labelCol: { span: 4 }, wrapperCol: { span: 16 } }

@EnhanceFormCreate()
export default class PaytypeForm extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      opportunity: {},
      selectPeriod: {},
      inputValue: ''
    }
    props.overrideGetDiff && props.overrideGetDiff(this.getDiff.bind(this))
  }

  getDiff = () => {
    let curDoc = JSON.stringify(this.getCurrentDoc())
    let initDoc = JSON.stringify(this.getInitDoc())
    let diff = curDoc === initDoc
    return Promise.resolve(diff)
  }

  getCurrentDoc() {
    let values = this.props.form.getFieldsValue()

    return {
      name: values.name
    }
  }

  getInitDoc() {
    let doc = this.props.selectedInfo
    if (doc) {
      return (
        doc && {
          name: doc.name
        }
      )
    } else {
      return {
        name: ''
      }
    }
  }

  componentWillMount() {
    const { bus } = this.props
    bus.on('payment:save:click', this.fnSavePayType)
    bus.on('payment:add:click', this.fnAddClick)
    bus.on('payment:select:change', this.fnPaymenShow)
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('payment:save:click', this.fnSavePayType)
    bus.un('payment:add:click')
    bus.un('payment:select:change')
  }

  componentDidMount() {
    this.fnPaymenShow({})
  }

  fnSavePayType = () => {
    this.props.form.validateFieldsAndScroll((err, values) => {
      if (!!err) return
      let { selectedInfo } = this.props
      if (selectedInfo) {
        api.dispatch(recompose(selectedInfo, values)).then(data => {
          this.props.bus.emit('list:change')
          this.props.bus.emit('recompose:finish', data)
        })
      } else {
        api.dispatch(savePayment(values)).then(_ => {
          this.props.bus.emit('list:change')
        })
      }
    })
  }

  fnAddClick = () => {
    this.setState({
      opportunity: {}
    })
    const fieldValues = {
      name: '',
      opportunity: '',
      period: ''
    }
    let { setFieldsValue } = this.props.form
    setFieldsValue(fieldValues)
  }

  fnPaymenShow = values => {
    const { payOpportunityList, selectedInfo } = this.props
    if (selectedInfo) {
      const payOpportiunity = payOpportunityList.find(item => item.key === selectedInfo.opportunity)
      this.setState({ opportunity: payOpportiunity }, _ => {
        const { name } = this.props.selectedInfo
        values = {
          name: name,
          opportunity: payOpportiunity.key
        }
        if (payOpportiunity.period) {
          const selectPeriod = payOpportiunity.period.find(item => item.key === selectedInfo.period)
          values.period = selectPeriod.key
        }
        let { setFieldsValue } = this.props.form
        setFieldsValue(values)
      })
    }
  }

  handleSelectOpportunity = key => {
    let { payOpportunityList } = this.props
    let opportunity = payOpportunityList.find(line => line.key === key)
    this.setState({ opportunity })
  }

  handleSelectPeriod = key => {
    const {
      opportunity: { period }
    } = this.state
    const selectPeriod = period.find(line => line.key === key)
    this.setState({ selectPeriod })
  }

  handleInputChange = e => {
    this.setState({
      inputValue: e.target.value
    })
  }

  renderContent() {
    let { payOpportunityList, selectedInfo, form } = this.props
    const isDisabled = selectedInfo ? true : false
    let { opportunity = {} } = this.state
    let { getFieldDecorator } = form
    return (
      <div className="header-bar">
        <Form layout="vertical">
          <FormItem id="control-input" label={i18n.get('结算方式名称')}>
            {getFieldDecorator('name', {
              rules: [
                { required: true, whitespace: true, message: i18n.get('结算方式名称不能为空') },
                { max: 14, message: i18n.get('名称不能超过14个字') }
              ]
            })(<Input onChange={this.handleInputChange} data-testid="pay-paymentsTypeForm-name-input" />)}
          </FormItem>

          <FormItem id="select" label={i18n.get('结算时机')}>
            <Row>
              <Col>
                {getFieldDecorator('opportunity', {
                  rules: [{ required: true, message: i18n.get('结算时机不能为空') }]
                })(
                  <Select disabled={isDisabled} onSelect={this.handleSelectOpportunity} data-testid="pay-paymentsTypeForm-opportunity-select">
                    {payOpportunityList.map(({ value, key }) => {
                      return <Option key={key}>{value}</Option>
                    })}
                  </Select>
                )}
              </Col>

              {opportunity.period ? (
                <Col style={{ marginTop: 8 }}>
                  {getFieldDecorator('period', {
                    initialValue: opportunity.period.length && opportunity.period[0].key,
                    rules: [{ required: true, message: i18n.get('结算时机不能为空') }]
                  })(
                    <Select disabled={isDisabled} onSelect={this.handleSelectPeriod} data-testid="pay-paymentsTypeForm-period-select">
                    {opportunity.period.map(({ value, key }) => {
                      return <Option key={key}>{value}</Option>
                    })}
                  </Select>
                  )}
                </Col>
              ) : null}
            </Row>
          </FormItem>
        </Form>
        <div className="payment-tip">
          {i18n.get('结算方式一旦选择不可修改。如果与扩展的结算方式发生了变化，请停用此扩展,再创建一个新的消费扩展。')}
        </div>
      </div>
    )
  }

  render() {
    return (
      <div className={styles['Paytype-form']}>
        <div className="paytype-main">{this.renderContent()}</div>
      </div>
    )
  }
}
