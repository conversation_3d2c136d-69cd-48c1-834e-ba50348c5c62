import styles from './ActionFooterBar.module.less'
import React, { PureComponent } from 'react'
import { Button } from 'antd'
import { showModal } from '@ekuaibao/show-util'
import { app as api } from '@ekuaibao/whispered'
import { restorePaymentType } from '../payments-method-action'
import { cloneDeep } from 'lodash'
import { observer } from 'mobx-react';
import { inject } from '@ekuaibao/react-ioc';

@observer
export default class ActionFooterBar extends PureComponent {
  @inject('permission') permission: any

  constructor(props) {
    super(props)
    let { doc } = this.props
    this.state = {
      active: doc && doc.active
    }
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.doc !== nextProps.doc) {
      this.setState({
        active: nextProps.doc && nextProps.doc.active
      })
    }
  }

  handleDisabled = () => {
    let { doc, refresh } = this.props
    let { active } = this.state
    const data = cloneDeep(doc)
    showModal.confirm({
      title: i18n.get(`确定{__k0}`, { __k0: active ? i18n.get('停用') : i18n.get('启用') }),
      content: active ? i18n.get('停用后将不在用户端展示') : '',
      onOk: () =>
        api.dispatch(restorePaymentType({ id: data.id, active: !active })).then(item => {
          this.setState({
            active: item.value.active
          })
          refresh()
        })
    })
  }

  handleSave = () => {
    let { bus } = this.props
    bus.emit('payment:save:click')
  }

  render() {
    let { doc } = this.props
    let { active } = this.state
    const isDisabled = !this.permission.isEnableMC
    return (
      <div className={styles['action-footer-bar']}>
        <div className="horizontal">
          {doc && (
            <Button disabled={isDisabled} className="mr-10" onClick={this.handleDisabled} data-testid="pay-actionFooterBar-disable-button">
              {i18n.get(active ? i18n.get('停 用') : i18n.get('启 用'))}
            </Button>
          )}
          <Button disabled={isDisabled} type="primary" onClick={this.handleSave} data-testid="pay-actionFooterBar-save-button">
            {i18n.get('保 存')}
          </Button>
        </div>
      </div>
    )
  }
}
