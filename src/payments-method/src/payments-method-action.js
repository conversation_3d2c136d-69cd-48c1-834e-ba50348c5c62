/**
 *  Created by gym on 2018/3/14 上午11:26.
 */
import key from './key'
import { Resource } from '@ekuaibao/fetch'
import { QuerySelect } from 'ekbc-query-builder'

const payment = new Resource('/api/v1/basedata/settlement')

export function getPayments(isActive = true) {
  const query = new QuerySelect().orderBy('createTime', 'ASC')
  if (isActive) {
    query.filterBy('active==true')
  }
  return {
    type: key.GET_PAYMENTS_LIST,
    payload: payment.POST('/search', query.value())
  }
}

export function savePayment(params) {
  return {
    type: key.SAVE_PAYMENT,
    payload: payment.POST('', params)
  }
}

export function recompose(data, params) {
  params.id = data.id
  return {
    type: key.RECOMPOSE_PAYMENT,
    payload: payment.PUT('/$id', params)
  }
}

export function getPaymentsOpportunity() {
  return {
    type: key.GET_OPPORTUNITY_LIST,
    payload: payment.GET('/getOpportunity')
  }
}

export function restorePaymentType(params) {
  const { id, active } = params
  return {
    type: key.RESTORE_PAYMENT_TYPE,
    payload: payment.PUT(`/disableOrOpen/$id`, { id }, { active })
  }
}
