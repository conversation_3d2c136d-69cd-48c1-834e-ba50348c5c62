@import '~@ekuaibao/web-theme-variables/styles/default';
@import '~@ekuaibao/eui-styles/less/token.less';

@white: @input-bg;
.payment-wrapper {
  height: 100%;
  display: flex;
  flex-direction: row;
  :global {
    .payment-content-left {
      width: 300px;
      background-color: @white;
      display: flex;
      flex-direction: column;
      position: relative;
      border-right: 1px solid @color-line-1;
      flex-shrink: 0;
      .list-header {
        height: 50px;
        padding-left: 18px;
        padding-right: 18px;
        border-bottom: 1px solid #e6e6e6;
        flex-shrink: 0;
        .title {
          font-size: 14px;
          font-weight: 600;
        }
        .add-icon {
          width: 20px;
          height: 20px;
          margin-top: 5px;
          cursor: pointer;
        }
      }
      .custom-triptype-content-wrapper {
        display: flex;
        flex-direction: column;
        overflow: hidden;
        flex: 1;
        .triptype-search {
          display: flex;
          height: 50px;
          padding: 12px 16px;
          flex-shrink: 0;
          border-bottom: 1px solid #efefef;
          .search-input {
            flex: 1;
          }
        }
        .payment-way {
          flex: 1;
          overflow-y: auto;
          overflow-x: hidden;
          .empty-wrapper {
            display: flex;
            flex-direction: column;
            padding-top: 120px;
            justify-content: center;
            align-items: center;
            .empty-image {
              width: 150px;
              height: 100px;
            }
            .empty-tip {
              width: 144px;
              height: 17px;
              font-size: 12px;
              text-align: left;
              color: #9e9e9e;
            }
          }
          .selected {
            background-color: @color-bg-2;
          }
          .payment-list {
            .font-size-2;
            width: 300px;
            height: 40px;
            padding: 0 @space-7;
            margin-bottom: @space-2;
            color: #333333;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            &:hover {
              background-color: @color-bg-2;
            }
          }
        }
      }
      .triptype-footer {
        bottom: 0;
        display: flex;
        justify-content: space-between;
        height: 50px;
        flex-shrink: 0;
        .shadow-black-3;
        align-items: center;
        padding: 0 @space-7;
        span {
          .font-size-2;
          color: @color-black-2;
        }
        .triptype-sw-last {
          margin-left: 8px;
        }
      }
    }
    .layout-content-right {
      margin-right: 16px;
      display: flex;
      flex-grow: 1;
      flex-direction: column;
      position: relative;
      border-radius: 4px;
      background-color: @white;
      .triptype-detail {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        justify-content: flex-start;
        flex-grow: 1;
        .detail-header {
          height: 50px;
          padding: 0 24px;
          flex-shrink: 0;
          display: flex;
          border-bottom: 1px solid #e6e6e6;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          .title {
            font-size: 20px;
            font-weight: 600;
            color: #333333;
            line-height: 28px;
          }
        }
      }
    }
    .empty-body {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      flex: 1;
      background-color: @white;
      .no-flow-img {
        width: 250px;
        height: 166px;
      }
    }
  }
}
