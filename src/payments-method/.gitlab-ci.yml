variables:
  GIT_SUBMODULE_STRATEGY: recursive

before_script:
  - git config --global user.name "${GITLAB_USER_NAME}"
  - git config --global user.email "${GITLAB_USER_EMAIL}"

stages:
  - report
  - build

build:
  stage: build
  image: registry.ekuaibao.com/ci/ci-nodejs:14.5.0
  script:
    - npm install
    - npm run build
    - npm publish build
    - npm run upload_plugin_to_cdn
  only:
    - /^v?\d+(\.\d+)+[\.\-_\w]*/
  tags:
    - i9

sonarqube-check:
  stage: report
  image: 
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script: 
    - sonar-scanner
  allow_failure: true
  tags:
    - i9
