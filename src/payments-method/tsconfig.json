{"compilerOptions": {"importHelpers": true, "allowSyntheticDefaultImports": true, "sourceMap": false, "declaration": true, "target": "ES5", "module": "commonjs", "moduleResolution": "node", "jsx": "react", "esModuleInterop": true, "strictFunctionTypes": true, "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitReturns": true, "noResolve": false, "removeComments": true, "strictNullChecks": false, "inlineSourceMap": false, "emitDecoratorMetadata": true, "experimentalDecorators": true, "outDir": "dist", "rootDir": ".", "skipLibCheck": true, "lib": ["dom", "es5", "es6", "es7", "es2015.promise", "es2018.promise", "es2015.collection", "es2015.core", "es2015", "es2016", "es2016.array.include", "es2017", "es2017.object", "es2018", "es2015.iterable"]}}