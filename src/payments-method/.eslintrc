{"parser": "@typescript-eslint/parser", "extends": ["plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:jsx-control-statements/recommended", "prettier/@typescript-eslint", "plugin:prettier/recommended"], "settings": {"react": {"version": "detect"}}, "plugins": ["@typescript-eslint", "react", "jsx-control-statements", "prettier"], "env": {"browser": true, "node": true, "es6": true, "jsx-control-statements/jsx-control-statements": true}, "globals": {"$": true}, "rules": {"prettier/prettier": 1, "no-console": ["warn", {"allow": ["warn", "error"]}], "eqeqeq": ["warn", "always"], "prefer-const": ["error", {"destructuring": "all", "ignoreReadBeforeAssign": true}], "@typescript-eslint/ban-ts-comment": 0, "@typescript-eslint/indent": ["error", 2, {"VariableDeclarator": 2, "SwitchCase": 1}], "@typescript-eslint/no-unused-vars": 0, "@typescript-eslint/no-var-requires": 0, "@typescript-eslint/no-explicit-any": 0, "@typescript-eslint/interface-name-prefix": 0, "@typescript-eslint/explicit-member-accessibility": 0, "@typescript-eslint/explicit-module-boundary-types": 0, "@typescript-eslint/no-triple-slash-reference": 0, "@typescript-eslint/ban-ts-ignore": 0, "@typescript-eslint/no-this-alias": 0, "@typescript-eslint/triple-slash-reference": ["error", {"path": "always", "types": "never", "lib": "never"}], "react/jsx-indent": [2, 2], "react/prop-types": 0, "react/jsx-no-undef": [2, {"allowGlobals": true}], "jsx-control-statements/jsx-use-if-tag": 0}}